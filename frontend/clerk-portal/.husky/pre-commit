#!/bin/sh
. "$(dirname "$0")/_/husky.sh"
echo 'Styling, testing and building before committing...'
# Check Prettier standards
npm run check-format ||
(
 echo 'Your code styling looks not well. <PERSON>tti<PERSON> failed. Please run npm run format, add
changes and try commit again.';
 false;
)
# Check ESLint Standards
npm run check-lint ||
(
 echo 'ESLint failed. Make the required changes listed above, add changes and try to commit
again.'
 false;
)
# Check tsconfig standards
npm run check-types ||
(
 echo 'Failed Type check. Make the changes required above, add changes and try to commit
again.'
 false;
)
# If everything passes... Now we can commit
echo 'The code looks good... Trying to build...'
npm run build ||
(
 echo ' Your build failed
 View the errors above to see why.'
 false;
)
# If everything passes... Now we can commit
echo ' Pre-commit passed successfully'