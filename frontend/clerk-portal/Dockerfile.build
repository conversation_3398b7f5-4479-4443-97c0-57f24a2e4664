# Build the Next.js app
ARG NODE_VERSION=20.18.1

FROM gitlab.interzero.de/software-development/dependency_proxy/containers/node:${NODE_VERSION}-alpine

ARG API="http://oneepr:8080"

# Install dependencies
RUN apk add --no-cache libc6-compat

WORKDIR /app

RUN npm install -g corepack --verbose

# Install dependencies based on the preferred package manager
COPY package.json .npmrc pnpm-lock.yaml* ./
RUN corepack enable && \
    corepack prepare pnpm@9.12.2 --activate && \
    pnpm config set store-dir /tmp/.pnpm-store;

RUN pnpm i --frozen-lockfile

# Copy the rest of the app's source code
COPY . .

ENV NEXT_TELEMETRY_DISABLED=1

RUN echo "$API"

# Build the app
RUN pnpm build
