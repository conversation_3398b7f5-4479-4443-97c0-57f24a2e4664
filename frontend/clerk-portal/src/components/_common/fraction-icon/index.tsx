import { cn } from "@/lib/utils";
import NextImage from "next/image";
interface FractionIconProps {
  size: "small" | "medium" | "large";
  iconUrl: string;
  className?: string;
}

export type FractionIconType = keyof typeof fractionSvgs;

type FractionIconSvgProps = React.ComponentProps<"svg">;

const fractionSvgs = {
  aluminium: (props: FractionIconSvgProps) => (
    <svg {...props} viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M29.1145 20.5407H7.67993V40.6541H29.1145V20.5407Z" fill="#002652" />
      <path
        d="M38.9772 5.46783L36.4259 2.91987H37.3858C38.1561 2.91987 38.7806 2.29616 38.7806 1.52693V1.39294C38.7806 0.623707 38.1561 0 37.3858 0H20.8502C20.0799 0 19.4554 0.623707 19.4554 1.39294V1.52693C19.4554 2.29616 20.0799 2.91987 20.8502 2.91987H21.8101L19.2588 5.46783C18.7083 6.01761 18.4007 6.76144 18.4007 7.53761V11.8389H10.1294C9.35917 11.8389 8.73466 12.4626 8.73466 13.2318V13.3658C8.73466 14.135 9.35917 14.7587 10.1294 14.7587H10.9274L8.53806 17.145C8.07777 17.6047 7.78864 18.2007 7.70769 18.8383H29.0868C29.0035 18.2007 28.7167 17.6024 28.2564 17.145L25.8648 14.7564H26.6628C27.433 14.7564 28.0575 14.1327 28.0575 13.3635V13.2295C28.0575 12.4603 27.433 11.8366 26.6628 11.8366H23.1586C24.6134 9.25627 26.4985 7.697 28.5617 7.697C33.1623 7.697 36.8932 15.4541 36.8932 25.0245C36.8932 34.5949 33.7475 42.352 29.1469 42.352H7.70769C7.79096 42.9872 8.08008 43.5809 8.53806 44.0406L11.6444 47.143C12.1949 47.6928 12.9397 48 13.7169 48H23.0799C23.8571 48 24.6019 47.6928 25.1524 47.143L26.406 45.8909H33.803C34.5802 45.8909 35.3249 45.5837 35.8754 45.0339L38.9818 41.9316C39.5323 41.3818 39.8399 40.638 39.8399 39.8618V7.53761C39.8399 6.76144 39.5323 6.01761 38.9818 5.46783H38.9772Z"
        fill="#002652"
      />
    </svg>
  ),
  "composite-beverage-cartoons": (props: FractionIconSvgProps) => (
    <svg {...props} viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M36.9964 9.30011L30.7635 2.99673V1.09785C30.7635 0.491088 30.2779 0 29.6779 0H28.2168C27.6168 0 27.1312 0.491088 27.1312 1.09785V3.27392L24.7658 5.66606H15.4229C14.2036 5.66606 13.2173 6.6657 13.2173 7.89669V11.5853H15.5675V12.9625H23.3414V11.5853H24.7378C25.3032 11.5853 25.8277 11.8931 26.1061 12.3907L29.311 18.0939H9.6001V44.8156C9.6001 46.5748 11.0094 48 12.7489 48H26.1622C27.9017 48 29.311 46.5748 29.311 44.8156V44.3485H34.7713C36.5108 44.3485 37.9201 42.9232 37.9201 41.1641V11.5526C37.9201 10.7079 37.5877 9.89814 36.9985 9.30011H36.9964ZM19.4545 8.61695C21.6019 8.61695 23.3414 9.12332 23.3414 9.74973C23.3414 10.3761 21.6019 10.8825 19.4545 10.8825C17.3071 10.8825 15.5675 10.3761 15.5675 9.74973C15.5675 9.12332 17.3071 8.61695 19.4545 8.61695Z"
        fill="#002652"
      />
    </svg>
  ),
  "composite-beverage": (props: FractionIconSvgProps) => (
    <svg {...props} viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M12.0333 39.126L12.5783 44.6199C12.5783 46.0415 17.6925 48 24.0026 48C30.3126 48 35.4268 46.0415 35.4268 44.6199L35.9719 39.126C33.0668 39.9158 28.7834 40.4133 24.0052 40.4133C19.227 40.4133 14.9409 39.9131 12.0359 39.126H12.0333Z"
        fill="#002652"
      />
      <path
        d="M8.15991 10.4771V11.9434C8.15991 12.4752 8.6335 12.9832 9.48542 13.4413L10.93 28.0118C13.7847 28.9937 18.5735 29.6361 23.9999 29.6361C29.4263 29.6361 34.2151 28.9937 37.0698 28.0118L38.5144 13.4413C39.3663 12.9832 39.8399 12.4752 39.8399 11.9434V10.4771C36.3634 12.4225 28.622 13.0043 23.9999 13.0043C19.3778 13.0043 11.6364 12.4225 8.15991 10.4771Z"
        fill="#002652"
      />
      <path
        d="M38.4483 6.43896L36.6571 1.25304C36.6571 2.90095 30.9899 4.23824 23.9999 4.23824C17.0099 4.23824 11.3507 2.90359 11.3427 1.25568L9.55421 6.43633C8.65731 6.9049 8.15991 7.42349 8.15991 7.97104V8.26588C8.33188 8.5765 9.24995 9.36887 12.356 10.1033C15.4568 10.8351 19.5921 11.2379 23.9999 11.2379C28.4077 11.2379 32.543 10.8351 35.6438 10.1033C38.7499 9.3715 39.6679 8.5765 39.8399 8.26588V7.97104C39.8399 7.42349 39.3425 6.9049 38.4483 6.43633V6.43896Z"
        fill="#002652"
      />
      <path
        d="M23.9999 2.50609C20.6187 2.50609 17.9518 2.20336 16.0733 1.82165C15.4568 1.69793 15.4568 0.810793 16.0733 0.684436C17.9518 0.302731 20.6187 0 23.9999 0C27.3812 0 30.0481 0.302731 31.9265 0.684436C32.543 0.808161 32.543 1.69529 31.9265 1.82165C30.0481 2.20336 27.3812 2.50609 23.9999 2.50609Z"
        fill="#002652"
      />
    </svg>
  ),
  "ferrous-metal": (props: FractionIconSvgProps) => (
    <svg {...props} viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M42.5411 18.0196C41.6217 17.7533 40.4491 17.4749 38.9601 17.2056C35.572 16.5914 31.4273 16.2011 27.0475 16.077C28.5366 13.4657 29.5192 9.69859 29.5192 8.4338C29.5192 6.42769 27.0475 4.7998 24 4.7998C20.9525 4.7998 18.4808 6.42769 18.4808 8.4338C18.4808 9.69859 19.4635 13.4657 20.9525 16.077C16.5727 16.2011 12.428 16.5914 9.03994 17.2056C7.55087 17.4749 6.3783 17.7563 5.45893 18.0196C4.73851 18.2284 4.73851 19.2571 5.45893 19.4659C6.3783 19.7322 7.55087 20.0106 9.03994 20.2799C13.2057 21.0363 18.517 21.4508 23.997 21.4508C29.477 21.4508 34.7913 21.0333 38.954 20.2799C40.4431 20.0106 41.6157 19.7292 42.535 19.4659C43.2555 19.2571 43.2555 18.2284 42.535 18.0196H42.5411ZM20.6752 8.42775C20.6752 7.60171 22.1643 6.933 24.003 6.933C25.8417 6.933 27.3308 7.60171 27.3308 8.42775C27.3308 9.2538 25.8417 9.9225 24.003 9.9225C22.1643 9.9225 20.6752 9.2538 20.6752 8.42775ZM24.003 16.431C23.0354 16.431 22.2517 15.6443 22.2517 14.673C22.2517 13.7017 23.0354 12.915 24.003 12.915C24.9706 12.915 25.7543 13.7017 25.7543 14.673C25.7543 15.6443 24.9706 16.431 24.003 16.431Z"
        fill="#002652"
      />
      <path
        d="M24.003 23.9441C10.746 23.9441 0 21.6173 0 18.7427V38.4135C0.0693293 41.2729 10.7882 43.5877 24 43.5877C37.2118 43.5877 47.9337 41.2729 48 38.4135V18.7427C48 21.6173 37.254 23.9441 23.997 23.9441H24.003ZM36.6601 36.0443C36.6601 36.9672 35.9578 37.7388 35.0384 37.8144C31.5388 38.1019 27.8071 38.2502 23.9759 38.2502C20.1447 38.2502 16.4491 38.1019 12.9646 37.8175C12.0482 37.7418 11.3429 36.9733 11.3429 36.0474V31.1213C11.3429 30.0835 12.2261 29.2665 13.254 29.3543C16.3557 29.6175 19.9216 29.7809 23.9729 29.7809C28.0241 29.7809 31.6323 29.6175 34.743 29.3512C35.7739 29.2635 36.6541 30.0805 36.6541 31.1183V36.0443H36.6601Z"
        fill="#002652"
      />
    </svg>
  ),
  glass: (props: FractionIconSvgProps) => (
    <svg {...props} viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M36 20.204C35.9595 17.7266 34.6738 15.5503 32.7335 14.2558C31.8678 13.6791 31.3603 12.7046 31.3603 11.6732V0.816663C31.3603 0.366235 30.9893 0 30.533 0H26.6481C26.1918 0 25.8208 0.366235 25.8208 0.816663V11.6859C25.8208 12.7193 25.3005 13.6854 24.4349 14.2663C23.0873 15.1693 22.0574 16.4974 21.5436 18.055C21.3666 17.6951 21.1854 17.3373 20.9935 16.9836C20.4284 15.9418 20.132 14.7778 20.132 13.597V6.94164C20.132 6.26389 19.5755 5.71454 18.889 5.71454H17.245C16.5585 5.71454 16.0019 6.26389 16.0019 6.94164V13.597C16.0019 14.7778 15.7056 15.9418 15.1405 16.9836C12.7631 21.3679 11.52 26.2637 11.52 31.2374V43.8388V44.0766H11.5264C11.6288 46.7603 13.4838 47.7201 16.0233 48V29.9071C16.0233 28.5853 17.1086 27.5119 18.4497 27.5119H18.6544V22.6666C18.6544 22.1109 19.1107 21.6605 19.6736 21.6605H21.4498C22.0127 21.6605 22.469 22.1109 22.469 22.6666V27.5119H22.6737C24.0127 27.5119 25.1001 28.5832 25.1001 29.9071V43.7041C25.1001 46.6971 27.4456 47.9874 30.0426 47.9874H31.049C32.7505 47.9874 34.3433 47.4338 35.2409 46.2088C35.2452 46.2025 35.2495 46.1983 35.2516 46.1941C35.2708 46.1667 35.29 46.1372 35.3092 46.1078C35.4286 45.9289 35.5331 45.7415 35.6226 45.5458C35.8572 45.0238 35.9894 44.4113 35.9894 43.7041L35.9979 20.2061L36 20.204Z"
        fill="#002652"
      />
    </svg>
  ),
  other: (props: FractionIconSvgProps) => (
    <svg {...props} viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M31.4025 12.3343C29.989 11.4669 29.204 9.87053 29.4355 8.23459L30.1808 2.95787C30.4014 1.39439 29.1841 0 27.5987 0H21.7728C20.1873 0 18.9701 1.39659 19.1906 2.95787L19.6316 6.08262C19.7463 6.88851 19.1178 7.60877 18.2997 7.60877H15.2192C10.3216 7.60877 6.23999 11.5153 6.23999 16.3945C6.23999 18.933 7.3249 21.2211 9.05812 22.8285C9.98647 23.6893 10.5289 24.8926 10.5003 26.1553C10.4958 26.3002 10.4958 26.4451 10.4958 26.5923C10.4958 31.2431 12.218 38.4215 14.9744 44.234C14.981 44.2494 14.9899 44.267 14.9987 44.2824C16.099 46.5705 18.4541 47.9978 21.001 47.9978H21.442C22.5159 47.4664 23.2546 46.3641 23.2546 45.0882V38.4325C22.8797 38.5203 22.4894 38.5708 22.0859 38.5708C19.3053 38.5708 17.0516 36.3266 17.0516 33.5576C17.0516 32.3477 17.4816 31.2388 18.1983 30.3736C18.0417 29.9432 17.9557 29.4798 17.9557 28.9946C17.9557 27.1193 19.2369 25.5426 20.9745 25.0815C20.99 22.8241 22.8334 20.9971 25.1047 20.9971C27.3759 20.9971 29.2062 22.8131 29.2326 25.0617C31.0122 25.4943 32.3308 27.0907 32.3308 28.9946C32.3308 29.4798 32.2448 29.9432 32.0883 30.3736C32.8049 31.2388 33.2349 32.3477 33.2349 33.5576C33.2349 36.3266 30.9813 38.5708 28.2007 38.5708C27.398 38.5708 26.6394 38.382 25.9647 38.0504V45.0904C25.9647 46.3663 26.7034 47.4664 27.7773 48H28.3749C30.9218 48 33.2768 46.5727 34.3772 44.2845C34.386 44.2692 34.3926 44.2516 34.4014 44.2362C37.1578 38.4215 38.88 31.2453 38.88 26.5944C38.88 20.4284 35.8546 15.0704 31.4047 12.3387L31.4025 12.3343ZM17.9932 12.319C17.9932 12.319 17.9778 12.3277 17.969 12.3343C15.3272 13.9549 13.1883 16.5043 11.8851 19.5961C11.5587 20.3691 10.5047 20.503 10.0284 19.8135C9.35581 18.8342 8.9633 17.6506 8.96771 16.377C8.97873 13.0129 11.8013 10.3273 15.1795 10.3273H17.3956C18.4783 10.3273 18.9105 11.748 17.991 12.319H17.9932Z"
        fill="#002652"
      />
    </svg>
  ),
  "paper-paperboard-cardboard": (props: FractionIconSvgProps) => (
    <svg {...props} viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M28.1443 15.6127H28.166V27.8335L26.1022 26.6605L24.0384 27.8335L21.9745 26.6605L19.9107 27.8335V15.6127H19.9324L20.5407 10.7607H2.37885L0.79295 13.7999C0.271558 14.8023 0 15.9007 0 17.031V40.8221C0 42.6669 1.52073 44.1598 3.39991 44.1598H45.6001C47.4793 44.1598 49 42.6669 49 40.8221V16.999C49 15.9007 48.7393 14.8129 48.2396 13.8319L46.6755 10.7607H27.5469L28.1552 15.6127H28.1443Z"
        fill="#002652"
      />
      <path
        d="M44.7202 6.93236L43.7426 5.01287C43.3733 4.29839 42.6347 3.83984 41.82 3.83984H26.6888L27.0689 6.93236H44.7202Z"
        fill="#002652"
      />
      <path
        d="M21.4097 3.85051H7.31035C6.50654 3.85051 5.7679 4.29839 5.39858 5.0022L4.38839 6.94302H21.0295L21.4097 3.85051Z"
        fill="#002652"
      />
    </svg>
  ),
  plastics: (props: FractionIconSvgProps) => (
    <svg {...props} viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M36.3688 7.11934H32.3434V4.61932C32.3434 2.07088 30.3944 0 27.999 0H20.322C17.9266 0 15.9776 2.07308 15.9776 4.61932V7.11934H11.631C9.7151 7.11934 8.15991 8.67305 8.15991 10.5921V44.5273C8.15991 46.4441 9.7129 48 11.631 48H12.9091C14.825 48 16.3802 46.4463 16.3802 44.5273V31.3295C16.3802 28.2089 18.1927 25.513 20.8214 24.2344V23.5345C20.8214 22.1195 21.8112 20.9377 23.1354 20.6406V16.9742H20.4364C19.8139 16.9742 19.3102 16.4702 19.3102 15.8474C19.3102 15.2246 19.8139 14.7206 20.4364 14.7206H26.2986C29.6355 14.7206 32.35 17.4363 32.35 20.7748V21.0873C32.35 21.7101 31.8462 22.2141 31.2237 22.2141C30.6012 22.2141 30.0975 21.7101 30.0975 21.0873V20.7748C30.0975 18.6797 28.3927 16.9742 26.2986 16.9742H25.3857V20.6406C26.7099 20.9377 27.6998 22.1195 27.6998 23.5345V24.2344C30.3284 25.513 32.141 28.2089 32.141 31.3295V44.7209C32.141 46.3054 31.0521 47.6347 29.5827 48H36.3688C38.2847 48 39.8399 46.4463 39.8399 44.5273V10.5921C39.8399 8.67526 38.2869 7.11934 36.3688 7.11934ZM18.2741 7.11934V4.61932C18.2741 3.3385 19.1936 2.29536 20.322 2.29536H28.0012C29.1318 2.29536 30.0491 3.3385 30.0491 4.61932V7.11934H18.2741Z"
        fill="#002652"
      />
    </svg>
  ),
} as const;

export function FractionIcon({ size, iconUrl, className }: FractionIconProps) {
  const sizeClasses = {
    small: "size-8 rounded-sm",
    medium: "size-12 rounded-md",
    large: "size-16 rounded-lg",
  };

  return (
    <div
      className={cn(
        "bg-tonal-dark-blue-90 text-white flex-none overflow-hidden flex items-center justify-center",
        sizeClasses[size],
        className
      )}
    >
      <NextImage
        draggable={false}
        src={iconUrl}
        alt="Fraction icon"
        width={100}
        height={100}
        className="object-cover aspect-square w-full h-full"
      />
    </div>
  );
}
