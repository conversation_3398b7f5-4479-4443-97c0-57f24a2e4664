"use client";

import { cn } from "@/lib/utils";
import { Navigation } from "../navigation";
import { useDashboardHeader } from "./dashboard-header-context";
import { Menu } from "@arthursenno/lizenzero-ui-react/Icon";

export function DashboardHeaderDrawer() {
  const { isDrawerOpen } = useDashboardHeader();

  return (
    <div
      className={cn(
        "absolute top-full w-full px-4 py-6 bg-background shadow-elevation-01-2 transition-all duration-200 opacity-0 -translate-y-2",
        isDrawerOpen ? "opacity-100 translate-y-0" : "invisible pointer-events-none"
      )}
    >
      <Navigation className="flex-col items-start lg:hidden" />
    </div>
  );
}

export function DashboardHeaderDrawerTrigger() {
  const { toggleDrawer } = useDashboardHeader();

  return (
    <button
      onClick={toggleDrawer}
      className="flex items-center justify-center size-10 p-2 hover:bg-primary/10 rounded-full transition-all duration-75"
    >
      <Menu className="text-primary size-7" />
    </button>
  );
}
