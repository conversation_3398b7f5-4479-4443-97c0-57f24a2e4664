import Link from "next/link";
import { CalendarToday, CheckCircle, Launch } from "@arthursenno/lizenzero-ui-react/Icon";
import { TaskGroup, TaskGroupItem } from "@/lib/api/home/<USER>";
import { TaskTag } from "../task-tag";
import { filterTags } from "@/lib/api/home";

export function TaskCard({ task }: { task: TaskGroupItem }) {
  const createdAt = task?.column_values?.find((item) => item.column.title === "Timeline")?.text?.split(` - `)?.[0];

  const formattedDate = createdAt
    ? new Date(createdAt).toLocaleDateString("en-US", {
        day: "numeric",
        month: "short",
      })
    : "-";

  // TODO: Validar as outras tags (Report, Docs & Info, Invoice e Termination)
  const tags: { key: string; label: string }[] = task.column_values
    .filter((col) => col.column.title && filterTags.includes(col.column.title))
    .map((item) => ({ key: item.column.title.toLocaleLowerCase(), label: item?.text || `` }));

  const taskId = task.id;
  const textContent = task.name ?? "-";
  const customerId = task?.column_values?.find((item) => item.column.title === "Customer ID")?.text;

  return (
    <div className="space-y-4 whitespace-normal p-5 rounded-xl border border-surface-01">
      <ul className="flex items-center flex-wrap gap-2">
        <li>
          <CheckCircle className="size-6 fill-white stroke-[1.5] stroke-tonal-dark-cream-40" />
        </li>
        {tags?.map((tag) => (
          <li key={tag.key}>
            <TaskTag tag={tag} />
          </li>
        ))}
      </ul>

      <div className="flex flex-col gap-2">
        <p className="text-sm text-tonal-dark-cream-20">{textContent}</p>
        {customerId && <span className="underline text-sm text-support-blue">Customer #{customerId}</span>}
      </div>

      <div className="flex flex-wrap items-center justify-between gap-4">
        <div className="flex items-center gap-2 text-tonal-dark-cream-50">
          <CalendarToday className="size-4 fill-text-tonal-dark-cream-50" />
          <span className="mt-1 text-xs font-medium">{formattedDate}</span>
        </div>

        {/* {task.comments > 0 && (
          <div className="flex items-center gap-2 text-tonal-dark-cream-50">
            <ChatBubbleOutline className="size-4 fill-tonal-dark-cream-50" />
            <span className="mt-1 text-xs font-medium">{task.comments}</span>
          </div>
        )} */}

        <span className="mt-1 text-xs font-medium text-tonal-dark-cream-50">#{taskId}</span>

        <Link aria-label="Launch" href={`/customers/${customerId}`} prefetch={false}>
          <Launch className="size-5 fill-support-blue" />
        </Link>
      </div>
    </div>
  );
}
