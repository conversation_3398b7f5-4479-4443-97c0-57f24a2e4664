"use client";

import { getTasks } from "@/lib/api/home";
import { TaskBoard } from "../task-board";
import { Skeleton } from "@/components/ui/skeleton";
import { useQuery } from "@tanstack/react-query";
import { useSearchParams } from "next/navigation";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useTranslations } from "next-intl";

export function TaskKanban() {
  const t = useTranslations("kanban");
  const { paramValues } = useQueryFilter(["country"]);
  const country = paramValues.country ?? undefined;

  const { data: tasks, isLoading } = useQuery({
    queryKey: ["monday-tasks", country],
    queryFn: async () => getTasks(undefined, country),
    // TODO: Monday API give us a timeout of 20 seconds, so we need to set a stale time to avoid empty responses
    staleTime: 30000,
  });

  if (isLoading) {
    return <TaskKanbanPlaceholder />;
  }

  return (
    <div className="relative flex-grow">
      <ol className="absolute inset-0 flex whitespace-nowrap overflow-x-auto overflow-y-hidden pb-2">
        <li>
          <TaskBoard tasks={tasks?.registrations?.[0]?.groups ?? []} title={t("registration")} />
        </li>
        <li>
          <TaskBoard tasks={tasks?.volumeReports?.[0]?.groups ?? []} title={t("volumeReports")} />
        </li>
        <li>
          <TaskBoard tasks={tasks?.thirdPartyInvoices?.[0]?.groups ?? []} title={t("thirdPartyInvoices")} />
        </li>
        <li>
          <TaskBoard tasks={tasks?.terminations?.[0]?.groups ?? []} title={t("terminations")} />
        </li>
      </ol>
    </div>
  );
}

export function TaskKanbanPlaceholder() {
  return (
    <div className="relative flex-grow">
      <ol className="absolute inset-0 flex whitespace-nowrap space-x-6 overflow-x-auto overflow-y-hidden pb-2">
        <li>
          <Skeleton className="h-full w-72 rounded-lg" />
        </li>
        <li>
          <Skeleton className="h-full w-72 rounded-lg" />
        </li>
        <li>
          <Skeleton className="h-full w-72 rounded-lg" />
        </li>
        <li>
          <Skeleton className="h-full w-72 rounded-lg" />
        </li>
      </ol>
    </div>
  );
}
