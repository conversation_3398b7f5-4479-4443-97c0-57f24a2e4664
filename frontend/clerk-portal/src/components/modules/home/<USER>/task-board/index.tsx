"use client";

import { FilterAlt, KeyboardArrowDown } from "@arthursenno/lizenzero-ui-react/Icon";
import { ScrollArea } from "@/components/ui/scroll-area";
import { TaskGroup } from "@/lib/api/home/<USER>";
import { TaskCard } from "../task-card";
import { useTranslations } from "next-intl";

interface TaskBoardProps {
  title: string;
  tasks: Array<TaskGroup>;
}

export function TaskBoard({ title, tasks }: TaskBoardProps) {
  const c = useTranslations("common");
  const getId = () => Math.random().toString(36).substr(2, 9);

  const hasEmptyTaskList = tasks.every((task) => task.items_page.items.length === 0);
  const allItemPage = tasks?.[0]?.items_page?.items;

  return (
    <div className="block flex-shrink-0 items-start h-full whitespace-nowrap px-3">
      <div className="relative flex flex-col justify-between w-72 max-h-full pb-2 rounded-lg bg-white whitespace-normal">
        <div className="relative flex flex-grow-0 flex-wrap items-start justify-between p-4">
          <h4 className="text-[#183362]">{title}</h4>
          {/* <button type="button" aria-label="Filter" className="inline-flex gap-2 items-center">
            <FilterAlt className="size-5 fill-support-blue" />
            <KeyboardArrowDown className="size-5 fill-support-blue" />
          </button> */}
        </div>

        {hasEmptyTaskList ? (
          <div className="flex flex-col items-center justify-center flex-grow mt-5 mb-7">
            <p className="text-tonal-dark-cream-20">{c("noTasks")}</p>
          </div>
        ) : (
          <ScrollArea className="flex flex-1 flex-col p-4 z-10 overflow-x-hidden">
            <ul className="flex-grow space-y-4">
              {allItemPage.map((task) => (
                <li key={getId()}>
                  <TaskCard task={task} />
                </li>
              ))}
            </ul>
          </ScrollArea>
        )}
      </div>
    </div>
  );
}
