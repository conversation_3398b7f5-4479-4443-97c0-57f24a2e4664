import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { formatCurrency } from "@/utils/format-currency";

interface BalanceItemProps {
  label: string;
  value: string | number;
  tooltipInfo?: string;
  className?: string;
}

export function BalanceItem({ label, value = 0, tooltipInfo, className }: BalanceItemProps) {
  return (
    <div className={cn("space-y-1 text-primary", className)}>
      <div className="inline-flex items-center gap-2">
        <p className="-mb-1 text-sm sm:text-base">{label}</p>
        {tooltipInfo && (
          <QuestionTooltip>
            <QuestionTooltipDescription className="p-0">{tooltipInfo}</QuestionTooltipDescription>
          </QuestionTooltip>
        )}
      </div>
      <h3 className="text-[1.75rem] font-bold">{formatCurrency(Number(value) || 0)}</h3>
    </div>
  );
}

export function BalanceItemPlaceholder() {
  return (
    <div className="space-y-1">
      <Skeleton className="h-2 w-full max-w-24" />
      <Skeleton className="h-6 w-full max-w-52" />
    </div>
  );
}
