"use client";

import { BalanceItem, BalanceItemPlaceholder } from "@/components/modules/invoices/components/balance-item";
import { getTransactionTotals } from "@/lib/api/transaction";
import { useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";

export function BalanceSection() {
  const t = useTranslations("BalanceSection");
  const { data: totals, isLoading } = useQuery({
    queryKey: ["transaction-totals"],
    queryFn: () => getTransactionTotals(),
  });

  if (isLoading) {
    return <BalanceSectionPlaceholder />;
  }

  const total = totals.total / 100;
  const assignedTotal = totals.assignedTotal / 100;
  const unassignedTotal = totals.unassignedTotal / 100;

  return (
    <section id="balance-section" className="grid md:grid-cols-2 lg:grid-cols-3 gap-y-6 gap-x-2 sm:p-6">
      <BalanceItem label={t("totalReceived")} value={total} />
      <BalanceItem label={t("totalAssignedTransactions")} value={assignedTotal} tooltipInfo="Lorem ipsum dolor at" />
      <BalanceItem
        label={t("totalUnassignedInvoice")}
        value={unassignedTotal}
        tooltipInfo="Lorem ipsum dolor at"
        className="[&>h3]:text-tonal-dark-blue-50"
      />
    </section>
  );
}

export function BalanceSectionPlaceholder() {
  return (
    <section className="grid md:grid-cols-2 lg:grid-cols-3 gap-y-6 gap-x-2 sm:p-6">
      <BalanceItemPlaceholder />
      <BalanceItemPlaceholder />
      <BalanceItemPlaceholder />
    </section>
  );
}
