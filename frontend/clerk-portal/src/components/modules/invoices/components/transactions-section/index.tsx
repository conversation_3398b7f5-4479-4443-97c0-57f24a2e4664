"use client";

import {
  TransactionsTable,
  TransactionsTablePlaceholder,
} from "@/components/modules/invoices/components/transactions-table";
import { ITransaction } from "@/lib/api/invoices/types";
import { getTransactions } from "@/lib/api/transaction";
import { Transaction } from "@/lib/api/transaction/types";
import { useQuery } from "@tanstack/react-query";

interface TransactionsSectionProps {
  invoiceStatus: string;
  search?: string;
  year?: string;
  data: ITransaction[];
}

export function TransactionsSection(props: TransactionsSectionProps) {
  const { search = "", invoiceStatus, year } = props;
  const { data: transactions, isLoading } = useQuery({
    queryKey: ["transactions", invoiceStatus],
    queryFn: () => getTransactions(invoiceStatus),
  });

  if (isLoading) {
    return <TransactionsTablePlaceholder className="mt-8" />;
  }

  let filteredTransactions = transactions.filter((transaction: Transaction) =>
    transaction.company_name.toLowerCase().includes(search.toLowerCase())
  );

  if (invoiceStatus.length > 0) {
    filteredTransactions = filteredTransactions.filter((transaction: any) => transaction.status === invoiceStatus);
  }

  filteredTransactions = filteredTransactions.filter(
    (transaction: any) => new Date(transaction.transactionDate).getFullYear() === Number(year)
  );

  return (
    <section id="transactions" className="mt-[2.375rem] w-full">
      <TransactionsTable transactions={filteredTransactions} />
    </section>
  );
}
