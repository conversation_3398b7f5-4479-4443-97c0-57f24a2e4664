"use client";

import { useState } from "react";
import { usePathname, useRouter } from "@/i18n/navigation";
import { useSearchParams } from "next/navigation";

import { KeyboardArrowDown } from "@arthursenno/lizenzero-ui-react/Icon";
import { Dropdown, DropdownItem } from "@/components/_common/dropdown";
import { cn } from "@/lib/utils";
import { TransactionStatus } from "@/lib/api/transaction/types";

export const INVOICE_STATUS_FILTERS: Array<{ label: string; value: "total" | TransactionStatus }> = [
  { label: "Total Transactions", value: "total" },
  { label: "Unassigned invoice", value: TransactionStatus.UNASSIGNED },
  { label: "Assigned transactions", value: TransactionStatus.ASSIGNED },
  { label: "Refund", value: TransactionStatus.REFUND },
] as const;

type FilterType = (typeof INVOICE_STATUS_FILTERS)[number];

export function InvoiceStatusFilter() {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const filterParamValue = searchParams.get("invoiceStatus");

  const [selectedFilter, setSelectedFilter] = useState<FilterType>(() => {
    return INVOICE_STATUS_FILTERS.find((filter) => filter.value === filterParamValue) ?? INVOICE_STATUS_FILTERS[0];
  });

  function handleSelectFilter(filter: FilterType) {
    const params = new URLSearchParams(searchParams.toString());

    if (filter.value === "total") params.delete("invoiceStatus");
    else params.set("invoiceStatus", filter.value);

    setSelectedFilter(filter);
    router.replace(`${pathname}?${params.toString()}`, { scroll: false });
  }

  return (
    <Dropdown
      trigger={
        <button className="inline-flex items-center text-support-blue font-bold text-lg sm:text-2xl">
          <span className="ml-1 mr-2 mt-1 text-left">{selectedFilter.label}</span>
          <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
        </button>
      }
    >
      {INVOICE_STATUS_FILTERS.map((filter) => (
        <DropdownItem
          key={filter.value}
          onClick={() => handleSelectFilter(filter)}
          className={cn(
            "group py-5 px-4 text-base sm:text-xl text-tonal-dark-cream-10 focus:outline-none cursor-pointer hover:bg-support-blue/10",
            { "font-bold text-primary": filter.value === selectedFilter.value }
          )}
        >
          {filter.label}
        </DropdownItem>
      ))}
    </Dropdown>
  );
}
