"use client";

import { useState } from "react";
import { usePathname, useRouter } from "@/i18n/navigation";
import { useSearchParams } from "next/navigation";

import { FilterAlt, KeyboardArrowDown } from "@arthursenno/lizenzero-ui-react/Icon";
import { Dropdown, DropdownItem } from "@/components/_common/dropdown";
import { cn } from "@/lib/utils";

const currentYear = new Date().getFullYear();

const YEARS = [2024, 2023, 2022, 2021, 2020];

export function FilterByYear() {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const filterParamValue = searchParams.get("year");

  const [selectedYear, setSelectedYear] = useState(() => {
    return YEARS.find((year) => year === Number(filterParamValue)) ?? currentYear;
  });

  function handleSelectYear(year: number) {
    const params = new URLSearchParams(searchParams.toString());
    params.set("year", String(year));

    setSelectedYear(year);
    router.replace(`${pathname}?${params.toString()}`, { scroll: false });
  }

  return (
    <Dropdown
      trigger={
        <button className="inline-flex items-center text-support-blue font-bold">
          <FilterAlt className="size-5 fill-support-blue" />
          <span className="ml-1 mr-2 mt-1 text-left">{selectedYear}</span>
          <KeyboardArrowDown className="size-5 fill-support-blue" />
        </button>
      }
    >
      {YEARS.map((year) => (
        <DropdownItem
          key={year}
          onClick={() => handleSelectYear(year)}
          className={cn(
            "group py-5 px-4 text-tonal-dark-cream-10 focus:outline-none cursor-pointer hover:bg-support-blue/10",
            { "font-bold text-primary": year === selectedYear }
          )}
        >
          {year}
        </DropdownItem>
      ))}
    </Dropdown>
  );
}
