"use client";

import { useState } from "react";

import { PopoverAnchor } from "@radix-ui/react-popover";

import { Search } from "@arthursenno/lizenzero-ui-react/Icon";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useTranslations } from "next-intl";

const mockData = [
  { invoiceId: "1", code: "029231", customerName: "John Doe" },
  { invoiceId: "2", code: "149231", customerName: "<PERSON>" },
  { invoiceId: "3", code: "249231", customerName: "<PERSON> Do<PERSON>" },
];

interface InvoiceOrCustomerSelectProps {
  id?: string;
  value?: string;
  onValueChange: (values: string) => void;
}

export function InvoiceOrCustomerSelect({ id, value, onValueChange }: InvoiceOrCustomerSelectProps) {
  const [inputValue, setInputValue] = useState(value);
  const [, setSelectedValue] = useState(value);
  const [data, setData] = useState(mockData);

  function handleSelectValue(name: string) {
    setInputValue(name);
    setSelectedValue(name);
    onValueChange(name);
  }

  function handleSearch(value: string) {
    setInputValue(value);

    if (value) {
      setData(
        mockData.filter(({ invoiceId, customerName }) => {
          return invoiceId === value || customerName.toLowerCase().includes(value.toLowerCase());
        })
      );
    } else {
      setData(mockData);
    }
  }
  const t = useTranslations("InvoiceOrCustomerSelect");
  return (
    <Popover>
      <PopoverAnchor className="group relative flex items-center gap-2 h-14 w-full rounded-2xl px-3 py-2 bg-background border border-tonal-dark-cream-80 focus-within:ring-2 focus-within:ring-primary">
        <Search className="size-6 fill-primary" />
        <PopoverTrigger asChild autoFocus={false}>
          <input
            id={id}
            type="text"
            autoComplete="off"
            placeholder={t("placeholder")}
            value={inputValue}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => handleSearch(e.target.value)}
            className="w-full h-full bg-none border-0 outline-none focus:outline-none text-tonal-dark-cream-10 placeholder-tonal-dark-cream-60"
          />
        </PopoverTrigger>
      </PopoverAnchor>

      <PopoverContent className="w-[--radix-popover-trigger-width] max-h-[--radix-popover-content-available-height] overflow-hidden shadow-elevation-04-1 pointer-events-auto p-0 pr-1 py-3 rounded-2xl">
        <ul className="flex flex-col overflow-y-auto h-60">
          {data.length === 0 && (
            <li className="w-full text-center pt-2">
              <span className="text-primary text-sm font-normal">{t("noResults")}</span>
            </li>
          )}
          {data.map(({ invoiceId, code, customerName }) => {
            return (
              <li key={invoiceId} className="w-full">
                <button
                  onClick={() => handleSelectValue(customerName)}
                  className="flex flex-col w-full px-4 py-5 hover:bg-primary/15"
                >
                  <span className="text-primary">#{code}</span>
                  <span className="text-sm text-tonal-dark-cream-40">{customerName}</span>
                </button>
              </li>
            );
          })}
        </ul>
      </PopoverContent>
    </Popover>
  );
}
