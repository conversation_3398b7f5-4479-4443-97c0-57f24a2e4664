"use client";

import { Fragment, useState } from "react";

import { KeyboardArrowDown } from "@arthursenno/lizenzero-ui-react/Icon";
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Transaction, TransactionStatus } from "@/lib/api/transaction/types";
import { formatCurrency } from "@/utils/format-currency";
import { Skeleton } from "@/components/ui/skeleton";
import { TableMenuOptions } from "@/components/modules/invoices/components/table-menu-options";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
import { formatDate } from "@/utils/format-date";

interface TransactionsTableProps {
  transactions: Transaction[];
}

export function TransactionsTable({ transactions }: TransactionsTableProps) {
  const [expandedRow, setExpandedRow] = useState<string | null>(null);

  function toggleRow(id: string) {
    setExpandedRow((prev) => (prev === id ? null : id));
  }

  function renderStatus(status: TransactionStatus) {
    switch (status) {
      case TransactionStatus.ASSIGNED:
        return <span className="font-bold text-sm text-success">{t("assigned")}</span>;
      case TransactionStatus.UNASSIGNED:
        return <span className="font-bold text-sm text-error">{t("unassigned")}</span>;
      case TransactionStatus.REFUND:
        return <span className="font-bold text-sm text-primary">{t("refund")}</span>;
      default:
        return null;
    }
  }

  const t = useTranslations("TransactionTable");

  return (
    <Table aria-label="Transactions table">
      <TableCaption className="sr-only">{t("caption")}</TableCaption>
      <TableHeader>
        <TableRow>
          <TableHead scope="col" aria-label="Expand trigger" className="w-8">
            {" "}
          </TableHead>
          <TableHead scope="col" aria-label="Company name" className="pl-4">
            {t("company")} <br /> {t("name")}
          </TableHead>
          <TableHead scope="col" aria-label="External Reference">
            {t("external")} <br /> {t("reference")}
          </TableHead>
          <TableHead scope="col" aria-label="Transaction amount">
            {t("transaction")} <br /> {t("amount")}
          </TableHead>
          <TableHead scope="col" aria-label="Remaining transaction amount">
            {t("remaining")} <br /> {t("transaction")} <br /> {t("amount")}
          </TableHead>
          <TableHead scope="col" aria-label="IBAN">
            {t("IBAN")}
          </TableHead>
          <TableHead scope="col" aria-label="Transaction date">
            {t("transaction")} <br /> {t("date")}
          </TableHead>
          <TableHead scope="col" aria-label="Client or Invoice ID">
            {t("clientID")} <br /> /{t("invoiceId")}
          </TableHead>
          <TableHead scope="col" aria-label="Status" className="text-right">
            {t("status")}
          </TableHead>
        </TableRow>
      </TableHeader>

      <TableBody>
        {transactions.map((transaction) => {
          const transactionId = String(transaction.id);
          const isExpanded = expandedRow === transactionId;

          return (
            <Fragment key={transaction.id}>
              <TableRow
                className={cn({
                  "bg-tonal-pink-96": transaction.status === TransactionStatus.UNASSIGNED,
                  "bg-tonal-dark-blue-96": transaction.status === TransactionStatus.REFUND,
                })}
              >
                <TableCell aria-labelledby="Expand trigger">
                  <button
                    type="button"
                    aria-label="Expand details"
                    onClick={() => toggleRow(transactionId)}
                    className="flex items-center justify-center size-7 rounded-full hover:cursor-pointer hover:bg-on-secondary/15"
                  >
                    <KeyboardArrowDown className={cn("size-6 fill-primary", { "rotate-180": isExpanded })} />
                  </button>
                </TableCell>
                <TableCell aria-labelledby="Company name">{transaction.company_name}</TableCell>
                <TableCell aria-labelledby="External Reference">{transaction.external_id}</TableCell>
                <TableCell aria-labelledby="Transaction amount">{formatCurrency(transaction.amount || 0)}</TableCell>
                <TableCell aria-labelledby="Remaining transaction amount">
                  {formatCurrency(transaction.remaining_amount || 0)}
                </TableCell>
                <TableCell aria-labelledby="IBAN">{transaction.debtor_iban}</TableCell>
                <TableCell>{formatDate(transaction.date)}</TableCell>
                <TableCell aria-labelledby="Client or Invoice ID" className="flex flex-col gap-1 text-left">
                  <span>#{transaction.invoice_id}</span>
                  <strong className="text-xs text-[#656773]">{t("clientID")}</strong>
                </TableCell>
                <TableCell aria-labelledby="Status" className="text-right">
                  <div className="flex items-center justify-end gap-3">
                    <span className="mt-1">{renderStatus(transaction.status)} </span>
                    <TableMenuOptions
                      transactionId={transactionId}
                      customerId={""}
                      transactionStatus={transaction.status}
                    />
                  </div>
                </TableCell>
              </TableRow>

              {isExpanded && (
                <TableRow>
                  <TableCell aria-label="Other info" colSpan={9} className="px-10">
                    <div className="grid grid-cols-3">
                      <p className="col-span-1 text-primary text-sm py-4 px-2">
                        <strong>{t("otherInfo")}: </strong>Lorem impsum dolor amet
                      </p>
                      <p className="col-span-2 text-primary text-sm py-4 px-2">
                        <strong>{t("otherInfo2")}: </strong>Lorem impsum dolor amet
                      </p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </Fragment>
          );
        })}
      </TableBody>
    </Table>
  );
}

export function TransactionsTablePlaceholder({ className }: { className?: string }) {
  return (
    <Table className={className}>
      <TableHeader>
        <TableRow>
          {Array.from({ length: 8 }).map((_, idx) => (
            <TableHead key={idx}>
              <Skeleton className="h-3 min-w-24" />
            </TableHead>
          ))}
        </TableRow>
      </TableHeader>
      <TableBody>
        {Array.from({ length: 4 }).map((_, idx) => (
          <TableRow key={idx}>
            {Array.from({ length: 8 }).map((_, idx) => (
              <TableCell key={idx}>
                <Skeleton className="h-3 min-w-24" />
              </TableCell>
            ))}
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
