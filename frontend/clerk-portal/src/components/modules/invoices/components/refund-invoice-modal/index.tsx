"use client";

import Image from "next/image";
import { usePathname, useRouter } from "@/i18n/navigation";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Clear, KeyboardArrowLeft } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { Select } from "@arthursenno/lizenzero-ui-react/Select";
import { zodResolver } from "@hookform/resolvers/zod";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";

import { CurrencyInput } from "@/components/_common/currency-input";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { PaymentMethod, RefundType } from "@/lib/api/invoices/types";
import { getTransactionById, refundTransaction } from "@/lib/api/transaction";
import { formatCurrency } from "@/utils/format-currency";
import { useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";

function useRefundInvoiceTranslations() {
  const t = useTranslations("RefundInvoiceModal");
  const c = useTranslations("common");

  const REFUND_TYPES_MAPPING = {
    [RefundType.DUNNING_CHARGE]: t("dunning"),
    [RefundType.OTHER]: c("other"),
  };

  const PAYMENT_METHODS_MAPPING = {
    [PaymentMethod.BANK_TRANSFER]: t("banktransfer"),
    [PaymentMethod.OTHER]: c("other"),
  };

  const refundTypeOptions = Object.entries(RefundType).map(([key, value]) => ({
    value: key,
    label: REFUND_TYPES_MAPPING[value],
  }));

  const paymentMethodOptions = Object.entries(PaymentMethod).map(([key, value]) => ({
    value: key,
    label: PAYMENT_METHODS_MAPPING[value],
  }));

  const refundInvoiceFormSchema = z.object({
    refundType: z.nativeEnum(RefundType),
    paymentMethod: z.nativeEnum(PaymentMethod),
    amount: z.coerce.number().min(1, "Required Field").default(0),
    isNotPayToTheCustomer: z.boolean().default(true),
    note: z.string().optional(),
  });

  return {
    t,
    c,
    REFUND_TYPES_MAPPING,
    PAYMENT_METHODS_MAPPING,
    refundTypeOptions,
    paymentMethodOptions,
    refundInvoiceFormSchema,
  };
}

type RefundInvoiceFormSchemaType = z.infer<ReturnType<typeof useRefundInvoiceTranslations>["refundInvoiceFormSchema"]>;

export function RefundInvoiceModal() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { t, c, refundTypeOptions, paymentMethodOptions, refundInvoiceFormSchema } = useRefundInvoiceTranslations();

  const [step, setStep] = useState<"1" | "2" | "3">("1");

  const transactionId = searchParams.get("refund-invoice");
  const isModalOpen = transactionId !== null;

  const { data: transaction, isLoading } = useQuery({
    queryKey: ["transaction"],
    queryFn: () => getTransactionById(Number(transactionId)),
    enabled: isModalOpen && !!transactionId,
  });

  const form = useForm<RefundInvoiceFormSchemaType>({
    resolver: zodResolver(refundInvoiceFormSchema),
    defaultValues: {
      refundType: RefundType.DUNNING_CHARGE,
      paymentMethod: PaymentMethod.BANK_TRANSFER,
      amount: 0,
      isNotPayToTheCustomer: true,
      note: "",
    },
  });

  useEffect(() => {
    if (!isModalOpen) {
      // clear state
      setStep("1");
      form.reset();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isModalOpen]);

  function handleCloseModal() {
    const params = new URLSearchParams(searchParams.toString());
    params.delete("refund-invoice");

    router.push(`${pathname}?${params.toString()}`, { scroll: false });
  }

  function handleOnOpenChange(open: boolean) {
    if (!open) handleCloseModal();
  }

  function handleSubmitStep1(values: RefundInvoiceFormSchemaType) {
    try {
      // eslint-disable-next-line no-console
      console.log({ values });

      setStep("2");
    } catch (error) {
      //
    }
  }

  async function handleConfirmRefund() {
    try {
      const amount = form.getValues("amount");
      const formattedAmount = Math.round(amount * 100);

      const result = await refundTransaction({
        transactionId: transactionId!,
        amount: formattedAmount,
      });

      if (result.success) {
        setStep("3");
      } else {
        console.error(result.errorMessage);
      }
    } catch (error) {
      console.error(t("errorToProcess"), error);
    }
  }

  const amountValue = form.watch("amount") ?? 0;
  const formErrors = form.formState.errors;

  const step1 = (
    <>
      <div className="space-y-4">
        <h3 className="font-bold text-[1.75rem] text-primary">{t("refund")}</h3>
        <p className="text-tonal-dark-cream-20">{t("refundSubtitle")}</p>
      </div>

      <form onSubmit={form.handleSubmit(handleSubmitStep1)} className="mt-6 flex flex-col gap-6 w-full px-1">
        <Controller
          control={form.control}
          name="refundType"
          render={({ field }) => (
            <Select
              label={t("refundType")}
              options={refundTypeOptions as []}
              defaultValue={RefundType.DUNNING_CHARGE}
              value={field.value}
              onChange={field.onChange}
            />
          )}
        />

        <div className="flex flex-col sm:flex-row items-start sm:items-end gap-1 sm:gap-5">
          <Controller
            control={form.control}
            name="paymentMethod"
            render={({ field }) => (
              <div className="w-full sm:max-w-xs">
                <Select
                  label={t("originalPaymentMethod")}
                  defaultValue={PaymentMethod.BANK_TRANSFER}
                  value={field.value}
                  onChange={field.onChange}
                  options={paymentMethodOptions as []}
                  className="w-full"
                />
              </div>
            )}
          />
          <Button type="button" variant="text" color="light-blue" size="small">
            {t("change")}
          </Button>
        </div>

        <Controller
          control={form.control}
          name="amount"
          render={({ field }) => (
            <div className="flex flex-col gap-2.5">
              <CurrencyInput
                id="amount"
                label={t("amountToPay")}
                value={field.value}
                onValueChange={({ floatValue }) => field.onChange(floatValue)}
              />
              {formErrors.amount && <span className="text-error text-sm">{formErrors.amount.message}</span>}
            </div>
          )}
        />

        <Controller
          control={form.control}
          name="isNotPayToTheCustomer"
          render={({ field }) => (
            <div className="flex items-center space-x-4">
              <Checkbox
                id="isNotPayToTheCustomer"
                checked={field.value}
                onCheckedChange={(checked) => field.onChange(checked === true)}
              />
              <label htmlFor="isNotPayToTheCustomer" className="mt-1.5 text-[#183362]">
                {t("notPayToTheCustomer")}
              </label>
            </div>
          )}
        />

        <Controller
          control={form.control}
          name="note"
          render={({ field }) => (
            <div className="flex flex-col gap-2">
              <label htmlFor="note" className="text-primary">
                {t("additionalInfo")}
              </label>
              <Textarea id="note" placeholder={t("addComments")} {...field} />
            </div>
          )}
        />

        <div className="mt-6 flex justify-end">
          <Button type="submit" variant="filled" size="medium" color="dark-blue">
            {c("continue")}
          </Button>
        </div>
      </form>
    </>
  );
  const step2 = (
    <>
      <div className="flex flex-col items-start gap-6">
        <button
          type="button"
          onClick={() => setStep("1")}
          className="text-support-blue font-bold inline-flex items-center gap-2 hover:opacity-85"
        >
          <KeyboardArrowLeft className="size-6   fill-support-blue" /> <span className="mt-1">{c("back")}</span>
        </button>

        <div className="space-y-4">
          <h3 className="font-bold text-[1.75rem] text-primary">{t("refund")}</h3>
          <p className="text-tonal-dark-cream-20">{t("revisedRefundInfo")}</p>
        </div>

        <div className="grid sm:grid-cols-3 sm:items-center gap-6">
          <div className="sm:col-span-2 space-y-2 text-primary">
            <h3>{t("refundAmount")}</h3>
            <p className="py-4 font-bold">{formatCurrency(amountValue)}</p>
          </div>
          <div className="space-y-2 text-primary">
            <h3>{t("originalPayment")}</h3>
            <p className="py-4 font-bold">{formatCurrency(transaction?.amount || 0)}</p>
          </div>
        </div>

        <p className="text-tonal-dark-cream-20">{t("authorize")}</p>

        <div className="w-full flex justify-end">
          <Button type="button" variant="filled" color="dark-blue" size="medium" onClick={handleConfirmRefund}>
            {t("confirmRefund")}
          </Button>
        </div>
      </div>
    </>
  );
  const step3 = (
    <div className="space-y-10 pt-8">
      <div className="flex items-center gap-2">
        <Image alt="Leaf seal" src="/assets/images/leaf-seal.svg" width={35} height={36} />
        <h3 className="mt-1.5 flex-1 font-bold text-[1.75rem] text-primary">{t("refundComplete")}</h3>
      </div>
      <div className="flex justify-end">
        <Button type="button" color="yellow" variant="filled" size="medium" onClick={handleCloseModal}>
          {c("close")}
        </Button>
      </div>
    </div>
  );

  return (
    <Modal
      open={isModalOpen}
      onOpenChange={handleOnOpenChange}
      className="z-50 !rounded-[52px] w-full max-w-[672px] !max-h-full !py-8 !px-9"
    >
      <>
        <div className="flex items-center justify-end">
          <Clear onClick={handleCloseModal} className="w-8 h-8 fill-primary hover:opacity-75 cursor-pointer" />
        </div>

        {step === "1" && step1}
        {step === "2" && step2}
        {step === "3" && step3}
      </>
    </Modal>
  );
}
