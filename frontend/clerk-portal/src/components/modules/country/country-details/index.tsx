"use client";

import { useQuery } from "@tanstack/react-query";
import { useRouter } from "@/i18n/navigation";
import { useEffect } from "react";

import Breadcrumb from "@/components/_common/breadcrumb/breadcrumb";
import Container from "@/components/_common/container/container";
import { CountryIcon } from "@/components/_common/country-icon";
import { getCountryOverview } from "@/lib/api/country";
import { Skeleton } from "@/components/ui/skeleton";

import { CustomerTableSkeleton } from "../../customers/customer-list/customer-table";
import { CountryCustomers } from "./country-customers";
import { CountryKpis, CountryKpisSkeleton } from "./country-kpis";
import CountryRequiredInformations, { CountryRequiredInformationsSkeleton } from "./country-required-informations";
import { CountryTasks, CountryTasksSkeleton } from "./country-tasks";
import { CountryFollowerButton } from "./country-follower-button";

interface CountryDetailsProps {
  countryCode: string;
}

export function CountryDetails({ countryCode }: CountryDetailsProps) {
  const router = useRouter();

  const { data: country, status: queryStatus } = useQuery({
    queryKey: ["country-overview", { country_code: countryCode }],
    queryFn: () => getCountryOverview(countryCode),
    enabled: !!countryCode,
  });

  useEffect(() => {
    if (queryStatus === "error") router.push("/countries");
  }, [country, queryStatus, router]);

  if (queryStatus === "pending") {
    return <CountryDetailsSkeleton />;
  }

  if (!country) {
    return null;
  }

  const breadcrumbPaths = [
    { label: "Countries", href: "/countries" },
    { label: country.name, href: "" },
  ];

  return (
    <>
      <Container className="pb-14">
        <Breadcrumb paths={breadcrumbPaths} />
        <div className="flex items-center gap-6 md:gap-0 justify-between">
          <div className="flex items-center gap-3 md:gap-6">
            <CountryIcon country={{ flag: country.flag_url, name: country.name }} className="size-8 md:size-10" />
            <h1 className="text-2xl md:text-4xl text-grey-blue font-bold">{country.name}</h1>
          </div>
          <CountryFollowerButton country={country} />
        </div>
        <CountryKpis country={country} />
      </Container>

      <div className="py-14 px-4 sm:px-6 flex flex-col flex-grow  bg-tonal-cream-96">
        <div className="w-full max-w-7xl mx-auto flex flex-col flex-1">
          <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
            <div className="col-span-1 lg:col-span-2 w-full">
              <CountryRequiredInformations country={country} />
            </div>
            <div className="col-span-1 lg:col-span-3 w-full">
              <CountryTasks countryCode={countryCode} />
            </div>
          </div>
        </div>
      </div>

      <Container className="pb-14">
        <CountryCustomers countryCode={countryCode} />
      </Container>
    </>
  );
}

function CountryDetailsSkeleton() {
  return (
    <Container>
      <Skeleton className="w-1/2 sm:w-1/12 h-4 mt-10" />
      <div className="w-full pt-10 flex flex-col md:flex-row items-start md:items-center gap-6 md:gap-0 justify-between">
        <div className="w-full flex items-center gap-6">
          <Skeleton className="flex-shrink-0 size-11 rounded-full" />
          <Skeleton className="h-7 w-full sm:w-1/6" />
        </div>
        <Skeleton className="h-10 w-1/2 sm:w-1/12 rounded-full" />
      </div>
      <CountryKpisSkeleton />
      <div className="py-14">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-6">
          <div className="col-span-1 lg:col-span-2 w-full">
            <CountryRequiredInformationsSkeleton />
          </div>
          <div className="col-span-1 lg:col-span-3 w-full">
            <CountryTasksSkeleton />
          </div>
        </div>
      </div>
      <div className="py-14">
        <CustomerTableSkeleton />
      </div>
    </Container>
  );
}
