"use client";

import { useMutation } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";

import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import type { CreateCountryFollowerRequest, DeleteCountryFollowerRequest } from "@/lib/api/country-followers/types";
import { createCountryFollower, deleteCountryFollower } from "@/lib/api/country-followers";
import type { ICountry } from "@/lib/api/country/types";
import { queryClient } from "@/lib/react-query";

interface CountryFollowerButtonProps {
  country: ICountry;
}

export function CountryFollowerButton({ country }: CountryFollowerButtonProps) {
  const session = useSession();
  const userId = session.data?.user.id;

  const [isFollowing, setIsFollowing] = useState(() => {
    return country.followers.some((follower) => follower.user_id === Number(userId));
  });

  const followingMutation = useMutation({
    mutationFn: async (requestData: CreateCountryFollowerRequest) => {
      await createCountryFollower(requestData);
    },
    onSuccess: () => {
      setIsFollowing(true);
      queryClient.invalidateQueries({ queryKey: ["country-overview", { country_code: country.code }] });
      queryClient.invalidateQueries({ queryKey: ["monday-tasks", country.name] });
    },
    onError: () => {
      enqueueSnackbar("Failed to follow country", { variant: "error" });
    },
  });

  const unFollowingMutation = useMutation({
    mutationFn: async (requestData: DeleteCountryFollowerRequest) => {
      await deleteCountryFollower(requestData);
    },
    onSuccess: () => {
      setIsFollowing(false);
      queryClient.invalidateQueries({ queryKey: ["country-overview", { country_code: country.code }] });
      queryClient.invalidateQueries({ queryKey: ["monday-tasks", country.name] });
    },
    onError: () => {
      enqueueSnackbar("Failed to stop following country", { variant: "error" });
    },
  });

  function handleFollowCountry() {
    if (!userId) return;
    followingMutation.mutate({ country_id: country.id, user_ids: [Number(userId)] });
  }

  function handleUnFollowCountry() {
    if (!userId) return;
    unFollowingMutation.mutate({ country_id: country.id, user_id: Number(userId) });
  }

  if (!userId) return null;

  if (!country) return null;

  if (isFollowing) {
    return (
      <Button
        type="button"
        color="dark-blue"
        variant="outlined"
        size="small"
        onClick={handleUnFollowCountry}
        disabled={unFollowingMutation.isPending}
      >
        {unFollowingMutation.isPending ? "Unfollowing..." : "Following"}
      </Button>
    );
  }

  return (
    <Button
      type="button"
      color="yellow"
      variant="filled"
      size="small"
      onClick={handleFollowCountry}
      disabled={followingMutation.isPending}
    >
      {followingMutation.isPending ? "Following..." : "Follow"}
    </Button>
  );
}
