"use client";

import { SearchInput } from "@/components/_common/search-input";
import { getCustomers } from "@/lib/api/customer";
import { useQuery } from "@tanstack/react-query";
import OrderFilter, { ORDER_FILTERS } from "../../customers/components/filters/order-filter";
import StatusFilter, { StatusFilterType } from "../../customers/components/filters/status-filter";
import { useCustomersCountryColumns } from "@/hooks/table-columns/customers/customerCountryColumns";
import { useQueryFilter } from "@/hooks/use-query-filter";
import AsyncPaginatedTable from "@/components/_common/async-paginated-table";

const STATUS_FILTERS: StatusFilterType[] = [
  { label: "All", value: "ALL" },
  { label: "Open to do’s", value: "Open to do’s" },
  { label: "No open to do’s", value: "No open to do’s" },
  { label: "Terminated", value: "Terminated" },
];

interface CountryCustomersProps {
  countryCode: string;
}

export function CountryCustomers({ countryCode }: CountryCustomersProps) {
  const columns = useCustomersCountryColumns(countryCode);

  const { paramValues, changeParam, changeParams } = useQueryFilter(["order", "status", "search", "page"]);

  const search = paramValues.search || undefined;
  const status = (paramValues.status || STATUS_FILTERS[0]?.value) as "ACTIVE" | "TERMINATED";
  const order = (paramValues.order || ORDER_FILTERS[0]?.value) as "ASC" | "DESC" | "LAST_MODIFIED" | "FIRST_MODIFIED";
  const page = paramValues.page ? Number(paramValues.page) : 1;

  const { data: customers, isLoading } = useQuery({
    queryKey: ["customers", { country_code: countryCode, page, limit: 10, search, status, order }],
    queryFn: async () => {
      const response = await getCustomers({ country_code: countryCode, page, limit: 10, search, status, order });

      return {
        ...response,
        customers: (response.customers || []).filter((customer) =>
          customer.contracts.some((contract) => contract.type === "DIRECT_LICENSE" || contract.type === "EU_LICENSE")
        ),
      };
    },
  });

  const onSearch = (value: string) => {
    changeParams({ page: "1", search: value });
  };

  return (
    <div className="w-full h-auto bg-tonal-cream-96 p-5 py-7 md:p-8 rounded-2xl flex flex-col gap-8">
      <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-4 md:gap-0 w-full">
        <h3 className="col-span-1 md:col-span-2 text-primary text-title-1 font-bold">Customers</h3>
        <div className="col-span-1 md:col-span-2 w-full flex flex-col md:flex-row items-center gap-6">
          <div className="flex-1 w-full md:w-auto">
            <SearchInput queryName="search" onSearch={onSearch} />
          </div>
          <div className="w-full md:w-auto flex gap-3 items-center">
            <OrderFilter />
            <div className="border-[1px] h-6 border-tonal-blue-40"></div>
            <StatusFilter filters={STATUS_FILTERS} />
          </div>
        </div>
      </div>

      <AsyncPaginatedTable
        columns={columns}
        currentPage={page}
        isLoading={isLoading}
        data={customers?.customers || []}
        pages={customers?.pages || 0}
        onPageChange={(page) => {
          changeParam("page", page.toString());
        }}
        noResultsMessage={search ? `No results for "${search}" in Customer List` : "No customers found"}
      />
    </div>
  );
}
