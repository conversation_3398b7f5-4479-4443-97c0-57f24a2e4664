"use client";

import { useQuery } from "@tanstack/react-query";

import { Help } from "@arthursenno/lizenzero-ui-react/Icon";
import Divider from "@/components/_common/divider";
import { Skeleton } from "@/components/ui/skeleton";
import { getServiceSetupRequiredInformations } from "@/lib/api/service-setups";
import type { ICountry } from "@/lib/api/country/types";

import { RequiredInformationIcon } from "../../customers/customer-country/customer-country-required-informations/required-information-icon";
import { AddRequiredInformationModalTrigger } from "./add-required-information-modal/add-required-information-modal-trigger";
import { AddNewInformationDialog } from "./add-required-information-modal";
import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { useTranslations } from "next-intl";
interface CountryRequiredInformationsProps {
  country: ICountry;
}

export default function CountryRequiredInformations({ country }: CountryRequiredInformationsProps) {
  const t = useTranslations("CountryRequired");
  const c = useTranslations("common");
  const { data: requiredInformations, isLoading } = useQuery({
    queryKey: ["service-setup-required-informations", { country_code: country.code }],
    queryFn: () => getServiceSetupRequiredInformations({ country_code: country.code }),
  });

  if (isLoading) {
    return <CountryRequiredInformationsSkeleton />;
  }

  return (
    <>
      <div className="bg-white rounded-xl p-6 w-full h-full">
        <div className="flex items-center justify-between">
          <p className="text-tonal-dark-blue-10 text-large-paragraph-bold font-bold">{t("basicInfo")}</p>
          <AddRequiredInformationModalTrigger countryCode={country.code} />
        </div>
        <div className="mt-4 flex flex-col max-h-[500px] overflow-y-auto">
          {requiredInformations?.map((information) => (
            <div key={information.id} className="">
              <div className="flex items-center gap-1 w-full py-3">
                <div className="flex items-center gap-2">
                  <QuestionTooltip>
                    <QuestionTooltipDescription className="p-1">{t("basicInfo")}</QuestionTooltipDescription>
                  </QuestionTooltip>
                  <RequiredInformationIcon requiredInformationType={information.type} className="size-12" />
                  <div className="text-sm space-y-1">
                    <p className="font-bold text-tonal-dark-cream-20">{information.name}</p>
                    <p className=" text-tonal-dark-cream-50">{new Date(information.created_at).toLocaleDateString()}</p>
                  </div>
                </div>
              </div>
              <Divider initialMarginDisabled />
            </div>
          ))}
          {!!requiredInformations && (
            <p className="text-small-paragraph-regular text-tonal-dark-cream-50 text-center mt-6">{c("end")}</p>
          )}
        </div>
      </div>

      <AddNewInformationDialog countryId={country.id} />
    </>
  );
}

export function CountryRequiredInformationsSkeleton() {
  return (
    <div className="space-y-4 rounded-xl sm:p-6 w-full h-full">
      <div className="flex items-center justify-between gap-6">
        <Skeleton className="h-5 w-full" />
        <Skeleton className="h-10 w-1/2 rounded-full" />
      </div>
      <ul className="flex flex-col">
        <li className="flex items-center gap-1 w-full py-3">
          <Skeleton className="flex-shrink-0 size-12 rounded-md" />
          <div className="flex-1 w-full space-y-1">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-3 w-1/2" />
          </div>
        </li>
        <li className="flex items-center gap-1 w-full py-3">
          <Skeleton className="flex-shrink-0 size-12 rounded-md" />
          <div className="flex-1 w-full space-y-1">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-3 w-1/2" />
          </div>
        </li>
      </ul>
    </div>
  );
}
