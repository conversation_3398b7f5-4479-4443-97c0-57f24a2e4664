"use client";

import { DragFile } from "@/components/ui/drag-file";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { uploadAdminFile } from "@/lib/api/file";
import { UploadedFile } from "@/lib/api/file/types";
import { createAdminRequiredInformation } from "@/lib/api/required-information";
import { CreateAdminRequiredInformationRequest } from "@/lib/api/required-information/types";
import { queryClient } from "@/lib/react-query";
import { UserTypes } from "@/utils/user";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Add, Clear } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { ArrowRight, ChevronLeft, ChevronRight } from "lucide-react";
import { useSession } from "next-auth/react";
import { enqueueSnackbar } from "notistack";
import { useEffect, useMemo } from "react";
import { Controller, useForm, useWatch } from "react-hook-form";
import { z } from "zod";
import { useTranslations } from "next-intl";

type AddNewInformationFormData = z.infer<ReturnType<typeof createAddNewInformationSchema>>;

interface AddNewInformationDialogProps {
  countryId: number;
}

function createAddNewInformationSchema(t: any) {
  return z
    .object({
      document_type: z.union([z.literal("general"), z.literal("specific")]),
      name: z.string().min(1, t("nameRequired")),
      description: z.string().default(""),
      type: z.enum(["TEXT", "NUMBER", "DOCUMENT", "FILE", "IMAGE"]),
      file: z.any().optional(),
    })
    .refine(
      (data) => {
        if (data.type === "DOCUMENT" || data.type === "FILE" || data.type === "IMAGE") {
          return !!data.file;
        }
        return true;
      },
      {
        message: "File is required for this type",
        path: ["file"],
      }
    )
    .superRefine((data, ctx) => {
      if (data.type === "DOCUMENT" || data.type === "FILE" || data.type === "IMAGE") {
        if (!data.file) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: t("fileRequired"),
            path: ["file"],
          });
        }
      }
    });
}

export function AddNewInformationDialog({ countryId }: AddNewInformationDialogProps) {
  const t = useTranslations("AddRequiredInfoModal");
  const c = useTranslations("common");

  const REQUIRED_INFORMATION_TYPES = {
    DOCUMENT: {
      name: t("document"),
      description: t("documentDescription"),
      icon: () => (
        <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M38.82 14.82L29.16 5.16C28.42 4.42 27.4 4 26.34 4H12C9.8 4 8.02 5.8 8.02 8L8 40C8 42.2 9.78 44 11.98 44H36C38.2 44 40 42.2 40 40V17.66C40 16.6 39.58 15.58 38.82 14.82ZM28 18C26.9 18 26 17.1 26 16V7L37 18H28Z"
            fill="#F1988D"
          />
          <path
            d="M17.1611 31.6001H20.0171C21.8411 31.6001 23.1011 32.5841 23.1011 34.3121C23.1011 36.0281 21.8411 37.0241 20.0171 37.0241H18.9611V40.0001H17.1611V31.6001ZM18.9611 33.2201V35.4281H20.0531C20.7731 35.4281 21.2891 35.0201 21.2891 34.3241C21.2891 33.6281 20.7731 33.2201 20.0531 33.2201H18.9611Z"
            fill="#F2F2F2"
          />
          <path
            d="M31.367 35.8001C31.367 38.1641 29.447 40.0001 27.095 40.0001H24.251V31.6001H27.095C29.447 31.6001 31.367 33.4361 31.367 35.8001ZM29.519 35.8001C29.519 34.4081 28.439 33.2921 27.047 33.2921H26.051V38.3081H27.047C28.439 38.3081 29.519 37.1921 29.519 35.8001Z"
            fill="#F2F2F2"
          />
          <path
            d="M34.5471 36.5801V40.0001H32.7471V31.6001H37.5591V33.2321H34.5471V34.9361H37.3191V36.5801H34.5471Z"
            fill="#F2F2F2"
          />
        </svg>
      ),
    },
    FILE: {
      name: t("fileInput"),
      description: t("fileInputDescription"),
      icon: () => (
        <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M38.82 14.82L29.16 5.16C28.42 4.42 27.4 4 26.34 4H12C9.8 4 8.02 5.8 8.02 8L8 40C8 42.2 9.78 44 11.98 44H36C38.2 44 40 42.2 40 40V17.66C40 16.6 39.58 15.58 38.82 14.82ZM28 18C26.9 18 26 17.1 26 16V7L37 18H28Z"
            fill="#F1988D"
          />
          <path
            d="M17.1611 31.6001H20.0171C21.8411 31.6001 23.1011 32.5841 23.1011 34.3121C23.1011 36.0281 21.8411 37.0241 20.0171 37.0241H18.9611V40.0001H17.1611V31.6001ZM18.9611 33.2201V35.4281H20.0531C20.7731 35.4281 21.2891 35.0201 21.2891 34.3241C21.2891 33.6281 20.7731 33.2201 20.0531 33.2201H18.9611Z"
            fill="#F2F2F2"
          />
          <path
            d="M31.367 35.8001C31.367 38.1641 29.447 40.0001 27.095 40.0001H24.251V31.6001H27.095C29.447 31.6001 31.367 33.4361 31.367 35.8001ZM29.519 35.8001C29.519 34.4081 28.439 33.2921 27.047 33.2921H26.051V38.3081H27.047C28.439 38.3081 29.519 37.1921 29.519 35.8001Z"
            fill="#F2F2F2"
          />
          <path
            d="M34.5471 36.5801V40.0001H32.7471V31.6001H37.5591V33.2321H34.5471V34.9361H37.3191V36.5801H34.5471Z"
            fill="#F2F2F2"
          />
        </svg>
      ),
    },
    IMAGE: {
      name: t("image"),
      description: t("imageDescription"),
      icon: () => (
        <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M38.82 14.82L29.16 5.16C28.42 4.42 27.4 4 26.34 4H12C9.8 4 8.02 5.8 8.02 8L8 40C8 42.2 9.78 44 11.98 44H36C38.2 44 40 42.2 40 40V17.66C40 16.6 39.58 15.58 38.82 14.82ZM28 18C26.9 18 26 17.1 26 16V7L37 18H28Z"
            fill="#1F71FF"
          />
          <path
            d="M20.5416 37.3238C20.5416 39.0638 19.4616 40.1558 17.7456 40.1558C16.4736 40.1558 15.4776 39.4718 15.0576 38.3918L16.6176 37.5278C16.8336 38.0798 17.2296 38.4518 17.7456 38.4518C18.3816 38.4518 18.7176 38.0318 18.7176 37.2758V31.5998H20.5416V37.3238Z"
            fill="#F2F2F2"
          />
          <path
            d="M22.2118 31.5998H25.0678C26.8918 31.5998 28.1518 32.5838 28.1518 34.3118C28.1518 36.0278 26.8918 37.0238 25.0678 37.0238H24.0118V39.9998H22.2118V31.5998ZM24.0118 33.2198V35.4278H25.1038C25.8238 35.4278 26.3398 35.0198 26.3398 34.3238C26.3398 33.6278 25.8238 33.2198 25.1038 33.2198H24.0118Z"
            fill="#F2F2F2"
          />
          <path
            d="M37.2068 33.6158L35.5748 34.4078C35.1548 33.6038 34.3748 33.1478 33.4028 33.1478C31.9748 33.1478 30.8468 34.3238 30.8468 35.7998C30.8468 37.2758 31.9748 38.4638 33.4148 38.4638C34.5308 38.4638 35.4068 37.8038 35.6228 36.8438H33.1868V35.3318H37.5548V36.0038C37.5548 38.3438 35.8028 40.1558 33.4028 40.1558C30.9548 40.1558 29.0228 38.1998 29.0228 35.7998C29.0228 33.3998 31.0028 31.4438 33.4148 31.4438C35.0948 31.4438 36.5228 32.2718 37.2068 33.6158Z"
            fill="#F2F2F2"
          />
        </svg>
      ),
    },
    TEXT: {
      name: t("textField"),
      description: t("textFieldDescription"),
      icon: () => (
        <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="4" y="4" width="40" height="40" rx="8" fill="#FF9E14" />
          <path
            d="M35.9163 17.5002V13.1668C35.9163 12.571 35.4288 12.0835 34.833 12.0835H30.4997C29.9038 12.0835 29.4163 12.571 29.4163 13.1668V14.2502H18.583V13.1668C18.583 12.571 18.0955 12.0835 17.4997 12.0835H13.1663C12.5705 12.0835 12.083 12.571 12.083 13.1668V17.5002C12.083 18.096 12.5705 18.5835 13.1663 18.5835H14.2497V29.4168H13.1663C12.5705 29.4168 12.083 29.9043 12.083 30.5002V34.8335C12.083 35.4293 12.5705 35.9168 13.1663 35.9168H17.4997C18.0955 35.9168 18.583 35.4293 18.583 34.8335V33.7502H29.4163V34.8335C29.4163 35.4293 29.9038 35.9168 30.4997 35.9168H34.833C35.4288 35.9168 35.9163 35.4293 35.9163 34.8335V30.5002C35.9163 29.9043 35.4288 29.4168 34.833 29.4168H33.7497V18.5835H34.833C35.4288 18.5835 35.9163 18.096 35.9163 17.5002ZM14.2497 14.2502H16.4163V16.4168H14.2497V14.2502ZM16.4163 33.7502H14.2497V31.5835H16.4163V33.7502ZM29.4163 31.5835H18.583V30.5002C18.583 29.9043 18.0955 29.4168 17.4997 29.4168H16.4163V18.5835H17.4997C18.0955 18.5835 18.583 18.096 18.583 17.5002V16.4168H29.4163V17.5002C29.4163 18.096 29.9038 18.5835 30.4997 18.5835H31.583V29.4168H30.4997C29.9038 29.4168 29.4163 29.9043 29.4163 30.5002V31.5835ZM33.7497 33.7502H31.583V31.5835H33.7497V33.7502ZM31.583 16.4168V14.2502H33.7497V16.4168H31.583ZM25.018 19.2877C24.8555 18.8652 24.4438 18.5835 23.9888 18.5835C23.5338 18.5835 23.1222 18.8652 22.9705 19.2877L19.9697 27.2285C19.7638 27.7593 20.1538 28.3335 20.728 28.3335C21.0747 28.3335 21.378 28.1168 21.4972 27.7918L22.093 26.1668H25.8738L26.4805 27.8027C26.5997 28.1168 26.903 28.3335 27.2497 28.3335H27.2605C27.8347 28.3335 28.2247 27.7593 28.0297 27.2285L25.018 19.2877ZM22.5805 24.8018L23.9997 20.6527L25.408 24.8018H22.5805Z"
            fill="#F5F5F5"
          />
        </svg>
      ),
    },
    NUMBER: {
      name: t("numberField"),
      description: t("numberFieldDescription"),
      icon: () => (
        <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
          <rect x="4" y="4" width="40" height="40" rx="8" fill="#002652" />
          <path
            d="M35.7155 19.9736L35.7284 19.8961C35.9351 19.0953 35.3409 18.3332 34.5272 18.3332H30.9622L31.8664 14.7294C32.0601 13.9286 31.4659 13.1665 30.6522 13.1665C30.0709 13.1665 29.5801 13.554 29.438 14.1094L28.3789 18.3332H23.2122L24.1164 14.7294C24.3101 13.9286 23.7159 13.1665 22.9022 13.1665C22.3209 13.1665 21.8301 13.554 21.688 14.1094L20.6289 18.3332H16.4439C15.8626 18.3332 15.3589 18.7207 15.2297 19.2761L15.2039 19.3536C15.0101 20.1544 15.6043 20.9165 16.418 20.9165H19.983L18.6914 26.0832H14.5064C13.9251 26.0832 13.4343 26.4707 13.2922 27.0261L13.2664 27.1036C13.0726 27.9044 13.6668 28.6665 14.4805 28.6665H18.0455L17.1414 32.2703C16.9476 33.0711 17.5418 33.8332 18.3555 33.8332C18.9368 33.8332 19.4276 33.4457 19.5697 32.8903L20.6289 28.6665H25.7955L24.8914 32.2703C24.6976 33.0711 25.2918 33.8332 26.1055 33.8332C26.6868 33.8332 27.1776 33.4457 27.3197 32.8903L28.3789 28.6665H32.5639C33.1451 28.6665 33.6359 28.279 33.778 27.7236L33.7909 27.6461C33.9847 26.8582 33.3905 26.0832 32.5768 26.0832H29.0247L30.3164 20.9165H34.5014C35.0826 20.9165 35.5864 20.529 35.7155 19.9736ZM26.4414 26.0832H21.2747L22.5664 20.9165H27.733L26.4414 26.0832Z"
            fill="white"
          />
        </svg>
      ),
    },
  };

  const addNewInformationSchema = createAddNewInformationSchema(t);

  const { paramValues, deleteParam } = useQueryFilter(["addRequiredInformation"]);

  const countryCode = paramValues.addRequiredInformation;
  const isModalOpen = countryCode !== null;

  const {
    register,
    handleSubmit,
    control,
    reset,
    setValue,
    clearErrors,
    formState: { errors },
  } = useForm<AddNewInformationFormData>({
    resolver: zodResolver(addNewInformationSchema),
    defaultValues: {
      document_type: "specific",
    },
  });

  const { data: session } = useSession();

  const { mutate: createInformation, isPending: isCreatingInformation } = useMutation({
    mutationFn: async (
      data: Omit<CreateAdminRequiredInformationRequest, "file_id"> & {
        userId: number;
        userRole: UserTypes;
        file?: File;
        document_type?: AddNewInformationFormData["document_type"];
      }
    ) => {
      let file: UploadedFile | null = null;

      if (data.file) {
        file = await uploadAdminFile({
          user_id: data.userId,
          user_role: data.userRole,
          file: data.file!,
          document_type: data.document_type === "general" ? "GENERAL_INFORMATION" : "REQUIRED_INFORMATION",
          country_id: data.country_id,
        });

        if (!file) {
          return enqueueSnackbar(t("failedToCreateRequiredInformation"), { variant: "error" });
        }
      }

      return createAdminRequiredInformation({
        ...data,
        country_id: data.country_id,
        type: data.type,
        name: data.name,
        description: data.description,
        ...((data.type === "TEXT" || data.type === "NUMBER") && { question: data.name }),
        ...(!!file && { file_id: file.id }),
      });
    },
    onSuccess: () => {
      enqueueSnackbar(t("requiredInformationCreated"), { variant: "success" });
      handleOnOpenChange(false);
      queryClient.invalidateQueries({
        queryKey: ["service-setup-required-informations", { country_code: countryCode }],
      });
      reset();
    },
    onError: () => {
      enqueueSnackbar(t("failedToCreateRequiredInformation"), { variant: "error" });
    },
  });

  useEffect(() => {
    reset();
  }, [isModalOpen, reset]);

  async function handleFormSubmit(data: AddNewInformationFormData) {
    if (!session?.user?.id) return;

    const userId = Number(session.user.id);
    const userRole = session.user.role;

    createInformation(
      {
        country_id: countryId,
        type: data.type,
        name: data.name,
        description: data.description,
        file: data.file,
        userId,
        userRole,
        document_type: data.document_type,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: ["service-setup-required-informations", { country_code: countryCode }],
          });
          handleOnOpenChange(false);
          reset();
        },
      }
    );
  }

  function handleOnOpenChange(open: boolean) {
    if (open) return;

    deleteParam("addRequiredInformation");

    reset();
  }

  function handleAddFile(file: File) {
    setValue("file", file);
    clearErrors("file");
  }

  const requiredInformationType = useWatch({ control, name: "type" });

  const selectedRequiredInformationType = useMemo(() => {
    return REQUIRED_INFORMATION_TYPES[requiredInformationType as keyof typeof REQUIRED_INFORMATION_TYPES] || null;
  }, [requiredInformationType]);

  return (
    <Modal
      open={isModalOpen}
      onOpenChange={handleOnOpenChange}
      className="z-50 !rounded-[52px] w-full max-w-[672px] !py-9 !px-9 !bg-surface-01 min-h-144 overflow-x-auto"
    >
      <div className="flex items-start justify-between">
        <h1 className="font-large-paragraph-bold text-2xl text-tonal-dark-blue-10 font-bold my-5 sm:text-3xl">
          {t("newInfo")}
        </h1>
        <Button
          type="button"
          color="dark-blue"
          size="iconXSmall"
          variant="text"
          onClick={() => handleOnOpenChange(false)}
          className="!bg-white !rounded-full"
        >
          <Clear className="size-6" />
        </Button>
      </div>
      <p className="text-paragraph-regular text-tonal-dark-cream-10 mb-5">{t("requestNewInfo")}</p>
      <div id="add-new-information-form" className="w-full space-y-10">
        <div className="w-full">
          {!requiredInformationType && (
            <div className="bg-background rounded-2xl overflow-hidden">
              {Object.entries(REQUIRED_INFORMATION_TYPES).map(([key, type]) => (
                <label
                  htmlFor={key}
                  key={key}
                  className="flex items-center gap-2 p-4 hover:bg-surface-02 cursor-pointer"
                >
                  <input type="radio" id={key} className="hidden" {...register("type")} value={key} />
                  <div className="h-12 w-12 flex-none flex items-center justify-center">
                    <type.icon />
                  </div>
                  <div className="space-y-1 flex-1">
                    <p className="text-tonal-dark-cream-20 font-bold">{type.name}</p>
                    <span className="text-tonal-dark-cream-50 text-sm">{type.description}</span>
                  </div>
                  <ChevronRight className="stroke-support-blue" />
                </label>
              ))}
            </div>
          )}
          {!!selectedRequiredInformationType && (
            <div className="w-full space-y-10">
              <div className="space-y-4">
                <Button
                  variant="text"
                  color="light-blue"
                  size="medium"
                  leadingIcon={<ChevronLeft className="size-5" />}
                  onClick={() => reset()}
                >
                  {t("back")}
                </Button>
                <div className="flex items-center gap-2">
                  <div className="h-12 w-12 flex-none flex items-center justify-center">
                    <selectedRequiredInformationType.icon />
                  </div>
                  <div className="space-y-1 flex-1">
                    <p className="text-tonal-dark-cream-20 font-bold">{selectedRequiredInformationType.name}</p>
                    <span className="text-tonal-dark-cream-50 text-sm">
                      {selectedRequiredInformationType.description}
                    </span>
                  </div>
                </div>
                <div className="h-[1px] w-full bg-tonal-dark-cream-80 rounded-full" />
              </div>

              <Controller
                control={control}
                name="document_type"
                render={({ field }) => (
                  <div className="flex flex-col gap-4">
                    <label htmlFor="document_type" className="text-paragraph-regular text-primary">
                      {t("selectInfoType")}
                    </label>
                    <RadioGroup
                      id="document_type"
                      className="flex flex-col gap-3"
                      value={field.value}
                      onValueChange={field.onChange}
                    >
                      <div className="flex gap-2">
                        <RadioGroupItem value="general" id="general" />
                        <label htmlFor="general" className="text-small-paragraph-regular text-primary">
                          {t("general")}
                        </label>
                      </div>
                      <div className="flex gap-2">
                        <RadioGroupItem value="specific" id="specific" />
                        <label htmlFor="specific" className="text-small-paragraph-regular text-primary">
                          {t("specific")}
                        </label>
                      </div>
                    </RadioGroup>
                  </div>
                )}
              />

              <div className="space-y-10">
                {requiredInformationType === "DOCUMENT" && (
                  <>
                    <Input
                      label={t("title")}
                      placeholder={t("titlePlaceholder")}
                      {...register("name")}
                      variant={errors.name ? "error" : "default"}
                      errorMessage={errors.name?.message}
                    />
                    <DragFile
                      title={c("uploadTemplate")}
                      onFile={handleAddFile}
                      errorMessage={errors.file?.message?.toString()}
                      accept="application/pdf"
                    />
                    <Textarea
                      id="description"
                      label={t("additionalInfo")}
                      placeholder={t("additionalInfo")}
                      rows={6}
                      className="resize-none"
                      maxLength={350}
                      {...register("description")}
                    />
                  </>
                )}
                {requiredInformationType === "FILE" && (
                  <>
                    <>
                      <Input
                        label={t("title")}
                        placeholder={t("titlePlaceholder")}
                        {...register("name")}
                        variant={errors.name ? "error" : "default"}
                        errorMessage={errors.name?.message}
                      />
                      <DragFile
                        title={c("uploadTemplate")}
                        onFile={handleAddFile}
                        errorMessage={errors.file?.message?.toString()}
                        accept="application/pdf"
                      />
                      <Textarea
                        id="description"
                        label={t("additionalInfo")}
                        placeholder={t("additionalInfo")}
                        rows={6}
                        className="resize-none"
                        maxLength={350}
                        {...register("description")}
                      />
                    </>
                  </>
                )}
                {requiredInformationType === "IMAGE" && (
                  <>
                    <Input
                      label={t("title")}
                      placeholder={t("titlePlaceholder")}
                      {...register("name")}
                      variant={errors.name ? "error" : "default"}
                      errorMessage={errors.name?.message}
                    />
                    <Textarea
                      id="description"
                      label={t("additionalInfo")}
                      placeholder={t("additionalInfo")}
                      rows={6}
                      className="resize-none"
                      maxLength={350}
                      {...register("description")}
                    />
                    <DragFile
                      title={c("uploadTemplate")}
                      description={c("orDrag")}
                      onFile={handleAddFile}
                      errorMessage={errors.file?.message?.toString()}
                      accept="image/png,image/jpeg,image/jpg"
                    />
                  </>
                )}
                {requiredInformationType === "TEXT" && (
                  <>
                    <Input
                      label={t("questionToBeAnswered")}
                      placeholder={t("questionToBeAnswered")}
                      {...register("name")}
                      variant={errors.name ? "error" : "default"}
                      errorMessage={errors.name?.message}
                    />
                    <Textarea
                      id="description"
                      label={t("additionalInfo")}
                      placeholder={t("additionalInfo")}
                      rows={6}
                      className="resize-none"
                      maxLength={350}
                      {...register("description")}
                    />
                  </>
                )}
                {requiredInformationType === "NUMBER" && (
                  <>
                    <Input
                      label={t("questionToBeAnswered")}
                      placeholder={t("questionToBeAnswered")}
                      {...register("name")}
                      variant={errors.name ? "error" : "default"}
                      errorMessage={errors.name?.message}
                    />
                    <Textarea
                      id="description"
                      label={t("additionalInfo")}
                      placeholder={t("additionalInfo")}
                      rows={6}
                      className="resize-none"
                      maxLength={350}
                      {...register("description")}
                    />
                  </>
                )}
              </div>
            </div>
          )}
        </div>
        <div className="flex flex-col mt-8">
          <div className="flex items-center justify-end">
            <Button
              form="add-new-information-form"
              type="submit"
              variant="filled"
              color="yellow"
              size="medium"
              trailingIcon={<ArrowRight />}
              onClick={() => handleSubmit(handleFormSubmit)()}
              disabled={isCreatingInformation}
            >
              {isCreatingInformation ? c("saving") : c("save")}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
}
