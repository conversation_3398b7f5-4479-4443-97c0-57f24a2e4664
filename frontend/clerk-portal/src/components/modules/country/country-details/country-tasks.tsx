"use client";

import Divider from "@/components/_common/divider";
// import { getTasks } from "@/lib/api/country";
import { filterTags, getTasks } from "@/lib/api/home";
import { CheckCircle, CheckCircleOutline, Launch } from "@arthursenno/lizenzero-ui-react/Icon";
// import { ProgressBar } from "@arthursenno/lizenzero-ui-react/ProgressBar";
import { useQuery } from "@tanstack/react-query";
import Image from "next/image";
import { useEffect, useMemo, useState } from "react";
// import Status from "../components/task-status";
// import StatusBadge from "../components/task-type";
import { FilterTasks } from "../components/task-type-filter";
import { Skeleton } from "@/components/ui/skeleton";
import { ScrollArea } from "@/components/ui/scroll-area";
import { TaskTag } from "../../home/<USER>/task-tag";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useTranslations } from "next-intl";

interface CountryTasksProps {
  countryCode: string;
}

export function CountryTasks({ countryCode }: CountryTasksProps) {
  const { paramValues } = useQueryFilter(["type"]);

  const [taskType, setTaskType] = useState<
    "ALL" | "REGISTRATION" | "VOLUME_REPORTS" | "THIRD_PARTY_INVOICES" | "TERMINATIONS"
  >("ALL");

  useEffect(() => {
    const paramTaskType = paramValues.type as typeof taskType;
    setTaskType(paramTaskType ?? "ALL");
  }, [paramValues.type]);

  // const { data: tasks, isLoading } = useQuery({
  //   queryKey: ["tasks", { country_code: countryCode }],
  //   queryFn: async () => getTasks(),
  // });
  const { data: tasks, status: queryStatus } = useQuery({
    queryKey: ["country-monday-tasks", countryCode],
    queryFn: () => {
      // return getTasks(undefined, countryCode);
      return getTasks(undefined, countryCode);
    },
    // TODO: Monday API give us a timeout of 20 seconds, so we need to set a stale time to avoid empty responses
    staleTime: 30000,
  });

  const allTasks = useMemo(() => {
    if (!tasks) return [];
    return [
      ...(tasks?.registrations?.[0]?.groups?.[0]?.items_page?.items ?? []),
      ...(tasks?.volumeReports?.[0]?.groups?.[0]?.items_page?.items ?? []),
      ...(tasks?.thirdPartyInvoices?.[0]?.groups?.[0]?.items_page?.items ?? []),
      ...(tasks?.terminations?.[0]?.groups?.[0]?.items_page?.items ?? []),
    ];
  }, [tasks]);

  const registrationTasks = useMemo(() => tasks?.registrations?.[0]?.groups?.[0].items_page.items ?? [], [tasks]);
  const volumeReportTasks = useMemo(() => tasks?.volumeReports?.[0]?.groups?.[0].items_page.items ?? [], [tasks]);
  const thirdPartyInvoiceTasks = useMemo(
    () => tasks?.thirdPartyInvoices?.[0]?.groups?.[0].items_page.items ?? [],
    [tasks]
  );
  const terminationTasks = useMemo(() => tasks?.terminations?.[0]?.groups?.[0].items_page.items ?? [], [tasks]);

  const taskCounts: Record<typeof taskType, number> = useMemo(() => {
    if (!tasks) {
      return { ALL: 0, REGISTRATION: 0, VOLUME_REPORTS: 0, THIRD_PARTY_INVOICES: 0, TERMINATIONS: 0 };
    }
    return {
      ALL: allTasks.length ?? 0,
      REGISTRATION: registrationTasks.length ?? 0,
      VOLUME_REPORTS: volumeReportTasks.length ?? 0,
      THIRD_PARTY_INVOICES: thirdPartyInvoiceTasks.length ?? 0,
      TERMINATIONS: terminationTasks.length ?? 0,
    };
  }, [
    allTasks.length,
    registrationTasks.length,
    tasks,
    terminationTasks.length,
    thirdPartyInvoiceTasks.length,
    volumeReportTasks.length,
  ]);

  const tasksGroupedByType = useMemo(() => {
    if (taskType === "ALL") return allTasks;
    if (taskType === "REGISTRATION") return registrationTasks;
    if (taskType === "VOLUME_REPORTS") return volumeReportTasks;
    if (taskType === "THIRD_PARTY_INVOICES") return thirdPartyInvoiceTasks;
    if (taskType === "TERMINATIONS") return terminationTasks;
    return [];
  }, [allTasks, registrationTasks, taskType, terminationTasks, thirdPartyInvoiceTasks, volumeReportTasks]);
  const c = useTranslations("common");
  const t = useTranslations("Monday");

  const hasEmptyTaskList = !tasksGroupedByType.length;

  const getId = () => Math.random().toString(36).substring(2, 9);

  if (queryStatus === "pending") {
    return <CountryTasksSkeleton />;
  }

  // TODO: Como fazer essa contagem de task done?
  // const completedTasks = tasks?.filter((task) => task.status === "Complete").length;
  return (
    <div className="w-full h-full bg-white rounded-xl">
      <div className="flex items-center justify-between p-6">
        <div className="flex items-center gap-2">
          <div className="size-10 border-[1px] border-solid border-[#ECECEC] rounded-xl flex justify-center items-center">
            <Image src={`/assets/images/LogoMonday.svg`} alt={"Logo Monday"} width={40} height={40} />
          </div>
          <p className="text-tonal-dark-blue-10 text-title-3 font-bold">{t("name")}</p>
        </div>
        <Launch className="size-6 fill-[#009DD3]" />
      </div>
      {/* {isLoading && <CountryTasksSkeleton />} */}
      {queryStatus === "success" && (
        <>
          <div className="px-6 flex items-center justify-between">
            <FilterTasks taskCounts={taskCounts} />
            <div className="flex items-center gap-1">
              <p className="text-small-paragraph-regular text-tonal-dark-cream-20">{t("tasksDone")}:</p>
              <p className="text-small-paragraph-regular text-tonal-dark-cream-20">
                <span className="text-small-paragraph-regular text-tonal-dark-cream-20 font-bold">
                  0 {/* {completedTasks} */}
                </span>{" "}
                / {taskType === "ALL" && `${allTasks.length}`}
                {taskType === "REGISTRATION" && `${registrationTasks.length}`}
                {taskType === "VOLUME_REPORTS" && `${volumeReportTasks.length}`}
                {taskType === "THIRD_PARTY_INVOICES" && `${thirdPartyInvoiceTasks.length}`}
                {taskType === "TERMINATIONS" && `${terminationTasks.length}`}
                {!taskType && `${allTasks.length}`}
              </p>
            </div>
          </div>
          <div className="p-6">
            {/* <div className="overflow-auto max-h-[480px]">
              {!!tasks.length ? (
                tasks.map((task) => (
                  <>
                    <div key={task.id} className=" p-2 flex items-center justify-between">
                      <div className="flex flex-col gap-0.5">
                        <p className="text-support-blue text-small-paragraph-regular underline">
                          Customer {task.customerId}
                        </p>
                        <p className="font-bold text-paragraph-regular text-tonal-dark-cream-20">{task.documentName}</p>
                        <p className="text-small-paragraph-regular text-tonal-dark-cream-50">{task.taskProgress}</p>
                      </div>
                      <div className="flex items-center justify-between  gap-6">
                        <div className="flex flex-col justify-start items-start gap-2">
                          <Status status={task.status} />
                          <StatusBadge status={task.type} />
                        </div>
                        <div className="flex justify-end">
                          {task.status === "Complete" ? (
                            <CheckCircle className="size-8 fill-success" />
                          ) : (
                            <CheckCircleOutline className="size-8 fill-tonal-dark-cream-40" />
                          )}
                        </div>
                      </div>
                    </div>
                    <Divider initialMarginDisabled className="my-1" />
                  </>
                ))
              ) : (
                <p className="text-small-paragraph-regular text-tonal-dark-cream-50 text-center mt-6">
                  No tasks found.
                </p>
              )}
            </div> */}
            {hasEmptyTaskList ? (
              <div className="flex flex-col items-center justify-center flex-grow mt-5 mb-7">
                <p className="text-tonal-dark-cream-20">{t("noTasks")}</p>
              </div>
            ) : (
              <ScrollArea className="flex flex-1 flex-col p-4 z-10 overflow-x-hidden max-h-80">
                <ul className="flex-grow space-y-4">
                  {tasksGroupedByType.map((task) => {
                    const customerId = task?.column_values?.find((item) => item.column.title === "Customer ID")?.text;

                    const textContent = task?.name ?? "-";

                    // TODO: Validar as outras tags (Report, Docs & Info, Invoice e Termination)
                    const tags: { key: string; label: string }[] = task.column_values
                      .filter((col) => col.column.title && filterTags.includes(col.column.title))
                      .map((item) => ({ key: item.column.title.toLocaleLowerCase(), label: item?.text || `` }));

                    return (
                      <li key={getId()}>
                        {/* <TaskCard task={task} /> */}

                        <div className="p-2 flex items-center justify-between">
                          <div className="flex flex-col gap-0.5">
                            {!!customerId && (
                              <p className="text-support-blue text-small-paragraph-regular underline">
                                Customer #{customerId}
                              </p>
                            )}
                            <p className="font-bold text-paragraph-regular text-tonal-dark-cream-20">{textContent}</p>
                            {/* <p className="text-small-paragraph-regular text-tonal-dark-cream-50">{task.taskProgress}</p> */}
                          </div>
                          <div className="flex items-center justify-between  gap-6">
                            <div className="flex flex-col justify-start items-start gap-2">
                              {/* <Status status={task.status} /> */}
                              {/* <StatusBadge status={task.type} /> */}
                              <ul className="flex items-center flex-wrap gap-2">
                                {tags?.map((tag) => (
                                  <li key={tag.key}>
                                    <TaskTag tag={tag} />
                                  </li>
                                ))}
                              </ul>
                            </div>
                            <div className="flex justify-end">
                              <CheckCircle className="size-6 fill-white stroke-[1.5] stroke-tonal-dark-cream-40" />
                              {/* {task.status === "Complete" ? (
                              <CheckCircle className="size-8 fill-success" />
                            ) : (
                              <CheckCircleOutline className="size-8 fill-tonal-dark-cream-40" />
                            )} */}
                            </div>
                          </div>
                        </div>
                        <Divider initialMarginDisabled className="my-1" />
                      </li>
                    );
                  })}
                </ul>
              </ScrollArea>
            )}
          </div>
        </>
      )}
    </div>
  );
}

export function CountryTasksSkeleton() {
  return (
    <div className="w-full h-full bg-white rounded-xl">
      <div className="flex items-center gap-2 py-6">
        <div className="size-10 border-[1px] border-solid border-[#ECECEC] rounded-xl flex justify-center items-center">
          <Skeleton className="flex-shrink-0 size-10" />
        </div>
        <Skeleton className="h-5 w-1/2 sm:w-1/6" />
      </div>
      <ul className="w-full overflow-auto max-h-[480px]">
        <li className="w-full space-y-2">
          <Skeleton className="h-14 w-full" />
          <Skeleton className="h-14 w-full" />
          <Skeleton className="h-14 w-full" />
        </li>
      </ul>
    </div>
  );
}
