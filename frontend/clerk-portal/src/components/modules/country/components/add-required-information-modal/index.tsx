import FormatShapes from "@/components/_common/icons/format-shapes";
import JpgFile from "@/components/_common/icons/jpg-file";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { createRequiredInformation } from "@/lib/api/required-information";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import {
  Pdf,
  KeyboardArrowRight,
  KeyboardArrowLeft,
  File,
  Delete,
  East,
  Clear,
} from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Hash, Loader2Icon } from "lucide-react";
import { useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";
import { useTranslations } from "next-intl";

const formSchema = z.object({
  type: z.union([z.literal("general"), z.literal("specific")]),
  title: z.string(),
  fileTemplate: z
    .any()
    .refine((value) => {
      return Array.isArray(value) && value.length > 0;
    })
    .optional(),
  additionalInformation: z.string().optional(),
});

type NewRequiredInformationFormData = z.infer<typeof formSchema>;

type AddRequiredInformationModalProps = {
  licenseId?: number;
};

const AddRequiredInformationModal = ({ licenseId }: AddRequiredInformationModalProps) => {
  const t = useTranslations("AddRequiredInfoModal");
  const c = useTranslations("common");

  const documentTypes = [
    {
      key: "DOCUMENT",
      label: t("document"),
      description: t("documentDescription"),
      icon: <Pdf className="size-12 fill-tonal-red-70" />,
    },
    {
      key: "FILE",
      label: t("fileInput"),
      description: t("fileInputDescription"),
      icon: <Pdf className="size-12 fill-tonal-red-70" />,
    },
    {
      key: "IMAGE",
      label: t("image"),
      description: t("imageDescription"),
      icon: <JpgFile className="size-12 fill-tonal-dark-blue-50" />,
    },
    {
      key: "TEXT",
      label: t("textField"),
      description: t("textFieldDescription"),
      icon: (
        <div className="w-[48px] h-[48px] bg-on-surface-04 flex items-center justify-center rounded-lg">
          <FormatShapes className="size-9 fill-tonal-red-70" />
        </div>
      ),
    },
    {
      key: "NUMBER",
      label: t("numberField"),
      description: t("numberFieldDescription"),
      icon: (
        <div className="w-[48px] h-[48px] bg-tonal-dark-blue-10 text-white flex items-center justify-center rounded-lg">
          <Hash className="size-9 fill-white" />
        </div>
      ),
    },
  ] as const;
  type DocumentType = (typeof documentTypes)[number];
  const { paramValues, deleteParam } = useQueryFilter(["add-required-information"]);

  const isModalOpen = paramValues["add-required-information"] === "true";

  const [currentStep, setCurrentStep] = useState(0);
  const [selectedDocumentType, setSelectedDocumentType] = useState<DocumentType>();
  const { handleSubmit, formState, control, reset, watch, setValue, setError } =
    useForm<NewRequiredInformationFormData>({
      resolver: zodResolver(formSchema),
    });
  const queryClient = useQueryClient();

  const { mutateAsync } = useMutation({
    mutationFn: async (data: NewRequiredInformationFormData) => {
      if (!licenseId) return;
      await createRequiredInformation({
        description: data.additionalInformation ?? "",
        license_id: licenseId,
        name: data.title,
        setup_required_information_id: 1,
        status: "OPEN",
        type: selectedDocumentType?.key ?? "FILE",
      });
    },
    onSuccess: () => {
      handleOnOpenChange(false);
      queryClient.invalidateQueries({
        queryKey: ["required-informations"],
      });
    },
    onError: () => {
      setError("title", { message: t("unexpected") });
    },
  });

  const handleOnOpenChange = (open: boolean) => {
    if (open) return;

    setCurrentStep(0);
    setSelectedDocumentType(undefined);
    reset();

    deleteParam("add-required-information");
  };

  const handleNextStep = () => {
    setCurrentStep(1);
  };

  const handlePreviousStep = () => {
    setCurrentStep(0);
    setSelectedDocumentType(undefined);
    reset();
  };

  const handleRemoveFile = (file: File) => {
    setValue(
      "fileTemplate",
      watch("fileTemplate")?.filter((f: File) => f.name !== file.name && f.lastModified !== file.lastModified)
    );
  };

  const onSubmit = async (data: NewRequiredInformationFormData) => {
    await mutateAsync(data);
  };

  return (
    <Modal
      open={isModalOpen}
      onOpenChange={handleOnOpenChange}
      className="z-50 !rounded-[52px] w-full max-w-[672px] !py-9 !px-9 !bg-surface-01 min-h-144 overflow-x-auto"
    >
      <div className="flex items-start justify-between">
        <h1 className="font-large-paragraph-bold text-2xl text-tonal-dark-blue-10 font-bold my-5 sm:text-3xl">
          {t("newInfo")}
        </h1>
        <Button
          type="button"
          color="dark-blue"
          size="iconXSmall"
          variant="text"
          onClick={() => handleOnOpenChange(false)}
          className="!bg-white !rounded-full"
        >
          <Clear className="size-6" />
        </Button>
      </div>
      <p className="text-paragraph-regular text-tonal-dark-cream-10 mb-5">{t("requestNewInfo")}</p>

      {currentStep === 0 && (
        <div className="flex flex-col bg-white rounded-2xl gap-2">
          {documentTypes.map((type) => (
            <button
              type="button"
              key={type.key}
              className="flex items-center justify-start gap-4 cursor-pointer hover:bg-surface-02 p-4 rounded-lg focus:bg-surface-02"
              onClick={(e) => {
                e.stopPropagation();
                setSelectedDocumentType(type);
                handleNextStep();
              }}
            >
              {type.icon}
              <div className="flex flex-col items-start">
                <h2 className="text-small-paragraph-regular text-tonal-dark-cream-20 font-bold">{type.label}</h2>
                <p className="text-small-paragraph-regular text-tonal-dark-cream-50">{type.description}</p>
              </div>
              <KeyboardArrowRight className="size-6 ml-auto fill-support-blue" />
            </button>
          ))}
        </div>
      )}

      {currentStep === 1 && selectedDocumentType && (
        <div className="flex flex-col gap-4">
          <div className="max-w-16">
            <Button
              type="button"
              color="light-blue"
              size="small"
              variant="text"
              onClick={handlePreviousStep}
              leadingIcon={<KeyboardArrowLeft />}
            >
              {c("back")}
            </Button>
          </div>
          <div className="flex items-center justify-start gap-4 border-b border-b-tonal-dark-cream-80 pb-4">
            {selectedDocumentType?.icon}
            <div className="flex flex-col items-start">
              <h2 className="text-small-paragraph-regular text-tonal-dark-cream-20 font-bold">
                {selectedDocumentType?.label}
              </h2>
              <p className="text-small-paragraph-regular text-tonal-dark-cream-50">
                {selectedDocumentType?.description}
              </p>
            </div>
          </div>
          <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-8 mt-6">
            <Controller
              control={control}
              name="type"
              render={({ field }) => (
                <div className="flex flex-col gap-4">
                  <label htmlFor="informationType" className="text-paragraph-regular text-primary">
                    {t("selectInfoType")}*
                  </label>
                  <RadioGroup
                    id="informationType"
                    className="flex flex-col gap-3"
                    value={field.value}
                    onValueChange={field.onChange}
                  >
                    <div className="flex gap-2">
                      <RadioGroupItem value="general" id="general" />
                      <label htmlFor="general" className="text-small-paragraph-regular text-primary">
                        {t("general")}
                      </label>
                    </div>
                    <div className="flex gap-2">
                      <RadioGroupItem value="specific" id="specific" />
                      <label htmlFor="specific" className="text-small-paragraph-regular text-primary">
                        {t("specific")}
                      </label>
                    </div>
                  </RadioGroup>
                </div>
              )}
            />

            <Controller
              control={control}
              name="title"
              render={({ field }) => (
                <div className="max-w-[400px]">
                  <Input
                    label={t("title") + "*"}
                    placeholder={selectedDocumentType.key === "IMAGE" ? t("titleOfImage") : t("infoTitle")}
                    {...field}
                  />
                </div>
              )}
            />

            {watch("fileTemplate")?.length ? (
              watch("fileTemplate")?.map((file: File) => (
                <div key={file.name} className="flex flex-col">
                  <div className="flex items-center gap-2">
                    <Pdf className="size-12 fill-tonal-red-70" />
                    <p className="text-paragraph-regular text-tonal-dark-cream-10 font-bold">{file.name}</p>
                  </div>
                  <div className="flex items-center justify-between pl-8 pr-3 py-3">
                    <div className="flex items-center gap-2">
                      <File className="size-6 fill-primary mb-1" />
                      <p className="text-small-paragraph-regular-underline text-primary underline">{file.name}</p>
                    </div>
                    <div className="flex items-center gap-6">
                      <p className="text-small-paragraph-regular text-tonal-dark-cream-30">
                        {file.lastModified
                          ? new Date(file.lastModified).toLocaleDateString()
                          : new Date().toLocaleDateString()}
                      </p>
                      <Button
                        type="button"
                        color="gray"
                        size="iconSmall"
                        variant="text"
                        onClick={() => {
                          handleRemoveFile(file);
                        }}
                      >
                        <Delete className="size-6 fill-tonal-dark-cream-30" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <Controller
                control={control}
                name="fileTemplate"
                render={({ field }) => (
                  <>
                    <input
                      type="file"
                      className="hidden"
                      id="fileTemplate"
                      multiple
                      onChange={(e) => {
                        field.onChange(Array.from(e.target.files || []));
                      }}
                    />
                    <label
                      className="border-2 border-dashed border-tonal-dark-cream-60 bg-surface-01 rounded-2xl p-4 h-20 grid place-items-center"
                      htmlFor="fileTemplate"
                    >
                      <p className="text-small-paragraph-regular text-tonal-dark-cream-20 font-bold">
                        {c("uploadTemplate")}
                        <span className="text-tonal-dark-cream-50 font-normal"> {c("orDrag")}</span>
                      </p>
                    </label>
                  </>
                )}
              />
            )}

            <Controller
              control={control}
              name="additionalInformation"
              render={({ field }) => (
                <div className="flex flex-col gap-2">
                  <label htmlFor="note" className="text-primary">
                    {t("additionalInfo")}
                  </label>
                  <Textarea id="note" placeholder={t("addDetails")} {...field} className="resize-none" />
                </div>
              )}
            />

            <div className="flex justify-end">
              <Button
                type="submit"
                color="yellow"
                variant="filled"
                size="medium"
                disabled={!formState.isValid || formState.isSubmitting}
                trailingIcon={formState.isSubmitting ? <Loader2Icon className="animate-spin" /> : <East />}
                className="max-w-52 w-full h-14"
              >
                Save
              </Button>
            </div>
          </form>
        </div>
      )}
    </Modal>
  );
};

export default AddRequiredInformationModal;
