"use client";

import { usePathname } from "@/i18n/navigation";
import { useSearchParams } from "next/navigation";
import type { BestMatch } from "string-similarity";

interface BestMatchSearchProps {
  bestMatch: BestMatch;
}

export function BestMatchSearch({ bestMatch }: BestMatchSearchProps) {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  // const router = useRouter();

  const handleClick = () => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("q", bestMatch.bestMatch.target);

    // Fix: update SearchInput state after replace
    // router.replace(`${pathname}?${params.toString()}`, { scroll: false });
    if (typeof window !== "undefined") {
      window.location.href = `${pathname}?${params.toString()}`;
    }
  };

  if (bestMatch.bestMatch.rating < 0.1) {
    return null;
  }

  return (
    <span className="text-tonal-dark-cream-40">
      Did you mean &quot;
      <button type="button" onClick={handleClick} className="text-primary underline">
        {bestMatch.bestMatch.target}
      </button>
      &quot;?
    </span>
  );
}
