"use client";

import { useMemo } from "react";

import { useQueryFilter } from "@/hooks/use-query-filter";
import { SelectDropdown } from "@/components/_common/select-dropdown";

export const ORDER_FILTERS = [
  { label: "Last added", value: "LAST_MODIFIED" },
  { label: "Most recent added", value: "FIRST_MODIFIED" },
  { label: "A-Z", value: "ASC" },
  { label: "Z-A", value: "DESC" },
] as const;

const DEFAULT_ORDER = ORDER_FILTERS[0];

export function SortCountries() {
  const { paramValues, changeParam } = useQueryFilter(["order"]);

  const orderValue = (paramValues.order || DEFAULT_ORDER.value) as (typeof ORDER_FILTERS)[number]["value"];

  const selectedOrder = useMemo(() => {
    return ORDER_FILTERS.find((o) => o.value === orderValue) ?? DEFAULT_ORDER;
  }, [orderValue]);

  function handleChangeOrder(value: (typeof ORDER_FILTERS)[number]["value"]) {
    changeParam("order", value);
  }

  return (
    <SelectDropdown
      options={ORDER_FILTERS.map((orderFilter) => ({
        label: orderFilter.label,
        value: orderFilter.value,
        disabled: orderFilter.value === selectedOrder.value,
      }))}
      value={selectedOrder?.value || ORDER_FILTERS[0].value}
      onChangeValue={(value) => handleChangeOrder(value as (typeof ORDER_FILTERS)[number]["value"])}
    />
  );
}
