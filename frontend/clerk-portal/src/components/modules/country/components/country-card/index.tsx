"use client";

import Link from "next/link";

import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { CountryIcon } from "@/components/_common/country-icon";
import { Skeleton } from "@/components/ui/skeleton";
import type { ICountry } from "@/lib/api/country/types";
import { getNameInitials } from "@/utils/get-name-initials";

import { SelectRepresentativeModalTrigger } from "../select-representative-modal/select-representative-modal-trigger";
import { useTranslations } from "next-intl";

interface CountryCardProps {
  country: ICountry;
  openTasksLength: number;
}

export function CountryCard({ country, openTasksLength = 0 }: CountryCardProps) {
  const t = useTranslations("CountryCard");
  const representatives = country.followers || [];

  return (
    <Link
      href={`/countries/${country.code}`}
      className="space-y-6 px-4 py-6 w-full cursor-pointer rounded-lg hover:bg-tonal-dark-cream-96 transition-colors duration-300"
    >
      <div className="flex  gap-3 justify-between">
        <div className="flex items-center gap-2 leading-none">
          <CountryIcon className="size-6 md:size-8" country={{ flag: country.flag_url, name: country.name }} />
          <h3 className="font-bold text-lg md:text-xl text-primary text-nowrap">{country.name}</h3>
        </div>
        <SelectRepresentativeModalTrigger countryCode={country.code} />
      </div>

      <div className="space-y-4">
        <div className="flex items-center gap-1 text-primary">
          <p className="flex-1 text-sm">{t("openTasks")}</p>
          <span className="text-base">{openTasksLength}</span>
        </div>

        <div className="flex items-center gap-2 text-tonal-dark-cream-40 pt-2 border-t border-tonal-dark-cream-80">
          <p className="text-sm">{t("representative")}</p>
          {representatives.length === 0 && <span className="flex text-primary">-</span>}
          <div className="flex items-center gap-2 flex-wrap">
            {representatives.slice(0, 4).map((representative) => (
              <Avatar key={representative.id}>
                <AvatarFallback>
                  {getNameInitials(`${representative.user_first_name} ${representative.user_last_name}`)}
                </AvatarFallback>
              </Avatar>
            ))}
            {representatives.length > 4 && (
              <Avatar>
                <AvatarFallback>+{representatives.length - 4}</AvatarFallback>
              </Avatar>
            )}
          </div>
        </div>

        <div className="flex items-center gap-1 text-primary">
          <p className="flex-1 text-sm">{t("licensedCustomers")}</p>
          <span className="text-base">{country.licensed_customer_count ?? 0}</span>
        </div>

        <div className="flex items-center gap-1 text-primary">
          <p className="flex-1 text-sm">{t("unlicensedCustomers")}</p>
          <strong className="text-base text-support-blue font-bold">{country.unlicensed_customer_count ?? 0}</strong>
        </div>
      </div>
    </Link>
  );
}

export function CountryCardPlaceholder() {
  return (
    <div className="space-y-6 px-4 py-6 w-full">
      <div className="flex flex-col gap-3 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2 leading-none">
          <Skeleton className="size-8 rounded-full" />
          <Skeleton className="h-3 w-24" />
        </div>
        <Skeleton className="h-8 rounded-full w-full md:max-w-44" />
      </div>

      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <Skeleton className="h-2.5 w-1/2" />
          <Skeleton className="h-2.5 w-9" />
        </div>
        <div className="flex items-center justify-between">
          <Skeleton className="h-2.5 w-1/2" />
          <Skeleton className="h-2.5 w-9" />
        </div>
        <div className="flex items-center justify-between">
          <Skeleton className="h-2.5 w-1/2" />
          <Skeleton className="h-2.5 w-9" />
        </div>
        <div className="flex items-center justify-between">
          <Skeleton className="h-2.5 w-1/2" />
          <Skeleton className="h-2.5 w-9" />
        </div>
      </div>
    </div>
  );
}
