import { CountryIcon } from "@/components/_common/country-icon";
import { cn } from "@/lib/utils";
import { ServiceType } from "@/lib/api/customer/serviceType";

interface CountriesPreviewProps extends React.ComponentProps<"div"> {
  countries: Array<{
    id: number;
    name: string;
    flag: string;
    status: "No open to do\u2019s" | "Open to do\u2019s" | "Active" | "Inactive" | "Termination case in progress";
    service: {
      id: ServiceType;
      status:
        | "Active"
        | "Inactive"
        | "Terminated"
        | "Termination in progress"
        | "Termination completed"
        | "Termination";
    };
  }>;
  label?: string;
  /** Maximum number of countries to display */
  max?: number;
}

export function CountriesPreview(props: CountriesPreviewProps) {
  const { countries, label = "Countries", max = 2, className, ...restProps } = props;

  if (countries.length < 0) {
    return null;
  }

  return (
    <div className={cn("flex flex-col items-start gap-2", className)} {...restProps}>
      <span className="text-tonal-dark-cream-30">{label}</span>
      <ul className="flex items-center gap-3">
        {countries.slice(0, max).map((country) => (
          <li key={country.id}>
            <CountryIcon country={{ name: country.name, flag: country.flag }} />
          </li>
        ))}
        <li key="count" className="mt-1 ml-1">
          <span className="text-tonal-dark-cream-30">({countries.length})</span>
        </li>
      </ul>
    </div>
  );
}
