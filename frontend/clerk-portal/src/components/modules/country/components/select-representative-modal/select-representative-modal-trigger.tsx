"use client";

import { usePathname, useRouter } from "@/i18n/navigation";
import { useSearchParams } from "next/navigation";

import { Button } from "@arthursenno/lizenzero-ui-react/Button";

interface SelectRepresentativeModalTriggerProps {
  countryCode: string;
}

export function SelectRepresentativeModalTrigger({ countryCode }: SelectRepresentativeModalTriggerProps) {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  function handleClick(e: React.MouseEvent<HTMLButtonElement>) {
    e.preventDefault();
    e.stopPropagation();

    const params = new URLSearchParams(searchParams.toString());
    params.set("select-representative", countryCode);

    router.push(`${pathname}?${params.toString()}`, { scroll: false });
  }

  return (
    <Button className="text-nowrap" variant="filled" color="yellow" size="small" onClick={handleClick}>
      Select Representative
    </Button>
  );
}
