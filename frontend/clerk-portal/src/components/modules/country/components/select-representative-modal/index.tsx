"use client";

import React from "react";
import { useMutation, useQuery } from "@tanstack/react-query";
import { zodResolver } from "@hookform/resolvers/zod";
import { enqueueSnackbar } from "notistack";
import { Controller, useForm } from "react-hook-form";
import { z } from "zod";

import { Clear, KeyboardArrowRight } from "@arthursenno/lizenzero-ui-react/Icon";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { CountryIcon } from "@/components/_common/country-icon";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { getCountryOverview } from "@/lib/api/country";
import { Skeleton } from "@/components/ui/skeleton";

import { AdminUsersAutocomplete } from "../admin-users-autocomplete";
import { createCountryFollower } from "@/lib/api/country-followers";
import { queryClient } from "@/lib/react-query";
import { useTranslations } from "next-intl";

const selectRepresentativeFormSchema = z.object({
  user_ids: z.array(z.number()),
});

type SelectRepresentativeFormData = z.infer<typeof selectRepresentativeFormSchema>;

export function SelectRepresentativeModal() {
  const t = useTranslations("SelectRepresentativeModal");
  const c = useTranslations("common");
  const { paramValues, deleteParam } = useQueryFilter(["select-representative"]);

  const selectedCountryCode = paramValues["select-representative"] || null;
  const isModalOpen = selectedCountryCode !== null;

  const {
    data: country,
    status: queryStatus,
    refetch,
  } = useQuery({
    queryKey: ["country-overview", { country_code: selectedCountryCode }],
    queryFn: async () => await getCountryOverview(selectedCountryCode!),
    enabled: !!selectedCountryCode,
  });

  const { mutateAsync } = useMutation({
    mutationFn: async (data: SelectRepresentativeFormData) => {
      await createCountryFollower({ country_id: country!.id, user_ids: data.user_ids });
    },
    onSuccess: () => {
      enqueueSnackbar(t("successSelect"), { variant: "success" });
      queryClient.invalidateQueries({ queryKey: ["countries-overview"] });
      refetch();
      handleCloseModal();
      form.reset();
    },
    onError: () => {
      enqueueSnackbar(t("errorSelect"), { variant: "error" });
    },
  });

  const form = useForm<SelectRepresentativeFormData>({
    resolver: zodResolver(selectRepresentativeFormSchema),
    defaultValues: {
      user_ids: [],
    },
  });

  // React.useEffect(() => {
  //   if (country) {
  //     form.reset({ user_ids: country.followers.map((follower) => follower.user_id) });
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [country]);

  function handleCloseModal() {
    deleteParam("select-representative");
  }

  function handleOnOpenChange(open: boolean) {
    if (!open) {
      handleCloseModal();
      form.reset();
    }
  }

  async function handleSubmit(data: SelectRepresentativeFormData) {
    await mutateAsync(data);
  }

  const isSubmitting = form.formState.isSubmitting;
  const selectedValues = form.watch("user_ids");

  return (
    <Modal
      open={isModalOpen}
      onOpenChange={handleOnOpenChange}
      className="z-50 !rounded-[52px] w-full max-w-[672px] !py-8 !px-9"
    >
      <div className="flex items-center justify-end">
        <Clear onClick={handleCloseModal} className="w-8 h-8 fill-primary hover:opacity-75 cursor-pointer" />
      </div>

      {queryStatus === "pending" && (
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-2">
            <Skeleton className="size-7 rounded-full" />
            <Skeleton className="h-5 w-1/4" />
          </div>
          <Skeleton className="mt-1 h-7 w-1/2" />
          <div className="mt-10 flex flex-col gap-2">
            <Skeleton className="h-3 w-1/4" />
            <Skeleton className="h-14 w-full" />
          </div>
          <div className="mt-10 flex justify-end items-end">
            <Skeleton className="h-14 w-1/3" />
          </div>
        </div>
      )}

      {queryStatus === "success" && (
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-2">
            <CountryIcon country={{ flag: country.flag_url, name: country.name }} className="h-6 w-6" />
            <span className="mt-1 text-xl text-primary">{country.name}</span>
          </div>
          <h2 className="text-primary text-[1.75rem] font-bold">{t("selectPrompt")}</h2>
        </div>
      )}

      {queryStatus === "success" && (
        <form onSubmit={form.handleSubmit(handleSubmit)} className="mt-10 w-full">
          <Controller
            control={form.control}
            name="user_ids"
            render={({ field }) => {
              return <AdminUsersAutocomplete country={country} value={field.value} onValueChange={field.onChange} />;
            }}
          />

          <div className="mt-10 flex justify-end">
            <Button
              type="submit"
              variant="filled"
              size="medium"
              color="yellow"
              className="sm:min-w-52"
              disabled={isSubmitting || selectedValues.length === 0}
            >
              {isSubmitting ? c("saving") : c("save")} <KeyboardArrowRight className="-mt-1 size-5 fill-on-tertiary" />
            </Button>
          </div>
        </form>
      )}
    </Modal>
  );
}
