"use client";

import { Dropdown, DropdownItem } from "@/components/_common/dropdown";
import { KeyboardArrowDown } from "@arthursenno/lizenzero-ui-react/Icon";
import { usePathname, useRouter } from "@/i18n/navigation";
import { useSearchParams } from "next/navigation";
import { useMemo } from "react";

export const TASK_FILTERS = [
  { label: "All", value: "ALL" },
  { label: "Registration", value: "REGISTRATION" },
  { label: "Volume Reports", value: "VOLUME_REPORTS" },
  { label: "Third Party Invoices", value: "THIRD_PARTY_INVOICES" },
  { label: "Terminations", value: "TERMINATIONS" },
] as const;

const DEFAULT_TASK = TASK_FILTERS[0];

interface FilterTasksProps {
  taskCounts: Record<"ALL" | "REGISTRATION" | "VOLUME_REPORTS" | "THIRD_PARTY_INVOICES" | "TERMINATIONS", number>;
}

export function FilterTasks({ taskCounts }: FilterTasksProps) {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const typeParamValue = searchParams.get("type");

  const selectedTaskType = useMemo(() => {
    return TASK_FILTERS.find((task) => task.value === typeParamValue) ?? DEFAULT_TASK;
  }, [typeParamValue]);

  function handleChangeTaskType(newTaskType: (typeof TASK_FILTERS)[number]) {
    const params = new URLSearchParams(searchParams.toString());

    if (newTaskType.value === "ALL") {
      params.delete("type");
    } else {
      params.set("type", newTaskType.value);
    }

    const newUrl = `${pathname}?${params.toString()}`;

    router.push(newUrl, { scroll: false });
  }

  return (
    <Dropdown
      trigger={
        <button className="flex items-center text-support-blue font-bold text-base">
          <div>
            <span className="ml-1 mr-2 mt-1 text-left">
              {selectedTaskType.label} ({taskCounts[selectedTaskType.value] || 0})
            </span>
          </div>
          <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
        </button>
      }
    >
      {TASK_FILTERS.map((taskFilter, idx) => (
        <DropdownItem
          key={idx}
          onClick={() => handleChangeTaskType(taskFilter)}
          className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
        >
          {taskFilter.label}
        </DropdownItem>
      ))}
    </Dropdown>
  );
}
