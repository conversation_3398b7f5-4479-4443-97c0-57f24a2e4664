import { cn } from "@/lib/utils";
import React from "react";

export type StatusType =
  | "Complete"
  | "Pending"
  | "Open task"
  | "No open to do’s"
  | "Open to do’s"
  | "Terminated"
  | "Open"
  | "Payed"
  | "Active"
  | "ACTIVE"
  | "Inactive"
  | "Needed"
  | "Information needed"
  | "Unprocessed Invoice"
  | "In progress"
  | "Termination case in progress"
  | "TERMINATION_PROCESS"
  | "TERMINATED"
  | "Termination in progress"
  | "Lizenzero registration"
  | "Waiting for dual system"
  | "Registered in dual system"
  | "Reported successfully"
  | "Error";

interface StatusProps {
  status: StatusType;
}

const Status: React.FC<StatusProps> = ({ status }) => {
  const statusColors = {
    Complete: "text-success bg-success",
    Payed: "text-success bg-success",
    Pending: "text-error bg-error",
    "Open task": "text-alert bg-alert",
    "Open to do’s": "text-alert bg-alert",
    Open: "text-alert bg-alert",
    "No open to do’s": "text-success bg-success",
    Terminated: "text-tonal-dark-cream-30 bg-tonal-dark-cream-30",
    TERMINATED: "text-tonal-dark-cream-30 bg-tonal-dark-cream-30",
    Active: "text-success bg-success",
    ACTIVE: "text-success bg-success",
    Inactive: "text-tonal-dark-cream-30 bg-tonal-dark-cream-30",
    Needed: "text-error bg-error",
    "Information needed": "text-error bg-error",
    "Unprocessed Invoice": "text-error bg-error",
    "In progress": "text-alert bg-alert",
    TERMINATION_PROCESS: "text-alert bg-alert",
    "Termination case in progress": "text-error bg-error",
    "Termination in progress": "text-alert bg-alert",
    "Lizenzero registration": "text-tonal-dark-cream-50 bg-tonal-dark-cream-30 text-paragraph-regular",
    "Waiting for dual system": "text-alert bg-alert text-paragraph-regular",
    "Registered in dual system": "text-on-surface-04 bg-on-surface-04 text-paragraph-regular",
    "Reported successfully": "text-success bg-success text-paragraph-regular",
    Error: "text-error bg-error text-paragraph-regular",
  };

  return (
    <div className="flex items-center space-x-2">
      <span className={cn("size-2 rounded-full", statusColors[status]?.split(" ")[1])} />
      <span className={cn("text-small-paragraph-regular font-medium", statusColors[status]?.split(" ")[0])}>
        {status}
      </span>
    </div>
  );
};

export default Status;
