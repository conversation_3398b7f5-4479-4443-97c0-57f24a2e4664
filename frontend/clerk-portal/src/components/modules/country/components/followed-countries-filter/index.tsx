"use client";

import {
  CountryFilterButton,
  CountryFilterButtonPlaceholder,
} from "@/components/modules/country/components/country-filter-button";
import { getCountriesOverview } from "@/lib/api/country";
import { useSession } from "next-auth/react";
import { useQuery } from "@tanstack/react-query";

export function FollowedCountriesFilter() {
  const { data: allCountries = [], isLoading } = useQuery({
    queryKey: ["countries-overview-followed"],
    queryFn: () => getCountriesOverview({}),
  });

  const session = useSession();
  const userId = session?.data?.user?.id;

  const followedCountries = allCountries?.filter(
    (country) => country.followers?.some((follower) => follower.user_id === Number(userId))
  );

  if (isLoading) {
    return <FollowedCountriesFilterPlaceholder />;
  }

  return (
    <ul className="flex items-center flex-wrap gap-3 sm:gap-5">
      <li>
        <CountryFilterButton isAll />
      </li>
      {followedCountries?.map((country) => (
        <li key={country.id}>
          <CountryFilterButton country={country} />
        </li>
      ))}
    </ul>
  );
}

export function FollowedCountriesFilterPlaceholder() {
  return (
    <ul className="flex items-center flex-wrap gap-5">
      {Array.from({ length: 8 }).map((_, idx) => (
        <li key={idx}>
          <CountryFilterButtonPlaceholder />
        </li>
      ))}
    </ul>
  );
}
