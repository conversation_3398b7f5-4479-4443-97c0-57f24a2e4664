"use client";

import { Tabs, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useQueryFilter } from "@/hooks/use-query-filter";

const FILTERS = [
  { label: "All", value: "ALL" },
  { label: "Representative Countries", value: "REPRESENTATIVE" },
];

const DEFAULT_FILTER = FILTERS[0];

export function CountriesFilterTabs() {
  const { paramValues, changeParam } = useQueryFilter(["filter"]);

  const filterValue = (paramValues.filter || DEFAULT_FILTER.value) as (typeof FILTERS)[number]["value"];

  function handleValueChange(value: string) {
    changeParam("filter", value);
  }

  return (
    <Tabs defaultValue={DEFAULT_FILTER.value} value={filterValue} onValueChange={handleValueChange}>
      <TabsList>
        <TabsTrigger value={FILTERS[0].value}>{FILTERS[0].label}</TabsTrigger>
        <TabsTrigger value={FILTERS[1].value}>{FILTERS[1].label}</TabsTrigger>
      </TabsList>
    </Tabs>
  );
}
