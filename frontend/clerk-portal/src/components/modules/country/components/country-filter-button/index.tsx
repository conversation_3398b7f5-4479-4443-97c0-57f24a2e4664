"use client";

import Image from "next/image";
import { usePathname, useRouter } from "@/i18n/navigation";
import { useSearchParams } from "next/navigation";

import { CountryIcon } from "@/components/_common/country-icon";
import { ICountry } from "@/lib/api/country/types";
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";
import { useQueryFilter } from "@/hooks/use-query-filter";

interface CountryFilterButtonProps {
  country?: ICountry;
  isAll?: boolean;
}

export function CountryFilterButton({ country, isAll }: CountryFilterButtonProps) {
  const { paramValues, changeParam, deleteParam } = useQueryFilter(["country"]);

  const isSelected = paramValues?.country === country?.name;
  const isSelectedAll = !paramValues.country;

  function handleSelect() {
    if (isAll) return deleteParam("country");

    changeParam("country", country ? country.name : "ALL");
  }

  return (
    <button
      type="button"
      aria-label={`Filter by ${country ? country.name : "All countries"}`}
      data-selected={isSelected || (isAll && isSelectedAll)}
      onClick={handleSelect}
      className={cn(
        "rounded-2xl inline-flex gap-2 items-center px-2 sm:px-5 py-1.5 sm:pt-2.5 sm:pb-3 bg-tonal-blue-96 text-primary data-[selected=true]:bg-primary data-[selected=true]:text-on-primary"
      )}
    >
      {country && <CountryIcon country={{ flag: country.flag_url, name: country.name }} />}
      {!country && <Image src="/assets/images/eu-flag.svg" alt="Europe Union Flag" width={24} height={24} />}
      <strong className="mt-1.5 text-xs sm:text-base">{country ? country.name : "All"}</strong>
    </button>
  );
}

export function CountryFilterButtonPlaceholder() {
  return <Skeleton className="h-12 w-full min-w-36 rounded-2xl" />;
}
