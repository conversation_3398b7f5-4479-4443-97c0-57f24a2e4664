"use client";

import { deleteCustomerFile, downloadCustomerFile, uploadFile } from "@/lib/api/file";
import { UploadedFile } from "@/lib/api/file/types";
import { updateTermination } from "@/lib/api/termination";
import { queryClient } from "@/lib/react-query";
import { downloadFile } from "@/utils/download-file";
import { formatDate } from "@/utils/format-date";
import { UserTypes } from "@/utils/user";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Delete, Download, Error, File, Upload } from "@arthursenno/lizenzero-ui-react/Icon";
import { useMutation } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { CgSpinnerAlt } from "react-icons/cg";
import { useCustomerLicense } from "./use-customer-license";

export function CustomerCountryTermination() {
  const t = useTranslations("CustomerProfileServicesContract");
  const session = useSession();

  const { license, refetch } = useCustomerLicense();
  const [submittingFile, setSubmittingFile] = useState<boolean>(false);

  const downloadFileMutation = useMutation({
    mutationFn: async (file: UploadedFile) => {
      const downloadedFile = await downloadCustomerFile({
        user_id: 0,
        user_role: UserTypes.CLERK,
        file_id: file.id,
      });

      downloadFile({ buffer: downloadedFile, fileName: file.original_name });
    },
  });

  const deleteFileMutation = useMutation({
    mutationFn: async (file: UploadedFile) => {
      const terminationId = license?.termination?.id;

      if (!terminationId) return;

      await deleteCustomerFile(file.id);

      await updateTermination(terminationId, { status: "PENDING" });
    },
  });

  const uploadFileMutation = useMutation({
    mutationFn: async (file: File) => {
      const terminationId = license?.termination?.id;

      if (!terminationId) return;

      const uploadedFile = await uploadFile({
        file,
        type: "PROOF_OF_TERMINATION",
        user_id: String(session.data?.user?.id),
        termination_id: terminationId,
      });

      await updateTermination(terminationId, { status: "COMPLETED" });

      return uploadedFile;
    },
  });

  async function handleDownloadFile(file: UploadedFile) {
    downloadFileMutation.mutate(file, {
      onSuccess: () => {
        enqueueSnackbar(t("fileDownloaded"), { variant: "success" });
      },
      onError: () => {
        enqueueSnackbar(t("fileDownloadFailed"), { variant: "error" });
      },
    });
  }

  async function handleDeleteFile(file: UploadedFile) {
    deleteFileMutation.mutate(file, {
      onSuccess: () => {
        enqueueSnackbar(t("fileDeleted"), { variant: "success" });
        queryClient.invalidateQueries({ queryKey: ["licenses", license?.id] });
        refetch();
      },
      onError: () => {
        enqueueSnackbar(t("fileDeleteFailed"), { variant: "error" });
      },
    });
  }

  async function handleUploadTerminationProofFile(file: File) {
    if (!session.data?.user) return;
    if (!license) return;

    uploadFileMutation.mutate(file, {
      onSuccess: () => {
        enqueueSnackbar(t("fileUploaded"), { variant: "success" });
        queryClient.invalidateQueries({ queryKey: ["licenses", license?.id] });
      },
      onError: () => {
        enqueueSnackbar(t("fileUploadFailed"), { variant: "error" });
      },
    });
  }

  const termination = license?.termination;

  if (!termination) return null;

  const proofOfTerminationFiles = (termination.files || []).filter((file) => file.type === "PROOF_OF_TERMINATION");

  return (
    <>
      {!!termination && termination.status === "PENDING" && (
        <div className="w-full h-[76px] px-4 bg-[#FCEAE8] rounded-xl mb-6">
          <div className="flex items-center justify-between h-full">
            <div className="flex items-center justify-center gap-2 ">
              <Error className="size-6 fill-error " />
              <div>
                <p className="text-paragraph-regular font-bold text-error">{t("terminationCasePending")}</p>
                <p className="text-small-paragraph-regular text-error">{t("terminationPendingOnDualSystem")}</p>
              </div>
            </div>
            <input
              type="file"
              id="file-input"
              style={{ display: "none" }}
              disabled={uploadFileMutation.isPending}
              onChange={(e) => e.target.files && handleUploadTerminationProofFile(e.target.files?.[0])}
            />

            <Button
              size="small"
              variant="filled"
              color="red"
              onClick={() => document.getElementById("file-input")?.click()}
              disabled={uploadFileMutation.isPending}
              trailingIcon={
                uploadFileMutation.isPending ? <CgSpinnerAlt className="size-4 animate-spin" /> : <Upload />
              }
            >
              {t("uploadTerminationProof")}
            </Button>
          </div>
        </div>
      )}
      {!!termination && termination.status === "COMPLETED" && (
        <div className="w-full p-4 px-4 bg-[#EDE9E4] rounded-xl mb-4">
          <div className="flex items-center justify-between h-full w-full">
            <div className="flex flex-col gap-2 w-full">
              <div className="flex items-center gap-2 ">
                <Error className="size-6 fill-error " />
                <div>
                  <p className="text-paragraph-regular font-bold text-error">{t("terminationCaseComplete")}</p>
                  <p className="text-small-paragraph-regular text-primary">
                    {t("contractTerminationWillBeIn")}{" "}
                    <span className="font-bold">{formatDate(license.contract.end_date)}</span>
                  </p>
                </div>
              </div>
              {proofOfTerminationFiles
                .filter((file) => file.type === "PROOF_OF_TERMINATION")
                .map((file) => (
                  <div className="flex justify-between items-center w-full mt-2" key={file.id}>
                    <div className="flex items-center gap-3">
                      <File className="size-6 fill-tonal-dark-cream-20 mb-1" />
                      <p className="text-tonal-dark-cream-20 text-small-paragraph-regular underline cursor-pointer">
                        {file.original_name}
                      </p>
                    </div>
                    <div className="flex items-center gap-12">
                      <p className="text-tonal-dark-cream-20 text-small-paragraph-regular">
                        {formatDate(file.created_at)}
                      </p>
                      <div className="flex gap-2">
                        <Button
                          variant="text"
                          color="light-blue"
                          size="iconSmall"
                          leadingIcon={
                            downloadFileMutation.isPending && downloadFileMutation.variables?.id === file.id ? (
                              <CgSpinnerAlt className="size-4 animate-spin" />
                            ) : (
                              <Download />
                            )
                          }
                          onClick={() => handleDownloadFile(file)}
                        />
                        <Button
                          variant="text"
                          color="dark-blue"
                          size="iconSmall"
                          leadingIcon={
                            deleteFileMutation.isPending && deleteFileMutation.variables?.id === file.id ? (
                              <CgSpinnerAlt className="size-4 animate-spin" />
                            ) : (
                              <Delete className="fill-primary" />
                            )
                          }
                          onClick={() => handleDeleteFile(file)}
                        />
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </div>
      )}
    </>
  );
}
