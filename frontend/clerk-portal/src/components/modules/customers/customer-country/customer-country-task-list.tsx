import Divider from "@/components/_common/divider";
import { MondayTasks } from "@/lib/api/country/types";
import { CheckCircle, CheckCircleOutline, Launch } from "@arthursenno/lizenzero-ui-react/Icon";
import { ProgressBar } from "@arthursenno/lizenzero-ui-react/ProgressBar";
import Image from "next/image";
import React from "react";
import Status from "../../country/components/task-status";
import StatusBadge from "../../country/components/task-type";
import { FilterTasks } from "../../country/components/task-type-filter";
import { useTranslations } from "next-intl";

interface TaskListProps {
  taskCounts: Record<string, number>;
  filteredTasks: MondayTasks[] | null;
  loading: boolean;
  completedTasks?: number;
  typeParam: string | null;
}

const TaskListCustomerCountry = ({ taskCounts, filteredTasks, loading, completedTasks, typeParam }: TaskListProps) => {
  const t = useTranslations("Monday");
  const c = useTranslations("common");

  return (
    <div className="w-auto md:w-[704px] bg-[#F7F5F2] rounded-[20px]">
      {loading ? (
        <ProgressBar />
      ) : (
        <div className="p-6 flex flex-col gap-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-[40px] h-[40px] border-[1px] border-solid border-[#ECECEC] rounded-xl flex justify-center items-center">
                <Image src={`/assets/images/LogoMonday.svg`} alt={"Logo Monday"} width={40} height={40} />
              </div>
              <p className="text-tonal-dark-blue-10 text-title-3 font-bold">{t("name")}</p>
            </div>
            <Launch className="size-6 fill-[#009DD3]" />
          </div>
          <div className="flex flex-col gap-3">
            <div className="flex items-center justify-between">
              <FilterTasks taskCounts={taskCounts} />
              <div className="flex items-center gap-1">
                <p className="text-small-paragraph-regular text-tonal-dark-cream-20">{t("tasksDone")}:</p>
                <p className="text-small-paragraph-regular text-tonal-dark-cream-20">
                  <span className="text-small-paragraph-regular text-tonal-dark-cream-20 font-bold">
                    {completedTasks}
                  </span>{" "}
                  / {typeParam === "ALL" && `${taskCounts.ALL}`}
                  {typeParam === "REGISTRATION" && `${taskCounts.REGISTRATION}`}
                  {typeParam === "VOLUME_REPORTS" && `${taskCounts.VOLUME_REPORTS}`}
                  {typeParam === "THIRD_PARTY_INVOICES" && `${taskCounts.THIRD_PARTY_INVOICES}`}
                  {typeParam === "TERMINATIONS" && `${taskCounts.TERMINATIONS}`}
                  {!typeParam && `${taskCounts.ALL}`}
                </p>
              </div>
            </div>
            <div className="overflow-auto h-full bg-white rounded-xl">
              {filteredTasks && filteredTasks.length > 0 ? (
                filteredTasks.map((task) => (
                  <React.Fragment key={task.id}>
                    <div className="px-2 py-4 flex items-center justify-between">
                      <div className="flex flex-col gap-1">
                        <p className="text-support-blue text-small-paragraph-regular underline">
                          Customer {task.customerId}
                        </p>
                        <p className="font-bold text-paragraph-regular text-tonal-dark-cream-20">{task.documentName}</p>
                        <p className="text-xs text-tonal-dark-cream-50">{task.taskProgress}</p>
                      </div>
                      <div className="flex items-center justify-between  gap-6">
                        <div className="flex flex-col justify-start items-start gap-2">
                          <Status status={task.status} />
                          <StatusBadge status={task.type} isSmall />
                        </div>
                        <div className="flex justify-end">
                          {task.status === "Complete" ? (
                            <CheckCircle className="size-8 fill-success" />
                          ) : (
                            <CheckCircleOutline className="size-8 fill-tonal-dark-cream-40" />
                          )}
                        </div>
                      </div>
                    </div>
                    <Divider initialMarginDisabled />
                  </React.Fragment>
                ))
              ) : (
                <p className="text-small-paragraph-regular text-tonal-dark-cream-50 text-center mt-6">
                  {t("noTasksFound")}
                </p>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskListCustomerCountry;
