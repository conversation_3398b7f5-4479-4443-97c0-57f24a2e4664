import Status from "@/components/modules/country/components/task-status";
import { useCustomerLicense } from "./use-customer-license";
import { cn } from "@/lib/utils";
import { useCustomer } from "../customer-profile/use-customer";
import { Skeleton } from "@/components/ui/skeleton";
import { useTranslations } from "next-intl";

export function CustomerCountrySaasCard() {
  const t = useTranslations("SaasStorefront");
  const { customer, isLoading: isCustomerLoading } = useCustomer();
  const { license, isLoading: isLicenseLoading } = useCustomerLicense();

  if (isCustomerLoading || isLicenseLoading || !customer || !license) return <SaasCardSkeleton />;

  const contractTermination = customer.contracts.find((contract) => contract.type === "EU_LICENSE")?.termination;
  const licenseTermination = license.termination;

  return (
    <div className="p-6 bg-tonal-cream-96 rounded-3xl w-full">
      <div className="flex flex-row items-center justify-between">
        <p className="text-title-3 font-bold text-[#183362]">{t("saasStatus")}</p>
      </div>
      <div className="mt-6 w-full flex flex-col gap-3">
        <div className="bg-tonal-dark-cream-90 w-full h-auto rounded-xl overflow-hidden flex flex-col gap-[1px]">
          <div className="flex justify-between items-center p-4 gap-2 bg-white">
            <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("registration")}</p>
            <Status status="Open" />
          </div>
          <div className="flex justify-between items-center p-4 gap-2 bg-white">
            <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("volumeReports")}</p>
            {license.pendencies.find((pendency) => pendency.type === "VOLUME_REPORTS") ? (
              <div>
                <Status status="In progress" />
              </div>
            ) : (
              <Status status="Complete" />
            )}
          </div>
          <div className="flex justify-between items-center p-4 gap-2 bg-white">
            <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("thirdPartyInvoices")}</p>
            {license.third_party_invoices.length ? (
              license.pendencies.find((pendency) => pendency.type === "INVOICES") ? (
                <div>
                  <Status status="Unprocessed Invoice" />
                </div>
              ) : (
                <Status status="Complete" />
              )
            ) : (
              <div className="flex items-center space-x-2">
                <span className={cn("size-2 rounded-full", "bg-tonal-dark-cream-30")} />
                <span className={cn("text-small-paragraph-regular font-medium", "text-tonal-dark-cream-30")}>
                  {t("noThirdPartyInvoice")}
                </span>
              </div>
            )}
          </div>
          <div className="flex justify-between items-center p-4 gap-2 bg-white">
            <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("termination")}</p>
            {contractTermination || licenseTermination ? (
              <div>
                <Status status="Termination in progress" />
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <span className={cn("size-2 rounded-full", "bg-tonal-dark-cream-30")} />
                <span className={cn("text-small-paragraph-regular font-medium", "text-tonal-dark-cream-30")}>
                  No termination
                </span>
              </div>
            )}
          </div>
          <div className="flex justify-between items-center p-4 gap-2 bg-white">
            <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("documents&Information")}</p>
            {license.pendencies.find((pendency) => pendency.type === "REQUIRED_INFORMATIONS") ? (
              <div>
                <Status status="Information needed" />
              </div>
            ) : (
              <Status status="Complete" />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

function SaasCardSkeleton() {
  const t = useTranslations("SaasStorefront");
  return (
    <div className="bg-tonal-dark-cream-90 w-full h-auto rounded-xl overflow-hidden flex flex-col gap-[1px]">
      <div className="flex justify-between items-center p-4 gap-2 bg-white">
        <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("registration")}</p>
        <Skeleton className="h-6 w-20" />
      </div>
      <div className="flex justify-between items-center p-4 gap-2 bg-white">
        <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("volumeReports")}</p>
        <Skeleton className="h-6 w-20" />
      </div>
      <div className="flex justify-between items-center p-4 gap-2 bg-white">
        <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("thirdPartyInvoices")}</p>
        <Skeleton className="h-6 w-20" />
      </div>
      <div className="flex justify-between items-center p-4 gap-2 bg-white">
        <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("termination")}</p>
        <Skeleton className="h-6 w-20" />
      </div>
      <div className="flex justify-between items-center p-4 gap-2 bg-white">
        <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("documents&Information")}</p>
        <Skeleton className="h-6 w-20" />
      </div>
    </div>
  );
}
