"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { createLicenseVolumeReportError } from "@/lib/api/license-volume-report-error";
import { getReportDeclineReasons } from "@/lib/api/report-decline-reason";
import { VolumeReportItem } from "@/lib/api/volume-report-item/types";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Clear } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Loader2Icon } from "lucide-react";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useTranslations } from "next-intl";

type Props = {
  selectedItem?: VolumeReportItem;
  setSelectedItem: (item?: VolumeReportItem) => void;
};

export function DeclineQuantityReportModal({ selectedItem, setSelectedItem }: Props) {
  const t = useTranslations("DeclineQuantityReportModal");

  const formSchema = z.object({
    reasons: z
      .array(z.number(), {
        message: t("fillAllFields"),
      })
      .min(1, t("fillAllFields")),
    comments: z.string().min(1, t("writeAComment")),
  });

  type DeclineQuantityReportFormData = z.infer<typeof formSchema>;

  const queryClient = useQueryClient();
  const isModalOpen = !!selectedItem?.id;

  const { data: reasons, isLoading } = useQuery({
    queryKey: ["report-decline-reasons"],
    queryFn: getReportDeclineReasons,
    enabled: isModalOpen,
  });

  const handleOnOpenChange = (open: boolean) => {
    if (open) return;
    setSelectedItem(undefined);
  };

  const form = useForm<DeclineQuantityReportFormData>({
    resolver: zodResolver(formSchema),
  });
  const selectedReasons = form.watch("reasons");

  const { mutateAsync } = useMutation({
    mutationFn: async (data: DeclineQuantityReportFormData) => {
      await createLicenseVolumeReportError({
        license_volume_report_item_id: selectedItem?.id,
        decline_reason_ids: data.reasons,
        description: data.comments,
      });
      queryClient.invalidateQueries({
        queryKey: ["packaging-service"],
      });
      setSelectedItem(undefined);
    },
  });

  const onSubmit = async (data: DeclineQuantityReportFormData) => mutateAsync(data);

  useEffect(() => {
    const errorSelect = selectedItem?.errors?.at(-1);

    if (errorSelect)
      return form.reset({
        comments: errorSelect.description,
        reasons: errorSelect.decline_reasons.map((item) => item.report_decline_reason_id),
      });

    form.reset({
      comments: ``,
      reasons: [],
    });
  }, [selectedItem, form]);

  return (
    <Modal
      open={isModalOpen}
      onOpenChange={handleOnOpenChange}
      className="z-50 !rounded-[52px] w-full max-w-[672px] !py-9 !px-9 min-h-144"
    >
      <div className="flex items-start justify-between">
        <h1 className="font-large-paragraph-bold text-2xl text-tonal-dark-blue-10 font-bold my-5 sm:text-3xl">
          {t("declineQuantityReportButton")}
        </h1>
        <Button color="dark-blue" size="iconXSmall" variant="text" onClick={() => handleOnOpenChange(false)}>
          <Clear className="size-6" />
        </Button>
      </div>
      {isLoading ? (
        <Skeleton className="w-full h-96" />
      ) : (
        <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col gap-6">
          <p className="text-paragraph-regular text-tonal-dark-cream-10">{t("selectReasons")}</p>
          {form.formState.errors.reasons && (
            <p className="text-paragraph-regular text-tonal-red-50" aria-invalid="true">
              {form.formState.errors.reasons.message}
            </p>
          )}
          {reasons?.map((reason) => (
            <div key={reason.id} className="flex items-center gap-2">
              <input
                type="checkbox"
                id={`reason-${reason.id}`}
                className="-mt-1"
                checked={selectedReasons.some((item) => item === reason.id)}
                onChange={(e) => {
                  form.setValue(
                    "reasons",
                    e.target.checked
                      ? selectedReasons
                        ? [...selectedReasons, reason.id]
                        : [reason.id]
                      : selectedReasons?.filter((id) => id !== reason.id)
                  );
                }}
              />
              <label htmlFor={`reason-${reason.id}`} className="text-paragraph-regular text-tonal-dark-cream-10">
                {reason.title}
              </label>
            </div>
          ))}
          <div className="flex flex-col">
            {form.formState.errors.comments && (
              <p className="text-paragraph-regular text-tonal-red-50 mb-2" aria-invalid="true">
                {form.formState.errors.comments.message}
              </p>
            )}
            <label htmlFor="comments" className="text-paragraph-regular text-primary mb-2">
              {t("addComments")} *
              {/* <span className="text-tonal-dark-green-40">
                <i>(optional)</i>
              </span> */}
            </label>
            <textarea
              className="block w-full border rounded-2xl p-4 text-tonal-dark-cream-10 focus:outline-primary border-tonal-dark-cream-80 min-h-40 resize-none"
              placeholder={t("writeComment")}
              id="comments"
              {...form.register("comments")}
            />
          </div>

          <div className="flex self-end ml-auto">
            <Button
              color="red"
              variant="filled"
              size="medium"
              disabled={!form.formState.errors || form.formState.isSubmitting}
              leadingIcon={form.formState.isSubmitting ? <Loader2Icon className="animate-spin" /> : undefined}
            >
              {t("declineReport")}
            </Button>
          </div>
        </form>
      )}
    </Modal>
  );
}
