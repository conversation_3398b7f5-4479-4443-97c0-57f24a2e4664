import Divider from "@/components/_common/divider";
import { useState } from "react";
import StatusFilter, { StatusFilterType } from "../../components/filters/status-filter";
import { useCustomerLicense } from "../use-customer-license";
import { QuantityReportItem } from "./eu/quantity-report-item";
import { QuantityReportItem as QuantityReportItemDirectLicensing } from "./direct-licensing/quantity-report-item";
import { Download, KeyboardArrowDown } from "@arthursenno/lizenzero-ui-react/Icon";
import { PDFDownloadLink } from "@react-pdf/renderer";
import {
  VolumeReportPDF as DirectLicensingVolumeReportPDF,
  VolumeReportEntry,
} from "./direct-licensing/volume-report-pdf";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { Dropdown } from "@/components/_common/dropdown";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { useCustomer } from "../../customer-profile/use-customer";
import { useQuery } from "@tanstack/react-query";
import { getLicenses } from "@/lib/api/license";
import { getPackagingServices } from "@/lib/api/packaging-services";
import { DirectLicenseReportingTable } from "./direct-licensing/direct-license-reporting-table";
import { useTranslations } from "next-intl";

const currentYear = new Date().getFullYear();

export function CustomerCountryQuantityReport() {
  const t = useTranslations("QuantityReportItem");
  const c = useTranslations("common");
  const { customer } = useCustomer();
  const { license } = useCustomerLicense();

  const isDirectLicense = license?.contract.type === "DIRECT_LICENSE";

  const licensesQuery = useQuery({
    queryKey: ["licenses", license?.contract_id],
    queryFn: () => getLicenses({ contract_id: license?.contract.id }),
    enabled: !!license?.contract_id,
  });

  const licenses = licensesQuery.data;

  const availableYears = [2025];
  const [licenseYear, setLicenseYear] = useState(licenses?.[0]?.year || currentYear);

  const currentLicense = licenses?.find((license) => license.year === licenseYear);

  const packagingServicesQuery = useQuery({
    queryKey: ["packaging-services", license?.id],
    queryFn: () => {
      return getPackagingServices({ license_id: Number(license?.id) });
    },
    enabled: !!license?.id,
  });

  const [year, setYear] = useState(new Date().getFullYear().toString());
  const [volumeReportEntries, setVolumeReportEntries] = useState<VolumeReportEntry[]>([]);

  const isDirectLicensing = license?.country_code === "DE";

  return (
    <div className="p-6 bg-tonal-cream-96 rounded-3xl w-full">
      <div className="flex flex-row items-center justify-between">
        {isDirectLicensing && volumeReportEntries?.length > 0 ? (
          <div className="flex items-center gap-2">
            <p className="text-title-3 font-bold text-[#183362]">{t("quantityReport")}</p>
            <PDFDownloadLink
              className="outline-offset-2 flex font-centra items-center justify-center relative transition-all rounded-[100px] font-bold disabled:pointer-events-none disabled:text-tonal-dark-cream-50 disabled:fill-tonal-dark-cream-50 hover:bg-opacity-[.18] active:bg-opacity-[.36] text-tonal-blue-40 fill-tonal-blue-40 gap-4 text-xl hover:bg-tonal-blue-40 px-4 py-3 leading-6"
              document={<DirectLicensingVolumeReportPDF entries={volumeReportEntries} />}
            >
              <Download className="size-6 -mr-2" />
              <p className="font-paragraph-bold font-bold">{c("pdf")}</p>
            </PDFDownloadLink>
          </div>
        ) : (
          <p className="text-title-3 font-bold text-[#183362]">{t("quantityReport")}</p>
        )}
        <div className="flex flex-col md:flex-row items-end md:items-center md:gap-2">
          {!isDirectLicensing && <p className="text-tonal-dark-cream-40 font-medium text-lg">{t("licenseYear")}</p>}
          <Dropdown
            trigger={
              <button className="flex items-center text-support-blue font-bold gap-2">
                <span>{licenseYear}</span>
                <KeyboardArrowDown width={20} height={20} className="fill-error" />
              </button>
            }
          >
            {availableYears.map((year, idx) => (
              <DropdownMenu.Item
                key={idx}
                className="group py-5 px-4 text-base focus:outline-none cursor-pointer hover:bg-surface-02"
                onClick={() => setLicenseYear(year)}
                style={{
                  color: year === licenseYear ? "#002652" : "#242423",
                  fontWeight: year === licenseYear ? "bold" : "normal",
                }}
              >
                {year}
              </DropdownMenu.Item>
            ))}
          </Dropdown>
        </div>
      </div>
      <div className="mt-6 w-full flex flex-col gap-3">
        {packagingServicesQuery.data?.map((packagingService) =>
          isDirectLicensing ? (
            <QuantityReportItemDirectLicensing
              key={packagingService.id}
              packagingServiceId={packagingService.id}
              year={year}
              setYear={setYear}
              setVolumeReportPDF={setVolumeReportEntries}
              licenses={licenses || []}
            />
          ) : (
            <>
              <Divider initialMarginDisabled />
              <QuantityReportItem packagingService={packagingService} reloadData={packagingServicesQuery.refetch} />
            </>
          )
        )}
      </div>
    </div>
  );
}
