import { Loading } from "@/components/_common/loading";
import { ReportTable } from "@/components/_common/report-table";
import { FractionInput } from "@/components/ui/fraction-input";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { api } from "@/lib/api";
import { PackagingService } from "@/lib/api/packaging-services/types";
import { ReportSetColumn, ReportSetFraction } from "@/lib/api/report-set/types";
import { createVolumeReportItem, createVolumeReportItems, updateVolumeReportItem } from "@/lib/api/volume-report-item";
import { VolumeReportItem } from "@/lib/api/volume-report-item/types";
import { queryClient } from "@/lib/react-query";
import { cn } from "@/lib/utils";
import { formatWeight } from "@/utils/format-weight";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { CheckCircle, Download, Edit, Error } from "@arthursenno/lizenzero-ui-react/Icon";
import { PDFDownloadLink } from "@react-pdf/renderer";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Loader2Icon } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { CgSpinnerAlt } from "react-icons/cg";
import { read, writeFile } from "xlsx";
import StatusFilter from "../../../components/filters/status-filter";
import { useCustomerLicense } from "../../use-customer-license";
import { convertReportTableToCSV } from "./convert-report-table-to-csv";
import { VolumeReportPDF } from "./volume-report-pdf";

interface QuantityReportItemProps {
  packagingService: PackagingService;
  reloadData?: () => void;
}

type LocalVolumeReportItem = { columnId: number; fractionId: number; value?: number; virtualId: string };

const isVirtualItem = (item: VolumeReportItem | LocalVolumeReportItem | undefined): item is LocalVolumeReportItem =>
  (item as LocalVolumeReportItem)?.virtualId !== undefined;

export function QuantityReportItem({ packagingService, reloadData }: QuantityReportItemProps) {
  const t = useTranslations("QuantityReportItem");
  const c = useTranslations("common");
  const { license } = useCustomerLicense();
  const [selectedItem, setSelectedItem] = useState<{ fractionId: number; columnId: number } | null>();
  const [loadingNewData, setLoadingNewData] = useState(false);
  const [mode, setMode] = useState<"decline" | "edit">();
  const [values, setValues] = useState<{ fractionId: number; columnId: number; value?: number }[]>([]);

  const setupReportSetId = packagingService.report_set.setup_report_set_id;

  const setupReportSetQuery = useQuery({
    queryKey: ["setup-report-set", setupReportSetId],
    queryFn: async () => {
      try {
        const countryCode = license?.country_code;

        const response = await api.get(`/admin/service-setups/${countryCode}/report-sets/${setupReportSetId}`);
        if (!response.data || response.status !== 200) throw "Failed to fetch setup report set";

        return response.data as {
          columns: ReportSetColumn[];
          fractions: ReportSetFraction[];
        };
      } catch (error) {
        console.error(error);
        throw error;
      }
    },
  });

  const updateItemMutation = useMutation({
    mutationFn: async (data: { columnId: number; fractionId: number; value: number }) => {
      if (!volumeReport) return;
      const volumeReportItem = volumeReport.volume_report_items.find(
        (item) => item.setup_fraction_id === data.fractionId && item.setup_column_id === data.columnId
      );
      if (!volumeReportItem) {
        await createVolumeReportItem({
          license_volume_report_id: volumeReport.id,
          setup_column_id: data.columnId,
          setup_fraction_id: data.fractionId,
          value: data.value,
        } as any);
      } else {
        await updateVolumeReportItem({
          ...volumeReportItem,
          value: data.value,
          errors: undefined,
        });
      }
      queryClient.invalidateQueries({ queryKey: ["packaging-service", packagingService.id] });
      queryClient.invalidateQueries({ queryKey: ["packaging-services", packagingService.license_id] });
    },
  });

  const [selectedInterval, setSelectedInterval] = useState(packagingService?.volume_reports[0].interval);

  const volumeReport = packagingService?.volume_reports.find(
    (report) => report.interval === (selectedInterval || packagingService?.volume_reports[0].interval)
  );

  const downloadExcel = () => {
    if (!volumeReport) return;
    const data = convertReportTableToCSV(volumeReport);
    const workbook = read(data, { type: "string" });
    writeFile(workbook, `quantity-report-sales-packaging-${volumeReport.year}-${volumeReport.interval}.xlsx`);
  };

  const handleDecline = ({ fractionId, columnId }: { fractionId: number; columnId: number }) => {
    setMode("decline");
    setSelectedItem({
      fractionId,
      columnId,
    });
  };

  const handleEdit = (data: { fractionId: number; columnId: number }) => {
    setMode("edit");
    setSelectedItem(data);
  };

  const handleClose = (data: { fractionId: number; columnId: number; value?: number }) => {
    try {
      setMode(undefined);
      setSelectedItem(undefined);

      if (!data.value) return;

      const originalItem = volumeReport?.volume_report_items.find(
        (item) => item.setup_column_id === data.columnId && item.setup_fraction_id === data.fractionId
      );
      if (originalItem?.value === data.value) return;

      updateItemMutation.mutate({
        columnId: data.columnId,
        fractionId: data.fractionId,
        value: data.value || 0,
      });
    } finally {
      // packagingServiceQuery.refetch();
    }
  };

  const handleChangeValue = (params: VolumeReportItem | LocalVolumeReportItem) => {
    const alreadyExists = values.find(
      (value) => value.fractionId === (isVirtualItem(params) ? params.virtualId : params.id)
    );
    const newValues = alreadyExists
      ? values.map((value) =>
          value.fractionId === (isVirtualItem(params) ? params.virtualId : params.id)
            ? { fractionId: value.fractionId, columnId: value.columnId, value: params.value }
            : value
        )
      : [...values, { fractionId: isVirtualItem(params) ? params.virtualId : params.id, value: params.value }];
    setValues(newValues as { fractionId: number; columnId: number; value?: number }[]);
  };

  return (
    <div className="flex flex-col gap-5">
      <h2 className="font-bold text-primary text-xl">{packagingService.name}</h2>
      {setupReportSetQuery.isLoading && <Loading />}
      {!setupReportSetQuery.isLoading && (
        <>
          <div className="flex flex-col items-start justify-between gap-4">
            <div className="flex flex-col md:flex-row items-start md:items-center gap-4 w-full md:w-auto">
              <label htmlFor="frequency" className="text-tonal-dark-cream-40 font-medium text-nowrap">
                {t("reportFrequency")}:
              </label>
              <select
                id="frequency"
                className="appearance-none leading-tight focus:outline-noneblock border rounded-2xl p-4 text-tonal-dark-cream-10 focus:outline-primary invalid:text-tonal-dark-cream-50 w-full md:min-w-64"
                defaultValue={packagingService?.report_set_frequency?.rhythm}
              >
                <option value="MONTHLY">{t("monthly")}</option>
                <option value="QUARTERLY">{t("quarterly")}</option>
                <option value="ANNUALLY">{t("annually")}</option>
              </select>
            </div>
            <div className="flex w-full justify-end">
              <StatusFilter
                filters={
                  packagingService?.volume_reports.map((report) => ({
                    label: report.interval,
                    value: report.interval,
                  })) || []
                }
              />
            </div>
          </div>
          {!volumeReport || !setupReportSetQuery.data ? (
            <p className="text-primary text-center py-4">{t("dataNotFound")}</p>
          ) : (
            <>
              <ReportTable
                fractions={setupReportSetQuery.data.fractions}
                columns={setupReportSetQuery.data.columns}
                field={(field, fraction) => {
                  const item = volumeReport.volume_report_items.find(
                    (item) =>
                      item.setup_fraction_id === fraction.fractionId && item.setup_column_id === fraction.columnId
                  );

                  const isActive = !!setupReportSetQuery.data.columns.find((column) => {
                    return (column.children || []).find((c) =>
                      c.fractions.find(
                        (f) => f.column_code === fraction.columnCode && f.fraction_code === fraction.fractionCode
                      )
                    );
                  });

                  return (
                    <FractionCell
                      columnCode={fraction.columnCode}
                      fractionCode={fraction.fractionCode}
                      value={item?.value}
                      isNew={!item}
                      isSelected={
                        fraction.columnId === selectedItem?.columnId && fraction.fractionId === selectedItem?.fractionId
                      }
                      onSelect={() =>
                        !loadingNewData &&
                        setSelectedItem({ fractionId: fraction.fractionId, columnId: fraction.columnId })
                      }
                      isLoading={loadingNewData}
                      onSave={async (newValue) => {
                        const newData = [
                          {
                            license_volume_report_id:
                              item?.license_volume_report_id ||
                              volumeReport.volume_report_items?.[0].license_volume_report_id,
                            setup_column_id: fraction.columnId,
                            setup_fraction_id: fraction.fractionId,
                            setup_fraction_code: fraction.fractionCode,
                            setup_column_code: fraction.columnCode,
                            value: newValue,
                          },
                        ];
                        setLoadingNewData(true);
                        await createVolumeReportItems(newData);

                        reloadData?.();

                        setSelectedItem(null);
                        setLoadingNewData(false);
                      }}
                      onDecline={() => {}}
                      onCancel={() => setSelectedItem(null)}
                      disabled={updateItemMutation.isPending}
                    />
                  );
                }}
              />
              <div className="flex items-center gap-2">
                <PDFDownloadLink
                  document={
                    <VolumeReportPDF
                      volumeReport={volumeReport}
                      packagingServiceName={packagingService.name}
                      packagingService={packagingService}
                    />
                  }
                  fileName={`quantity-report-sales-packaging-${volumeReport.year}-${volumeReport.interval}.pdf`}
                  className="outline-offset-2 flex font-centra items-center justify-center relative transition-all rounded-[100px] font-bold disabled:pointer-events-none disabled:text-tonal-dark-cream-50 disabled:fill-tonal-dark-cream-50 hover:bg-opacity-[.18] active:bg-opacity-[.36] text-tonal-blue-40 fill-tonal-blue-40 gap-4 text-xl hover:bg-tonal-blue-40 px-4 py-3 leading-6"
                >
                  <Download className="size-6" />
                  <p className="font-paragraph-bold font-bold">{c("pdf")}</p>
                </PDFDownloadLink>
                <Button
                  color="gray"
                  size="large"
                  variant="text"
                  leadingIcon={<Download />}
                  className="text-[#66A73F] fill-[#66A73F]"
                  onClick={downloadExcel}
                >
                  Excel
                </Button>
              </div>
            </>
          )}

          {/* <DeclineQuantityReportModal
            selectedItem={mode === "decline" && !isVirtualItem(selectedItem) ? selectedItem : undefined}
            setSelectedItem={setSelectedItem}
          /> */}
        </>
      )}
    </div>
  );
}

type FractionCellProps = {
  fractionCode: string;
  columnCode: string;
  value?: number;
  isNew: boolean;
  isSelected: boolean;
  onSelect: () => void;
  onSave: (value: number) => void;
  onDecline: () => void;
  disabled: boolean;
  onCancel: () => void;
  isLoading: boolean;
};

function FractionCell({
  fractionCode,
  columnCode,
  value,
  isSelected,
  isNew,
  onSelect,
  onSave,
  onDecline,
  disabled,
  onCancel,
  isLoading,
}: FractionCellProps) {
  const [isInvalid, setIsInvalid] = useState(!value);
  const [newValue, setNewValue] = useState(value);
  const t = useTranslations("QuantityReportItem");
  const c = useTranslations("common");
  if (disabled) {
    return (
      <div className="flex-1 flex flex-col justify-center relative ml-2">
        <span className="text-tonal-dark-cream-40 py-4">{value ? formatWeight(value) : "-- kg"}</span>
      </div>
    );
  }

  if (isSelected) {
    return (
      <>
        <div className="flex-1 flex flex-col justify-center relative ml-2">
          <FractionInput
            type="weight"
            className={cn("text-left max-w-48", isInvalid && "border-tonal-red-50 outline-tonal-red-50")}
            value={newValue || value}
            onChange={(value) => {
              if (isInvalid) setIsInvalid(false);

              setNewValue(Number(value));
            }}
            placeholder={t("enterValue")}
            disabled={disabled}
            aria-invalid={!value}
          />
        </div>
        <Popover open defaultOpen key={`save-btn`}>
          <PopoverTrigger></PopoverTrigger>
          <PopoverContent
            className="shadow shadow-elevation-04-1 border-none rounded-2xl w-auto h-auto flex p-0 ml-40"
            side="bottom"
          >
            <div className="flex gap-2 rounded-2xl py-2 px-2 bg-white z-10">
              <Button
                color="yellow"
                size="iconSmall"
                variant="filled"
                leadingIcon={disabled ? <Loader2Icon className="animate-spin" /> : <CheckCircle />}
                onClick={() => {
                  if (!newValue || newValue === value) {
                    setIsInvalid(true);
                    return;
                  }

                  onSave(newValue);
                }}
                className="gap-2"
                disabled={disabled || isLoading}
              >
                {isLoading ? <CgSpinnerAlt size={20} className="animate-spin text-primary" /> : c("save")}
              </Button>
              <Button
                variant="link"
                size="small"
                color={"gray" as unknown as undefined}
                leadingIcon={undefined}
                trailingIcon={undefined}
                onClick={() => {
                  setNewValue(value);
                  onCancel();
                }}
                className="mr-1"
                disabled={isLoading}
              >
                {c("cancel")}
              </Button>
            </div>
          </PopoverContent>
        </Popover>
      </>
    );
  }

  return (
    <Popover>
      <PopoverTrigger className={cn("flex-1 px-2 py-4 text-left flex items-center gap-4")}>
        <span className={cn(isInvalid && "text-tonal-dark-cream-50")}>{value ? formatWeight(value) : "-- kg"}</span>
        {isInvalid && !disabled && <Error className="size-4 fill-tonal-red-50" />}
        {!isInvalid && (value ?? 0) > 0 && <CheckCircle className="size-4 fill-success" />}
      </PopoverTrigger>
      <PopoverContent
        className="shadow shadow-elevation-04-1 border-none rounded-2xl w-auto h-auto flex flex-col p-0 -ml-12"
        side="bottom"
      >
        <Button
          color="dark-blue"
          size="small"
          variant="text"
          leadingIcon={<Edit />}
          onClick={() => onSelect()}
          className={cn(
            "p-4 rounded-none rounded-t-2xl items-center justify-start pr-1 min-w-44",
            isNew && "rounded-2xl"
          )}
        >
          <p className="text-paragraph-regular text-tonal-dark-cream-10 font-normal mt-1">{c("edit")}</p>
        </Button>
        {!isNew && (
          <Button
            color="dark-blue"
            size="small"
            variant="text"
            leadingIcon={<Error />}
            onClick={() => onDecline()}
            className="p-4 rounded-none rounded-b-2xl items-center justify-start"
          >
            <p className="text-paragraph-regular text-tonal-dark-cream-10 font-normal mt-1">{t("reportProblem")}</p>
          </Button>
        )}
      </PopoverContent>
    </Popover>
  );
}
