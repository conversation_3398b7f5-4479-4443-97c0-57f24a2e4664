"use client";

import { useQueryFilter } from "@/hooks/use-query-filter";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { useSession } from "next-auth/react";
import { CustomerCountryProofFile } from "./customer-country-proof-file";
import { useCustomerLicense } from "./use-customer-license";
import { useTranslations } from "next-intl";

export function CustomerCountryLicense() {
  const { license } = useCustomerLicense();

  const { changeParam } = useQueryFilter(["commitment"]);

  const handleOnOpenCommitment = () => {
    changeParam("commitment", "true");
  };

  const t = useTranslations("CustomerCountryLicense");
  return (
    <div className="p-6 bg-tonal-cream-96 rounded-3xl w-full">
      <div className="flex flex-row items-center justify-between">
        <p className="text-title-3 font-bold text-[#183362]">{t("licensingInformation")}</p>
      </div>
      <div className="mt-6 w-full flex flex-col gap-3">
        <div className="bg-white w-full rounded-xl flex flex-col gap-5 p-4">
          <div className="w-[40%]">
            <p className="text-tonal-dark-cream-30 text-small-paragraph-regular">{t("registrationNumber")}:</p>
            <Input
              enabled={false}
              placeholder={t("enterRegistrationNumber")}
              defaultValue={license?.registration_number}
              className="bg-white"
            />
          </div>
          <div className="w-full flex">
            <div className="flex-1">
              <p className="text-tonal-dark-cream-30 text-small-paragraph-regular">{t("licensingDate")}:</p>
              <p className="text-primary text-paragraph-regular">
                {license?.start_date ? new Date(license.start_date).toLocaleDateString().replaceAll("/", ".") : "-"}
              </p>
            </div>
            <div className="flex-1">
              <p className="text-tonal-dark-cream-30 text-small-paragraph-regular">{t("licensingRenew")}:</p>
              <p className="text-primary text-paragraph-regular">
                {license?.end_date ? new Date(license.end_date).toLocaleDateString().replaceAll("/", ".") : "-"}
              </p>
            </div>
          </div>
          <div className="w-[50%] flex flex-col gap-2">
            <p className="text-tonal-dark-cream-30 text-small-paragraph-regular">{t("commitmentAssessment")}</p>
            <button
              className="text-paragraph-regular text-support-blue underline text-start"
              onClick={handleOnOpenCommitment}
            >
              {t("viewAnswers")}
            </button>
          </div>
          <CustomerCountryProofFile />
        </div>
      </div>
    </div>
  );
}
