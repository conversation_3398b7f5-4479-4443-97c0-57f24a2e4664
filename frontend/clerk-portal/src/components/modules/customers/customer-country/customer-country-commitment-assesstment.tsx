import { CountryIcon } from "@/components/_common/country-icon";
import Divider from "@/components/_common/divider";
import Cardboard from "@/components/_common/icons/cardboard";
import Composite from "@/components/_common/icons/composite";
import Glass from "@/components/_common/icons/glass";
import Plastic from "@/components/_common/icons/plastic";
import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { ActionGuide } from "@/lib/api/customer/types";
import { License } from "@/lib/api/license/types";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Aluminium, Clear, Download, Edit } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { SVGProps, useState } from "react";
import { Control, Controller, useForm } from "react-hook-form";
import { z } from "zod";
import { QuestionSimpleAnswer } from "../components/question-simple-answer";
import { useTranslations } from "next-intl";

// Define the form data type structure without the actual schema
type CommitmentAssessmentFormData = {
  isCompanyFromCzechRepublic?: string;
  isSellingMoreThan300kgOfPackagingPerYear?: string;
  isSellingMoreThanOneMillionEurosPerYear?: string;
  whichYear?: string;
  salesPackaging?: {
    firstB2B?: number;
    secondB2B?: number;
    thirdB2B?: number;
    fourthB2B?: number;
    fifthB2B?: number;
    sixthB2B?: number;
  };
  b2bPackaging?: {
    firstB2B?: number;
    secondB2B?: number;
    thirdB2B?: number;
    fourthB2B?: number;
    fifthB2B?: number;
    sixthB2B?: number;
  };
};

type CustomerCountryCommitmentAssesstment = {
  licenseOrActionGuide?: License | ActionGuide;
};

export default function CustomerCountryCommitmentAssesstment({
  licenseOrActionGuide,
}: CustomerCountryCommitmentAssesstment) {
  const t = useTranslations("CustomerCountryCommitmentAssessment");
  const c = useTranslations("common");

  // Define the schema inside the component to use translations
  const formSchema = z.object({
    isCompanyFromCzechRepublic: z.string().optional(),
    isSellingMoreThan300kgOfPackagingPerYear: z.string().optional(),
    isSellingMoreThanOneMillionEurosPerYear: z.string().optional(),
    whichYear: z.string().optional(),
    salesPackaging: z
      .object({
        firstB2B: z.coerce.number().optional(),
        secondB2B: z.coerce.number().optional(),
        thirdB2B: z.coerce.number().optional(),
        fourthB2B: z.coerce.number().optional(),
        fifthB2B: z.coerce.number().optional(),
        sixthB2B: z.coerce.number().optional(),
      })
      .optional(),
    b2bPackaging: z
      .object({
        firstB2B: z.coerce.number().optional(),
        secondB2B: z.coerce.number().optional(),
        thirdB2B: z.coerce.number().optional(),
        fourthB2B: z.coerce.number().optional(),
        fifthB2B: z.coerce.number().optional(),
        sixthB2B: z.coerce.number().optional(),
      })
      .optional(),
  });

  const { paramValues, deleteParam } = useQueryFilter(["commitment"]);

  const isModalOpen = paramValues?.commitment === "true";

  const [isEditing, setIsEditing] = useState(false);

  const { handleSubmit, control, reset } = useForm<CommitmentAssessmentFormData>({
    resolver: zodResolver(formSchema),
    disabled: !isEditing,
  });

  const handleOnOpenChange = (open: boolean) => {
    if (open) return;

    deleteParam("commitment");
  };

  const onSubmit = (data: CommitmentAssessmentFormData) => {
    // eslint-disable-next-line no-console
    setIsEditing(false);
  };

  const handleOnCancel = () => {
    reset();
    setIsEditing(false);
  };

  return (
    <Modal
      open={isModalOpen}
      onOpenChange={handleOnOpenChange}
      className="z-50 !py-9 !px-9 !bg-surface-01 overflow-x-auto"
      style={{ minWidth: "100vw", minHeight: "100vh" }}
    >
      <Button
        type="button"
        color="dark-blue"
        size="iconXSmall"
        variant="text"
        onClick={() => handleOnOpenChange(false)}
        className="!bg-white !rounded-full justify-self-end"
      >
        <Clear className="size-6" />
      </Button>

      <section className="w-full grid place-items-center grid-cols-1">
        <div className="flex flex-col max-w-[600px] w-full">
          <div className="flex items-center gap-4">
            <CountryIcon
              className="w-[24px] h-[24px]"
              country={{
                flag: licenseOrActionGuide?.country_flag ?? "",
                name: licenseOrActionGuide?.country_name ?? "",
              }}
            />
            <p className="text-large-paragraph-bold text-grey-blue font-bold mt-2">
              {licenseOrActionGuide?.country_name}
            </p>
          </div>
          <h1 className="font-large-paragraph-bold text-2xl text-tonal-dark-blue-10 font-bold my-5 sm:text-4xl">
            {t("viewAnswers")} <br /> {t("commitmentAssessment")}
          </h1>
          <p className="text-paragraph-regular text-tonal-dark-cream-10 mb-5">{t("seeCustomersAnswers")}</p>
          {!isEditing && (
            <div className="flex items-center justify-between">
              <Button color="light-blue" size="medium" variant="text" leadingIcon={<Download />} className="-ml-4">
                {t("pdf")}
              </Button>
              <Button
                color="dark-blue"
                size="small"
                variant="outlined"
                leadingIcon={<Edit />}
                onClick={() => setIsEditing((prev) => !prev)}
              >
                {t("edit")}
              </Button>
            </div>
          )}
          <form className="flex flex-col gap-y-6 my-6" onSubmit={handleSubmit(onSubmit)}>
            <div className="flex flex-col min-h-auto bg-[#EDE9E4] rounded-4xl p-8 gap-5">
              <p className="text-large-paragraph-bold text-grey-blue font-bold">{t("commitmentAssessment")}</p>
              <p className="text-small-paragraph-regular text-tonal-dark-cream-30">{t("seeCustomerAnswers")}</p>
              <div className="flex flex-col bg-white rounded-2xl p-8 gap-10">
                <Controller
                  control={control}
                  name="isCompanyFromCzechRepublic"
                  render={({ field }) => (
                    <QuestionSimpleAnswer
                      question={t("isCzech")}
                      disabled={field.disabled}
                      valueSelect={field.value}
                      onChange={(e) => {
                        field.onChange(e.target.value);
                      }}
                    />
                  )}
                />

                <Controller
                  control={control}
                  name="isSellingMoreThan300kgOfPackagingPerYear"
                  render={({ field }) => (
                    <QuestionSimpleAnswer
                      question={t("sellMoreThan300")}
                      disabled={field.disabled}
                      valueSelect={field.value}
                      onChange={(e) => {
                        field.onChange(e.target.value);
                      }}
                    />
                  )}
                />

                <Controller
                  control={control}
                  name="whichYear"
                  render={({ field }) => (
                    <QuestionSimpleAnswer
                      question="Which year would your company like to license 2023 or 2024?*"
                      disabled={field.disabled}
                      valueSelect={field.value}
                      onChange={(e) => {
                        field.onChange(e.target.value);
                      }}
                    />
                  )}
                />
              </div>
            </div>
            <CountryPackaging
              control={control}
              isEditing={isEditing}
              items={[
                {
                  Icon: Aluminium,
                  name: `B2B Packaging`,
                  inputId: `salesPackagingFirstB2B`,
                  inputName: "salesPackaging.firstB2B",
                },
                {
                  Icon: Composite,
                  name: `B2B Packaging`,
                  inputId: `salesPackagingSecondB2B`,
                  inputName: "salesPackaging.secondB2B",
                },
                {
                  Icon: Glass,
                  name: `B2B Packaging`,
                  inputId: `salesPackagingThirdB2B`,
                  inputName: "salesPackaging.thirdB2B",
                },
                {
                  Icon: Plastic,
                  name: `B2B Packaging`,
                  inputId: `salesPackagingFourthB2B`,
                  inputName: "salesPackaging.fourthB2B",
                },
                {
                  Icon: Plastic,
                  name: `B2B Packaging`,
                  inputId: `salesPackagingFifthB2B`,
                  inputName: "salesPackaging.fifthB2B",
                },
                {
                  Icon: Cardboard,
                  name: `B2B Packaging`,
                  inputId: `salesPackagingSixthB2B`,
                  inputName: "salesPackaging.sixthB2B",
                },
              ]}
              name={t("salesPackaging")}
              translations={{
                info: t("info"),
                result: t("result"),
                licensingRequired: t("licensingRequired"),
              }}
            />

            <CountryPackaging
              control={control}
              isEditing={isEditing}
              items={[
                {
                  Icon: Aluminium,
                  name: `B2B Packaging`,
                  inputId: `b2bPackagingFirstB2B`,
                  inputName: "b2bPackaging.firstB2B",
                },
                {
                  Icon: Composite,
                  name: `B2B Packaging`,
                  inputId: `b2bPackagingSecondB2B`,
                  inputName: "b2bPackaging.secondB2B",
                },
                {
                  Icon: Glass,
                  name: `B2B Packaging`,
                  inputId: `b2bPackagingThirdB2B`,
                  inputName: "b2bPackaging.thirdB2B",
                },
                {
                  Icon: Plastic,
                  name: `B2B Packaging`,
                  inputId: `b2bPackagingFourthB2B`,
                  inputName: "b2bPackaging.fourthB2B",
                },
                {
                  Icon: Plastic,
                  name: `B2B Packaging`,
                  inputId: `b2bPackagingFifthB2B`,
                  inputName: "b2bPackaging.fifthB2B",
                },
                {
                  Icon: Cardboard,
                  name: `B2B Packaging`,
                  inputId: `b2bPackagingSixthB2B`,
                  inputName: "b2bPackaging.sixthB2B",
                },
              ]}
              name={t("b2bPackaging")}
              translations={{
                info: t("info"),
                result: t("result"),
                licensingRequired: t("licensingRequired"),
              }}
            />

            {isEditing && (
              <div className="flex items-center self-end gap-4">
                <Button
                  color="dark-blue"
                  size="small"
                  variant="outlined"
                  type="button"
                  className="min-w-32"
                  onClick={handleOnCancel}
                >
                  {c("cancel")}
                </Button>
                <Button color="yellow" size="small" variant="filled" type="submit" className="min-w-32">
                  {c("save")}
                </Button>
              </div>
            )}
          </form>
        </div>
      </section>
    </Modal>
  );
}

interface CountryPackagingProps {
  control: Control;
  items: {
    inputId: string;
    inputName: string;
    Icon: (props: SVGProps<SVGSVGElement>) => JSX.Element;
    name: string;
  }[];
  isEditing: boolean;
  name: string;
  info?: string;
  translations: {
    info: string;
    result: string;
    licensingRequired: string;
  };
}

const CountryPackaging = ({
  control,
  items,
  name,
  isEditing,
  translations,
  info = translations.info,
}: CountryPackagingProps) => {
  return (
    <div className="flex flex-col min-h-auto bg-[#EDE9E4] rounded-4xl p-8 gap-5">
      {!isEditing && (
        <>
          <div className="flex items-center gap-4">
            <p className="text-large-paragraph-bold text-tonal-dark-cream-30 font-bold">{translations.result}</p>
            <p className="text-paragraph-bold text-on-surface-04 font-bold">{translations.licensingRequired}</p>
            <QuestionTooltip>
              <QuestionTooltipDescription>Lorem ipsum</QuestionTooltipDescription>
            </QuestionTooltip>
          </div>
          <Divider initialMarginDisabled />
        </>
      )}
      <p className="text-large-paragraph-bold text-grey-blue font-bold">{name}</p>
      <p className="text-small-paragraph-regular text-tonal-dark-cream-30">{info}</p>

      <div className="flex flex-col rounded-[20px] overflow-auto">
        {items.map(({ Icon, ...props }, idx) => (
          <section key={idx} className="flex justify-between bg-white py-4 px-5 border-b border-b-[#EDE9E4]">
            <div className="flex items-center gap-4">
              <Icon className="size-9" />
              <p className="text-paragraph-bold text-primary font-bold mt-1">{props.name}</p>
            </div>
            <Controller
              control={control}
              name={props.inputName}
              render={({ field }) => (
                <div className="flex items-center gap-4">
                  <Input
                    {...field}
                    id={props.inputId}
                    type="number"
                    min={0}
                    placeholder="-"
                    enabled={!field.disabled}
                    readonly={field.disabled}
                  />
                  <label htmlFor={props.inputId} className="text-paragraph-regular text-primary mt-2">
                    kg
                  </label>
                </div>
              )}
            />
          </section>
        ))}
      </div>
    </div>
  );
};
