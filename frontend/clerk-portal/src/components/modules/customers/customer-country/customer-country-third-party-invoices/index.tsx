"use client";

import StatusBadge, { StatusType } from "@/components/modules/country/components/task-type";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Add, Download, File, Link, Task } from "@arthursenno/lizenzero-ui-react/Icon";
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { getThirdPartyInvoices } from "@/lib/api/third-party-invoice";
import { useCustomerLicense } from "../use-customer-license";
import { AddThirdPartyInvoiceModal } from "./add-third-party-invoice-modal";
import { ThirdPartyInvoiceStatus } from "@/lib/api/third-party-invoice/types";
import { DatePickerWithRange } from "@/components/ui/date-picker";
import { DateRange } from "react-day-picker";
import { enqueueSnackbar } from "notistack";
import { downloadAdminFile, downloadCustomerFile } from "@/lib/api/file";
import { CgSpinnerAlt } from "react-icons/cg";
import { useSession } from "next-auth/react";
import { UserTypes } from "@/utils/user";
import { useTranslations } from "next-intl";

const translateStatus = (status: ThirdPartyInvoiceStatus): StatusType => {
  if (status === "OPEN") {
    return "Open";
  }
  if (status === "PAYED") {
    return "Processed";
  }
  if (status === "UNPROCESSED") {
    return "Unprocessed Invoice";
  }
  if (status === "CANCELLED") {
    return "Declined";
  }
  return "Processed";
};

export function CustomerCountryThirdPartyInvoices() {
  const t = useTranslations("TransactionTable");
  const c = useTranslations("common");
  const authSession = useSession();
  const userId = authSession?.data?.user.id;

  const { license } = useCustomerLicense();
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [loadingId, setLoadingId] = useState<number | null>(null);

  const { data: thirdPartyInvoices, refetch } = useQuery({
    queryKey: ["third-party-invoices", license?.id, dateRange?.from, dateRange?.to],
    queryFn: async () => {
      if (!license) return [];

      const response = await getThirdPartyInvoices({
        license_id: Number(license.id),
        from_date: dateRange?.from ? dateRange.from.toISOString() : undefined,
        to_date: dateRange?.to ? dateRange.to.toISOString() : undefined,
      });

      return response;
    },
    enabled: !!license,
  });

  const [isAddInvoiceModalOpen, setIsAddInvoiceModalOpen] = useState(false);

  const handleModalOpen = () => {
    setIsAddInvoiceModalOpen(!isAddInvoiceModalOpen);
  };

  async function handleInvoiceDownload(invoiceId: number) {
    setLoadingId(invoiceId);

    try {
      const invoiceSelect = thirdPartyInvoices?.find((item) => item.id === invoiceId);

      if (!invoiceSelect) return;

      const file = invoiceSelect?.license?.files.find((i) => i.original_name === invoiceSelect.title);
      const fileId = file?.id;
      if (!fileId || !userId) throw new Error("Invoice not found");

      const result = await downloadCustomerFile({
        file_id: fileId,
        user_id: Number(userId),
        user_role: UserTypes.CUSTOMER,
      });

      if (!result) {
        throw new Error("Error downloading invoice");
      }

      const typeFile = file.extension.split("/")?.[1];

      const blob = new Blob([result], { type: file.extension });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = file.original_name || `invoice.${typeFile}`;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      enqueueSnackbar(t("invoiceDownload"), { variant: "success" });
    } catch (error) {
      console.error(error);
      enqueueSnackbar(t("errorDownloadInvoice"), { variant: "error" });
    } finally {
      setLoadingId(null);
    }
  }

  const todaySixMonthsAgo = new Date();
  todaySixMonthsAgo.setMonth(todaySixMonthsAgo.getMonth() - 6);

  return (
    <div className="p-6 bg-tonal-cream-96 rounded-3xl w-full">
      <div className="flex flex-row items-center justify-between">
        <p className="text-title-3 font-bold text-[#183362]">{t("authorize")}</p>
        <Button onClick={handleModalOpen} color="yellow" variant="filled" size="small" leadingIcon={<Add />}>
          Add
        </Button>
      </div>
      <div className="mt-6 w-full flex flex-col gap-3">
        <div className="mb-3 flex items-center gap-3">
          <DatePickerWithRange onDateChange={setDateRange} />
        </div>
        <div className="flex flex-col gap-2">
          {thirdPartyInvoices?.map((invoice) => (
            <div
              className="bg-white w-full h-[64px] rounded-xl flex items-center justify-between px-4"
              key={invoice.id}
            >
              <div className="flex gap-2 items-center">
                {invoice.status === "PAYED" ? (
                  <Task className="size-8 fill-success" />
                ) : (
                  <File className="size-8 fill-alert" />
                )}
                <div>
                  <p className="text-tonal-dark-blue-10 text-paragraph-regular">{invoice.title}</p>
                  <div className="flex gap-2">
                    <p className="text-tonal-dark-cream-10 text-small-paragraph-regular">{t("debitCharge")}</p>
                    <p className="text-tonal-dark-cream-40 text-small-paragraph-regular">
                      {new Date(invoice.issued_at).toLocaleDateString().replaceAll("/", ".")}
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <StatusBadge status={translateStatus(invoice.status)} />
                <Button
                  variant="text"
                  color="light-blue"
                  size="iconMedium"
                  disabled={!!loadingId}
                  onClick={() => handleInvoiceDownload(invoice.id)}
                  leadingIcon={loadingId === invoice.id ? <CgSpinnerAlt className="animate-spin" /> : <Download />}
                />
                {/* <Link className="size-6 fill-tonal-dark-blue-10" /> */}
              </div>
            </div>
          ))}
        </div>
        <p className="text-small-paragraph-regular text-tonal-dark-cream-50 text-center mt-6">{c("end")}</p>
        <AddThirdPartyInvoiceModal
          isOpen={isAddInvoiceModalOpen}
          setOpen={setIsAddInvoiceModalOpen}
          refetch={refetch}
        />
      </div>
    </div>
  );
}
