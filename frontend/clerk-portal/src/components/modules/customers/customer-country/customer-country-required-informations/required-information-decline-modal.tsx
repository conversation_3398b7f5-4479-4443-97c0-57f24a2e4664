import { Checkbox } from "@/components/ui/checkbox";
import { Skeleton } from "@/components/ui/skeleton";
import { Textarea } from "@/components/ui/textarea";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { getDeclineReasons } from "@/lib/api/decline-reasons";
import { getReasons } from "@/lib/api/reasons";
import {
  declineRequiredInformation,
  DeclineRequiredInformationParams,
  updateRequiredInformationStatus,
} from "@/lib/api/required-information";
import { RequiredInformation } from "@/lib/api/required-information/types";
import { queryClient } from "@/lib/react-query";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Clear } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { Controller, useForm, useWatch } from "react-hook-form";
import { z } from "zod";
import { useTranslations } from "next-intl";

const declineRequiredInformationSchema = z.object({
  reasons: z.record(z.string(), z.boolean()),
  title: z.string().optional(),
});

type DeclineRequiredInformationFormData = z.infer<typeof declineRequiredInformationSchema>;

interface DeclineRequiredInformationModalProps {
  information: RequiredInformation;
}

export function DeclineRequiredInformationModal({ information }: DeclineRequiredInformationModalProps) {
  const t = useTranslations("DeclineQuantityReportModal");
  const { paramValues, deleteParam } = useQueryFilter(["document-id"]);
  const selectedDocumentId = paramValues["document-id"];
  const isModalOpen = selectedDocumentId !== null && selectedDocumentId === String(information.id);

  const declineRequiredInformationMutation = useMutation({
    mutationFn: async (data: DeclineRequiredInformationParams) => {
      await declineRequiredInformation(Number(selectedDocumentId), data);
    },
  });

  const reasonsQuery = useQuery({
    queryKey: ["reasons", { type: "LICENSE_INFORMATION" }],
    queryFn: async () => {
      const reasons = await getReasons({ type: "LICENSE_INFORMATION" });

      form.setValue(
        "reasons",
        reasons.reduce(
          (acc, reason) => {
            acc[`reason-${reason.id}`] = false;
            return acc;
          },
          {} as Record<string, boolean>
        )
      );

      return reasons;
    },
  });

  const form = useForm<DeclineRequiredInformationFormData>({
    resolver: zodResolver(declineRequiredInformationSchema),
  });

  function handleOnOpenChange(open: boolean) {
    if (open) return;

    form.unregister("reasons");
    form.unregister("title");

    deleteParam("document-id");
  }

  // eslint-disable-next-line no-console
  function onSubmit(data: DeclineRequiredInformationFormData) {
    const reasons = Object.entries(data.reasons);

    const isEveryFalse = reasons.every(([_, value]) => value === false);

    if (isEveryFalse) {
      form.setError("reasons", { message: t("selectAtLeastOneReason") });
      return;
    }

    const reasonIds = reasons.filter(([_, value]) => value).map(([key]) => Number(key.split("-")[1]));

    declineRequiredInformationMutation.mutate(
      {
        reason_ids: reasonIds,
        title: data.title || undefined,
      },
      {
        onSuccess: () => {
          if (information.license_id) {
            queryClient.invalidateQueries({
              queryKey: ["required-informations", information.license_id],
            });
          }

          if (information.contract_id) {
            queryClient.invalidateQueries({
              queryKey: ["general-informations", information.contract_id],
            });
          }

          enqueueSnackbar(t("informationDeclined"), { variant: "success" });
          handleOnOpenChange(false);
        },
        onError: () => {
          enqueueSnackbar(t("declineReportFailed"), { variant: "error" });
        },
      }
    );
  }

  const declineReasons = reasonsQuery.data;

  return (
    <Modal
      open={isModalOpen}
      onOpenChange={handleOnOpenChange}
      className="z-50 !rounded-[52px] w-full max-w-[672px] !p-9 px-2"
    >
      <div className="flex items-start justify-between">
        <h1 className="font-large-paragraph-bold text-2xl text-tonal-dark-blue-10 font-bold my-5 sm:text-3xl">
          Decline
        </h1>
        <Button color="dark-blue" size="iconXSmall" variant="text" onClick={() => handleOnOpenChange(false)}>
          <Clear className="size-6" />
        </Button>
      </div>
      <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col gap-6 h-80 overflow-y-auto px-2">
        <p data-error={!!form.formState.errors.reasons?.message} className="text-primary data-[error=true]:text-error">
          {t("selectReasons")}
        </p>
        {reasonsQuery.isLoading &&
          Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex items-center gap-2">
              <Skeleton key={index} className="size-5 flex-none" />
              <Skeleton key={index} className="h-5 w-full" />
            </div>
          ))}
        {!reasonsQuery.isLoading &&
          declineReasons?.map((declineReason) => (
            <label
              key={declineReason.id}
              htmlFor={String(declineReason.id)}
              className="flex items-center gap-2 text-primary"
            >
              <Controller
                control={form.control}
                name={`reasons.${`reason-${declineReason.id}`}`}
                defaultValue={false}
                render={({ field }) => (
                  <Checkbox
                    id={String(declineReason.id)}
                    checked={field.value}
                    onCheckedChange={(checked) => field.onChange(checked === true)}
                    disabled={reasonsQuery.isLoading || declineRequiredInformationMutation.isPending}
                  />
                )}
              />
              {declineReason.title}
            </label>
          ))}

        <div className="flex flex-col">
          <Textarea
            id="title"
            label={t("addCommentsoptional")}
            placeholder={t("writeComment")}
            rows={6}
            className="resize-none"
            maxLength={350}
            disabled={reasonsQuery.isLoading || declineRequiredInformationMutation.isPending}
            {...form.register("title")}
          />
        </div>

        <div className="flex self-end ml-auto">
          <Button
            color="red"
            variant="filled"
            size="medium"
            disabled={reasonsQuery.isLoading || declineRequiredInformationMutation.isPending}
            type="submit"
          >
            {declineRequiredInformationMutation.isPending ? t("declining") : t("declineDocument")}
          </Button>
        </div>
      </form>
    </Modal>
  );
}
