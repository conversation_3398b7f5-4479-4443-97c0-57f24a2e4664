import { Skeleton } from "@/components/ui/skeleton";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { getRequiredInformations } from "@/lib/api/required-information";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Add } from "@arthursenno/lizenzero-ui-react/Icon";
import { useQuery } from "@tanstack/react-query";
import AddRequiredInformationModal from "../../../country/components/add-required-information-modal";
import { useCustomerLicense } from "../use-customer-license";
import { RequiredInformationItem } from "./required-information-item";
import { useCustomer } from "../../customer-profile/use-customer";
import { RequiredInformationLucid } from "./required-information-lucid";
import { useTranslations } from "next-intl";

export function CustomerCountryRequiredInformations() {
  const t = useTranslations("RequiredInformationItem");
  const c = useTranslations("common");
  const { license } = useCustomerLicense();

  const { customer } = useCustomer();
  const company = customer?.companies?.[0];

  const { data: requiredInformations, isLoading: isLoadingRequiredInformations } = useQuery({
    queryKey: ["required-informations", license?.id],
    queryFn: async () => {
      if (!license) return [];

      const response = await getRequiredInformations({ licenseId: license.id });
      return response;
    },
    enabled: !!license,
  });

  const { data: generalRequiredInformations, isLoading: isLoadingGeneralRequiredInformations } = useQuery({
    queryKey: ["general-informations", license?.contract_id],
    queryFn: async () => {
      if (!license) return [];

      const response = await getRequiredInformations({ contractId: license.contract_id });
      return response;
    },
    enabled: !!license,
  });

  const { paramValues, changeParam } = useQueryFilter(["add-required-information"]);

  function openAddRequiredInformationModal() {
    changeParam("add-required-information", "true");
  }

  const fileGeneralRequiredInformations = generalRequiredInformations?.filter(
    (information) => information.type === "DOCUMENT" || information.type === "FILE" || information.type === "IMAGE"
  );
  const questionGeneralRequiredInformations = generalRequiredInformations?.filter(
    (information) => information.type === "TEXT" || information.type === "NUMBER"
  );

  const fileRequiredInformations = requiredInformations?.filter(
    (information) => information.type === "DOCUMENT" || information.type === "FILE" || information.type === "IMAGE"
  );
  const questionRequiredInformations = requiredInformations?.filter(
    (information) => information.type === "TEXT" || information.type === "NUMBER"
  );

  const isLoading = isLoadingRequiredInformations || isLoadingGeneralRequiredInformations;

  const isDirect = license?.contract?.type === "DIRECT_LICENSE";

  return (
    <div className="p-6 bg-tonal-cream-96 rounded-3xl w-full">
      <div className="flex flex-row items-center justify-between">
        <p className="text-title-3 font-bold text-[#183362]">{t("requiredInformation")}</p>
        <Button
          color="yellow"
          variant="filled"
          size="small"
          leadingIcon={<Add />}
          onClick={openAddRequiredInformationModal}
        >
          Add
        </Button>
      </div>
      <div className="mt-6 w-full flex flex-col gap-3">
        {!isLoading && (
          <div className="h-auto w-full bg-white rounded-lg py-4 px-6">
            <div className="flex flex-col">
              {isDirect && (
                <div className="flex flex-col gap-2">
                  <RequiredInformationLucid lucid={company?.lucid} />
                </div>
              )}
              <div className="flex flex-col gap-2">
                {questionGeneralRequiredInformations?.map((information) => (
                  <RequiredInformationItem key={information.id} information={information} />
                ))}
              </div>
            </div>
            <div className="flex flex-col">
              {fileRequiredInformations?.map((information) => (
                <RequiredInformationItem key={information.id} information={information} />
              ))}
              <div className="flex flex-col gap-2">
                {questionRequiredInformations?.map((information) => (
                  <RequiredInformationItem key={information.id} information={information} />
                ))}
              </div>
            </div>
          </div>
        )}
        {isLoading && (
          <div className="flex flex-col gap-2 h-auto w-full bg-white rounded-lg py-4 px-6">
            {Array.from({ length: 3 }).map((_, index) => (
              <div className="flex items-center py-4 gap-4" key={index}>
                <Skeleton className="size-10 rounded-lg flex-none" />
                <Skeleton className="h-8 w-full" />
              </div>
            ))}
          </div>
        )}
        <p className="text-small-paragraph-regular text-tonal-dark-cream-50 text-center mt-6">{c("end")}</p>
        <AddRequiredInformationModal licenseId={license?.id} />
      </div>
    </div>
  );
}
