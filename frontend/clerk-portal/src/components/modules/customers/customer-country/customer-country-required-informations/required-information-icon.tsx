import { RequiredInformationType } from "@/lib/api/required-information/types";
import { cn } from "@/lib/utils";
import Image, { type ImageProps } from "next/image";

function getImageSrcByType(type: RequiredInformationType) {
  const basePath = "/assets/images/required-informations";

  switch (type) {
    case "FILE":
    case "DOCUMENT":
      return `${basePath}/logo_pdf.png`;
    case "IMAGE":
      return `${basePath}/logo_png.png`;
    case "NUMBER":
      return `${basePath}/hashtag.png`;
    case "TEXT":
      return `${basePath}/text_input.png`;
    default:
      return "";
  }
}

interface RequiredInformationIconProps extends Omit<ImageProps, "src" | "alt"> {
  requiredInformationType: RequiredInformationType;
}

export function RequiredInformationIcon(props: RequiredInformationIconProps) {
  const { requiredInformationType: type, className, ...restProps } = props;

  const imageSrc = getImageSrcByType(type);

  return (
    <Image
      alt="Information icon"
      src={imageSrc}
      className={cn("size-10", className)}
      width={200}
      height={200}
      {...restProps}
    />
  );
}
