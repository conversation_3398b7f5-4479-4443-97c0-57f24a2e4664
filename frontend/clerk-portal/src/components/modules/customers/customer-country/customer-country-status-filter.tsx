"use client";

import { Dropdown, DropdownItem } from "@/components/_common/dropdown";
import Status from "@/components/modules/country/components/task-status";
import { KeyboardArrowDown } from "@arthursenno/lizenzero-ui-react/Icon";
import { useState } from "react";

export const STATUS_FILTERS = [
  { label: "Open to do’s", value: "Open to do’s" },
  { label: "Complete", value: "Complete" },
] as const;

type StatusType =
  | "Complete"
  | "Open to do’s"
  | "Pending"
  | "Open task"
  | "Terminated"
  | "Open"
  | "Payed"
  | "Needed"
  | "In progress";

type FilterCountryStatusProps = {
  status: StatusType;
};

export function FilterCountryStatus({ status }: FilterCountryStatusProps) {
  const [selectedStatus, setSelectedStatus] = useState<StatusType>(status ?? "Open to do’s");

  const handleStatusChange = (status: StatusType) => {
    setSelectedStatus(status);
  };

  return (
    <Dropdown
      trigger={
        <button className="flex items-center gap-1 text-support-blue font-bold text-base">
          <div>
            <Status status={selectedStatus} />
          </div>
          <KeyboardArrowDown className="fill-tonal-dark-cream-30 size-6" />
        </button>
      }
    >
      {STATUS_FILTERS.map((statusFilter, idx) => (
        <DropdownItem
          key={idx}
          className="text-tonal-dark-cream-10 hover:bg-surface-01 py-5 px-4 outline-none text-base hover:cursor-pointer"
          onClick={() => handleStatusChange(statusFilter.label as StatusType)}
        >
          {statusFilter.label}
        </DropdownItem>
      ))}
    </Dropdown>
  );
}
