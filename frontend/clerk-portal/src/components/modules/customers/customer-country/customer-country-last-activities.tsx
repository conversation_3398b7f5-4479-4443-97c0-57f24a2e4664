import { CheckCircle } from "@arthursenno/lizenzero-ui-react/Icon";
import { useTranslations } from "next-intl";

export function CustomerCountryLastActivities() {
  const t = useTranslations("CustomerCountryLastActivities");
  return (
    <div className="p-6 bg-tonal-cream-96 rounded-3xl w-full">
      <div className="flex flex-row items-center justify-between">
        <p className="text-title-3 font-bold text-[#183362]">{t("lastActivities")}</p>
      </div>
      <div className="mt-6 w-full flex flex-col gap-3">
        <div className="bg-white w-full h-[64px] rounded-[20px] flex items-center justify-between px-4">
          <div className="flex gap-2 items-center">
            <CheckCircle className="size-6 fill-success" />
            <p className="text-tonal-dark-blue-10 text-paragraph-regular">{t("declareVolumeReport")}</p>
          </div>
          <p className="text-tonal-dark-cream-50 text-base">{t("done")}: 02.04.23</p>
        </div>
        <div className="bg-white w-full h-[64px] rounded-[20px] flex items-center justify-between px-4">
          <div className="flex gap-2 items-center">
            <CheckCircle className="size-6 fill-success" />
            <p className="text-tonal-dark-blue-10 text-paragraph-regular">{t("uploadLizenzeroContract")}</p>
          </div>
          <p className="text-tonal-dark-cream-50 text-base">{t("done")}: 02.03.23</p>
        </div>
      </div>
    </div>
  );
}
