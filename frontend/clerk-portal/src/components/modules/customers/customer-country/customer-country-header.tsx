"use client";

import { CountryIcon } from "@/components/_common/country-icon";
import Divider from "@/components/_common/divider";
import { ListCustomer } from "@/lib/api/customer/types";
import { License } from "@/lib/api/license/types";
import { formatCustomerNumber } from "@/utils/format-customer-number";
import { KeyboardArrowLeft } from "@arthursenno/lizenzero-ui-react/Icon";
import { useRouter } from "@/i18n/navigation";
import { FilterCountryStatus } from "./customer-country-status-filter";
import { useCustomerLicense } from "./use-customer-license";
import { useCustomer } from "../customer-profile/use-customer";
import Link from "next/link";
import { useTranslations } from "next-intl";

const clerkControlStatusToStatusType = (clerkControlStatus: License["clerk_control_status"]) => {
  if (clerkControlStatus === "DONE") {
    return "Complete" as const;
  }
  return "Open to do’s" as const;
};

export function CustomerCountryHeader() {
  const t = useTranslations("CustomerCountryHeader");
  const { customer } = useCustomer();
  const { license } = useCustomerLicense();

  if (!license) return null;

  return (
    <section className="md:mt-8 mb-4">
      <div className="flex flex-row items-center gap-1 ">
        <div className="flex flex-col md:flex-row items-start md:items-center gap-2">
          <Link
            href={`/customers/${customer?.id}`}
            className="text-xl md:text-2xl text-support-blue font-bold hover:underline cursor-pointer flex items-center gap-2"
          >
            <KeyboardArrowLeft className="size-7 fill-support-blue" />
            {t("backToCustomer")} #{formatCustomerNumber(customer?.id)}
          </Link>
          <div className="hidden md:block">|</div>
          <p className="text-tonal-dark-cream-30 text-paragraph-regular">{customer?.companies?.[0]?.name}</p>
        </div>
      </div>
      <div className="flex flex-row justify-between gap-3 mt-8 w-full h-16">
        <div className="flex items-center gap-4">
          <CountryIcon
            className="w-[42px] h-[42px]"
            country={{ flag: license?.country_flag ?? "", name: license?.country_name ?? "" }}
          />
          <p className="text-h3 text-grey-blue font-bold mt-2">{license?.country_name ?? "-"}</p>
        </div>
        <div className="flex flex-col gap-2">
          <p className="text-tonal-dark-cream-50 text-small-paragraph-regular text-right">{t("clerkControl")}</p>
          <div className="flex items-center gap-2">
            <FilterCountryStatus status={clerkControlStatusToStatusType(license.clerk_control_status!)} />
          </div>
        </div>
      </div>
      <div className="p-3">
        <Divider initialMarginDisabled />
      </div>
    </section>
  );
}
