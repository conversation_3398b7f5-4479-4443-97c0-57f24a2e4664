"use client";

import Breadcrumb from "@/components/_common/breadcrumb/breadcrumb";
import Container from "@/components/_common/container/container";
import { Skeleton } from "@/components/ui/skeleton";
import { deleteCustomerFile, downloadAdminFile, uploadFile } from "@/lib/api/file";
import { updateTermination } from "@/lib/api/termination";
import { downloadFile } from "@/utils/download-file";
import { formatCustomerNumber } from "@/utils/format-customer-number";
import { UserTypes } from "@/utils/user";
import { File } from "@arthursenno/lizenzero-ui-react/Icon";
import { useSession } from "next-auth/react";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { CustomerProfileTasks } from "../customer-profile/customer-profile-tasks";
import { useCustomer } from "../customer-profile/use-customer";
import { CustomerCountryHeader } from "./customer-country-header";
import { CustomerCountryLastActivities } from "./customer-country-last-activities";
import { CustomerCountryLicense } from "./customer-country-license";
import { CustomerCountryPriceList } from "./customer-country-price-list-card";
import { CustomerCountryQuantityReport } from "./customer-country-quantity-report";
import { CustomerCountryRequiredInformations } from "./customer-country-required-informations";
import { CustomerCountrySaasCard } from "./customer-country-saas-card";
import { CustomerCountryTermination } from "./customer-country-termination";
import { CustomerCountryThirdPartyInvoices } from "./customer-country-third-party-invoices";
import { useCustomerLicense } from "./use-customer-license";

interface CustomFile extends File {
  id: string;
}

export function CustomerCountry() {
  const session = useSession();
  const userId = session?.data?.user.id;
  const { customerId } = useCustomer();
  const [selectedFile, setSelectedFiles] = useState<CustomFile[]>();
  const { license, refetch } = useCustomerLicense();
  const [submittingFile, setSubmittingFile] = useState<boolean>(false);

  async function handleDownloadFile(fileId: string) {
    if (!userId) return;

    enqueueSnackbar("Uploading file...", { variant: "info" });

    try {
      const file = await downloadAdminFile({
        file_id: fileId,
        user_id: Number(userId),
        user_role: UserTypes.ADMIN,
      });
      const mimeType = file.type;
      const extension = mimeType.split("/")[1];
      const fileName = `downloaded-file.${extension}`;

      downloadFile({ buffer: file, fileName });
      enqueueSnackbar("File uploaded successfully", { variant: "success" });
    } catch (err) {
      enqueueSnackbar("Error uploading file", { variant: "error" });
    }
  }

  async function handleDeleteFile(fileId: string) {
    if (!session.data?.user || !license?.termination?.files) return;

    const fileToDelete = license.termination.files.find((file) => file.id === fileId);
    if (!fileToDelete) {
      enqueueSnackbar("File not found", { variant: "error" });
      return;
    }

    try {
      enqueueSnackbar("Deleting file...", { variant: "info" });

      await deleteCustomerFile(fileToDelete.id);

      // Update the state to remove only the deleted file
      setSelectedFiles((prevFiles) => prevFiles?.filter((file) => file.id !== fileId));
      enqueueSnackbar("File deleted successfully", { variant: "success" });
      refetch();
    } catch (error) {
      enqueueSnackbar("Error deleting file", { variant: "error" });
      console.error("Error deleting file:", error);
    }
  }

  async function handleSelectProofOfTerminationFiles(files: File[]) {
    if (!session.data?.user) return;
    if (!license?.termination) return;
    if (files.length === 0) {
      enqueueSnackbar("No files selected for upload. Please select files before proceeding.", { variant: "error" });
      return;
    }
    setSubmittingFile(true);

    enqueueSnackbar("Uploading files...", { variant: "info" });

    try {
      for (const file of files) {
        await uploadFile({
          file,
          type: "PROOF_OF_TERMINATION",
          user_id: String(session.data.user.id),
          termination_id: license?.termination?.id,
        });
      }

      await updateTermination(license.termination.id, { status: "COMPLETED" });

      enqueueSnackbar("Files uploaded successfully", { variant: "success" });
      refetch();
    } catch (error) {
      enqueueSnackbar("Error uploading files", { variant: "error" });
    } finally {
      setSubmittingFile(false);
    }
  }

  const paths = [
    { label: "Customers", href: "/customers" },
    { label: `Customer #${formatCustomerNumber(customerId)}`, href: "" },
  ];

  const termination = license?.termination;

  return (
    <Container className="bg-white">
      <Breadcrumb paths={paths} />
      {!license && <CustomerCountrySkeleton />}
      {license && (
        <>
          <CustomerCountryHeader />
          {}
          <CustomerCountryTermination />
          <div className="flex flex-col gap-6 mb-6">
            <section className="grid grid-cols-1 lg:grid-cols-5 gap-6">
              <div className="grid grid-cols-1 gap-6 col-span-1 lg:col-span-2">
                <CustomerCountrySaasCard />
                <CustomerCountryLicense />
                <CustomerCountryLastActivities />
                <CustomerCountryPriceList />
              </div>
              <div className="col-span-1 lg:col-span-3 flex flex-col gap-6">
                <CustomerProfileTasks customerId={customerId} />
                <CustomerCountryRequiredInformations />
                <CustomerCountryThirdPartyInvoices />
              </div>
            </section>
            <CustomerCountryQuantityReport />
          </div>
        </>
      )}
    </Container>
  );
}

function CustomerCountrySkeleton() {
  return (
    <div className="space-y-14 my-8">
      <div className="space-y-6">
        <Skeleton className="h-16 w-full lg:w-1/2" />
        <Skeleton className="h-5 w-full lg:w-1/3" />
      </div>
      <div className="flex flex-col gap-6 mb-6">
        <section className="grid grid-cols-1 lg:grid-cols-5 gap-6">
          <div className="col-span-1 lg:col-span-2 grid grid-cols-1 gap-6">
            <Skeleton className="h-[300px] w-full" />
            <Skeleton className="h-[300px] w-full" />
            <Skeleton className="h-[300px] w-full" />
            <Skeleton className="h-[300px] w-full" />
          </div>
          <div className="col-span-1 lg:col-span-3 flex flex-col gap-6">
            <Skeleton className="h-[500px] w-full" />
            <Skeleton className="h-[500px] w-full" />
            <Skeleton className="h-[500px] w-full" />
          </div>
        </section>
        <Skeleton className="h-[400px] w-full" />
      </div>
    </div>
  );
}
