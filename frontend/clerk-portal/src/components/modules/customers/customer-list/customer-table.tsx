"use client";

import { SearchInput } from "@/components/_common/search-input";
import PaginatedTable from "@/components/_common/tables/paginated-table";
import { Skeleton } from "@/components/ui/skeleton";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { getCustomers } from "@/lib/api/customer";
import { Lightbulb } from "@arthursenno/lizenzero-ui-react/Icon";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import OrderFilter, { ORDER_FILTERS } from "../components/filters/order-filter";
import StatusFilter, { StatusFilterType } from "../components/filters/status-filter";
import { useCustomersTableColumns } from "./customer-table-columns";
import AsyncPaginatedTable from "@/components/_common/async-paginated-table";

const STATUS_FILTERS: StatusFilterType[] = [
  { label: "All", value: "ALL" },
  { label: "Active", value: "ACTIVE" },
  { label: "Terminated", value: "TERMINATED" },
] as const;

export function CustomerTable() {
  const columns = useCustomersTableColumns();

  const { paramValues, changeParam, changeParams } = useQueryFilter(["order", "status", "search", "page"]);

  const search = paramValues.search || undefined;
  const status = (paramValues.status || STATUS_FILTERS[0]?.value) as "ACTIVE" | "TERMINATED";
  const order = (paramValues.order || ORDER_FILTERS[0]?.value) as "ASC" | "DESC" | "LAST_MODIFIED" | "FIRST_MODIFIED";
  const page = paramValues.page ? Number(paramValues.page) : 1;

  const { data: customers, isLoading } = useQuery({
    queryKey: ["customers", { page, limit: 10, search, status, order }],
    queryFn: async () => {
      const response = await getCustomers({
        page,
        limit: 10,
        search,
        status,
        order,
      });

      return response;
    },
  });

  const onSearch = (value: string) => {
    changeParams({ page: "1", search: value });
  };

  return (
    <div className="w-full h-auto bg-tonal-cream-96 p-5 md:p-8 rounded-2xl flex flex-col gap-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="col-span-1">
          <SearchInput queryName="search" onSearch={onSearch} />
        </div>
        <div className="col-span-1 lg:col-span-2 flex gap-3 items-center justify-end">
          <OrderFilter />
          <div className="border-[1px] h-6 border-tonal-blue-40"></div>
          <StatusFilter filters={STATUS_FILTERS} />
        </div>
      </div>
      <AsyncPaginatedTable
        columns={columns}
        currentPage={page}
        isLoading={isLoading}
        data={customers?.customers || []}
        pages={customers?.pages || 0}
        onPageChange={(page) => {
          changeParam("page", page.toString());
        }}
        noResultsMessage={search ? `No results for "${search}" in Customer List` : "No customers found"}
      />
    </div>
  );
}

export function CustomerTableSkeleton() {
  return (
    <ul className="space-y-4">
      <Skeleton className="w-full h-12 rounded-tl-3xl rounded-tr-3xl" />
      {Array.from({ length: 6 }).map((_, idx) => (
        <li key={idx} className="w-full flex items-start justify-between gap-6">
          <div className="w-full space-y-1">
            <Skeleton className="w-full h-4" />
            <Skeleton className="w-1/4 h-2" />
          </div>
          <Skeleton className="size-6 flex-shrink-0 rounded-full" />
          <div className="w-full space-y-1">
            <Skeleton className="w-full h-4" />
            <Skeleton className="w-1/2 h-2" />
          </div>
          <Skeleton className="w-full h-4" />
          <Skeleton className="w-full h-4" />
          <Skeleton className="w-full h-4" />
          <div className="w-full flex items-center gap-1">
            <Skeleton className="size-3" />
            <Skeleton className="w-full h-4" />
          </div>
        </li>
      ))}
    </ul>
  );
}
