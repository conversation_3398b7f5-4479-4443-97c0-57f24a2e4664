"use client";

import PaginatedTable from "@/components/_common/tables/paginated-table";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Clear, East, Lightbulb } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import Image from "next/image";
import { useEffect, useState } from "react";
import { useClientsCheckColumns } from "@/hooks/table-columns/customers/customersCheckColumns";
import { useTranslations } from "next-intl";
import { useInfoAddClient } from "../add-client-context";

const CheckCustomerDataModal = ({
  isOpen,
  onClickConfirmation,
  onOpenChange,
}: {
  isOpen: boolean;
  onClickConfirmation: () => void;
  onOpenChange?: () => void;
}) => {
  const t = useTranslations("AddNewClient");
  const c = useTranslations("common");
  const { infoClientsList } = useInfoAddClient();

  const columns = useClientsCheckColumns();

  return (
    <Modal
      open={isOpen}
      className="z-50 w-full"
      style={{ maxWidth: "1200px", borderRadius: "52px", maxHeight: "100vh", backgroundColor: "#F0F0EF" }}
      onOpenChange={onOpenChange}
    >
      <div className="p-5 max-h-[85vh] overflow-auto ">
        <div className="flex flex-row w-full justify-between">
          <div className="flex flex-col  gap-2 justify-start">
            <p className="text-[28px] text-primary font-bold">{t("newClients")}</p>
          </div>
          <div className="flex justify-end">
            <button onClick={onOpenChange}>
              <Clear className="size-6 fill-primary" />
            </button>
          </div>
        </div>

        <div className="mt-2">
          <p className="text-paragraph-regular text-tonal-dark-cream-20">{t("confirmInformations")}</p>
        </div>

        {infoClientsList.length === 0 ? (
          <div className="flex flex-col items-center justify-center mt-8">
            <Lightbulb className="size-10 fill-tonal-dark-cream-40" />
          </div>
        ) : (
          <PaginatedTable data={infoClientsList as any} columns={columns} />
        )}

        <div className=" flex justify-end">
          <Button trailingIcon={<East />} color="yellow" variant="filled" size="medium" onClick={onClickConfirmation}>
            {c("confirm")}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

const ConfirmCustomerDataModal = ({ isOpen, onOpenChange }: { isOpen: boolean; onOpenChange?: () => void }) => {
  const t = useTranslations("AddNewClient");
  const c = useTranslations("common");
  return (
    <Modal
      open={isOpen}
      className="z-50 w-full"
      style={{ maxWidth: "600px", borderRadius: "52px", maxHeight: "100vh", backgroundColor: "#F0F0EF" }}
      onOpenChange={onOpenChange}
    >
      <div className="p-5 max-h-[75vh] overflow-auto ">
        <div className="flex flex-row w-full justify-end">
          <div className="flex justify-end">
            <button onClick={onOpenChange}>
              <Clear className="size-6 fill-primary" />
            </button>
          </div>
        </div>

        <div className="flex gap-2 justify-start items-center mt-2">
          <Image width={35} height={35} src={`/assets/images/leaf_seal.png`} alt={"Leaf Seal"} />
          <p className="text-[28px] text-primary font-bold">{t("clientAdded")}</p>
        </div>

        <div className=" flex justify-end mt-10">
          <Button color="yellow" variant="filled" size="medium" onClick={onOpenChange}>
            {c("close")}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export const FlowConfirmCustomersListDataModal = () => {
  const { infoClientsList, setInfoClientsList } = useInfoAddClient();

  const [isOpenCheckModal, setIsOpenCheckModal] = useState(Boolean(infoClientsList.length !== 0));
  const [isOpenConfirmModal, setIsOpenConfirmModal] = useState(false);

  const handleConfirmationData = () => {
    setInfoClientsList([]);
    setIsOpenCheckModal(false);
    setIsOpenConfirmModal(true);
  };

  useEffect(() => {
    if (infoClientsList.length !== 0) {
      setIsOpenCheckModal(true);
      setIsOpenConfirmModal(false);
    }
  }, [infoClientsList]);

  return (
    <>
      <CheckCustomerDataModal
        isOpen={isOpenCheckModal}
        onOpenChange={() => setIsOpenCheckModal(false)}
        onClickConfirmation={handleConfirmationData}
      />
      <ConfirmCustomerDataModal isOpen={isOpenConfirmModal} onOpenChange={() => setIsOpenConfirmModal(false)} />
    </>
  );
};
