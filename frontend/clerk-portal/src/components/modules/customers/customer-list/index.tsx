"use client";

import Breadcrumb from "@/components/_common/breadcrumb/breadcrumb";
import Container from "@/components/_common/container/container";
import { TitleAndSubTitle } from "@/components/_common/title-and-subtitle";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Add } from "@arthursenno/lizenzero-ui-react/Icon";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { ConfirmCustomerDataModal } from "./confirm-customer-data-modal";
import { FlowConfirmCustomersListDataModal } from "./confirm-customers-list-data-modal";
import { CustomerTable } from "./customer-table";

const breadcrumbPaths = [
  { label: "Dashboard", href: "/" },
  { label: "Customers List", href: "" },
];

export function CustomerList() {
  const t = useTranslations("AddNewClient");
  const { paramValues, deleteParam } = useQueryFilter(["client-added"]);

  const isConfirmModalOpen = paramValues["client-added"] === "true";

  const handleCloseConfirmModal = () => {
    deleteParam("client-added");
  };

  return (
    <Container>
      <Breadcrumb paths={breadcrumbPaths} />
      <div className="flex flex-col mb-4 md:flex-row md:mb-0 justify-between">
        <TitleAndSubTitle
          icon={false}
          title={t("customerList")}
          subText="Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit. "
        />
        <Link href="/customers/add-new-client">
          <Button color="yellow" variant="filled" size="small" leadingIcon={<Add />}>
            {t("title")}
          </Button>
        </Link>
      </div>
      <CustomerTable />
      <ConfirmCustomerDataModal isOpen={isConfirmModalOpen} onOpenChange={handleCloseConfirmModal} />
      <FlowConfirmCustomersListDataModal />
    </Container>
  );
}
