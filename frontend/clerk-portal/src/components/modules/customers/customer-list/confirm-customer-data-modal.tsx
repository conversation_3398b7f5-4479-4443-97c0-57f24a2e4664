"use client";

import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Clear } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import Image from "next/image";
import { useTranslations } from "next-intl";

export const ConfirmCustomerDataModal = ({ isOpen, onOpenChange }: { isOpen: boolean; onOpenChange?: () => void }) => {
  const t = useTranslations("AddNewClient");
  const c = useTranslations("common");
  return (
    <Modal
      open={isOpen}
      className="z-50 w-full"
      style={{ maxWidth: "600px", borderRadius: "52px", maxHeight: "100vh", backgroundColor: "#F0F0EF" }}
      onOpenChange={onOpenChange}
    >
      <div className="p-5 max-h-[75vh] overflow-auto ">
        <div className="flex flex-row w-full justify-end">
          <div className="flex justify-end">
            <button onClick={onOpenChange}>
              <Clear className="size-6 fill-primary" />
            </button>
          </div>
        </div>

        <div className="flex gap-2 justify-start items-center mt-2">
          <Image width={35} height={35} src={`/assets/images/leaf_seal.png`} alt={"Leaf Seal"} />
          <p className="text-[28px] text-primary font-bold">{t("clientAdded")}</p>
        </div>

        <div className=" flex justify-end mt-10">
          <Button color="yellow" variant="filled" size="medium" onClick={onOpenChange}>
            {c("close")}
          </Button>
        </div>
      </div>
    </Modal>
  );
};
