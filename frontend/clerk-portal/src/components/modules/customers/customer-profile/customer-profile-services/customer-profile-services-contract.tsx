"use client";

import { CountryIcon } from "@/components/_common/country-icon";
import Status, { StatusType } from "@/components/modules/country/components/task-status";
import { cn } from "@/lib/utils";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Error, KeyboardArrowRight, Lightbulb } from "@arthursenno/lizenzero-ui-react/Icon";
import { ListCustomer } from "@/lib/api/customer/types";
import { ActionGuide, Contract, ContractType } from "@/lib/api/contracts/types";
import { useRouter } from "@/i18n/navigation";
import { License } from "@/lib/api/license/types";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useTranslations } from "next-intl";
import { formatDate } from "@/utils/format-date";

export const formatContractStatus = (status: string): StatusType => {
  switch (status) {
    case "ACTIVE":
      return "Active";
    case "TERMINATION_PROCESS":
      return "Termination in progress";
    case "TERMINATED":
      return "Terminated";
    default:
      return status as StatusType;
  }
};

function getContractTitle(contract: Contract) {
  if (contract.type === "ACTION_GUIDE") {
    return "Action Guide";
  }

  if (contract.type === "DIRECT_LICENSE") {
    return "Direct License";
  }

  return "Licensing Services";
}

interface CustomerProfileServicesContractProps {
  customer: ListCustomer;
  contractType: ContractType;
  licenseYear: number;
}

export function CustomerProfileServicesContract({
  customer,
  contractType,
  licenseYear,
}: CustomerProfileServicesContractProps) {
  const t = useTranslations("CustomerProfileServicesContract");
  const c = useTranslations("common");
  const { changeParam } = useQueryFilter(["termination-id"]);

  const contract = customer.contracts?.find((contract) => contract.type === contractType);

  if (!contract) return null;

  const items =
    contractType === "ACTION_GUIDE"
      ? contract.action_guides
      : contract.licenses.filter((license) => license.year === licenseYear);

  return (
    <div className="flex flex-col gap-6">
      <div className="flex items-center justify-between mt-14">
        <p
          className={cn(`text-[20px] font-bold`, {
            "text-error": !!contract.termination,
            "text-[#183362]": !contract.termination,
          })}
        >
          {getContractTitle(contract)}
        </p>
      </div>

      {contract.termination?.status === "REQUESTED" && (
        <div className={cn("h-[90px] bg-tonal-red-90 rounded-[20px] flex items-center justify-between py-5 px-4")}>
          <div className="flex items-center gap-2">
            <Error className="size-6 fill-error mb-7" />
            <div className="flex flex-col gap-1">
              <p className="text-paragraph-regular font-bold text-error">{t("contractTerminationRequest")}</p>
              <p className="text-small-paragraph-regular text-error">{t("contractTerminationDescription")}</p>
            </div>
            <Button
              size="small"
              variant="filled"
              color="red"
              onClick={() => changeParam("termination-id", String(contract.termination?.id))}
            >
              {c("check")}
            </Button>
          </div>
        </div>
      )}

      {contract.termination?.status === "PENDING" && (
        <div className={cn("h-[90px] bg-tonal-red-90 rounded-[20px] flex items-center justify-between py-5 px-4")}>
          <div className="flex items-center gap-2">
            <Error className="size-6 fill-error mb-7" />
            <div className="flex flex-col gap-1">
              <p className="text-paragraph-regular font-bold text-error">{t("terminationCasePending")}</p>
              <p className="text-small-paragraph-regular text-error">{t("terminationCasePendingDescription")}</p>
            </div>
          </div>
        </div>
      )}

      {contract.termination?.status === "COMPLETED" && new Date(contract.end_date) < new Date() && (
        <div className={cn("h-[90px] bg-[#EDE9E4] rounded-[20px] flex items-center justify-between py-5 px-4")}>
          <div className="flex items-center gap-2">
            <Error className="size-6 fill-primary mb-7" />
            <div className="flex flex-col gap-1">
              <p className="text-paragraph-regular font-bold text-primary">{t("terminationProcessConclude")}</p>
              <p className="text-small-paragraph-regular text-primary">
                {t("terminationProcessConcludeDescription")}{" "}
                <span className="font-bold">{formatDate(contract.end_date)}</span>
              </p>
            </div>
          </div>
        </div>
      )}

      {contract.termination?.status === "COMPLETED" && new Date(contract.end_date) > new Date() && (
        <div className={cn("h-[90px] bg-[#EDE9E4] rounded-[20px] flex items-center justify-between py-5 px-4")}>
          <div className="flex items-center gap-2">
            <Error className="size-6 fill-error mb-7" />
            <div className="flex flex-col gap-1">
              <p className="text-paragraph-regular font-bold text-error">{t("terminationCaseComplete")}</p>
              <p className="text-small-paragraph-regular text-primary">
                {t("terminationCaseCompleteDescription")}{" "}
                <span className="font-bold">{formatDate(contract.end_date)}</span>
              </p>
            </div>
          </div>
        </div>
      )}

      {!!items.length ? (
        items.map((item) => <CustomerProfileServicesContractItem key={item.id} contract={contract} item={item} />)
      ) : (
        <div className="h-[160px] bg-tonal-dark-cream-96 rounded-xl flex flex-col items-center justify-center gap-3">
          <Lightbulb className="size-8 fill-tonal-dark-cream-40" />
          <p className="text-small-paragraph-regular text-tonal-dark-cream-40">
            {t("noCountriesForLicensingServices")}
          </p>
        </div>
      )}
    </div>
  );
}

interface CustomerProfileServicesContractItemProps {
  contract: Contract;
  item: License | ActionGuide;
}

function CustomerProfileServicesContractItem({ contract, item }: CustomerProfileServicesContractItemProps) {
  const { changeParam } = useQueryFilter(["termination-id"]);
  const router = useRouter();

  function handleClickItem() {
    if (item.termination?.status === "REQUESTED") {
      return changeParam("termination-id", String(item.termination.id));
    }

    router.push(`/customers/${contract.customer_id}/${item.country_code}`);
  }
  const t = useTranslations("CustomerProfileServicesContract");
  const c = useTranslations("common");
  return (
    <div
      key={item.id}
      onClick={handleClickItem}
      className={cn("h-[80px] bg-white rounded-[20px] flex items-center justify-between py-5 px-4 cursor-pointer", {
        "border-[1px] border-error": !!item.termination && item.termination.status !== "COMPLETED",
      })}
    >
      <div className="flex items-center gap-3">
        <CountryIcon country={{ flag: item.country_flag, name: item.country_name }} className="size-10" />
        <p className="text-large-paragraph-regular font-bold text-primary">{item.country_name}</p>
      </div>
      <div className="flex items-center gap-3">
        <div className="flex flex-col gap-2 text-right items-end">
          <Status status={formatContractStatus(item.contract_status)} />

          {item.termination?.status === "REQUESTED" && (
            <p className="text-error text-xs underline underline-offset-2">
              {t("terminationRequestedOn")} {new Date(item.termination.requested_at || 0).toLocaleDateString()}
            </p>
          )}
          {item.termination?.status === "PENDING" && (
            <p className="text-error text-xs underline underline-offset-2">{t("terminationPendingOnDualSystem")}</p>
          )}
          {item.termination?.status === "COMPLETED" && new Date(contract.end_date || 0) < new Date() && (
            <p className="text-primary text-xs underline underline-offset-2">
              {t("contractTerminationOn")} {new Date(contract.end_date || 0).toLocaleDateString()}
            </p>
          )}
          {item.termination?.status === "COMPLETED" && new Date(contract.end_date || 0) > new Date() && (
            <p className="text-primary text-xs underline underline-offset-2">
              {t("contractTerminationWillBeIn")} {new Date(contract.end_date || 0).toLocaleDateString()}
            </p>
          )}
        </div>
        <KeyboardArrowRight className="size-7 fill-[#808FA9]" />
      </div>
    </div>
  );
}
