"use client";

import { ListCustomer } from "@/lib/api/customer/types";
import { FilterAlt, KeyboardArrowDown } from "@arthursenno/lizenzero-ui-react/Icon";
import { useRouter } from "@/i18n/navigation";
import { useState } from "react";

import { Dropdown } from "@/components/_common/dropdown";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { CustomerProfileServicesContract } from "./customer-profile-services-contract";
import { useTranslations } from "next-intl";

export interface CustomerProfileServicesProps {
  customer: ListCustomer;
}

export function CustomerProfileServices({ customer }: CustomerProfileServicesProps) {
  const router = useRouter();

  //Contracts
  const euLicenseContract = customer.contracts?.find((contract) => contract.type === "EU_LICENSE");
  const directLicenseContract = customer.contracts?.find((contract) => contract.type === "DIRECT_LICENSE");

  const licenseYears = (() => {
    const euLicenseYears = euLicenseContract?.licenses.map((license) => license.year) || [];
    const directLicenseYears = directLicenseContract?.licenses.map((license) => license.year) || [];
    return Array.from(new Set([...euLicenseYears, ...directLicenseYears]));
  })();

  const [selectedLicenseYear, setSelectedLicenseYear] = useState<number | null>(licenseYears[0]);
  const c = useTranslations("common");
  return (
    <div className="p-6 bg-tonal-cream-96 rounded-3xl w-full h-full">
      <div className="flex items-center justify-between">
        <p className="text-title-3 font-bold text-[#183362]">{c("countries")}</p>
        <Dropdown
          trigger={
            <button className="flex items-center text-support-blue font-bold text-base gap-2">
              <FilterAlt className="size-5 fill-support-blue" />
              <span className="text-left">{selectedLicenseYear}</span>
              <KeyboardArrowDown className="size-5 fill-support-blue" />
            </button>
          }
        >
          {licenseYears.map((year, idx) => (
            <DropdownMenu.Item
              key={idx}
              className="group py-5 px-4 text-base focus:outline-none cursor-pointer hover:bg-surface-02"
              onClick={() => setSelectedLicenseYear(year)}
              style={{
                color: year === selectedLicenseYear ? "#002652" : "#242423",
                fontWeight: year === selectedLicenseYear ? "bold" : "normal",
              }}
            >
              {year}
            </DropdownMenu.Item>
          ))}
        </Dropdown>
      </div>

      {selectedLicenseYear && (
        <CustomerProfileServicesContract
          customer={customer}
          contractType="EU_LICENSE"
          licenseYear={selectedLicenseYear}
        />
      )}
      {selectedLicenseYear && (
        <CustomerProfileServicesContract
          customer={customer}
          contractType="ACTION_GUIDE"
          licenseYear={selectedLicenseYear}
        />
      )}
      {selectedLicenseYear && (
        <CustomerProfileServicesContract
          customer={customer}
          contractType="DIRECT_LICENSE"
          licenseYear={selectedLicenseYear}
        />
      )}

      <div className="flex justify-center mt-8">
        <p className="text-small-paragraph-regular text-tonal-dark-cream-50">{c("end")}</p>
      </div>
    </div>
  );
}
