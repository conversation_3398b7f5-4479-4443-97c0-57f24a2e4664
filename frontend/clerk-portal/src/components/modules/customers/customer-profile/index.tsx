"use client";

import { useRouter } from "@/i18n/navigation";
import { useState } from "react";

import Breadcrumb from "@/components/_common/breadcrumb/breadcrumb";
import Container from "@/components/_common/container/container";
import { ClaimManagement } from "@/lib/api/claim-management/types";
import { formatCustomerNumber } from "@/utils/format-customer-number";

import { StatusFilterType } from "../components/filters/status-filter";
import ClaimManagementSection from "./claim-management";
import { CustomerProfileCompany } from "./customer-profile-company";
import { CustomerProfileContact } from "./customer-profile-contact";
import { CustomerProfileContract } from "./customer-profile-contract";
import CustomerProfileHeader from "./customer-profile-header";
import { CustomerProfileServicesControl } from "./customer-profile-services-control";
import { CustomerProfileTasks } from "./customer-profile-tasks";
import { useCustomer } from "./use-customer";
import { Skeleton } from "@/components/ui/skeleton";
import { CustomerProfileServices } from "./customer-profile-services";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { CustomerTermination } from "../components/termination/customer-contract-termination";

const STATUS_FILTERS: StatusFilterType[] = [
  { label: "2024", value: "2024" },
  { label: "2025", value: "2025" },
];

const ORDER_FILTERS: StatusFilterType[] = [
  { label: "ALL", value: "all" },
  { label: "2024", value: "2024" },
  { label: "2025", value: "2025" },
];

export function CustomerProfile() {
  const { paramValues } = useQueryFilter(["termination-id"]);

  const terminationId = paramValues?.["termination-id"];

  const [claimManagement, setClaimManagement] = useState<ClaimManagement[]>([]);
  const [filteredClaims, setFilteredClaims] = useState<ClaimManagement[]>(claimManagement);
  const [searchTerm, setSearchTerm] = useState("");

  const { customerId, customer, isLoading, refetch } = useCustomer();

  const paths = [
    { label: "Customers", href: "/customers" },
    { label: `Customer #${formatCustomerNumber(customerId)}`, href: "" },
  ];

  return (
    <Container className="bg-white pb-16">
      <Breadcrumb paths={paths} />
      {!customer && <CustomerProfileSkeleton />}
      {customer && (
        <>
          <CustomerProfileHeader customer={customer} />
          {terminationId && <CustomerTermination />}
          {!terminationId && (
            <div className="flex flex-col gap-6 mb-6">
              <section className="grid grid-cols-1 lg:grid-cols-5 gap-6">
                <div className="col-span-1 lg:col-span-3">
                  <CustomerProfileServices customer={customer} />
                </div>
                <div className="col-span-1 lg:col-span-2 grid grid-cols-1 gap-6">
                  <CustomerProfileContract customer={customer} />
                  <CustomerProfileContact customer={customer} refetch={refetch} />
                  <CustomerProfileCompany customer={customer} refetch={refetch} />
                </div>
              </section>
              <ClaimManagementSection
                filteredClaims={filteredClaims}
                setFilteredClaims={setFilteredClaims}
                searchTerm={searchTerm}
                setSearchTerm={setSearchTerm}
                claimManagement={claimManagement}
              />
              <section className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-5 gap-6">
                <div className="col-span-1 md:col-span-2">
                  <CustomerProfileServicesControl customer={customer} refetch={refetch} />
                </div>
                <div className="col-span-1 md:col-span-2 lg:col-span-3 grid grid-cols-1 gap-6">
                  <CustomerProfileTasks customerId={customerId} />
                </div>
              </section>
            </div>
          )}
        </>
      )}
    </Container>
  );
}

function CustomerProfileSkeleton() {
  return (
    <div className="space-y-14 my-8">
      <div className="space-y-6">
        <Skeleton className="h-16 w-full lg:w-1/2" />
        <Skeleton className="h-5 w-full lg:w-1/3" />
      </div>
      <div className="flex flex-col gap-6 mb-6">
        <section className="grid grid-cols-1 lg:grid-cols-5 gap-6">
          <div className="col-span-1 lg:col-span-3">
            <Skeleton className="h-full w-full" />
          </div>
          <div className="col-span-1 lg:col-span-2 grid grid-cols-1 gap-6">
            <Skeleton className="h-[200px] w-full" />
            <Skeleton className="h-[200px] w-full" />
            <Skeleton className="h-[200px] w-full" />
          </div>
        </section>
        <Skeleton className="h-[400px] w-full" />
        <section className="grid grid-cols-1 md:grid-cols-4 xl:grid-cols-5 gap-6">
          <div className="col-span-1 md:col-span-2 xl:col-span-2">
            <Skeleton className="h-[400px] w-full" />
          </div>
          <div className="col-span-1 md:col-span-2 xl:col-span-3 grid grid-cols-1 gap-6">
            <Skeleton className="h-[400px] w-full" />
          </div>
        </section>
      </div>
    </div>
  );
}
