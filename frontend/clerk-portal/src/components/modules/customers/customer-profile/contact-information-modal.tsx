import { ListCustomer } from "@/lib/api/customer/types";
import { formatCustomerNumber } from "@/utils/format-customer-number";
import { Clear } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { usePathname, useRouter } from "@/i18n/navigation";
import { useSearchParams } from "next/navigation";

import { useTranslations } from "next-intl";

interface ContactInformationModalProps {
  customer: ListCustomer;
}

export function ContactInformationModal({ customer }: ContactInformationModalProps) {
  const t = useTranslations("CustomerProfileContract");
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();

  const selectedCustomerId = Number(searchParams.get("customer_id"));

  const isOpen = !!selectedCustomerId && selectedCustomerId === customer.id;

  function handleOpenChange(open: boolean) {
    if (open) return;

    const params = new URLSearchParams(searchParams.toString());
    params.delete("customer_id");
    router.push(`${pathname}?${params.toString()}`, { scroll: false });
  }

  return (
    <Modal
      open={!!isOpen}
      className="!z-50 !rounded-[52px] !bg-tonal-dark-cream-90 !p-9 !pb-16 !w-full !max-w-[850px]"
      onOpenChange={handleOpenChange}
    >
      <div className="flex items-center justify-end">
        <button
          onClick={() => handleOpenChange(false)}
          className="bg-white rounded-full p-1 hover:bg-tonal-dark-cream-80 transition-colors duration-300"
        >
          <Clear className="size-4 fill-primary" />
        </button>
      </div>
      <div className="flex flex-row w-full justify-between">
        <div className="flex flex-col gap-2 justify-start">
          <p className="text-[28px] text-primary font-bold">{t("contact")}</p>
        </div>
      </div>
      <div className="mt-2">
        <p className="text-paragraph-regular text-tonal-dark-cream-20">
          {t("contactList")} {customer?.companies?.[0]?.name}
        </p>
      </div>
      <div className="mt-8 bg-white rounded-xl w-full grid grid-cols-2">
        <div className="flex flex-col gap-2 p-4">
          <p className="text-tonal-dark-cream-30 text-sm">{t("name")}</p>
          <p className="text-primary">
            {customer?.first_name} {customer?.last_name}
          </p>
        </div>
        <div className="flex flex-col gap-2 p-4">
          <p className="text-tonal-dark-cream-30 text-sm">{t("customerNumber")}</p>
          <p className="text-primary">#{formatCustomerNumber(customer?.id)}</p>
        </div>
        <div className="flex flex-col gap-2 p-4">
          <p className="text-tonal-dark-cream-30 text-sm">{t("email")}</p>
          <p className="text-primary">{customer?.email}</p>
        </div>
        <div className="flex flex-col gap-2 p-4">
          <p className="text-tonal-dark-cream-30 text-sm">{t("phoneMobile")}</p>
          <p className="text-primary">{customer?.phones[0]?.phone_number}</p>
        </div>
      </div>
    </Modal>
  );
}
