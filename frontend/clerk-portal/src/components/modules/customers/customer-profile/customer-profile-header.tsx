"use client";

import { CountryIcon } from "@/components/_common/country-icon";
import { getServiceType, getStatusLabel } from "@/components/modules/customers/customer-list/customer-table-columns";
import { ListCustomer } from "@/lib/api/customer/types";
import { formatCustomerNumber } from "@/utils/format-customer-number";
import { Elipse } from "@arthursenno/lizenzero-ui-react/Icon";
import { useTranslations } from "next-intl";

interface CustomerSectionProps {
  customer: ListCustomer;
}

export default function CustomerProfileHeader({ customer }: CustomerSectionProps) {
  const t = useTranslations("CustomerProfileHeader");
  const countries = customer.contracts.reduce(
    (acc, contract) => {
      contract.licenses.forEach((license) => {
        if (acc.find((country) => country.country_code === license.country_code)) return;
        acc.push({
          country_code: license.country_code,
          country_name: license.country_name,
          country_flag: license.country_flag,
        });
      });

      contract.action_guides.forEach((actionGuide) => {
        if (acc.find((country) => country.country_code === actionGuide.country_code)) return;

        acc.push({
          country_code: actionGuide.country_code,
          country_name: actionGuide.country_name,
          country_flag: actionGuide.country_flag,
        });
      });

      return acc;
    },
    [] as { country_code: string; country_name: string; country_flag: string }[]
  );

  const customerStatus = (() => {
    for (const contract of customer.contracts) {
      if (contract.status !== "ACTIVE") return contract.status;

      if (contract.licenses) {
        for (const license of contract.licenses) {
          if (license.contract_status !== "ACTIVE") return license.contract_status;
        }
      }

      if (contract.action_guides) {
        for (const actionGuide of contract.action_guides) {
          if (actionGuide.contract_status !== "ACTIVE") return actionGuide.contract_status;
        }
      }
    }

    return "ACTIVE";
  })();

  const customerStatusLabel = getStatusLabel(customerStatus);

  const serviceType = (() => {
    const result = customer.contracts.reduce((acc, contract) => acc + getServiceType(contract) + "; ", "");

    if (customer.contracts.length > 1) return result;

    return result.replace(";", "");
  })();

  return (
    <section className="mt-8 mb-14">
      <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
        <h1 className="text-2xl md:text-5xl text-primary font-bold">
          {customer.companies[0]?.name} #{formatCustomerNumber(customer.id)}
        </h1>
        <div className="flex flex-col items-start gap-1">
          <p className="text-small-paragraph-regular text-tonal-dark-cream-30">{t("countries")}</p>
          <div className="flex flex-row gap-3 items-center">
            {countries.map((country) => (
              <CountryIcon
                key={country.country_code}
                country={{ flag: country.country_flag, name: country.country_name }}
              />
            ))}
            <p className="text-small-paragraph-regular text-tonal-dark-cream-30">({countries.length})</p>
          </div>
        </div>
      </div>
      <div className="flex flex-col md:flex-row items-start md:items-center gap-3 mt-6">
        <p className="text-tonal-dark-cream-30">
          {t("enrolledIn")}:{" "}
          {customer.created_at ? new Date(customer.created_at).toLocaleDateString().replaceAll("/", ".") : "-"}
        </p>
        <div className="hidden md:block border-[1px] border-solid border-tonal-dark-cream-80 h-[20px]"></div>
        <p className="text-tonal-dark-cream-30">
          {t("serviceType")}: {serviceType}
        </p>
        <div className="hidden md:block border-[1px] border-solid border-tonal-dark-cream-80 h-[20px]"></div>
        <div className="text-tonal-dark-cream-30 flex items-center gap-2">
          {t("status")}:
          <div
            data-status={customerStatus}
            className="text-sm group flex items-center gap-1 text-tonal-dark-cream-30 data-[status=ACTIVE]:text-success"
          >
            <Elipse className="fill-tonal-dark-cream-30 group-data-[status=ACTIVE]:fill-success size-3" />
            <p className="text-success">{customerStatusLabel}</p>
          </div>
        </div>
      </div>
    </section>
  );
}
