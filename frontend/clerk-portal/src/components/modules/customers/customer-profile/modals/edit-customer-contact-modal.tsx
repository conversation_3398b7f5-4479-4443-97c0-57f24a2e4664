"use client";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Clear, Delete, KeyboardArrowRight } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { zodResolver } from "@hookform/resolvers/zod";
import Image from "next/image";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import { z } from "zod";

import { Separator } from "@/components/_common/separator";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { updateCustomerContactInfo } from "@/lib/api/customer";
import { cn } from "@/lib/utils";
import { useCustomer } from "../../customer-profile/use-customer";
import { SPECIAL_CHARS_NUMERIC_REGEX } from "@/utils/regex";
import { RightCheckIcon } from "@/components/ui/right-check-icon";
import { enqueueSnackbar } from "notistack";
import { PhoneInput } from "@/components/_common/forms/phone-input";
import { useTranslations } from "next-intl";

function useEditCustomerContactModal() {
  const c = useTranslations("common");
  const t = useTranslations("EditCustomerContactModal");

  const editContactInfoFormSchema = z.object({
    fullName: z.string().min(1, t("required")).regex(SPECIAL_CHARS_NUMERIC_REGEX, t("noSpecial")),
    email: z.string().email(),
    emails: z.array(z.object({ email: z.string().email(t("invalidEmail")).min(1, t("required")) })),
    phones: z.array(z.string().trim().min(1, t("required"))),
  });

  return {
    c,
    t,
    editContactInfoFormSchema,
  };
}

type EditContactInfoForm = z.infer<ReturnType<typeof useEditCustomerContactModal>["editContactInfoFormSchema"]>;

export function EditCustomerContactModal({ onSubmit }: { onSubmit?: () => void }) {
  const { c, t, editContactInfoFormSchema } = useEditCustomerContactModal();
  const params = useParams();

  const customerId = params.customerId;
  const { paramValues, deleteParam } = useQueryFilter(["edit-contact"]);

  const isOpen = paramValues["edit-contact"] === "true";

  const { customer, refetch } = useCustomer();

  const [isSuccess, setIsSuccess] = useState(false);

  const form = useForm<EditContactInfoForm>({
    resolver: zodResolver(editContactInfoFormSchema),
    values: {
      fullName: (customer?.first_name || "") + " " + (customer?.last_name || ""),
      emails: [],
      email: customer?.email || ``,
      phones: customer?.phones?.map((phone) => phone.phone_number) || [],
    },
    mode: `onBlur`,
  });

  const hasError = !!Object.values(form?.formState?.errors).length;

  const emailsFieldArray = useFieldArray({
    control: form.control,
    name: "emails",
  });

  const phonesFieldArray = useFieldArray<any>({
    name: "phones",
    control: form.control,
  });

  useEffect(() => {
    if (!isOpen) {
      setIsSuccess(false);
      form.reset();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen]);

  function handleCloseModal() {
    deleteParam("edit-contact");
  }

  function handleOnOpenChange(open: boolean) {
    if (!open) handleCloseModal();
  }

  async function handleSubmit(values: EditContactInfoForm) {
    if (!customerId) return;

    try {
      const payload = {
        fullName: values.fullName,
        // emails: values.emails.map(({ email }) => email),
        email: values.email,
        phones: values.phones.filter((phone) => phone),
        customerId: Number(customerId),
      };
      const res: any = await updateCustomerContactInfo(payload);

      if (!res.data) {
        const statusRes = res?.response?.status;

        if (statusRes === 409) {
          form.setError("email", {
            type: "manual",
            message: t("emailInUse"),
          });
        }

        enqueueSnackbar("An error occurred when trying to save the data", { variant: "error" });
        return;
      }

      setIsSuccess(true);
      refetch();
      onSubmit && onSubmit();
    } catch (error) {
      setIsSuccess(false);
    }
  }

  const isSubmitting = form.formState.isSubmitting;

  return (
    <Modal
      open={isOpen}
      data-success={isSuccess}
      onOpenChange={handleOnOpenChange}
      className={cn(
        "z-50 !rounded-[52px] w-full max-w-[672px] !py-8 !px-9 overflow-hidden !max-h-[90vh]",
        isSuccess ? `h-[35vh]` : `h-[90vh]`
      )}
    >
      <div className="flex flex-col h-full">
        <div className="flex items-center justify-end">
          <Clear onClick={handleCloseModal} className="w-8 h-8 fill-primary hover:opacity-75 cursor-pointer" />
        </div>
        {!isSuccess && (
          <div className="flex flex-col flex-1 overflow-y-auto pb-8">
            <div className="mb-10 space-y-4">
              <h3 className="font-bold text-[1.75rem] text-primary">{t("contactInformation")}</h3>
              <p className="text-tonal-dark-cream-20">{t("editContactInfo")}</p>
            </div>
            <div className="w-full h-full pl-1 pr-5 pb-8">
              <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
                <Controller
                  control={form.control}
                  name="fullName"
                  render={({ field, fieldState: { error } }) => {
                    const fieldValue = form.getValues("fullName");
                    const hasRightIcon =
                      !error && fieldValue && editContactInfoFormSchema.shape.fullName.safeParse(fieldValue).success;
                    return (
                      <div className="flex flex-col gap-1">
                        <Input
                          label={t("fullnameRequired")}
                          placeholder={t("fullNamePlaceholder")}
                          variant={error && "error"}
                          errorMessage={error && error.message}
                          rightIcon={hasRightIcon && <RightCheckIcon />}
                          {...field}
                        />
                      </div>
                    );
                  }}
                />

                <Separator />

                <Controller
                  control={form.control}
                  name={`email`}
                  render={({ field, fieldState: { error } }) => {
                    const fieldValue = form.getValues("email");
                    const hasRightIcon =
                      !error && fieldValue && editContactInfoFormSchema.shape.email.safeParse(fieldValue).success;

                    return (
                      <div className="flex flex-col gap-1">
                        <Input
                          label={t("emailRequired")}
                          placeholder={t("emailPlaceholder")}
                          variant={error && "error"}
                          errorMessage={error && error.message}
                          rightIcon={hasRightIcon && <RightCheckIcon />}
                          {...field}
                        />
                      </div>
                    );
                  }}
                />

                {emailsFieldArray.fields.map((field, index) => {
                  return (
                    <div key={field.id} className="flex items-end gap-2">
                      <Controller
                        control={form.control}
                        name={`emails.${index}.email`}
                        render={({ field, fieldState }) => (
                          <div className="flex flex-col gap-1 sm:min-w-72">
                            <Input
                              id="email"
                              type="email"
                              label={t("emailRequired")}
                              placeholder={t("emailPlaceholder")}
                              {...field}
                            />
                            {fieldState.error && <span className="text-error text-sm">{fieldState.error.message}</span>}
                          </div>
                        )}
                      />

                      {index > 0 && (
                        <Button
                          type="button"
                          variant="text"
                          color="dark-blue"
                          size="small"
                          onClick={() => emailsFieldArray.remove(index)}
                          className="mb-3.5 rounded-full"
                        >
                          <Delete className="size-5 fill-primary" />
                        </Button>
                      )}
                    </div>
                  );
                })}

                {/* <Button
                  type="button"
                  variant="text"
                  color="light-blue"
                  size="small"
                  onClick={() => emailsFieldArray.append({ email: "" })}
                >
                  Add new e-mail →
                </Button> */}

                <Separator />

                {phonesFieldArray.fields.map((field, index) => {
                  return (
                    <div key={field.id} className="flex items-end gap-2">
                      <Controller
                        control={form.control}
                        name={`phones.${index}`}
                        render={({ field, fieldState: { error } }) => (
                          <div className="flex flex-col gap-1 sm:min-w-72">
                            <div className="flex flex-col gap-2">
                              <label htmlFor="phone" className="text-primary">
                                {t("phoneMobile")}
                              </label>
                              <div className="flex items-center gap-3">
                                <PhoneInput
                                  isMenuDiv
                                  name={`phones.${index}`}
                                  defaultValue={field.value}
                                  valueSetter={(value) => {
                                    field.onChange(value || ``);
                                  }}
                                  errorSetter={(valid) =>
                                    valid
                                      ? form.clearErrors(`phones.${index}`)
                                      : form.setError(`phones.${index}`, { message: t("invalidPhone") })
                                  }
                                  isError={Boolean(!!error)}
                                />

                                {index > 0 && (
                                  <Button
                                    type="button"
                                    variant="text"
                                    color="dark-blue"
                                    size="small"
                                    onClick={() => phonesFieldArray.remove(index)}
                                    className="rounded-full"
                                  >
                                    <Delete className="size-5 fill-primary" />
                                  </Button>
                                )}
                              </div>
                              {!!error && (
                                <div className="flex justify-start items-center space-x-2 ">
                                  <span slot="errorMessage" className="font-centra text-sm text-tonal-red-40">
                                    {error?.message}
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      />
                    </div>
                  );
                })}

                <Button
                  type="button"
                  variant="text"
                  color="light-blue"
                  size="small"
                  onClick={() => phonesFieldArray.append("")}
                >
                  {t("addPhone")} →
                </Button>

                <div className="flex justify-end">
                  <Button
                    type="submit"
                    variant="filled"
                    color="yellow"
                    size="medium"
                    disabled={isSubmitting || hasError}
                    className="sm:min-w-52"
                  >
                    {isSubmitting ? c("saving") : c("save")} <KeyboardArrowRight className="size-5 fill-on-tertiary" />
                  </Button>
                </div>
              </form>
            </div>
          </div>
        )}
        {isSuccess && (
          <div className="h-full space-y-10 pt-8 pb-8">
            <div className="flex items-center gap-2">
              <Image alt="Leaf seal" src="/assets/images/leaf-seal.svg" width={35} height={36} />
              <h3 className="mt-1.5 flex-1 font-bold text-[1.75rem] text-primary">{t("informationUpdated")}</h3>
            </div>
            <div className="flex justify-end">
              <Button type="button" color="yellow" variant="filled" size="medium" onClick={handleCloseModal}>
                {c("close")}
              </Button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
}
