"use client";

import { Textarea } from "@/components/ui/textarea";
import { ClaimManagement } from "@/lib/api/claim-management/types";
import { formatCurrency } from "@/utils/format-currency";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { CheckCircle, Clear, Error as ErrorIcon, KeyboardArrowLeft, Plant } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { Control, Controller, UseFormHandleSubmit, useForm } from "react-hook-form";
import { z } from "zod";
import { useTranslations } from "next-intl";

function useRefundModal() {
  const t = useTranslations("RefundInvoiceModal");
  const c = useTranslations("common");

  const REFUND_TYPES = [t("dunning"), t("volumereports"), t("thirdPartyInvoices"), t("terminations")] as const;

  const ORIGINAL_PAYMENT_METHODS = [
    c("creditCard"),
    c("paypal"),
    c("bankTransfer"),
    c("klarna"),
    c("eps"),
    c("ideal"),
  ] as const;

  const refundFormSchema = z.object({
    refundType: z.enum(REFUND_TYPES, {
      required_error: t("selectType"),
    }),
    paymentMethod: z.enum(ORIGINAL_PAYMENT_METHODS, {
      required_error: t("selectPaymentMethod"),
    }),
    amount: z
      .string()
      .min(1, t("amountRequired"))
      .refine((val) => !isNaN(parseFloat(val)), t("mustBeValidNumber"))
      .refine((val) => parseFloat(val) > 0, t("amountMustBeGreaterThanZero")),
    noPayout: z.boolean().default(false),
    additionalInfo: z.string().optional(),
  });

  return {
    t,
    c,
    REFUND_TYPES,
    ORIGINAL_PAYMENT_METHODS,
    refundFormSchema,
  };
}

type RefundFormData = z.infer<ReturnType<typeof useRefundModal>["refundFormSchema"]>;

interface FormStepProps {
  control: Control<RefundFormData>;
  handleSubmit: UseFormHandleSubmit<RefundFormData>;
  errors: Record<string, any>;
  onNextStep: (data: RefundFormData) => void;
  handleAddNewBankAccount: () => void;
}

const FormStep = ({ control, handleSubmit, errors, onNextStep, handleAddNewBankAccount }: FormStepProps) => {
  const { t, c, REFUND_TYPES, ORIGINAL_PAYMENT_METHODS } = useRefundModal();

  return (
    <form onSubmit={handleSubmit(onNextStep)} className="flex flex-col gap-6 mt-4">
      <p className="text-paragraph-regular text-tonal-dark-cream-20">{t("provideInformation")}</p>

      <div className="flex flex-col text-primary gap-2">
        <span>{t("refundType")}</span>
        <Controller
          name="refundType"
          control={control}
          render={({ field }) => (
            <select
              {...field}
              className={`w-full bg-white p-4 rounded-2xl border ${
                errors.refundType ? "border-error" : "border-tonal-dark-cream-80 focus:border-primary"
              }`}
            >
              {REFUND_TYPES.map((type) => (
                <option key={type} value={type}>
                  {type}
                </option>
              ))}
            </select>
          )}
        />
        {errors.refundType && <span className="text-error text-sm">{errors.refundType.message}</span>}
      </div>

      <div className="flex flex-col text-primary gap-2">
        <span>{t("originalPaymentMethod")}</span>
        <Controller
          name="paymentMethod"
          control={control}
          render={({ field }) => (
            <select
              {...field}
              className={`md:w-1/2 bg-white p-4 rounded-2xl border ${
                errors.paymentMethod ? "border-error" : "border-tonal-dark-cream-80 focus:border-primary"
              }`}
            >
              {ORIGINAL_PAYMENT_METHODS.map((method) => (
                <option key={method} value={method}>
                  {method}
                </option>
              ))}
            </select>
          )}
        />
        {errors.paymentMethod && <span className="text-error text-sm">{errors.paymentMethod.message}</span>}
        <span onClick={handleAddNewBankAccount} className="font-medium cursor-pointer hover:underline">
          + {t("addNewBank")}
        </span>
      </div>

      <Controller
        name="amount"
        control={control}
        render={({ field }) => (
          <div className="flex flex-col gap-1">
            <Input
              {...field}
              type="text"
              label={t("amountToPay")}
              placeholder="0.00"
              value={field.value || ""}
              onChange={(e: any) => {
                const value = e.target.value;
                if (value === "" || /^\d*\.?\d*$/.test(value)) {
                  field.onChange(value);
                }
              }}
              onKeyPress={(e: any) => {
                if (!/[\d.]/.test(e.key)) {
                  e.preventDefault();
                }
                if (e.key === "." && field.value?.includes(".")) {
                  e.preventDefault();
                }
              }}
              rightIcon={
                !errors.amount && field.value ? (
                  <CheckCircle className="fill-primary size-5" />
                ) : (
                  <ErrorIcon className="fill-error size-5" />
                )
              }
              leftIcon={<span className="text-primary">€</span>}
              error={errors.amount?.message}
            />
          </div>
        )}
      />

      <div className="flex items-center gap-2">
        <Controller
          name="noPayout"
          control={control}
          render={({ field }) => (
            <input
              type="checkbox"
              checked={field.value}
              onChange={field.onChange}
              className="h-4 w-4 rounded border-gray-300 mb-1"
            />
          )}
        />
        <label className="text-primary">{t("notPayToTheCustomer")}</label>
      </div>

      <div className="flex flex-col gap-2">
        <span className="text-primary">{t("additionalInfo")}</span>
        <Controller
          name="additionalInfo"
          control={control}
          render={({ field }) => <Textarea {...field} className={errors.additionalInfo ? "border-error" : ""} />}
        />
        {errors.additionalInfo && <span className="text-error text-sm">{errors.additionalInfo.message}</span>}
      </div>

      <div className="flex justify-end">
        <Button type="submit" color="dark-blue" variant="filled" size="medium">
          {c("continue")}
        </Button>
      </div>
    </form>
  );
};

interface ConfirmationStepProps {
  formData: RefundFormData;
  onConfirm: () => void;
  loading: boolean;
  originalPayment: number;
}

const ConfirmationStep = ({ formData, onConfirm, loading, originalPayment }: ConfirmationStepProps) => {
  const { t, c } = useRefundModal();

  return (
    <div className="flex flex-col gap-6 mt-4">
      <p className="text-paragraph-regular text-tonal-dark-cream-20">{t("revisedRefundInfo")}</p>

      <div className="grid grid-cols-2">
        <div className="flex flex-col gap-2">
          <span className="text-primary">{t("refundAmount")}</span>
          <span className="font-bold text-primary">{formatCurrency(+formData.amount)}</span>
        </div>
        <div className="flex flex-col gap-2">
          <span className="text-primary">{t("originalPayment")}</span>
          <span className="text-primary">{formatCurrency(originalPayment)}</span>
        </div>
      </div>

      <p className="text-paragraph-regular text-tonal-dark-cream-20">{t("authorize")}</p>

      <div className="flex justify-end">
        <Button type="button" color="dark-blue" variant="filled" size="medium" onClick={onConfirm} disabled={loading}>
          {loading ? c("loading") : t("confirmRefund")}
        </Button>
      </div>
    </div>
  );
};

export interface ClaimManagementRefundModalProps {
  isOpen: boolean;
  onClose: () => void;
  data?: ClaimManagement;
}

export function ClaimManagementRefundModal({ isOpen, onClose, data }: ClaimManagementRefundModalProps) {
  const { t, c, REFUND_TYPES, ORIGINAL_PAYMENT_METHODS, refundFormSchema } = useRefundModal();

  const [step, setStep] = useState<1 | 2 | 3>(1);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<RefundFormData | null>(null);

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<RefundFormData>({
    defaultValues: {
      refundType: REFUND_TYPES[0],
      paymentMethod: ORIGINAL_PAYMENT_METHODS[0],
      amount: "",
      additionalInfo: "",
    },
    resolver: zodResolver(refundFormSchema),
  });

  const handleClose = () => {
    reset();
    onClose();
    setStep(1);
    setFormData(null);
  };

  const handleAddNewBankAccount = () => {
    // TODO: handle add new bank account
    return;
  };

  const handleNextStep = (data: RefundFormData) => {
    setFormData(data);
    setStep(2);
  };

  const handleBack = () => {
    setStep(1);
  };

  const handleConfirm = async () => {
    if (formData) {
      try {
        setLoading(true);
        // TODO: Integrate with API
        const res = await new Promise((resolve) => {
          setTimeout(() => {
            resolve(true);
          }, 2000);
        });
        if (!res) throw new Error(t("errorToProcess"));
        setStep(3);
      } catch (error) {
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <Modal
      open={isOpen}
      className="z-50 w-full"
      style={{ maxWidth: "600px", borderRadius: "52px", maxHeight: "100vh", backgroundColor: "#F0F0EF" }}
      onOpenChange={handleClose}
    >
      <div className="p-5 max-h-[75vh] overflow-auto">
        {step === 2 && (
          <div onClick={handleBack} className="flex items-center gap-2 mb-6 w-fit cursor-pointer">
            <KeyboardArrowLeft className="size-4 mb-1 fill-support-blue" />
            <span className="text-support-blue font-medium">{c("back")}</span>
          </div>
        )}

        <div className="flex flex-row w-full justify-between">
          {step === 3 ? null : (
            <div className="flex flex-col gap-2 justify-start">
              <p className="text-[28px] text-primary font-bold">{t("refund")}</p>
            </div>
          )}
          <div className="flex justify-end w-full">
            <button onClick={handleClose}>
              <Clear className="size-6 fill-primary" />
            </button>
          </div>
        </div>

        {step === 1 && (
          <FormStep
            control={control}
            handleSubmit={handleSubmit}
            errors={errors}
            onNextStep={handleNextStep}
            handleAddNewBankAccount={handleAddNewBankAccount}
          />
        )}

        {step === 2 && formData && (
          <ConfirmationStep
            formData={formData}
            originalPayment={Number(data?.totalAmount) || 0}
            onConfirm={handleConfirm}
            loading={loading}
          />
        )}

        {step === 3 && (
          <div className="flex flex-col gap-6 mt-4">
            <div className="flex flex-row gap-4 items-center">
              <Plant className="size-8 fill-success" />
              <p className="text-title-2 text-primary font-bold mt-2">{t("refundComplete")}</p>
            </div>
            <div className="flex justify-end">
              <Button type="button" color="yellow" variant="filled" size="medium" onClick={handleClose}>
                {c("close")}
              </Button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
}
