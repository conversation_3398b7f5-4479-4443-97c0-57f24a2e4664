"use client";

import { SearchInput } from "@/components/_common/search-input";
import PaginatedTable from "@/components/_common/tables/paginated-table";
import { useClaimManagementCustomerColumn } from "@/hooks/table-columns/claim-management/claim-management-customers";
import { ClaimManagement } from "@/lib/api/claim-management/types";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Add } from "@arthursenno/lizenzero-ui-react/Icon";
import { useState } from "react";
import StatusFilter, { StatusFilterType } from "../../components/filters/status-filter";
import { CancelInvoiceModal } from "./action-modals/cancel-invoice-modal";
import { ClaimManagementDetailsModal } from "./action-modals/details-modal";
import { ClaimManagementRefundModal } from "./action-modals/refund-modal";
import { ClaimManagementReissueModal } from "./action-modals/reissue-modal";
import { useSearchParams } from "next/navigation";
import { useTranslations } from "next-intl";

export type ClaimManagementModalType = "refund" | "cancel" | "reissue" | "view" | "print" | "details";
export interface ClaimManagementModalState {
  type: ClaimManagementModalType | null;
  isOpen: boolean;
  data?: ClaimManagement;
}

interface ClaimManagementSectionProps {
  filteredClaims: ClaimManagement[];
  setFilteredClaims: (claims: ClaimManagement[]) => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
  claimManagement: ClaimManagement[];
}

const STATUS_FILTERS: StatusFilterType[] = [
  { label: "2023", value: "2023" },
  { label: "2024", value: "2024" },
  { label: "2025", value: "2025" },
];

const ORDER_FILTERS: StatusFilterType[] = [
  { label: "ALL", value: "all" },
  { label: "2024", value: "2024" },
  { label: "2025", value: "2025" },
];

const ClaimManagementSection = ({
  filteredClaims,
  setFilteredClaims,
  searchTerm,
  setSearchTerm,
  claimManagement,
}: ClaimManagementSectionProps) => {
  const searchParams = useSearchParams();
  const currentStatus = searchParams?.get("status");

  const filterClaimManagement =
    currentStatus === `all` || !currentStatus
      ? claimManagement
      : claimManagement.filter((item) => item.claimDate?.includes(currentStatus));

  const [modalState, setModalState] = useState<ClaimManagementModalState>({
    type: null,
    isOpen: false,
  });
  const columns = useClaimManagementCustomerColumn({ setModalState });

  const handleCloseModal = () => {
    setModalState((state) => ({
      ...state,
      isOpen: false,
      type: null,
    }));
  };

  const t = useTranslations("ClaimManagementActions");
  return (
    <>
      <section className="w-full h-auto bg-[#F7F5F2] rounded-3xl px-6 pt-8 gap-8 flex flex-col mb-8">
        <div className="flex items-center justify-between">
          <p className="text-title-3 font-bold text-[#002652]">{t("claimManagement")}</p>
          <Button color="yellow" size="small" variant="filled" leadingIcon={<Add className="size-4 fill-primary" />}>
            Add invoice
          </Button>
        </div>
        <div className="flex flex-col lg:flex-row w-full lg:w-[540px] bg-white rounded-xl p-6 gap-4">
          <div className="flex flex-1 flex-col gap-2">
            <p className="text-paragraph-regular text-primary">{t("currentBalance")}</p>
            <p className="text-title-3 font-bold text-primary">€ 600.00</p>
          </div>
          <div className="flex flex-1 flex-col gap-2">
            <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("totalPayed")}</p>
            <p className="text-title-3 font-bold text-tonal-dark-cream-30">€ 1 000.00</p>
          </div>
        </div>
        <div className="flex items-center justify-between">
          <div className="w-[400px]">
            <SearchInput
              customers={filteredClaims}
              onFilter={setFilteredClaims}
              onSuggestionSelect={setSearchTerm}
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
            />
          </div>
          <div className="flex gap-2">
            <StatusFilter filters={ORDER_FILTERS} />
            <StatusFilter filters={STATUS_FILTERS} />
          </div>
        </div>
        <div className="mb-4">
          <PaginatedTable data={filterClaimManagement} columns={columns} />
        </div>
      </section>

      <ClaimManagementRefundModal
        isOpen={modalState.isOpen && modalState.type === "refund"}
        onClose={handleCloseModal}
        data={modalState.data}
      />

      <ClaimManagementReissueModal
        isOpen={modalState.isOpen && modalState.type === "reissue"}
        onClose={handleCloseModal}
        data={modalState.data}
      />

      <CancelInvoiceModal
        isOpen={modalState.isOpen && modalState.type === "cancel"}
        onClose={handleCloseModal}
        data={modalState.data}
      />

      <ClaimManagementDetailsModal
        isOpen={modalState.isOpen && modalState.type === "details"}
        onClose={handleCloseModal}
        data={modalState.data}
      />
    </>
  );
};

export default ClaimManagementSection;
