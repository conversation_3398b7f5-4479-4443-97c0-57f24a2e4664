import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { Clear, Plant } from "@arthursenno/lizenzero-ui-react/Icon";
import { ClaimManagement } from "@/lib/api/claim-management/types";

interface CancelInvoiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  data?: ClaimManagement;
}

import { useState } from "react";

export const CancelInvoiceModal: React.FC<CancelInvoiceModalProps> = ({
  isOpen,
  onClose,
  data,
}: CancelInvoiceModalProps) => {
  const [step, setStep] = useState(1);
  const [loading, setLoading] = useState(false);

  const handleClose = () => {
    onClose();
    setStep(1);
  };

  const handleConfirm = async () => {
    if (data) {
      try {
        // TODO: Integrate with API
        setLoading(true);
        const res = await new Promise((resolve) => {
          setTimeout(() => {
            resolve(true);
          }, 2000);
        });
        if (!res) throw new Error("Unable to process reissue");
        setStep(2);
      } catch (error) {
        // Handle error
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <>
      {step === 1 && (
        <Modal
          open={isOpen}
          className="z-50 w-full"
          style={{ maxWidth: "600px", borderRadius: "52px", maxHeight: "100vh", backgroundColor: "#F0F0EF" }}
          onOpenChange={onClose}
        >
          <div>
            <div className="flex justify-end w-full">
              <button onClick={onClose}>
                <Clear className="size-6 fill-primary mr-2" />
              </button>
            </div>
            <div className="text-[28px] text-primary font-bold">
              <p>Are you sure?</p>
            </div>
          </div>
          <div className="flex flex-col gap-6 mt-4">
            <p className="text-paragraph-regular text-tonal-dark-cream-20">
              By changing thia information the invoice #{data?.invoiceId} will be automatic cancelled and a new one will
              be created.
            </p>

            <div className="flex justify-end gap-4">
              <Button
                onClick={handleClose}
                type="button"
                color="dark-blue"
                variant="outlined"
                size="small"
                className="px-10"
                disabled={loading}
              >
                Back
              </Button>
              <Button
                onClick={handleConfirm}
                type="button"
                color="yellow"
                variant="filled"
                size="small"
                disabled={loading}
              >
                Generate a cancellaiton invoice
              </Button>
            </div>
          </div>
        </Modal>
      )}
      {step === 2 && (
        <Modal
          open={isOpen}
          className="z-50 w-full"
          style={{ maxWidth: "600px", borderRadius: "52px", maxHeight: "100vh", backgroundColor: "#F0F0EF" }}
          onOpenChange={onClose}
        >
          <div className="flex flex-col gap-6 mt-4">
            <div className="flex flex-row gap-4 items-center">
              <Plant className="size-8 fill-success" />
              <p className="text-title-2 text-primary font-bold mt-2" style={{ lineHeight: "1" }}>
                Cancellation invoice generated successfully!
              </p>
            </div>
            <div className="flex justify-end">
              <Button type="button" color="dark-blue" variant="filled" size="medium" onClick={handleClose}>
                Close
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </>
  );
};
