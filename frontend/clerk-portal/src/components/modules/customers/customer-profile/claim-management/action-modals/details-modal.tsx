import Divider from "@/components/_common/divider";
import { ClaimManagement } from "@/lib/api/claim-management/types";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Clear } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { useTranslations } from "next-intl";

interface ClaimManagementModal {
  isOpen: boolean;
  onClose: () => void;
  data?: ClaimManagement;
}

export const ClaimManagementDetailsModal = ({ isOpen, onClose, data }: ClaimManagementModal) => {
  const handleClose = () => {
    onClose();
  };

  const t = useTranslations("DetailsModal");
  const c = useTranslations("common");
  return (
    <Modal
      open={isOpen}
      className="z-50 w-full"
      style={{ maxWidth: "600px", borderRadius: "52px", maxHeight: "100vh", backgroundColor: "#F0F0EF" }}
      onOpenChange={handleClose}
    >
      <div className="p-5 max-h-[75vh] overflow-auto ">
        <div className="flex flex-row w-full justify-between">
          <div className="flex flex-col  gap-2 justify-start">
            <p className="text-[28px] text-primary font-bold">{t("details")}</p>
          </div>
          <div className="flex justify-end">
            <button onClick={handleClose}>
              <Clear className="size-6 fill-primary" />
            </button>
          </div>
        </div>
        <div className="mt-8 flex flex-col gap-2">
          <div className="flex gap-2 items-center">
            <p className="text-paragraph-regular text-tonal-dark-cream-10 font-bold">{t("IDInvoice")}</p>
            <p className="text-paragraph-regular text-tonal-dark-cream-10">{data?.invoiceId || "#029231"}</p>
          </div>
          <div className="flex gap-2 items-center">
            <p className="text-paragraph-regular text-tonal-dark-cream-10 font-bold">{t("type")}</p>
            <p className="text-paragraph-regular text-tonal-dark-cream-10">{data?.type || "Invoice"}</p>
          </div>
          <div className="flex gap-2 items-center">
            <p className="text-paragraph-regular text-tonal-dark-cream-10 font-bold">{t("info")}</p>
            <p className="text-paragraph-regular text-tonal-dark-cream-10">{t("admin")}</p>
          </div>
          <Divider initialMarginDisabled />
          <div className="mt-2 flex flex-col gap-2">
            <div className="flex gap-2 items-center">
              <p className="text-paragraph-regular text-tonal-dark-cream-10 font-bold">{t("openBalance")}</p>
              <p className="text-paragraph-regular text-tonal-dark-cream-10">{t("registeredCountry")}</p>
            </div>
            <div className="flex gap-2 items-center">
              <p className="text-paragraph-regular text-tonal-dark-cream-10 font-bold">{t("claim")}</p>
              <p className="text-paragraph-regular text-tonal-dark-cream-10">{data?.dueDate || "15.08.2022, 15h13"}</p>
            </div>
          </div>
          <Divider initialMarginDisabled />
          <div className="mt-2 flex flex-col gap-2 mb-2">
            <div className="flex gap-2 items-center">
              <p className="text-paragraph-regular text-tonal-dark-cream-10 font-bold">{t("otherInfo")}</p>
              <p className="text-paragraph-regular text-tonal-dark-cream-10">Lorem impsum</p>
            </div>
            <div className="flex gap-2 items-center">
              <p className="text-paragraph-regular text-tonal-dark-cream-10 font-bold">{t("otherInfo2")}</p>
              <p className="text-paragraph-regular text-tonal-dark-cream-10">Lorem impsum</p>
            </div>
          </div>
        </div>

        <div className=" flex justify-end">
          <Button color="yellow" variant="filled" size="medium" onClick={handleClose}>
            {c("close")}
          </Button>
        </div>
      </div>
    </Modal>
  );
};
