import { Download, File } from "@arthursenno/lizenzero-ui-react/Icon";

import { ListCustomer } from "@/lib/api/customer/types";
import { UploadedFile } from "@/lib/api/file/types";
import { useMutation } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { downloadCustomerFile } from "@/lib/api/file";
import { downloadFile } from "@/utils/download-file";
import { UserTypes } from "@/utils/user";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { CgSpinnerAlt } from "react-icons/cg";
import { useTranslations } from "next-intl";

export interface CustomerProfileContractProps {
  customer: ListCustomer;
}

export function CustomerProfileContract({ customer }: CustomerProfileContractProps) {
  const contract = customer?.contracts?.[0];
  const t = useTranslations("CustomerProfileContract");

  const downloadCertificateMutation = useMutation({
    mutationFn: async (certificateFile: UploadedFile) => {
      if (!certificateFile || !customer) throw new Error();

      const file = await downloadCustomerFile({
        file_id: certificateFile.id,
        user_id: customer.user_id,
        user_role: UserTypes.CUSTOMER,
      });

      downloadFile({ buffer: file, fileName: certificateFile.original_name });
    },
  });

  function handleDownloadFile(file: UploadedFile) {
    downloadCertificateMutation.mutate(file, {
      onSuccess: () => {
        enqueueSnackbar("File downloaded successfully", { variant: "success" });
      },
      onError: () => {
        enqueueSnackbar("Error downloading file. Please try again.", { variant: "error" });
      },
    });
  }

  return (
    <div className="p-6 bg-tonal-cream-96 rounded-3xl w-full">
      <div className="flex flex-row items-center justify-between">
        <p className="text-title-3 font-bold text-[#183362]">{t("contract")}</p>
      </div>
      <div className="mt-6 w-full flex flex-col gap-3">
        <div className="bg-white w-full h-[79px] rounded-xl flex p-4">
          <div className="flex-1 flex flex-col items-start justify-center gap-2">
            <p className="text-small-paragraph-regular text-tonal-dark-cream-30">{t("contractSince")}</p>
            <p className="text-paragraph-regular text-[#002652]">
              {contract?.start_date ? new Date(contract.start_date).toLocaleDateString().replaceAll("/", ".") : "-"}
            </p>
          </div>
          <div className="flex-1 flex flex-col items-start justify-center gap-2">
            <p className="text-small-paragraph-regular text-tonal-dark-cream-30">{t("contractUntil")}</p>
            <div className="flex gap-2 items-center">
              <p className="text-paragraph-regular text-[#002652]">
                {contract?.start_date
                  ? new Date(new Date(contract.start_date).setFullYear(new Date(contract.start_date).getFullYear() + 1))
                      .toLocaleDateString()
                      .replaceAll("/", ".")
                  : "-"}
              </p>
              {contract?.termination_date && (
                <p className="text-paragraph-regular text-tonal-dark-blue-50">Auto Renewed</p>
              )}
            </div>
          </div>
        </div>
        {customer.contracts.map((contract) => {
          if (!contract.termination || contract.status !== "TERMINATED") return null;

          return (
            <div
              key={contract?.id}
              className="bg-white w-full h-[79px] rounded-xl flex flex-col items-start justify-center gap-2 p-4"
            >
              <p className="text-small-paragraph-regular text-tonal-dark-cream-30">
                {contract.type === "EU_LICENSE" && "Last date EU Termination"}
                {contract.type === "DIRECT_LICENSE" && "Last date DE Termination"}
                {contract.type === "ACTION_GUIDE" && "Last date Guide Termination"}
              </p>
              <p className="text-paragraph-regular text-[#002652]">
                {new Date(contract.termination.completed_at).toLocaleDateString().replaceAll("/", ".")}
              </p>
            </div>
          );
        })}
        {customer.contracts.map((contract) => (
          <div key={contract?.id} className="bg-white w-full h-[79px] rounded-xl flex items-center gap-3 p-4">
            <Button
              color="gray"
              trailingIcon={
                downloadCertificateMutation.isPending ? (
                  <CgSpinnerAlt className="animate-spin" />
                ) : (
                  <Download className="fill-tonal-dark-cream-50 cursor-pointer" />
                )
              }
              disabled={downloadCertificateMutation.isPending}
              size="small"
              variant="text"
              onClick={() => handleDownloadFile(contract.licenses?.[0]?.certificates?.[0]?.files?.[0])}
            />
            <Button
              color="gray"
              trailingIcon={<File className="fill-tonal-dark-cream-50 cursor-pointer" />}
              size="small"
              variant="text"
            />
            <p className="text-tonal-dark-cream-20">
              {contract.type === "EU_LICENSE" && "Contract EU.pdf"}
              {contract.type === "DIRECT_LICENSE" && "Contract DE.pdf"}
              {contract.type === "ACTION_GUIDE" && "Action Guide.pdf"}
            </p>
          </div>
        ))}
        {!!contract?.files &&
          contract?.files?.length > 0 &&
          contract?.files.map((file) => (
            <div key={file?.id} className="bg-white w-full h-[79px] rounded-xl flex items-center gap-3 p-4">
              <Download
                className="size-7 fill-tonal-dark-cream-50 cursor-pointer"
                role="button"
                onClick={() => handleDownloadFile(file)}
              />
              <File className="size-7 fill-tonal-dark-cream-50" />
              <p className="text-tonal-dark-cream-20">{file?.name}</p>
            </div>
          ))}
        {!contract?.files ||
          (contract?.files?.length === 0 && (
            <div className="bg-white w-full h-[79px] rounded-xl flex items-center gap-3 p-4">
              <Download className="size-7 fill-tonal-dark-cream-50" />
              <File className="size-7 fill-tonal-dark-cream-50" />
              <p className="text-tonal-dark-cream-20">-</p>
            </div>
          ))}
      </div>
    </div>
  );
}
