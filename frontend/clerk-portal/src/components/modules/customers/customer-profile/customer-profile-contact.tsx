import { useQueryFilter } from "@/hooks/use-query-filter";
import { ListCustomer } from "@/lib/api/customer/types";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { EditCustomerContactModal } from "./modals/edit-customer-contact-modal";
import { QueryObserverResult, RefetchOptions } from "@tanstack/react-query";
import { useTranslations } from "next-intl";

type CustomerProfileContactProps = {
  customer: ListCustomer;
  refetch?: (options?: RefetchOptions) => Promise<QueryObserverResult<ListCustomer, Error>>;
};

export function CustomerProfileContact({ customer, refetch }: CustomerProfileContactProps) {
  const t = useTranslations("CustomerProfileContract");
  const { changeParam } = useQueryFilter(["edit-contact"]);

  function handleEditContact() {
    changeParam("edit-contact", "true");
  }

  return (
    <div className="p-6 bg-tonal-cream-96 rounded-3xl w-full">
      <div className="flex flex-row items-center justify-between">
        <p className="text-title-3 font-bold text-[#183362]">{t("contactInfo")}</p>
        <Button variant="text" color="light-blue" size="medium" onClick={handleEditContact}>
          Edit
        </Button>
      </div>
      <div className="mt-6 w-full flex flex-col gap-3">
        <div className="bg-white w-full rounded-xl flex">
          <div className="flex-1 flex flex-col justify-around break-words w-2/4">
            <div className="flex flex-col gap-2 p-4">
              <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("fullName")}</p>
              <p className="text-paragraph-regular text-[#002652]">
                {customer?.first_name + " " + customer?.last_name}
              </p>
            </div>
            <div className="flex flex-col gap-2 p-4">
              <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("email")}</p>
              <p className="text-paragraph-regular text-[#002652]">{customer?.email}</p>
            </div>
            <div className="flex flex-col gap-2 p-4">
              <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("phoneMobile")}</p>
              <p className="text-paragraph-regular text-[#002652]">{customer?.phones?.[1]?.phone_number || "---"}</p>
            </div>
          </div>
          <div className="flex-1 mt-6">
            <div className="flex flex-col gap-2">
              <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("phoneMobile")}</p>
              <p className="text-paragraph-regular text-[#002652]">{customer?.phones?.[0]?.phone_number || "---"}</p>
            </div>
          </div>
        </div>
      </div>
      <EditCustomerContactModal onSubmit={refetch} />
    </div>
  );
}
