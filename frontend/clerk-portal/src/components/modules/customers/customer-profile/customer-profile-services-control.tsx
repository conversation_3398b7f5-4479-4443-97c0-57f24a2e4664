import { CountryIcon } from "@/components/_common/country-icon";
import { ListCustomer } from "@/lib/api/customer/types";
import { createTermination } from "@/lib/api/termination";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { useRouter } from "@/i18n/navigation";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { ConfirmTerminationCountryModal } from "../components/termination/customer-contract-termination-country-modal";
import { useTranslations } from "next-intl";

type CustomerProfileContactProps = {
  customer: ListCustomer;
  refetch?: () => void;
};

export function CustomerProfileServicesControl({ customer, refetch }: CustomerProfileContactProps) {
  const t = useTranslations("CustomerServicesControl");
  const [isOpenConfirmTerminationModal, setIsOpenConfirmTerminationModal] = useState(false);
  const [isTerminationConfirmed, setIsTerminationConfirmed] = useState(false);
  const router = useRouter();

  const onSubmit = async (countryCode: string, reasonIdSelected?: number) => {
    setIsTerminationConfirmed(true);
    try {
      const response = await createTermination({
        contract_id: customer.contracts[0].id,
        country_codes: [countryCode],
        reason: "",
        reason_ids: reasonIdSelected ? [reasonIdSelected] : [],
      });

      setIsTerminationConfirmed(false);
      setIsOpenConfirmTerminationModal(false);
      enqueueSnackbar(t("terminationConfirm"), { variant: "success" });
      refetch?.();
    } catch (error) {
      enqueueSnackbar(t("error"), { variant: "error" });
    }
  };

  return (
    <div className="p-6 bg-tonal-cream-96 rounded-3xl w-full">
      <div className="flex flex-row items-center justify-between">
        <p className="text-title-3 font-bold text-[#183362]">{t("servicesControl")}</p>
      </div>
      <div className="mt-6 w-full flex flex-col gap-3">
        <div className="flex flex-col gap-6 overflow-auto">
          {customer.contracts.map((contract) => (
            <div key={contract.id} className="bg-white rounded-xl p-4">
              <div className="flex items-center gap-4">
                <p className="text-large-paragraph-regular font-bold text-[#002652]">
                  {contract.type === "EU_LICENSE"
                    ? "License Service"
                    : contract.type === "DIRECT_LICENSE"
                      ? "Direct License"
                      : "Action Guide"}
                </p>
                <div className="flex items-center gap-3">
                  {(contract.licenses.length ? contract.licenses : contract.action_guides).map((service) => (
                    <CountryIcon
                      key={service.id}
                      country={{ flag: service?.country_flag, name: service?.country_name }}
                      className="size-6"
                    />
                  ))}
                </div>
              </div>
              {contract.termination && (
                <div className="flex gap-2 mt-4">
                  <p className="text-paragraph-bold text-tonal-dark-cream-30">{t("terminationDate")}</p>
                  <p className="text-paragraph-bold text-[#002652]">{contract.termination.created_at}</p>
                </div>
              )}
              <div className="flex flex-col md:flex-row items-center justify-start gap-4 mt-10">
                <Button
                  color="red"
                  variant="outlined"
                  size="small"
                  className="text-nowrap w-full md:w-auto"
                  onClick={() => setIsOpenConfirmTerminationModal(true)}
                >
                  {contract.type === "EU_LICENSE"
                    ? "Terminate EU Contract"
                    : contract.type === "DIRECT_LICENSE"
                      ? "Terminate DE Contract"
                      : "Terminate Contract"}
                </Button>
                <Button color="dark-blue" variant="outlined" size="small" className="text-nowrap w-full md:w-auto">
                  Deactivate Renewal
                </Button>

                <ConfirmTerminationCountryModal
                  open={isOpenConfirmTerminationModal}
                  setOpen={setIsOpenConfirmTerminationModal}
                  customer={customer}
                  onSubmit={onSubmit}
                />

                {/* <CreateTerminationModal
                  open={isOpenConfirmTerminationModal}
                  setOpen={setIsOpenConfirmTerminationModal}
                  // onConfirm={onSubmit}
                  customer={customer}
                  isLoading={isTerminationConfirmed}
                /> */}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
