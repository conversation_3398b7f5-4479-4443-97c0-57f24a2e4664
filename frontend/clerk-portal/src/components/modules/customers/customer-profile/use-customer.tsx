import { getCustomerById } from "@/lib/api/customer";
import { useQuery } from "@tanstack/react-query";
import { useParams } from "next/navigation";

export function useCustomer() {
  const params = useParams();

  const customerId = Number(params.customerId);

  const {
    data: customer,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["customers", params.customerId],
    queryFn: () => getCustomerById(Number(params.customerId)),
  });

  return {
    customerId,
    customer,
    isLoading,
    refetch,
  };
}
