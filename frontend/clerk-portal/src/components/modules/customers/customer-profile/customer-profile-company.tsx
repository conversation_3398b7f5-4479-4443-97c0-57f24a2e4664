import { useQueryFilter } from "@/hooks/use-query-filter";
import { ListCustomer } from "@/lib/api/customer/types";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { QueryObserverResult, RefetchOptions } from "@tanstack/react-query";
import { EditCustomerCompanyModal } from "./modals/edit-customer-company-modal";
import { useTranslations } from "next-intl";

type CustomerProfileCompanyProps = {
  customer: ListCustomer;
  refetch?: (options?: RefetchOptions) => Promise<QueryObserverResult<ListCustomer, Error>>;
};

export function CustomerProfileCompany({ customer, refetch }: CustomerProfileCompanyProps) {
  const t = useTranslations("CustomerProfileContract");
  const usedCompany = customer?.companies?.[0];

  const { changeParam } = useQueryFilter(["edit-company"]);

  function handleEditCompany() {
    changeParam("edit-company", "true");
  }

  return (
    <div className="p-6 bg-tonal-cream-96 rounded-3xl w-full">
      <div className="flex flex-row items-center justify-between">
        <p className="text-title-3 font-bold text-[#183362]">{t("companyData")}</p>
        <Button variant="text" color="light-blue" size="medium" onClick={handleEditCompany}>
          Edit
        </Button>
      </div>
      <div className="mt-6 w-full flex flex-col gap-3">
        <div className="bg-white w-full rounded-xl flex">
          <div className="flex flex-col justify-between">
            <div className="flex flex-col gap-2 p-4">
              <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("companyName")}</p>
              <p className="text-paragraph-regular text-[#002652]">{usedCompany?.name}</p>
            </div>
            <div className="flex flex-col gap-2 p-4">
              <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("location")}</p>
              <p className="text-paragraph-regular text-[#002652]">
                {usedCompany?.address.country_code ?? "-"}
                {", "}
                {usedCompany?.address.city ?? "-"}
              </p>
            </div>
            <div className="flex flex-col gap-2 p-4">
              <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("address")}</p>
              <p className="text-paragraph-regular text-[#002652]">{usedCompany?.address.address_line ?? "-"}</p>
            </div>
            <div className="grid grid-cols-2">
              <div className="flex flex-col gap-2 p-4">
                <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("vat")}</p>
                <p className="text-paragraph-regular text-[#002652]">{usedCompany?.vat ?? "-"}</p>
              </div>
              <div className="flex flex-col gap-2 p-4">
                <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("taxNumber")}</p>
                <p className="text-paragraph-regular text-[#002652]">{usedCompany?.tin ?? "-"}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <EditCustomerCompanyModal onSubmit={refetch} />
    </div>
  );
}
