import React, { useState, useEffect, useCallback } from "react";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Search } from "@arthursenno/lizenzero-ui-react/Icon";
import stringSimilarity from "string-similarity";
import { ListCustomer } from "@/lib/api/customer/types";
import { useTranslations } from "next-intl";

type SearchInputProps = {
  customers: ListCustomer[];
  onFilter: (filteredCustomers: ListCustomer[]) => void;
  onSuggestionSelect: (suggestion: string) => void;
  searchTerm: string;
  setSearchTerm: (term: string) => void;
};

const SearchInput: React.FC<SearchInputProps> = ({ customers, onFilter, searchTerm, setSearchTerm }) => {
  const [suggestions, setSuggestions] = useState<ListCustomer[]>([]);
  const [closeSuggestions, setCloseSuggestions] = useState("");
  const [isSuggestionSelected, setIsSuggestionSelected] = useState(false);
  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm);

  const debounce = <T extends (...args: any[]) => void>(func: T, delay: number) => {
    let timeoutId: NodeJS.Timeout;

    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        func(...args);
      }, delay);
    };
  };

  const debouncedSetSearchTerm = useCallback(
    debounce((value: string) => {
      setSearchTerm(value);
    }, 300),
    []
  );

  useEffect(() => {
    if (!customers.length) return;

    if (isSuggestionSelected) {
      setIsSuggestionSelected(false);
      return;
    }

    if (searchTerm) {
      const matchedSuggestions = customers.filter(
        (customer) => customer.companies[0]?.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setSuggestions(matchedSuggestions);

      if (!matchedSuggestions.length) {
        const customerNames = customers.map((customer) => customer.companies[0]?.name || "");

        const bestMatch = stringSimilarity.findBestMatch(searchTerm || "", customerNames);
        setCloseSuggestions(bestMatch.bestMatch.target);
      } else {
        setCloseSuggestions("");
      }
    } else {
      setSuggestions([]);
      setCloseSuggestions("");
    }
  }, [searchTerm, customers, isSuggestionSelected]);

  const t = useTranslations("LicensingSelectPriceListModal");
  return (
    <div className="w-[400px] relative" onBlur={() => setSuggestions([])} tabIndex={-1}>
      <Input
        leftIcon={<Search width={24} height={24} className="fill-tonal-dark-cream-60" />}
        placeholder={t("searchByCompany")}
        onChange={(e: any) => {
          const value = e.target.value;
          setLocalSearchTerm(value);
          setIsSuggestionSelected(false);
          debouncedSetSearchTerm(value);
        }}
        value={localSearchTerm}
      />
      {suggestions.length === 0 && searchTerm && closeSuggestions && (
        <p className="mt-4 text-tonal-dark-cream-40 text-paragraph-regular">
          00 results found. Did you mean{" "}
          <span
            className="text-primary text-paragraph-regular underline cursor-pointer"
            onMouseDown={() => {
              setLocalSearchTerm(closeSuggestions);
              setSearchTerm(closeSuggestions);
              onFilter(
                customers.filter(
                  (customer) => customer.companies[0]?.name.toLowerCase().includes(closeSuggestions.toLowerCase())
                )
              );
              setSuggestions([]);
              setIsSuggestionSelected(true);
            }}
          >
            {closeSuggestions}
          </span>{" "}
          ?
        </p>
      )}
      {suggestions.length > 0 && (
        <div className="absolute mt-1 w-full bg-white border rounded-md shadow-lg z-10">
          {suggestions.map((customer, idx) => (
            <div
              key={idx}
              className="cursor-pointer p-2 hover:bg-blue-100"
              onMouseDown={() => {
                const companyName = customer.companies[0]?.name || "";
                setLocalSearchTerm(companyName);
                setSearchTerm(companyName);
                onFilter([customer]);
                setSuggestions([]);
                setIsSuggestionSelected(true);
              }}
            >
              <p className="text-primary">{customer.companies[0]?.name}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default SearchInput;
