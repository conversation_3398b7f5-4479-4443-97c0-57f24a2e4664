import React from "react";
import { Dropdown } from "@/components/_common/dropdown";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { Sort, KeyboardArrowDown } from "@arthursenno/lizenzero-ui-react/Icon";
import { useRouter } from "@/i18n/navigation";
import { useSearchParams } from "next/navigation";

export const ORDER_FILTERS = [
  { label: "All", value: "ALL" },
  { label: "A-Z", value: "ASC" },
  { label: "Z-A", value: "DESC" },
  { label: "Last added", value: "LAST_MODIFIED" },
  { label: "Most recent added", value: "FIRST_MODIFIED" },
] as const;

export type OrderFilterType = (typeof ORDER_FILTERS)[number];

function OrderFilter() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const currentOrder = searchParams.get("order") || ORDER_FILTERS[0].value;
  const currentOrderFilter = ORDER_FILTERS.find((filter) => filter.value === currentOrder) || ORDER_FILTERS[0];

  const handleOrderChange = (orderFilter: OrderFilterType) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("order", orderFilter.value);
    params.delete("page");
    router.push(`?${params.toString()}`, { scroll: false });
  };

  return (
    <Dropdown
      trigger={
        <button className="flex items-center text-support-blue font-bold text-base">
          <Sort width={20} height={20} className="fill-support-blue" />
          <span className="ml-1 mr-2 mt-1 text-left">{currentOrderFilter.label}</span>
          <KeyboardArrowDown width={20} height={20} className="fill-support-blue" />
        </button>
      }
    >
      {ORDER_FILTERS.map((orderFilter, idx) => (
        <DropdownMenu.Item
          key={idx}
          className="group py-5 px-4 text-base focus:outline-none cursor-pointer hover:bg-surface-02"
          onClick={() => handleOrderChange(orderFilter)}
          style={{
            color: orderFilter.value === currentOrder ? "#002652" : "#242423",
            fontWeight: orderFilter.value === currentOrder ? "bold" : "normal",
          }}
        >
          {orderFilter.label}
        </DropdownMenu.Item>
      ))}
    </Dropdown>
  );
}

export default OrderFilter;
