import { RadioSelected, RadioUnselected } from "@arthursenno/lizenzero-ui-react/Icon";
import { DetailedHTMLProps, InputHTMLAttributes } from "react";
import { useTranslations } from "next-intl";

interface QuestionSimpleAnswerProps extends DetailedHTMLProps<InputHTMLAttributes<HTMLInputElement>, HTMLInputElement> {
  question: string;
  disabled?: boolean;
  valueSelect?: string;
}

export const QuestionSimpleAnswer = ({ question, disabled, valueSelect, ...props }: QuestionSimpleAnswerProps) => {
  const id = Math.floor(Math.random() * 1000) + 1;

  const c = useTranslations("common");
  return (
    <div className="space-y-6 md:space-y-10">
      <div className="flex flex-col gap-4 group" data-disabled={disabled}>
        <p className="text-base text-tonal-dark-cream-10">{question}</p>
        <label className="text-base text-tonal-dark-cream-20 flex gap-2 items-center cursor-pointer has-[input:checked]:cursor-default">
          <input
            type="radio"
            className="hidden peer"
            name={`commitment-${id}`}
            disabled={disabled}
            value={`yes`}
            checked={valueSelect === `yes`}
            {...props}
          />
          <RadioSelected className="hidden peer-checked:block size-5 fill-primary group-data-[disabled=true]:fill-[#ACACAB]" />
          <RadioUnselected className="block cursor-pointer peer-checked:hidden size-5 fill-primary peer-data-[invalid=true]:fill-error group-data-[disabled=true]:fill-[#ACACAB]" />
          <span className="mt-1">{c("yes")}</span>
        </label>
        <label className="text-base text-tonal-dark-cream-20 flex gap-2 items-center cursor-pointer has-[input:checked]:cursor-default">
          <input
            type="radio"
            className="hidden peer"
            name={`commitment-${id}`}
            disabled={disabled}
            value={`no`}
            checked={valueSelect === `no`}
            {...props}
          />
          <RadioSelected className="hidden peer-checked:block size-5 fill-primary group-data-[disabled=true]:fill-[#ACACAB]" />
          <RadioUnselected className="block cursor-pointer peer-checked:hidden size-5 fill-primary peer-data-[invalid=true]:fill-error group-data-[disabled=true]:fill-[#ACACAB]" />
          <span className="mt-1">{c("no")}</span>
        </label>
      </div>
    </div>
  );
};
