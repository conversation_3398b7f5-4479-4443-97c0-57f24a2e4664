"use client";

import { Delete, File, Upload } from "@arthursenno/lizenzero-ui-react/Icon";
import { ChangeEvent, useState } from "react";
import { useTranslations } from "next-intl";

export const OfferDoc = () => {
  const [documents, setDocuments] = useState<File[]>([]);

  const t = useTranslations("OfferDocuments");
  const handleFileUpload = (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setDocuments((prevDocuments) => [...prevDocuments, file]);
    }
  };

  const handleRemoveDoc = (index: number) => {
    setDocuments((current) => current.filter((_, idx) => idx !== index));
  };

  const handleClick = () => {
    document.getElementById("fileInput")?.click();
  };

  return (
    <div className="bg-surface-02 pt-9 pb-10 px-8 rounded-3xl">
      <div className="flex flex-col gap-3">
        <p className="text-primary text-2xl font-bold">{t("contractDocuments")}</p>
        <p className="text-[#808FA9]">{t("uploadAdditional")}</p>
      </div>

      <div className="mt-7 bg-background rounded-[20px] flex justify-center py-3 px-4 flex-col">
        <button onClick={handleClick} className="flex items-center gap-2 py-4">
          <Upload className="size-6 fill-support-blue" />
          <p className="text-support-blue font-bold">{t("uploadDocuments")}</p>
        </button>
        <input type="file" id="fileInput" onChange={handleFileUpload} style={{ display: "none" }} />
        {documents.map((doc, idx) => (
          <div className="py-4 flex items-center justify-between" key={idx}>
            <div className="flex items-center gap-2">
              <File className="fill-primary size-6" />
              <p className="text-primary text-sm">{doc.name}</p>
            </div>
            <button onClick={() => handleRemoveDoc(idx)}>
              <Delete className="size-6 fill-primary" />
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};
