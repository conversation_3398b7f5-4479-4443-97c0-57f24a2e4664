import { CountryIcon } from "@/components/_common/country-icon";
import { PriceInput } from "@/components/ui/PriceInput";
import { useState } from "react";
import { OfferModalEdit } from "./offer-modal-edit";
import { formatCurrency } from "@/utils/format-currency";
import { useTranslations } from "next-intl";

interface RenderCountryInfoProps {
  country: {
    name: string;
    flag: string;
  };
}
const MOCK_INITIAL_PRICE: [number, number] = [5000, 5000];

export const RenderCountryInfo = ({ country }: RenderCountryInfoProps) => {
  const t = useTranslations("CountriesOffersList");
  const [enabledEdition, setEnabledEdition] = useState(false);
  const [isOpenModalEdit, setIsOpenModalEdit] = useState(false);
  const [prices, setPrices] = useState<[number, number]>(MOCK_INITIAL_PRICE);

  const handleEdit = () => setEnabledEdition(true);

  const handleCancelEdit = () => {
    setEnabledEdition(false);
    setPrices(MOCK_INITIAL_PRICE);
  };

  const handleSave = () => {
    setIsOpenModalEdit(false);
    setEnabledEdition(false);
  };

  const handleChanged = (value: number, idx: number) => {
    const newPrice: [number, number] = [...prices];
    newPrice[idx] = value;
    setPrices(newPrice);
  };

  return (
    <div className="pt-3 px-4 pb-6 rounded-2xl border border-surface-03">
      <div>
        <div className="flex justify-between items-center">
          <div className="flex items-center py-4 gap-3">
            <CountryIcon country={country} />
            <p className="text-primary font-bold mt-1">{country.name}</p>
          </div>
          {enabledEdition ? (
            <button className="underline text-primary font-bold" onClick={() => setIsOpenModalEdit(true)}>
              Save changes
            </button>
          ) : (
            <button className="underline text-support-blue font-bold" onClick={handleEdit}>
              Edit price
            </button>
          )}
        </div>

        <hr className="text-on-surface-01 opacity-30" />

        <div>
          <div className="flex items-center py-4 gap-3 justify-between">
            <p className="text-primary text-sm">{t("registrationFee")}</p>
            {enabledEdition ? (
              <div className={`w-1/4`}>
                <PriceInput placeholder={`€`} value={prices[0]} onChange={(value) => handleChanged(value, 0)} />
              </div>
            ) : (
              <p className="text-primary text-sm font-bold flex-none">{formatCurrency(prices[0])}</p>
            )}
          </div>
          <hr className="text-on-surface-01 opacity-30" />
        </div>
        <div>
          <div className="flex items-center py-4 gap-3 justify-between">
            <p className="text-primary text-sm">{t("handlingFeeDescription")}</p>
            {enabledEdition ? (
              <div className={`w-1/4`}>
                <PriceInput placeholder={`€`} value={prices[1]} onChange={(value) => handleChanged(value, 1)} />
              </div>
            ) : (
              <p className="text-primary text-sm font-bold flex-none">{formatCurrency(prices[1])}</p>
            )}
          </div>
          <hr className="text-on-surface-01 opacity-30" />
        </div>
      </div>
      <OfferModalEdit
        isOpen={isOpenModalEdit}
        setIsOpen={setIsOpenModalEdit}
        onClickChange={handleSave}
        onClickCancel={handleCancelEdit}
      />
    </div>
  );
};
