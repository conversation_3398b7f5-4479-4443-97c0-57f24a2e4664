"use client";

import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { CheckBox, CheckBoxOutlineBlank, KeyboardArrowRight } from "@arthursenno/lizenzero-ui-react/Icon";
import { Dispatch, SetStateAction, useState } from "react";
import { OfferModalSend } from "../components/offer-modal-send";
import { PaymentConditions } from "../components/offer-payment-condition";
import { RenderCountryInfo } from "../components/offer-render-country-info";
import { OfferDocInfo, OfferDocInfoProps } from "./offer-doc-info";
import { OfferTitleStatus } from "./offer-title-status";
import { AddOfferUploadModal } from "./add-offer-upload-modal";
import { useTranslations } from "next-intl";

export interface OfferRenderItemsProps extends OfferDocInfoProps {
  showGenerate: boolean;
  setShowGenerate?: Dispatch<SetStateAction<boolean>>;
  countries?: {
    name: string;
    flag: string;
  }[];
}

export const OfferRenderItems = ({
  showGenerate,
  setShowGenerate,
  countries,
  title,
  ...props
}: OfferRenderItemsProps) => {
  const [checked, setChecked] = useState(false);
  const [isOpenModalSend, setIsOpenModalSend] = useState(false);
  const [offerSend, setOfferSend] = useState(false);
  const [isOpenUploadModal, setIsOpenUploadModal] = useState(false);

  const t = useTranslations("OfferModalEdit");
  const c = useTranslations("common");

  const handleSend = () => {
    setOfferSend(true);
    setIsOpenModalSend(false);
  };

  const handleUploadOffer = () => {
    setIsOpenUploadModal(true);
  };

  if (showGenerate) {
    return (
      <div className="flex flex-col justify-between w-3/4 gap-6 h-full">
        <Button
          color="yellow"
          variant="filled"
          size="medium"
          trailingIcon={<KeyboardArrowRight className="size-5 fill-primary" />}
          onClick={() => setShowGenerate?.(false)}
        >
          {t("generateOffer")}
        </Button>
        <Button color="dark-blue" variant="outlined" size="medium" onClick={handleUploadOffer}>
          {t("uploadOffer")}
        </Button>
        <AddOfferUploadModal open={isOpenUploadModal} setOpen={setIsOpenUploadModal} />
      </div>
    );
  }

  if (offerSend || props.docFile)
    return <OfferDocInfo title={title} onClickEdit={() => setOfferSend(false)} {...props} />;

  return (
    <div>
      <OfferTitleStatus title={title || t("offerGenerated")} onClickCancel={() => setShowGenerate?.(true)} />

      <div className="mt-6">
        <p className="text-primary font-bold">{t("euLicense")}</p>
        <div className="mt-5 flex flex-col gap-6">
          {countries?.map((country, idx) => <RenderCountryInfo country={country} key={idx} />)}
        </div>

        <div className="flex flex-row justify-between mt-6">
          <p className="text-sm text-primary font-bold">{t("paymentConditions")}</p>
          <PaymentConditions />
        </div>

        <div className="flex flex-row justify-between bg-[#D1EDF7] py-6 px-4 rounded-b-xl items-center mt-8">
          <p className="text-primary font-bold">{t("total")}</p>
          <p className="text-xl text-primary font-bold">300.00 €</p>
        </div>

        <div className="flex items-center gap-2 mt-6">
          <label className="cursor-pointer pt-1">
            <input type="checkbox" className="hidden" checked={checked} onClick={() => setChecked((curr) => !curr)} />
            {checked ? (
              <CheckBoxOutlineBlank className="text-on-tertiary size-5" />
            ) : (
              <CheckBox className="text-on-tertiary size-5" />
            )}
          </label>
          <p className="text-on-tertiary">{t("sendOfferAsPdf")}</p>
        </div>

        <div className="w-full flex justify-end mt-6">
          <Button
            color="yellow"
            variant="filled"
            size="medium"
            trailingIcon={<KeyboardArrowRight className="size-5 fill-primary" />}
            onClick={() => setIsOpenModalSend(true)}
          >
            {t("sendOfferToClient")}
          </Button>
        </div>
      </div>
      <OfferModalSend isOpen={isOpenModalSend} setIsOpen={setIsOpenModalSend} onClickSend={handleSend} />
    </div>
  );
};
