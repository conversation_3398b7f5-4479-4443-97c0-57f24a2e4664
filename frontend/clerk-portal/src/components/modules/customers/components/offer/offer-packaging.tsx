"use client";

import Divider from "@/components/_common/divider";
import {
  QuestionTooltip,
  QuestionTooltipDescription,
  QuestionTooltipTitle,
} from "@/components/_common/question-tooltip";
import { cn } from "@/lib/utils";
import { Aluminium } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { useTranslations } from "next-intl";

interface OfferPackagingProps {
  name: string;
  isEdit?: boolean;
}

export const OfferPackaging = ({ name, isEdit }: OfferPackagingProps) => {
  const t = useTranslations("CalculatorCountry");
  return (
    <div className="p-4 md:p-8 rounded-4xl bg-surface-03">
      {!isEdit && (
        <>
          <div className="flex items-center gap-4">
            <p className="text-xl font-bold text-tonal-dark-cream-30 mt-1">{t("result")}</p>
            <p
              data-license={true}
              className="text-base font-bold mt-1 text-tonal-dark-green-30 data-[license=true]:text-on-surface-04"
            >
              {true ? t("licensingRequired") : t("notObligated")}
            </p>
            <QuestionTooltip>
              <QuestionTooltipDescription>
                Lorem ipsum dolor sit amet consectetur. Gravida vitae fermentum faucibus arcu non. In volutpat nisl nunc
                pellentesque.
              </QuestionTooltipDescription>
            </QuestionTooltip>
          </div>
          <Divider style={{ margin: "20px 0" }} />
        </>
      )}

      <div>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <p className="text-xl font-bold text-primary data-[invalid=true]:text-error">{name}</p>
            {!isEdit && (
              <QuestionTooltip>
                <QuestionTooltipDescription>
                  Lorem ipsum dolor sit amet consectetur. Gravida vitae fermentum faucibus arcu non. In volutpat nisl
                  nunc pellentesque.
                </QuestionTooltipDescription>
              </QuestionTooltip>
            )}
          </div>
        </div>

        <div>
          <p className="text-sm text-tonal-dark-cream-30 data-[invalid=true]:text-error my-5">
            {t("pleaseProvideEstimateQuantity")}
          </p>

          <div className="rounded-[20px] overflow-hidden space-y-[1px] bg-tonal-dark-cream-80">
            <div className="bg-white">
              <Packaging name="Aluminium" isEdit={isEdit} />
              <Packaging name="Aluminium" isEdit={isEdit} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

interface PackagingProps {
  name: string;
  isEdit?: boolean;
}

const Packaging = ({ name, isEdit }: PackagingProps) => {
  const t = useTranslations("CalculatorCountry");
  return (
    <div className="py-[14px] px-5 flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
      <div className="flex items-center gap-4">
        {!isEdit && (
          <QuestionTooltip>
            <QuestionTooltipTitle>
              <Aluminium width={24} className="fill-primary" />
              <p className="text-primary text-md font-bold">{name}</p>
            </QuestionTooltipTitle>
            <QuestionTooltipDescription className="text-primary">
              {t("bottleTopsFilmChocolate")}
            </QuestionTooltipDescription>
          </QuestionTooltip>
        )}

        <div className="flex flex-1 items-center gap-3">
          <Aluminium className="size-6 md:size-9 fill-primary" />
          <p className="text-sm md:text-base font-bold text-primary">{name}</p>
        </div>
      </div>
      <div className={cn("flex items-center justify-between gap-4", isEdit ? `w-1/3` : `w-1/4`)}>
        {isEdit ? <Input placeholder={`kg`} /> : <p className="text-primary">250.00</p>}
        <p className="text-primary">kg</p>
      </div>
    </div>
  );
};
