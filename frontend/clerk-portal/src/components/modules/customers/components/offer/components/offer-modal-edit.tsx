import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { KeyboardArrowRight } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { Dispatch, SetStateAction } from "react";
import { useTranslations } from "next-intl";

interface OfferModalEditProps {
  isOpen: boolean;
  setIsOpen: Dispatch<SetStateAction<boolean>>;
  onClickChange: () => void;
  onClickCancel?: () => void;
}

export const OfferModalEdit = ({ isOpen, setIsOpen, onClickChange, onClickCancel }: OfferModalEditProps) => {
  const handleOnOpenChange = () => {
    setIsOpen(false);
    onClickCancel?.();
  };
  const t = useTranslations("OfferModalEdit");
  const c = useTranslations("common");

  return (
    <Modal
      open={isOpen}
      onOpenChange={handleOnOpenChange}
      className="z-50 !rounded-[52px] w-full !py-9 !px-9 !bg-surface-01 overflow-x-auto"
    >
      <div>
        <div className="flex flex-col gap-4">
          <p className="text-primary text-[28px] font-bold">{t("editOffer")}</p>
          <p className="text-tonal-dark-cream-20">{t("areYouSure")}</p>
        </div>
        <div className="flex justify-end gap-3 mt-10">
          <Button color="dark-blue" variant="outlined" size="small" onClick={handleOnOpenChange}>
            {c("cancel")}
          </Button>
          <Button
            color="yellow"
            variant="filled"
            size="small"
            onClick={onClickChange}
            trailingIcon={<KeyboardArrowRight className="size-5 fill-primary" />}
          >
            {c("edit")}
          </Button>
        </div>
      </div>
    </Modal>
  );
};
