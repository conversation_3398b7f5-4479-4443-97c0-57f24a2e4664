import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Clear, East } from "@arthursenno/lizenzero-ui-react/Icon";
import { useState } from "react";
import * as XLSX from "xlsx";
import UploadInput from "@/components/_common/fileUploader";
import { useTranslations } from "next-intl";

interface AddOffersModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export const AddOfferUploadModal = ({ open, setOpen }: AddOffersModalProps) => {
  const [file, setFile] = useState<File | undefined>();

  const handleOpen = () => setOpen(!open);
  const t = useTranslations("OfferUpload");
  const c = useTranslations("common");

  const addOffer = () => {
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      if (!e.target) return;
      const data = new Uint8Array(e.target.result as ArrayBuffer);
      const workbook = XLSX.read(data, { type: "array" });

      const offerSheet = XLSX.utils.sheet_to_json<{
        categoryName: string;
        countryCode: string;
        feeName: string;
        price: number;
      }>(workbook.Sheets["offer"]);

      const offerItems = offerSheet.reduce(
        (acc, fee) => {
          const category = acc.find((item) => item.categoryName === fee.categoryName);

          if (category) {
            const country = category.countriesOffers.find((c) => c.countryCode === fee.countryCode);
            if (country) {
              country.expenses.push({ feeName: fee.feeName, price: fee.price * 100 });
            } else {
              category.countriesOffers.push({
                countryCode: fee.countryCode,
                expenses: [{ feeName: fee.feeName, price: fee.price * 100 }],
              });
            }
          } else {
            acc.push({
              categoryName: fee.categoryName,
              countriesOffers: [
                {
                  countryCode: fee.countryCode,
                  expenses: [{ feeName: fee.feeName, price: fee.price * 100 }],
                },
              ],
            });
          }
          return acc;
        },
        [] as {
          categoryName: string;
          countriesOffers: { countryCode: string; expenses: { feeName: string; price: number }[] }[];
        }[]
      );

      const total = offerItems.reduce((sum, category) => {
        return (
          sum +
          category.countriesOffers.reduce((countrySum, country) => {
            return countrySum + country.expenses.reduce((expenseSum, expense) => expenseSum + expense.price, 0);
          }, 0)
        );
      }, 0);

      const offer = {
        id: `${Date.now()}`,
        name: file.name.replace(/\.[^/.]+$/, ""),
        offerItems,
        total,
        file,
        paymentCondition: "Monthly",
        isDraft: false,
      };

      setOpen(false);
    };

    reader.readAsArrayBuffer(file);
  };

  return (
    <Modal
      open={open}
      onOpenChange={handleOpen}
      className="z-50 w-full"
      style={{ maxWidth: "600px", borderRadius: "52px", maxHeight: "100vh", backgroundColor: "#F0F0EF" }}
    >
      <div className="p-5 max-h-[75vh] overflow-auto">
        <div className="flex justify-between">
          <p className="text-[28px] text-primary font-bold">{t("uploadOffer")}</p>
          <button onClick={handleOpen}>
            <Clear className="size-6 fill-primary" />
          </button>
        </div>
        <div className="mt-2">
          <p className="text-paragraph-regular text-tonal-dark-cream-20">{t("offerInfo")}</p>
        </div>
        <div className="my-4">
          <UploadInput acceptedFileTypes={["xlsx", "xls"]} file={file} setFile={setFile} />
        </div>
        <div className="flex justify-end">
          <Button trailingIcon={<East />} color="yellow" variant="filled" size="medium" onClick={addOffer}>
            {c("add")}
          </Button>
        </div>
      </div>
    </Modal>
  );
};
