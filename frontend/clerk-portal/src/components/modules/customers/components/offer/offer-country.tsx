"use client";

import { CountryIcon } from "@/components/_common/country-icon";
import Divider from "@/components/_common/divider";
import StatusBadge from "@/components/modules/country/components/task-type";
import { Delete, EditCircle, KeyboardArrowUp } from "@arthursenno/lizenzero-ui-react/Icon";
import { useState } from "react";
import { OfferCommitment } from "./offer-commitment";
import { OfferModalEditCountry } from "./offer-modal-edit-country";
import { OfferPackaging } from "./offer-packaging";

interface OfferCountryProps {
  country: { name: string; flag: string };
}

export const OfferCountry = ({ country }: OfferCountryProps) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="py-5 px-6 bg-background rounded-[20px]">
      <details className="group rounded-4xl overflow-hidden" open={false}>
        <summary className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Delete className="fill-primary size-6" />
            <CountryIcon country={country} className="size-12" />
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <p className="text-xl font-bold text-primary">{country.name}</p>
                <button onClick={() => setIsOpen(true)}>
                  <EditCircle className="fill-support-blue size-5" />
                </button>
              </div>
              <p className="text-sm text-[#808FA9]">Added in: 23/07/2023</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadge status={`To be generated`} />
            <KeyboardArrowUp className="flex-none size-8 fill-[#808FA9] group-open:-rotate-180 transition-all duration-300" />
          </div>
        </summary>

        <OfferCommitment />
        <Divider className="w-full" initialMarginDisabled />
        <div className="flex flex-col gap-10 mt-10">
          <OfferPackaging name={`Sales Packaging`} />
          <OfferPackaging name={`B2B Packaging`} />
        </div>
      </details>
      <OfferModalEditCountry isOpen={isOpen} setIsOpen={setIsOpen} country={country} />
    </div>
  );
};
