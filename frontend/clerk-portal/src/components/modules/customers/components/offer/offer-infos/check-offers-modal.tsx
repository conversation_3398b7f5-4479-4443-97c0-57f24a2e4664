"use client";

import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Clear, East } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import Image from "next/image";
import { useState } from "react";

import { useTranslations } from "next-intl";

const CheckOfferDataModal = ({
  isOpen,
  onClickConfirmation,
  onOpenChange,
}: {
  isOpen: boolean;
  onClickConfirmation: () => void;
  onOpenChange?: () => void;
}) => {
  const t = useTranslations("OfferUpload");
  const c = useTranslations("common");
  return (
    <Modal
      open={isOpen}
      className="z-50 w-full"
      style={{ maxWidth: "500px", borderRadius: "52px", maxHeight: "100vh", backgroundColor: "#F0F0EF" }}
      onOpenChange={onOpenChange}
    >
      <div className="p-5 max-h-[85vh] overflow-auto ">
        <div className="flex flex-row w-full justify-between">
          <div className="flex flex-col gap-2 justify-start">
            <p className="text-[28px] text-primary font-bold">{t("uploadOffer")}</p>
          </div>
          <div className="flex justify-end">
            <button onClick={onOpenChange}>
              <Clear className="size-6 fill-primary" />
            </button>
          </div>
        </div>

        <div className="mt-2">
          <p className="text-paragraph-regular text-tonal-dark-cream-20">{t("reviewDetail")}</p>
        </div>

        <div className="flex justify-end mt-6">
          <Button trailingIcon={<East />} color="yellow" variant="filled" size="medium" onClick={onClickConfirmation}>
            {c("add")}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

const ConfirmOfferDataModal = ({ isOpen, onOpenChange }: { isOpen: boolean; onOpenChange?: () => void }) => {
  const t = useTranslations("OfferUpload");
  const c = useTranslations("common");
  return (
    <Modal
      open={isOpen}
      className="z-50 w-full"
      style={{ maxWidth: "600px", borderRadius: "52px", maxHeight: "100vh", backgroundColor: "#F0F0EF" }}
      onOpenChange={onOpenChange}
    >
      <div className="p-5 max-h-[75vh] overflow-auto ">
        <div className="flex flex-row w-full justify-end">
          <button onClick={onOpenChange}>
            <Clear className="size-6 fill-primary" />
          </button>
        </div>

        <div className="flex gap-2 justify-start items-center mt-2">
          <Image width={35} height={35} src={`/assets/images/leaf_seal.png`} alt={"Leaf Seal"} />
          <p className="text-[28px] text-primary font-bold">{t("offerAddedSuccessfully")}</p>
        </div>

        <div className="flex justify-end mt-10">
          <Button color="yellow" variant="filled" size="medium" onClick={onOpenChange}>
            {c("close")}
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export const FlowConfirmOfferModal = () => {
  const [isOpenCheckModal, setIsOpenCheckModal] = useState(false);
  const [isOpenConfirmModal, setIsOpenConfirmModal] = useState(false);

  const handleConfirmationData = () => {
    setIsOpenCheckModal(false);
    setIsOpenConfirmModal(true);
  };

  return (
    <>
      <CheckOfferDataModal
        isOpen={isOpenCheckModal}
        onOpenChange={() => setIsOpenCheckModal(false)}
        onClickConfirmation={handleConfirmationData}
      />
      <ConfirmOfferDataModal isOpen={isOpenConfirmModal} onOpenChange={() => setIsOpenConfirmModal(false)} />
    </>
  );
};
