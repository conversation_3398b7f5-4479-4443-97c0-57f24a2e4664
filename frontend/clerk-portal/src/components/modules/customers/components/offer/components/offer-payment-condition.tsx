"use client";

import { Dropdown } from "@/components/_common/dropdown";
import { KeyboardArrowDown } from "@arthursenno/lizenzero-ui-react/Icon";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";
import { useState } from "react";

export const PaymentConditions = () => {
  const [selectCondition, setSelectCondition] = useState(`Monthly`);

  return (
    <Dropdown
      trigger={
        <button className={`flex items-center text-primary font-bold text-sm`}>
          <span className="ml-1 mr-2 mt-1 text-left">{selectCondition}</span>
          <KeyboardArrowDown width={20} height={20} className="fill-primary" />
        </button>
      }
    >
      {[`Monthly`, `Annual`].map((condition, idx) => (
        <DropdownMenu.Item
          key={idx}
          className="group py-5 px-4 text-base focus:outline-none cursor-pointer"
          onClick={() => setSelectCondition(condition)}
          style={{
            color: condition === selectCondition ? "#002652" : "#242423",
            fontWeight: condition === selectCondition ? "bold" : "normal",
          }}
        >
          {condition}
        </DropdownMenu.Item>
      ))}
    </Dropdown>
  );
};
