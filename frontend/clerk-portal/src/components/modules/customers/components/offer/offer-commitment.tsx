"use client";

import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { Add, Remove } from "@arthursenno/lizenzero-ui-react/Icon";
import { QuestionSimpleAnswer } from "../question-simple-answer";
import { useTranslations } from "next-intl";

interface OfferCommitmentProps {
  componentAlone?: boolean;
}

export const OfferCommitment = ({ componentAlone }: OfferCommitmentProps) => {
  const t = useTranslations("OfferCommitment");
  if (componentAlone) {
    return (
      <div className="p-8 rounded-4xl bg-surface-03 w-full">
        <div className="flex flex-col items-start gap-5">
          <p className="text-2xl font-bold text-primary mt-1">{t("commitmentAssessment")}</p>
          <p className="text-tonal-dark-cream-30 text-sm">{t("seeAnswers")}</p>
        </div>
        <div className="mt-5 bg-white rounded-2xl p-8 flex flex-col gap-10">
          <QuestionSimpleAnswer question={t("isCzech")} />
          <QuestionSimpleAnswer question={t("sellMoreThan300")} />
        </div>
      </div>
    );
  }

  return (
    <details className="group/commitment mt-7" open={true}>
      <summary className="flex flex-col">
        <div className="flex items-center justify-between py-6">
          <div className="flex items-center gap-4">
            <p className="text-xl font-bold text-primary">{t("commitmentAssessment")}</p>
            <QuestionTooltip>
              <QuestionTooltipDescription>
                Lorem ipsum dolor sit amet consectetur. Gravida vitae fermentum faucibus arcu non. In volutpat nisl nunc
                pellentesque.
              </QuestionTooltipDescription>
            </QuestionTooltip>
          </div>
          <Remove className="hidden group-open/commitment:block size-8 fill-support-blue cursor-pointer" />
          <Add className="block group-open/commitment:hidden size-8 fill-support-blue cursor-pointer" />
        </div>
      </summary>

      <div className="flex flex-col pb-6 gap-10">
        <QuestionSimpleAnswer question={t("isCzech")} />
        <QuestionSimpleAnswer question={t("sellMoreThan300")} />
      </div>
    </details>
  );
};
