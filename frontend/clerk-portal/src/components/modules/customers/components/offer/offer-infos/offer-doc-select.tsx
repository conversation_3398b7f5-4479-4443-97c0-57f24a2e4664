import Divider from "@/components/_common/divider";
import { cn } from "@/lib/utils";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { File } from "@arthursenno/lizenzero-ui-react/Icon";

const statusInfo = {
  OfferDenied: {
    className: `text-error`,
    label: `Offer Denied`,
  },
  Accepted: {
    className: `text-tonal-green-50`,
    label: `Accepted`,
  },
};

type StatusKeys = keyof typeof statusInfo;

export interface OfferDocSelecProps {
  showDownload?: boolean;
  status?: StatusKeys;
  docFile?: { name: string };
}

export const OfferDocSelec = ({ showDownload, status, docFile }: OfferDocSelecProps) => {
  const statusSelect = status && statusInfo?.[status];

  return (
    <div>
      <div className="flex items-center py-3">
        <File className="fill-tonal-dark-green-30 size-12" />
        <div className="flex justify-between w-full">
          <div className="flex flex-col gap-1">
            <p className="text-sm text-tonal-dark-cream-20 font-bold">{docFile?.name || `Client Offer`}</p>
            <p className="text-sm text-tonal-dark-cream-50">12.04.2023</p>
          </div>
          {statusSelect && <p className={cn(`text-sm`, statusSelect.className)}>{statusSelect.label}</p>}
          {showDownload && !status && (
            <Button color="light-blue" size="small" variant="text" className="!font-normal">
              Download
            </Button>
          )}
        </div>
      </div>
      <Divider initialMarginDisabled />
    </div>
  );
};
