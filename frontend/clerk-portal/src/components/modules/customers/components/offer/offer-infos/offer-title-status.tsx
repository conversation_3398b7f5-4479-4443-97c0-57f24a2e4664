import StatusBadge, { StatusType } from "@/components/modules/country/components/task-type";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";

interface OfferTitleStatusProps {
  title: string;
  onClickCancel?: () => void;
  status?: StatusType;
  onClickStatus?: () => void;
}

export const OfferTitleStatus = ({ title, onClickCancel, status, onClickStatus }: OfferTitleStatusProps) => {
  return (
    <div className="w-full flex justify-between items-center">
      <p className="text-2xl font-bold text-primary">{title}</p>
      {status ? (
        // TODO: remove button later, used only to show acceptance mode
        <button onClick={() => onClickStatus?.()}>
          <StatusBadge status={status} />
        </button>
      ) : (
        onClickCancel && (
          <Button color="light-blue" variant="text" size="medium" onClick={() => onClickCancel?.()}>
            Cancel
          </Button>
        )
      )}
    </div>
  );
};
