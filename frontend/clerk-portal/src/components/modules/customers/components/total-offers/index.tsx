import { formatCurrency } from "@/utils/format-currency";
import { useTranslations } from "next-intl";

interface TotalOffersProps {
  total: number;
}
export function TotalOffers({ total = 0 }: TotalOffersProps) {
  const t = useTranslations("OfferModalEdit");
  return (
    <div className="flex items-center justify-between gap-3 py-[1.625rem] px-4 rounded-2xl bg-support-blue/[0.18]">
      <p className="font-bold text-right text-primary">{t("total")}</p>
      <strong className="font-bold text-right text-xl text-primary">{formatCurrency(total)}</strong>
    </div>
  );
}
