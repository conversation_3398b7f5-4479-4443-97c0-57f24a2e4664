"use client";

import { PublishedCountry } from "@/hooks/use-liberated-countries";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { usePathname, useRouter } from "@/i18n/navigation";
import { getCommitment } from "@/lib/api/commitment";
import { PriceList } from "@/lib/api/commitment/types";
import { createPurchasePayment } from "@/lib/api/purchase";
import { createPurchaseObject } from "@/utils/create-purchase-object";
import { useParams, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { BiLoader } from "react-icons/bi";
import { z } from "zod";
import { LicensingSelectPriceListModal } from "../licensing-select-price-list-modal";
import { CalculatorCountry } from "./calculator/calculator-country";
import { CalculatorProvider } from "./calculator/calculator-provider";
import { CalculatorSubmit } from "./calculator/calculator-submit";
import { CalculatorLicense, Commitment, ItemCommitment, ItemLicense } from "./calculator/interface";

interface Step2Props {
  onRemoveCountryCode: (countryCode: string) => void;
  liberatedCountries: PublishedCountry[];
}

export const calculatorFormSchema = z.object({
  items: z
    .record(
      z.string(),
      z.object({
        commitment: z.object({
          filled: z
            .boolean({
              required_error: `Click "confirm" to validate your answers.`,
              invalid_type_error: `Click "confirm" to validate your answers.`,
            })
            .refine((filled) => !!filled, { message: `Click "confirm" to validate your answers.` }),
          questions: z.record(z.string(), z.string()),
        }),
        packagingServices: z.record(
          z.string(),
          z.object({
            fractions: z.record(z.string(), z.number().gte(0, { message: "The minimum weight is 0." }).optional()),
          })
        ),
      })
    )
    .refine((items) => items && Object.keys(items).length > 0, {
      message: "Select at least one country.",
      path: ["minimum"],
    }),
});

type calculatorFormData = z.infer<typeof calculatorFormSchema>;

type QuestionsType = { [countryCode: string]: { [questionIndex: number]: string } };

export function Step2({ onRemoveCountryCode, liberatedCountries }: Step2Props) {
  const { paramValues, changeParam } = useQueryFilter(["selected-codes", "select-price-list", "step"]);

  const [isOpenSelectPriceListModal, setIsOpenSelectPriceListModal] = useState(false);

  const searchParams = useSearchParams();
  const params = useParams<{ customerId: string }>();
  const customerId = params.customerId;

  const pathname = usePathname();
  const router = useRouter();

  const selectedCountryCodes = paramValues?.["selected-codes"]?.split(",").filter(Boolean) ?? [];

  const [countriesLicenses, setCountriesLicenses] = useState<CalculatorLicense>();
  const [priceListSelected, setPriceListSelected] = useState<PriceList | null>(null);
  const [isLoadingPurchase, setIsLoadingPurchase] = useState(false);

  const setItemCommitmentQuestions = (countryCode: string, questions: Commitment) => {
    setCountriesLicenses((curr) => {
      const data = { ...curr };

      const license = data?.items?.[countryCode]?.license;

      if (!license) return curr;

      for (const criteria of questions) {
        license.commitment.questions[criteria.id] = criteria as unknown as ItemCommitment["questions"]["number"];
      }

      return data;
    });
  };

  const addLicenses = () => {
    const license: ItemLicense = {
      type: "EU_LICENSE",
      year: new Date().getFullYear().toString(),
      commitment: {
        filled: false,
        questions: {},
      },
      packagingServices: {},
      priceList: {} as ItemLicense[`priceList`],
      otherCosts: [],
      requiredInformations: [],
    };

    selectedCountryCodes.forEach(async (countryCode) => {
      setCountriesLicenses((curr) => {
        const data = { ...curr };
        const country = liberatedCountries.find((country) => country.code === countryCode);

        if (!country) return curr;

        const items = data?.items;
        const selectItem = items?.[countryCode];

        if (selectItem) {
          selectItem.license = license;
          return data;
        }

        return {
          items: {
            ...items,
            [countryCode]: {
              country,
              license,
            },
          },
        };
      });

      const commitmentResponse = await getCommitment(countryCode);

      if (!commitmentResponse.success) return;

      setItemCommitmentQuestions(countryCode, commitmentResponse.data);
    });
  };

  function handleSubmit() {
    setIsOpenSelectPriceListModal(true);
  }

  useEffect(() => {
    if (!liberatedCountries || liberatedCountries.length === 0) return;

    addLicenses();
  }, [liberatedCountries]);

  useEffect(() => {
    if (!selectedCountryCodes || selectedCountryCodes.length === 0) return changeParam(`step`, `1`);
  }, [selectedCountryCodes]);

  const handleConfirm = async () => {
    if (!countriesLicenses) return { message: "No countries selected", success: false };
    if (!priceListSelected) return { message: "No price list selected", success: false };

    const purchaseData = createPurchaseObject(Number(customerId), countriesLicenses, priceListSelected);

    setIsLoadingPurchase(true);
    // const purchaseServicesResponse = await purchaseServices(purchaseData as unknown as PurchaseServicesParams);
    const purchaseServicesResponse = await createPurchasePayment({
      purchase_object: purchaseData,
    });
    setIsLoadingPurchase(false);

    if (!purchaseServicesResponse.success)
      return { message: purchaseServicesResponse.error || "Error save new countries", success: false };

    return { message: "Price list selected", success: true };
  };

  const items = Object.values(countriesLicenses?.items || {}).filter((i) => i.license);

  return (
    <>
      {items.length === 0 ? (
        <div className="flex justify-center items-center gap-1 md:gap-2 px-2 py-3 border-[1px] border-tonal-dark-cream-80 rounded-md">
          <BiLoader className="fill-primary animate-spin" />
          <p className="text-center text-primary"> Loading data...</p>
        </div>
      ) : (
        <CalculatorProvider countries={countriesLicenses}>
          <div className="flex flex-col gap-6 flex-1">
            {items.map((item) => (
              <CalculatorCountry
                key={`${item.country.code}-calculator-item`}
                calculatorItem={item}
                setCountriesLicenses={setCountriesLicenses}
                onRemoveCountryCode={onRemoveCountryCode}
              />
            ))}
          </div>

          <CalculatorSubmit countriesLicenses={countriesLicenses} onSubmit={handleSubmit} />
        </CalculatorProvider>
      )}

      <LicensingSelectPriceListModal
        isOpen={isOpenSelectPriceListModal}
        onClose={() => setIsOpenSelectPriceListModal(false)}
        onPriceListSelected={(priceList) => setPriceListSelected(priceList)}
        initialPriceListSelected={priceListSelected}
        onConfirm={handleConfirm}
        isLoadingConfirm={isLoadingPurchase}
      />
    </>
  );
}
