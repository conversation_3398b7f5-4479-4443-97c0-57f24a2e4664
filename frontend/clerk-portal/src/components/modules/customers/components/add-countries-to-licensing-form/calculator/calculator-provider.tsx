"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { Dispatch, ReactNode, SetStateAction, useEffect, useState } from "react";
import { FormProvider, useForm, useWatch } from "react-hook-form";
import { z } from "zod";

import { usePathname } from "@/i18n/navigation";
import { CalculatorItem, CalculatorLicense } from "./interface";

export const calculatorFormSchema = z.object({
  items: z
    .record(
      z.string(),
      z.object({
        commitment: z.object({
          filled: z
            .boolean({
              required_error: `Click "confirm" to validate your answers.`,
              invalid_type_error: `Click "confirm" to validate your answers.`,
            })
            .refine((filled) => !!filled, { message: `Click "confirm" to validate your answers.` }),
          questions: z.record(z.string(), z.string()),
        }),
        packagingServices: z.record(
          z.string(),
          z.object({
            fractions: z.record(z.string(), z.number().gte(0, { message: "The minimum weight is 0." }).optional()),
          })
        ),
      })
    )
    .refine((items) => items && Object.keys(items).length > 0, {
      message: "Select at least one country.",
      path: ["minimum"],
    }),
});

export type calculatorFormData = z.infer<typeof calculatorFormSchema>;

interface CalculatorProviderProps {
  children: ReactNode;
  countries?: CalculatorLicense;
}

type FormItems = calculatorFormData["items"];
type FormItem = FormItems[string];

export function CalculatorProvider({ children, countries }: CalculatorProviderProps) {
  const pathname = usePathname();

  const methods = useForm<calculatorFormData>({
    resolver: zodResolver(calculatorFormSchema),
    mode: "all",
  });

  const { control, reset, getValues, unregister } = methods;

  const formValues = useWatch({
    control: control,
  });

  useEffect(() => {
    const formItems = getValues("items");
    if (!formItems) return;

    Object.keys(formItems).forEach((key) => {
      if (!countries?.items?.[key]) {
        unregister(`items.${key}`);
      }
    });
  }, [countries?.items, getValues, unregister]);

  useEffect(() => {
    if (formValues.items) return;

    const licenseItems = Object.values(countries?.items || {}).filter((item) => item.license);
    const entries = licenseItems.reduce(
      (acc, item) => {
        acc[item.country.code] = {
          commitment: {
            filled: item.license!.commitment.filled,
            questions: {
              ...Object.values(item.license!.commitment.questions).reduce(
                (acc, curr) => {
                  acc[`criteria_${curr.id}`] = curr.answer as string;
                  return acc;
                },
                {} as Record<string, string>
              ),
            },
          },
          packagingServices: {
            ...Object.values(item.license!.packagingServices).reduce(
              (packagingResult, packagingService) => {
                packagingResult[packagingService.id] = {
                  fractions: {
                    ...Object.values(packagingService.fractions).reduce(
                      (acc, curr) => {
                        acc[`fraction_${curr.key}`] = curr.value ? curr.value * 100 : undefined;
                        return acc;
                      },
                      {} as Record<string, number | undefined>
                    ),
                  },
                };

                return packagingResult;
              },
              {} as Record<string, calculatorFormData["items"][number]["packagingServices"][number]>
            ),
          },
        };
        return acc;
      },
      {} as Record<string, FormItem>
    );

    reset({
      items: entries,
    });
  }, [countries?.items]);

  return <FormProvider {...methods}>{children}</FormProvider>;
}
