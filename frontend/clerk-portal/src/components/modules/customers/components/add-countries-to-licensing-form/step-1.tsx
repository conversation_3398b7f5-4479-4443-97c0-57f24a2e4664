"use client";

import { useEffect } from "react";

import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { KeyboardArrowRight } from "@arthursenno/lizenzero-ui-react/Icon";

import { CountryInput } from "@/components/_common/forms/country-input";
import { CountryCardRow } from "@/components/ui/country-card-row";
import { PublishedCountry } from "@/hooks/use-liberated-countries";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";

interface Step1Props {
  onRemoveCountryCode: (countryCode: string) => void;
  liberatedCountries: PublishedCountry[];
}

export function Step1({ onRemoveCountryCode, liberatedCountries }: Step1Props) {
  const { paramValues, changeParam } = useQueryFilter(["selected-codes", `step`]);

  const t = useTranslations("Step1");
  const c = useTranslations("common");
  const selectedCountryCodes = paramValues?.["selected-codes"]?.split(",").filter(Boolean) ?? [];

  const totalSelectedCountries = selectedCountryCodes.length ?? 0;
  const totalCountries = liberatedCountries.length ?? 0;

  function handleSelectCountry(countryName: string) {
    const selectedCountry = liberatedCountries.find((country) =>
      country.name.toLowerCase().includes(countryName.toLowerCase())
    );

    if (!selectedCountry) return;

    const countryCode = selectedCountry.code;

    if (selectedCountryCodes.includes(countryCode)) {
      onRemoveCountryCode(countryCode);
      return;
    }

    const dataCountries = [...selectedCountryCodes, countryCode];
    const newCountries = [...new Set(dataCountries)];

    changeParam(`selected-codes`, newCountries?.join(","));
  }

  function handleContinue() {
    const isValidCountries = !selectedCountryCodes.some(
      (country) => !liberatedCountries.some((item) => item.code === country)
    );

    if (!isValidCountries) return enqueueSnackbar(t("selectedInvalidCountries"), { variant: "error" });

    changeParam(`step`, `2`);
  }

  function checkCountries() {
    const isValidCountries = !selectedCountryCodes.some(
      (country) => !liberatedCountries.some((item) => item.code === country)
    );

    const onlyCountriesValid = isValidCountries
      ? selectedCountryCodes
      : selectedCountryCodes.filter((country) => liberatedCountries.some((item) => item.code === country));

    const newCountries = [...new Set(onlyCountriesValid)];

    changeParam(`selected-codes`, newCountries?.join(","));
  }

  useEffect(() => {
    if (selectedCountryCodes.length && liberatedCountries.length) checkCountries();
  }, [paramValues]);

  return (
    <>
      <div className="py-5 sm:px-8 bg-surface-02 rounded-4xl">
        <p className="mb-6 text-sm text-[#183362]">{t("selectCountry")}</p>
        <div className="w-2/4">
          <CountryInput
            countries={liberatedCountries}
            onSelectCountry={(country) => handleSelectCountry(country.name)}
            className="w-full"
          />
        </div>

        {totalSelectedCountries > 0 && (
          <p className="mb-6 mt-10 text-sm text-tonal-dark-cream-30">
            {totalSelectedCountries}/{totalCountries} {t("countriesSelected")}
          </p>
        )}

        <ul className="flex flex-col gap-6">
          {selectedCountryCodes.map((countryCode) => {
            const selectedCountry = liberatedCountries.find((country) => country.code === countryCode);

            if (!selectedCountry) return null;

            return (
              <li key={countryCode} className="flex items-center gap-2">
                <CountryCardRow
                  country={selectedCountry}
                  onClickRemove={() => onRemoveCountryCode(countryCode)}
                  info="Lorem ipsum dolor sit."
                />
              </li>
            );
          })}
        </ul>
      </div>

      <div className="flex md:items-center md:justify-end">
        <Button
          type="button"
          variant="filled"
          color="yellow"
          size="medium"
          trailingIcon={<KeyboardArrowRight />}
          onClick={handleContinue}
          disabled={selectedCountryCodes.length === 0}
          className="w-full md:max-w-72"
        >
          {c("continue")}
        </Button>
      </div>
    </>
  );
}
