"use client";

import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import {
  Add,
  Aluminium,
  CheckCircle,
  Delete,
  East,
  KeyboardArrowUp,
  RadioSelected,
  RadioUnselected,
  Remove,
} from "@arthursenno/lizenzero-ui-react/Icon";

import { CountryIcon } from "@/components/_common/country-icon";
import Divider from "@/components/_common/divider";
import {
  QuestionTooltip,
  QuestionTooltipDescription,
  QuestionTooltipTitle,
} from "@/components/_common/question-tooltip";
import { Dispatch, SetStateAction, useRef } from "react";
import { Controller, useFormContext } from "react-hook-form";
import { BiLoader } from "react-icons/bi";
import { calculatorFormData, calculatorFormSchema } from "./calculator-provider";
import { CalculatorItem, CalculatorLicense, ItemLicense, ItemPackagingService } from "./interface";
import { submitCommitment } from "@/lib/api/commitment";
import { setupColumnToCartColumn, setupFractionToCartFraction } from "@/utils/column-fraction-inverter";
import { useSession } from "next-auth/react";
import { useCustomer } from "../../../customer-profile/use-customer";
import { FractionInput } from "@/components/ui/fraction-input";
import { useTranslations } from "next-intl";

interface JourneyCalculatorItemProps {
  calculatorItem: CalculatorItem;
  setCountriesLicenses: Dispatch<SetStateAction<CalculatorLicense | undefined>>;
  onRemoveCountryCode: (countryCode: string) => void;
}

const isValidCountryVolume = (cartItem: CalculatorItem) => {
  const packages = Object.values(cartItem?.license?.packagingServices || {}).filter((item) => item?.added);
  const allFractions = packages?.map((item) => item.fractions);

  const allValues = allFractions
    .map((fraction) =>
      Object.values(fraction)
        .map((fraction) => fraction?.value)
        .filter((fraction) => fraction)
    )
    .flat();

  return !!allValues.length;
};

export function CalculatorCountry({
  calculatorItem,
  onRemoveCountryCode,
  setCountriesLicenses: setLicenses,
}: JourneyCalculatorItemProps) {
  const commitmentRef = useRef<HTMLDetailsElement | null>(null);
  const code = calculatorItem.country.code;
  const { customerId, customer, isLoading, refetch } = useCustomer();
  const t = useTranslations("CalculatorCountry");
  const c = useTranslations("common");

  const {
    formState: { errors, isSubmitted, isValid },
    register,
    trigger,
    clearErrors,
    unregister,
    control,
    setValue,
    getValues,
    reset,
  } = useFormContext<calculatorFormData>();

  const license = calculatorItem.license;

  if (!license) return null;

  const status = license?.commitment.filled ? "packaging" : "commitment";
  const isValidFulfilled = (!errors.items || !errors.items[code]) && isSubmitted;

  const countryCheck = isValidCountryVolume(calculatorItem) && isValid;

  const unfilledQuestionErrors = errors?.items?.[code]?.commitment?.questions;

  const updateCommitmentFilled = (filled: boolean) => {
    setLicenses((curr) => {
      if (!curr || !curr.items) return curr;

      const deapCopy: CalculatorLicense = JSON.parse(JSON.stringify(curr));

      const license = deapCopy?.items?.[code].license;

      if (!license) return curr;

      license.commitment.filled = filled;

      return deapCopy;
    });
  };

  const updateCommitmentQuestionAnswer = (questionId: string, answer?: string) => {
    setLicenses((curr) => {
      if (!curr || !curr.items) return curr;

      const item = curr.items[code];
      if (!item?.license?.commitment?.questions) return curr;

      const deapCopy: CalculatorLicense = JSON.parse(JSON.stringify(curr));

      const license = deapCopy?.items?.[code].license;

      if (!license) return curr;

      license.commitment.questions[questionId].answer = answer;

      return deapCopy;
    });
  };

  const updatePackagingAdded = (packagingId: string, added: boolean) => {
    setLicenses((curr) => {
      if (!curr || !curr.items) return curr;

      const item = curr.items[code];
      if (!item?.license?.commitment?.questions) return curr;

      const deapCopy: CalculatorLicense = JSON.parse(JSON.stringify(curr));

      const license = deapCopy?.items?.[code].license;

      if (!license) return curr;

      license.packagingServices[packagingId].added = added;

      return deapCopy;
    });
  };

  const updatePackagingFraction = ({
    packagingId,
    fractionKey,
    fractionPrice,
    fractionValue,
  }: {
    packagingId: string;
    fractionKey: string;
    fractionValue: number;
    fractionPrice: number;
  }) => {
    setLicenses((curr) => {
      if (!curr || !curr.items) return curr;

      const item = curr.items[code];
      if (!item?.license?.commitment?.questions) return curr;

      const deapCopy: CalculatorLicense = JSON.parse(JSON.stringify(curr));

      const license = deapCopy?.items?.[code].license;

      if (!license) return curr;

      const reportSetItem = license.packagingServices[packagingId].reportSet.fractions.find(
        (item) => item.id === Number(fractionKey)
      );

      if (!reportSetItem) return curr;

      const packagingService = license.packagingServices[packagingId];

      if (!packagingService) return curr;

      license.packagingServices[packagingId].fractions[`fraction_${fractionKey}`].value = fractionValue;
      license.packagingServices[packagingId].fractions[`fraction_${fractionKey}`].price = fractionPrice;
      license.packagingServices[packagingId].fractionsCost = Object.values(
        license.packagingServices[packagingId].fractions
      ).reduce((total, fraction) => (total += (fraction.value || 0) * (fractionPrice || 99)), 0);

      return deapCopy;
    });
  };

  const updateCartItemCalculator = (license: Partial<ItemLicense>) => {
    setLicenses((curr) => {
      const data = { ...curr };
      const item = data?.items?.[code];

      if (!item) return curr;

      item.license = {
        ...item.license,
        ...license,
      } as ItemLicense;

      return data;
    });
  };

  async function handleCheckCommitment() {
    const isValid = await trigger(`items.${code}.commitment.questions`);

    if (!isValid) return;

    setValue(`items.${code}.commitment.filled`, true);

    updateCommitmentFilled(true);

    if (commitmentRef.current) commitmentRef.current.open = false;
    clearErrors(`items.${code}`);

    if (!license) return;

    const commitmentAnswers = Object.values(license.commitment.questions).map((criteria) => ({
      criteria_id: criteria.id,
      answer: String(criteria.answer),
    }));

    const submitCommitmentResponse = await submitCommitment({
      country_code: code,
      year: 2025,
      commitment_answers: commitmentAnswers,
      customer_email: customer?.email,
    });

    if (!submitCommitmentResponse.success) return;

    const customerCommitment = submitCommitmentResponse.data;
    const serviceSetup = customerCommitment.service_setup;

    const packagingServices = serviceSetup.packaging_services.reduce(
      (result, packagingService) => {
        result[packagingService.id] = {
          id: String(packagingService.id),
          title: packagingService.name,
          added: packagingService.obliged,
          required: packagingService.obliged,
          price: 99,
          service: packagingService.name,
          reportSet: {
            id: packagingService.report_set.id,
            mode: packagingService.report_set.mode,
            name: packagingService.report_set.name,
            type: packagingService.report_set.type,
            fractions: packagingService.report_set.fractions?.map(setupFractionToCartFraction),
            columns: packagingService.report_set.columns?.map(setupColumnToCartColumn),
          },
          reportFrequency: packagingService.report_set_frequency,
          fractions: packagingService?.report_set?.fractions?.reduce(
            (result, fraction) => {
              result[`fraction_${fraction.id}`] = {
                key: String(fraction.id),
                name: String(fraction.name),
                value: undefined,
              };
              return result;
            },
            {} as ItemPackagingService["fractions"]
          ),
          fractionsCost: 0,
        };
        return result;
      },
      {} as Record<string, ItemPackagingService>
    );

    updateCartItemCalculator({
      year: serviceSetup.year,
      packagingServices,
      otherCosts: serviceSetup.other_costs,
      representativeTier: serviceSetup.representative_tier,
      requiredInformations: serviceSetup.required_informations.map((info) => ({
        ...info,
        fileId: info.file_id,
      })),
      priceList: {
        id: serviceSetup.price_list.id,
        name: serviceSetup.price_list.name,
        description: serviceSetup.price_list.description,
        serviceType: serviceSetup.price_list.service_type,
        conditionType: serviceSetup.price_list.condition_type,
        conditionTypeValue: serviceSetup.price_list.condition_type_value,
        startDate: serviceSetup.price_list.start_date,
        endDate: serviceSetup.price_list.end_date,
        basicPrice: serviceSetup.price_list.basic_price,
        minimumPrice: serviceSetup.price_list.minimum_price,
        registrationFee: serviceSetup.price_list.registration_fee,
        handlingFee: serviceSetup.price_list.handling_fee,
        variableHandlingFee: serviceSetup.price_list.variable_handling_fee,
      },
    });
  }

  function handleEditCommitment() {
    if (!license) return;

    if (!license.commitment.filled) return;

    setValue(`items.${code}.commitment.filled`, false);

    updateCommitmentFilled(false);
  }

  const commitmentQuestions = Object.values(license.commitment.questions);
  const packagingServices = Object.values(license.packagingServices);

  function handleRemoveItem() {
    onRemoveCountryCode?.(calculatorItem.country.code);

    setLicenses((curr) => {
      if (!curr) return undefined;

      const data = { ...curr };

      if (!data.items || !data.items[code]) return curr;

      delete data.items[code];

      return data;
    });
  }

  return (
    <details className="group rounded-4xl overflow-hidden" open={true}>
      <summary
        data-status={status}
        className="flex items-center justify-between px-4 py-6 md:py-7 md:px-10 bg-surface-04 data-[status=commitment]:bg-tonal-dark-blue-90"
      >
        <div className="flex items-center gap-4">
          <CountryIcon country={calculatorItem.country} className="size-6 md:size-8" />
          <p className="text-2xl font-bold text-primary">{calculatorItem.country.name}</p>
          <Button color="light-blue" variant="text" size="small" onClick={handleRemoveItem} type="button">
            Remove
          </Button>
        </div>
        <div className="flex items-center gap-2">
          {countryCheck && <CheckCircle className="size-6 flex-none fill-success" />}
          <KeyboardArrowUp className="flex-none size-8 fill-primary group-open:-rotate-180 transition-all duration-300" />
        </div>
      </summary>
      <div className="bg-surface-02 px-6 md:px-10">
        <p className="text-base text-tonal-dark-cream-30 pt-4">{t("enterInformation")}</p>
        <details ref={commitmentRef} className="group/commitment" open={!license.commitment.filled}>
          <summary className="flex flex-col">
            <div className="flex items-center justify-between py-6">
              <div className="flex items-center gap-4">
                <p className="text-xl font-bold text-primary">{t("commitmentAssessment")}</p>
                <QuestionTooltip>
                  <QuestionTooltipDescription>
                    Lorem ipsum dolor sit amet consectetur. Gravida vitae fermentum faucibus arcu non. In volutpat nisl
                    nunc pellentesque.
                  </QuestionTooltipDescription>
                </QuestionTooltip>
              </div>
              <Remove className="hidden group-open/commitment:block size-8 fill-support-blue cursor-pointer" />
              <Add className="block group-open/commitment:hidden size-8 fill-support-blue cursor-pointer" />
            </div>
            {license.commitment.filled && (
              <Divider className="block group-open/commitment:hidden m-0" style={{ margin: 0 }} />
            )}
          </summary>
          <div className="flex flex-col pb-6">
            <div className="space-y-6 md:space-y-10">
              {!!commitmentQuestions.length &&
                commitmentQuestions.map((question) => (
                  <div key={`question-${question.id}`} className="flex flex-col gap-4">
                    <p className="text-base text-tonal-dark-cream-10">{question.title}</p>
                    {question.options.map((option) => (
                      <label
                        key={`question-${question.id}-option-${option.value}`}
                        className="text-base text-tonal-dark-cream-20 flex gap-2 items-center cursor-pointer has-[input:checked]:cursor-default"
                      >
                        <input
                          type="radio"
                          {...register(`items.${code}.commitment.questions.${`criteria_${question.id}`}`, {
                            onChange: (e) => {
                              const { value } = e.target;

                              clearErrors(`items.${code}.commitment.questions.${`criteria_${question.id}`}`);
                              updateCommitmentQuestionAnswer(String(question.id), value);
                            },
                          })}
                          value={option.value}
                          disabled={license.commitment.filled}
                          className="hidden peer"
                          data-invalid={!!unfilledQuestionErrors?.[`criteria_${question.id}`]}
                        />
                        <RadioSelected className="hidden peer-checked:block size-5 fill-primary" />
                        <RadioUnselected className="block cursor-pointer peer-checked:hidden size-5 fill-primary peer-data-[invalid=true]:fill-error" />
                        {question.input_type === "SELECT" && <span className="mt-1">{option.option_value}</span>}
                        {question.input_type === "YES_NO" && <>{option.option_value === "YES" ? c("yes") : c("no")}</>}
                      </label>
                    ))}
                  </div>
                ))}
              {!commitmentQuestions.length && (
                <div className="flex justify-center items-center gap-1 md:gap-2 px-2 py-3 border-[1px] border-tonal-dark-cream-80 rounded-md">
                  <BiLoader className="fill-primary animate-spin" />
                  <p className="text-center text-primary">{t("loadingCommitment")}</p>
                </div>
              )}
              <Divider style={{ marginTop: 24 }} />
            </div>

            {!license.commitment.filled ? (
              !!commitmentQuestions.length && (
                <div className="flex flex-col items-end justify-center gap-4 pt-6">
                  {!!unfilledQuestionErrors && <p className="text-error">{t("mustAnswerAll")}</p>}
                  <Button
                    color={!!unfilledQuestionErrors ? "red" : "yellow"}
                    disabled={!!unfilledQuestionErrors}
                    variant="filled"
                    size="medium"
                    onClick={handleCheckCommitment}
                    trailingIcon={<East />}
                    type="button"
                  >
                    {c("confirm")}
                  </Button>
                </div>
              )
            ) : (
              <div className="flex flex-col justify-center gap-4 pt-6">
                <div>
                  <Button
                    type="button"
                    color="dark-blue"
                    variant="filled"
                    size="small"
                    style={{ padding: "10px 44px" }}
                    onClick={handleEditCommitment}
                  >
                    {t("editAnswers")}
                  </Button>
                </div>
              </div>
            )}
          </div>
        </details>
        {license.commitment.filled && (
          <div className="flex flex-col gap-8 py-5 md:py-10">
            {!!packagingServices.length &&
              packagingServices.map((packagingService) => {
                const packagingErrors = errors?.items?.[code]?.packagingServices?.[packagingService.id];

                const fractionErrors = packagingErrors?.fractions;

                return (
                  <div key={`${code}_${packagingService.id}`} className="p-4 md:p-8 rounded-4xl bg-surface-03">
                    <div className="flex items-center gap-4">
                      <p className="text-xl font-bold text-tonal-dark-cream-30 mt-1">{t("result")}</p>
                      <p
                        data-license={packagingService.required}
                        className="text-base font-bold mt-1 text-tonal-dark-green-30 data-[license=true]:text-on-surface-04"
                      >
                        {packagingService.required ? t("licensingRequired") : t("notObligated")}
                      </p>
                      {packagingService.added && (
                        <QuestionTooltip>
                          <QuestionTooltipDescription>
                            Lorem ipsum dolor sit amet consectetur. Gravida vitae fermentum faucibus arcu non. In
                            volutpat nisl nunc pellentesque.
                          </QuestionTooltipDescription>
                        </QuestionTooltip>
                      )}
                    </div>
                    {!packagingService.added && (
                      <p className="text-sm text-tonal-dark-cream-30 mt-4 mb-6">
                        Lorem ipsum dolor sit amet consectetur. Posuere eget gravida et tincidunt. Lorem ipsum dolor sit
                        amet consectetur. Posuere eget gravida et tincidunt.Lorem ipsum.
                      </p>
                    )}
                    <Divider style={{ margin: "20px 0" }} />
                    {packagingService.added ? (
                      <div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <p
                              data-invalid={!!packagingErrors}
                              className="text-2xl font-bold text-primary data-[invalid=true]:text-error"
                            >
                              {packagingService.title}
                            </p>
                            <QuestionTooltip>
                              <QuestionTooltipDescription>
                                Lorem ipsum dolor sit amet consectetur. Gravida vitae fermentum faucibus arcu non. In
                                volutpat nisl nunc pellentesque.
                              </QuestionTooltipDescription>
                            </QuestionTooltip>
                          </div>
                          <Button
                            type="button"
                            variant="text"
                            color="gray"
                            size="iconXSmall"
                            onClick={() => {
                              updatePackagingAdded(packagingService.id, false);
                              reset({
                                items: {
                                  [code]: {
                                    packagingServices: {
                                      [packagingService.id]: {
                                        fractions: {},
                                      },
                                    },
                                  },
                                },
                              });
                            }}
                          >
                            <Delete className="size-5 fill-tonal-dark-cream-40" />
                          </Button>
                        </div>
                        <div>
                          <p
                            data-invalid={!!packagingErrors && isSubmitted}
                            className="text-sm text-tonal-dark-cream-30 data-[invalid=true]:text-error my-5"
                          >
                            {t("pleaseProvideEstimateQuantity")}
                          </p>
                          <div className="rounded-[20px] overflow-hidden space-y-[1px] bg-tonal-dark-cream-80">
                            {Object.values(packagingService.fractions).map((fraction) => (
                              <div className="bg-white" key={`${code}-fractionContainer-${fraction.name}`}>
                                <div className="py-[14px] px-5 flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
                                  <div className="flex items-center gap-4">
                                    <QuestionTooltip>
                                      <QuestionTooltipTitle>
                                        <Aluminium width={24} className="fill-primary" />
                                        <p className="text-primary text-md font-bold">{fraction.name}</p>
                                      </QuestionTooltipTitle>
                                      <QuestionTooltipDescription className="text-primary">
                                        {t("bottleTopsFilmChocolate")}
                                      </QuestionTooltipDescription>
                                    </QuestionTooltip>

                                    <div className="flex flex-1 items-center gap-3">
                                      <Aluminium className="size-6 md:size-9 fill-primary" />
                                      <p className="text-sm md:text-base font-bold text-primary">{fraction.name}</p>
                                    </div>
                                  </div>
                                  <div className="flex items-center gap-4 w-full md:w-48 flex-shrink-0">
                                    <Controller
                                      key={`${code}-fraction-${fraction.key}`}
                                      name={`items.${code}.packagingServices.${packagingService.id}.fractions.fraction_${fraction.key}`}
                                      control={control}
                                      render={({ field }) => (
                                        <FractionInput
                                          type="weight"
                                          {...field}
                                          data-invalid={
                                            !!fractionErrors && !!fractionErrors[`fraction_${fraction.key}`]
                                          }
                                          onChange={(value) => {
                                            field.onChange(value);
                                            updatePackagingFraction({
                                              packagingId: packagingService.id,
                                              fractionKey: fraction.key,
                                              fractionValue: Number(value || 0),
                                              fractionPrice: fraction.price || 0,
                                            });
                                          }}
                                        />
                                      )}
                                    />
                                    <span className="text-base text-primary">kg</span>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div>
                        <div className="flex items-center justify-between">
                          <p className="text-2xl font-bold text-primary">{packagingService.title}</p>
                          <div className="flex items-center gap-10">
                            <Button
                              type="button"
                              variant="filled"
                              color="yellow"
                              size="iconXSmall"
                              onClick={() => {
                                updatePackagingAdded(packagingService.id, true);
                              }}
                            >
                              <Add className="size-5 fill-primary" />
                            </Button>
                          </div>
                        </div>
                        <p className="mt-2 text-sm text-tonal-dark-cream-30">
                          Lorem ipsum dolor sit amet consectetur. Posuere eget gravida et tincidunt. Lorem ipsum dolor
                          sit amet consectetur. Posuere eget gravida et tincidunt.Lorem ipsum.
                        </p>
                      </div>
                    )}
                  </div>
                );
              })}
            {!packagingServices.length && (
              <div className="flex justify-center items-center gap-1 md:gap-2 px-2 py-3 border-[1px] border-tonal-dark-cream-80 rounded-md">
                <BiLoader className="fill-primary animate-spin" />
                <p className="text-center text-primary"> {t("loadingPackaging")}</p>
              </div>
            )}
          </div>
        )}
      </div>
    </details>
  );
}
