"use client";

import { useSearchParams } from "next/navigation";

import { useLiberatedCountries } from "@/hooks/use-liberated-countries";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useEffect } from "react";
import { useCustomer } from "../../customer-profile/use-customer";
import { Step1 } from "./step-1";
import { Step2 } from "./step-2";

export function AddCountriesToLicensingForm() {
  const { liberatedCountries } = useLiberatedCountries();

  const { paramValues, changeParam } = useQueryFilter(["selected-codes", "step"]);

  const searchParams = useSearchParams();

  const step = searchParams.get("step") === "2" ? "2" : "1";

  const { customerId, customer, isLoading, refetch } = useCustomer();

  const euLicenseContract = customer?.contracts?.find((contract) => contract.type === "EU_LICENSE");

  function handleRemoveCountryCode(countryCode: string) {
    const selectedCountryCodes = paramValues?.["selected-codes"]?.split(",").filter(Boolean) || [];

    const countryCodesFiltered = selectedCountryCodes.filter((code) => code !== countryCode);

    changeParam(`selected-codes`, countryCodesFiltered.join(","));
  }

  useEffect(() => {
    if (!customerId || isLoading) return;
  }, [customerId, isLoading]);

  const licenses = customer?.contracts.find((contract) => contract.type === `EU_LICENSE`)?.licenses;

  const liberatedCountriesFilter = liberatedCountries.filter(
    (country) => !licenses?.some((item) => item.country_code === country.code)
  );

  return (
    <div className="space-y-11 w-full md:max-w-2xl">
      {step === "1" && (
        <Step1 onRemoveCountryCode={handleRemoveCountryCode} liberatedCountries={liberatedCountriesFilter} />
      )}
      {step === "2" && (
        <Step2 onRemoveCountryCode={handleRemoveCountryCode} liberatedCountries={liberatedCountriesFilter} />
      )}
    </div>
  );
}
