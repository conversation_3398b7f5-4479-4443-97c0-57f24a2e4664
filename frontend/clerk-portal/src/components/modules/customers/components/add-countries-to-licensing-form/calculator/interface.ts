import { PublishedCountry } from "@/hooks/use-liberated-countries";

export type Criteria = {
  id: number;
  mode: "COMMITMENT" | "CALCULATOR";
  type: CriteriaType;
  title: string;
  help_text: string | null;
  input_type: "YES_NO" | "SELECT";
  calculator_type: string | null;
  country_id: number;
  packaging_service_id: number | null;
  required_information_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  options: {
    id: number;
    option_value: string;
    option_to_value: string | null;
    value: string;
  }[];
};

type CriteriaType =
  | "PACKAGING_SERVICE"
  | "REPORT_SET"
  | "REPORT_FREQUENCY"
  | "AUTHORIZE_REPRESENTATIVE"
  | "REPRESENTATIVE_TIER"
  | "OTHER_COST"
  | "PRICE_LIST"
  | "REQUIRED_INFORMATION";

export type Commitment = (Omit<Criteria, "mode"> & { mode: "COMMITMENT" })[];

export interface Fractions {
  key: string;
  name: string;
  value?: number;
  priceListCode?: string;
  price?: number;
}

export type ItemPackagingServiceReportSetFraction = {
  id: number;
  name: string;
  description: string;
  icon: string;
  isActive: boolean;
  reportSetId: number;
  parentId: number | null;
  value?: number;
  price: number;
  children: ItemPackagingServiceReportSetFraction[];
};

export type ItemPackagingServiceReportSetColumn = {
  id: number;
  name: string;
  description: string;
  reportSetId: number;
  parentId: number | null;
  unitType: string;
  children: ItemPackagingServiceReportSetColumn[];
};

export interface ItemPackagingService {
  id: string;
  service: PackagingServiceType;
  title: string;
  added: boolean;
  required: boolean;
  reportSet: {
    id: number;
    name: string;
    mode: string;
    type: string;
    fractions: ItemPackagingServiceReportSetFraction[];
    columns: ItemPackagingServiceReportSetColumn[];
  };
  reportFrequency: {
    id: number;
    rhythm: string;
    frequency: {
      deadline: unknown;
      open: unknown;
    };
  };
  fractions: Record<string, Fractions>;
  price: number;
  fractionsCost: number;
}

export type ShoppingCartItemCommitmentQuestion = Commitment[number] & {
  answer?: string;
};

export interface ItemCommitment {
  filled: boolean;
  questions: Record<string, ShoppingCartItemCommitmentQuestion>;
}

export type PackagingServiceType = string;

export type ItemLicense = {
  type: "EU_LICENSE" | "DIRECT_LICENSE";
  year: string;
  period?: string;
  packagingServices: {
    [Type in PackagingServiceType]: ItemPackagingService;
  };
  representativeTier?: {
    id: number;
    name: string;
    price: number;
  };
  otherCosts: {
    id: number;
    name: string;
    price: number;
  }[];
  requiredInformations: {
    id: number;
    type: string;
    name: string;
    description: string;
    question: string | null;
    fileId: string | null;
  }[];
  commitment: ItemCommitment;
  priceList: {
    id: string;
    serviceType: "EU_LICENSE";
    name: string;
    description: string;
    conditionType: "LICENSE_YEAR";
    conditionTypeValue: string;
    startDate: string;
    endDate: string;
    basicPrice: number;
    minimumPrice: number;
    registrationFee: number;
    handlingFee: number;
    variableHandlingFee: number;
  };
};

export type CalculatorItemActionGuide = {
  type: "PACKAGING";
  priceList: {
    id: string;
    serviceType: "ACTION_GUIDE";
    title: string;
    description: string;
    conditionType: "LICENSE_YEAR";
    conditionTypeValue: string;
    startDate: string;
    endDate: string;
    price: number;
  };
};

export interface CalculatorItem {
  country: PublishedCountry;
  license?: ItemLicense;
  actionGuide?: CalculatorItemActionGuide;
}

export interface CalculatorLicense {
  items?: Record<string, CalculatorItem>;
}
