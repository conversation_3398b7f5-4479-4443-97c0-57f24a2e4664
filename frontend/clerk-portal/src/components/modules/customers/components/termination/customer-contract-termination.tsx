"use client";

import { CountryIcon } from "@/components/_common/country-icon";
import Divider from "@/components/_common/divider";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useRouter } from "@/i18n/navigation";
import { downloadCustomerFile } from "@/lib/api/file";
import { UploadedFile } from "@/lib/api/file/types";
import { getTermination, updateTermination } from "@/lib/api/termination";
import { downloadFile } from "@/utils/download-file";
import { formatFileSize } from "@/utils/format-file-size";
import { UserTypes } from "@/utils/user";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { KeyboardArrowLeft, Pdf } from "@arthursenno/lizenzero-ui-react/Icon";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { CgSpinnerAlt } from "react-icons/cg";
import { useCustomer } from "../../customer-profile/use-customer";
import { ConfirmTerminationModal } from "./customer-contract-termination-modal";
import { formatDate } from "@/utils/format-date";

export function CustomerTermination() {
  const t = useTranslations("Termination");
  const c = useTranslations("common");
  const { customer } = useCustomer();

  const [isOpenConfirmTerminationModal, setIsOpenConfirmTerminationModal] = useState(false);
  const [isTerminationConfirmed, setIsTerminationConfirmed] = useState(false);
  const router = useRouter();

  const { paramValues } = useQueryFilter(["termination-id"]);

  const terminationId = paramValues?.["termination-id"];

  const terminationQuery = useQuery({
    queryKey: ["termination", terminationId],
    queryFn: () => getTermination(Number(terminationId)),
    enabled: !!terminationId,
  });

  const confirmTerminationMutation = useMutation({
    mutationFn: () => updateTermination(Number(terminationId), { status: "PENDING" }),
  });

  const termination = terminationQuery.data;
  const isContractTermination = !!termination?.contract;

  const terminationItems = (() => {
    if (!termination) return [];

    if (isContractTermination) {
      return termination.licenses.length ? termination.licenses : termination.action_guides;
    }

    return termination.licenses.length ? termination.licenses : termination.action_guides;
  })();

  function handleSubmit() {
    confirmTerminationMutation.mutate(undefined, {
      onSuccess: () => {
        setIsTerminationConfirmed(true);
        enqueueSnackbar(t("confirmed"), { variant: "success" });
        router.push(`/customers/${customer?.id}`);
      },
      onError: () => {
        enqueueSnackbar(t("errorToConfirm"), { variant: "error" });
      },
    });
  }

  const downloadFileMutation = useMutation({
    mutationFn: async (file: UploadedFile) => {
      const downloadedFile = await downloadCustomerFile({
        file_id: file.id,
        user_id: 0,
        user_role: UserTypes.CUSTOMER,
      });

      downloadFile({ buffer: downloadedFile, fileName: file.original_name });
    },
  });

  function downloadTerminationFile(file: UploadedFile) {
    downloadFileMutation.mutate(file, {
      onSuccess: () => {
        enqueueSnackbar(t("fileDownloaded"), { variant: "success" });
      },
      onError: () => {
        enqueueSnackbar(t("fileDownloadFailed"), { variant: "error" });
      },
    });
  }

  if (!customer || !terminationId) return null;

  const extraordinaryFiles = (termination?.files || []).filter((file) => file.type === "CONTRACT_TERMINATION");

  const contract = (() => {
    if (isContractTermination) {
      return termination?.contract || null;
    }

    if (!!termination?.licenses.length) {
      const contract = customer.contracts.find((contract) => contract.type === "EU_LICENSE");

      return contract;
    }

    if (!!termination?.action_guides.length) {
      const contract = customer.contracts.find((contract) => contract.type === "ACTION_GUIDE");

      return contract;
    }

    return null;
  })();

  const terminationText = (() => {
    if (isContractTermination) {
      if (contract?.type === "EU_LICENSE") {
        return {
          title: t("contractTermination"),
          description: t("licenseAllCountries"),
        };
      }

      if (contract?.type === "ACTION_GUIDE") {
        return {
          title: t("contractTermination"),
          description: t("actionAllCountries"),
        };
      }

      if (contract?.type === "DIRECT_LICENSE") {
        return {
          title: t("contractTermination"),
          description: t("directAllYears"),
        };
      }

      return null;
    }

    const itemCountryName = termination?.licenses[0]?.country_name || termination?.action_guides[0]?.country_name;

    return {
      title: t("countryContractTermination"),
      description: itemCountryName,
    };
  })();

  return (
    <div className="w-full space-y-10">
      <div className="flex items-center">
        <KeyboardArrowLeft className="size-6 fill-support-blue" />
        <p
          className="text-small-paragraph-regular text-support-blue hover:underline cursor-pointer"
          onClick={() => router.back()}
        >
          {t("backToCustomer")}
        </p>
      </div>
      <section className="space-y-8">
        <div className="flex items-center justify-between">
          <p className="text-title-1 text-tonal-dark-blue-10 font-bold">
            {isContractTermination ? t("contractRequest") : t("countryRequest")}
          </p>
          <div className="flex items-center gap-2">
            {terminationItems.map((item) => (
              <div key={item.country_code}>
                <CountryIcon country={{ flag: item?.country_flag, name: item?.country_name }} />
              </div>
            ))}
          </div>
        </div>
        <div>
          <div className="w-full h-auto flex flex-row justify-between mb-10 gap-4">
            <div className=" w-[704px] h-[285px] bg-[#F7F5F2] rounded-xl p-6 flex flex-col justify-center gap-8">
              <p className="text-title-3 text-primary font-bold">{t("terminationInfo")}</p>
              <div className="bg-white w-full h-[158px] rounded-xl flex p-4 justify-center">
                <div className="flex flex-col w-full gap-4">
                  <div className="flex w-full">
                    <div className="flex flex-col gap-2 flex-1">
                      <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("date")}</p>
                      <p className="text-paragraph-regular text-[#002652]">
                        {termination?.requested_at && formatDate(termination.requested_at)}
                      </p>
                    </div>
                    <div className="flex flex-col gap-2 flex-1">
                      <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("terminationDate")}</p>
                      <p className="text-paragraph-regular text-[#002652]">
                        {contract?.end_date && formatDate(contract.end_date)}
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    <p className="text-paragraph-regular text-tonal-dark-cream-30">{terminationText?.title}</p>
                    <p className="text-paragraph-regular text-[#002652]">{terminationText?.description}</p>
                  </div>
                </div>
              </div>
            </div>
            <div className=" w-[520px] h-[261px] bg-[#F7F5F2] rounded-xl p-6 flex flex-col justify-center gap-8">
              <p className="text-title-3 text-primary font-bold">{t("contactInfo")}</p>
              <div className="bg-white w-full h-[158px] rounded-xl flex p-4 justify-center">
                <div className="flex flex-col w-full gap-4">
                  <div className="flex w-full">
                    <div className="flex flex-col gap-2 flex-1">
                      <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("fullName")}</p>
                      <p className="text-paragraph-regular text-[#002652]">
                        {customer.first_name} {customer.last_name}
                      </p>
                    </div>
                    <div className="flex flex-col gap-2 flex-1">
                      <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("phoneMobile")}</p>
                      <p className="text-paragraph-regular text-[#002652]">{customer.phones[0]?.phone_number}</p>
                    </div>
                  </div>
                  <div className="flex flex-col gap-2">
                    <p className="text-paragraph-regular text-tonal-dark-cream-30">{t("email")}</p>
                    <p className="text-paragraph-regular text-[#002652]">{customer.email}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div>
          <div className="w-full  bg-surface-01 p-6 rounded-xl mb-8">
            <div className="flex flex-col gap-4 mb-3">
              <p className="text-title-3 text-tonal-dark-blue-10 font-bold">{t("reason")}</p>
              {!isContractTermination && !!termination?.licenses.length && (
                <p className="text-large-paragraph-regular text-tonal-dark-cream-20">
                  {c("year")}:{" "}
                  <span className="text-large-paragraph-regular font-bold text-tonal-dark-cream-20">
                    {!!termination?.licenses[0] && termination?.licenses[0].year}
                  </span>
                </p>
              )}
            </div>
            <Divider initialMarginDisabled />
            <div className="my-6 ">
              <ul className="flex flex-col gap-4">
                <ul className="flex flex-col gap-4">
                  {termination?.reasons?.map((terminationReason) => (
                    <li
                      data-extraordinary={terminationReason.reason.value === "EXTRAORDINARY_TERMINATION"}
                      key={terminationReason.reason.id}
                      className="text-paragraph-italic text-tonal-dark-cream-10 list-disc ml-4 data-[extraordinary=true]:font-medium data-[extraordinary=true]:underline underline-offset-2"
                    >
                      {terminationReason.reason.title}
                    </li>
                  ))}
                </ul>
              </ul>
            </div>
            <Divider initialMarginDisabled className="mt-12" />
            <div className="flex flex-col gap-3 mt-5 w-[350px]">
              <p className="text-large-paragraph-bold text-tonal-dark-blue-10 font-bold">
                {t("attachment")} ({extraordinaryFiles.length})
              </p>
              {!!extraordinaryFiles.length ? (
                extraordinaryFiles.map((extraordinaryFile) => (
                  <div key={extraordinaryFile.id} className="flex items-center gap-3 w-full">
                    <Pdf className="size-12" />
                    <div className="w-full">
                      <div className="flex justify-between items-center w-full">
                        <p className="text-paragraph-regular text-[#333333]">{extraordinaryFile.original_name}</p>
                        <Button
                          variant="text"
                          color="light-blue"
                          size="medium"
                          disabled={
                            downloadFileMutation.isPending && downloadFileMutation.variables.id === extraordinaryFile.id
                          }
                          onClick={() => downloadTerminationFile(extraordinaryFile)}
                          trailingIcon={
                            downloadFileMutation.isPending &&
                            downloadFileMutation.variables.id === extraordinaryFile.id && (
                              <CgSpinnerAlt size={16} className="animate-spin text-primary" />
                            )
                          }
                        >
                          {c("download")}
                        </Button>
                      </div>
                      <p className="text-small-paragraph-regular text-[#828282]">
                        {formatFileSize(Number(extraordinaryFile.size))}
                      </p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-paragraph-regular text-[#828282]">{c("noFilesUploaded")}</p>
              )}
            </div>
          </div>
        </div>
        <Divider />
        <div className="mb-10 flex flex-col gap-4">
          <p className="text-paragraph-regular text-tonal-dark-cream-10">{t("continue")}</p>
          <Button
            size="medium"
            color="red"
            variant="filled"
            className="w-[230px]"
            onClick={() => setIsOpenConfirmTerminationModal(true)}
          >
            {t("confirm")}
          </Button>
        </div>
      </section>
      <ConfirmTerminationModal
        open={isOpenConfirmTerminationModal}
        setOpen={setIsOpenConfirmTerminationModal}
        onConfirm={handleSubmit}
        customer={customer}
        isLoading={isTerminationConfirmed}
      />
    </div>
  );
}
