"use client";
import CustomCheckbox from "@/components/_common/checkbox";
import { CountryIcon } from "@/components/_common/country-icon";
import { ListCustomer } from "@/lib/api/customer/types";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { useState } from "react";
import { ConfirmTerminationModal } from "./customer-contract-termination-modal";
import { useQuery } from "@tanstack/react-query";
import { getReasons } from "@/lib/api/reasons";
import { Checkbox } from "@/components/ui/checkbox";
import { useTranslations } from "next-intl";

interface IModalCompletedProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  customer: ListCustomer;
  onSubmit?: (country: string, reasonIdSelected?: number) => void;
  onConfirm?: () => void;
}

export const ConfirmTerminationCountryModal = ({
  open,
  setOpen,
  customer,
  onSubmit,
  onConfirm,
}: IModalCompletedProps) => {
  const [selectedCountry, setSelectedCountry] = useState<{
    country_code: string;
    country_name: string;
    country_flag: string;
  } | null>(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [reasonIdSelected, setReasonIdSelected] = useState<number>();

  const t = useTranslations("CustomerProfileServicesContract");
  const c = useTranslations("common");
  const reasonsQuery = useQuery({
    queryKey: ["reasons", { type: "TERMINATION" }],
    queryFn: async () => {
      const reasons = await getReasons({ type: "TERMINATION" });

      if (reasons.length) setReasonIdSelected(reasons?.[0]?.id);

      return reasons.filter((reason) => reason.value !== "OTHER");
    },
  });

  const handleOpen = () => {
    setOpen(!open);
    if (!open === false) {
      setSelectedCountry(null);
    }
  };

  const handleCountrySelect = (country: { country_code: string; country_name: string; country_flag: string }) => {
    setSelectedCountry(country);
  };

  const handleTerminate = () => {
    if (selectedCountry) {
      setShowConfirmModal(true);
    }
  };

  const countries = customer?.contracts
    .reduce(
      (acc, contract) => {
        contract.licenses.forEach((license) => {
          if (acc.find((country) => country.country_code === license.country_code)) return;
          acc.push({
            country_code: license.country_code,
            country_name: license.country_name,
            country_flag: license.country_flag,
            hasTermination: !!license.termination,
          });
        });

        contract.action_guides.forEach((actionGuide) => {
          if (acc.find((country) => country.country_code === actionGuide.country_code)) return;

          acc.push({
            country_code: actionGuide.country_code,
            country_name: actionGuide.country_name,
            country_flag: actionGuide.country_flag,
            hasTermination: false,
          });
        });

        return acc;
      },
      [] as { country_code: string; country_name: string; country_flag: string; hasTermination: boolean }[]
    )
    .filter((country) => !country.hasTermination);

  const allCountriesTerminated = countries?.every((country) => country.hasTermination);

  const declineReasons = reasonsQuery.data;

  return (
    <>
      <Modal
        open={open}
        className="z-50 w-full"
        style={{
          maxWidth: "600px",
          borderRadius: "52px",
          maxHeight: "100vh",
          backgroundColor: "#F0F0EF",
        }}
        onOpenChange={handleOpen}
      >
        <div className="p-5 h-[350px] overflow-auto flex flex-col gap-2">
          <div className="flex flex-row w-full justify-between">
            <div className="flex flex-col gap-2 justify-start">
              {allCountriesTerminated ? (
                <p className="text-[22px] text-primary font-bold">{t("noCountriesToTerminate")}</p>
              ) : (
                <p className="text-[22px] text-primary font-bold">{t("select")}</p>
              )}
            </div>
          </div>
          <div className="mt-2 flex flex-col gap-4">
            <p className="text-paragraph-regular text-tonal-dark-cream-20 my-2">{t("selectReason")}</p>
          </div>
          {!reasonsQuery.isLoading &&
            declineReasons?.map((declineReason) => (
              <div key={declineReason.id} className="flex items-center gap-2">
                <CustomCheckbox
                  id={String(declineReason.id)}
                  checked={declineReason.id === reasonIdSelected}
                  onChange={() => setReasonIdSelected(declineReason.id)}
                />
                <p className="text-paragraph-regular text-primary">{declineReason.title}</p>
              </div>
            ))}
          <div className="mt-2 flex flex-col gap-4">
            {allCountriesTerminated ? (
              <p className="text-paragraph-regular text-tonal-dark-cream-20 my-4">{t("allCountriesTerminated")}</p>
            ) : (
              <>
                <p className="text-paragraph-regular text-tonal-dark-cream-20">{t("select")}</p>
                <p className="text-tonal-dark-cream-20 ">
                  {c("year")}: <span className="text-tonal-dark-cream-20 font-bold">{new Date().getFullYear()}</span>
                </p>
              </>
            )}
          </div>
          {!allCountriesTerminated && (
            <div className="flex flex-col gap-2">
              {countries?.map((country) => (
                <div key={country.country_code} className="flex items-center gap-2">
                  <CustomCheckbox
                    id=""
                    checked={selectedCountry?.country_code === country.country_code}
                    onChange={() => handleCountrySelect(country)}
                  />
                  <CountryIcon country={{ flag: country.country_flag ?? "", name: country.country_name ?? "" }} />
                  <p className="text-paragraph-regular text-primary">{country.country_name}</p>
                </div>
              ))}
            </div>
          )}

          <div className="flex justify-end gap-3">
            <Button color="dark-blue" variant="outlined" size="medium" onClick={handleOpen}>
              {c("cancel")}
            </Button>
            <Button
              color="red"
              variant="filled"
              size="medium"
              onClick={handleTerminate}
              disabled={!selectedCountry || allCountriesTerminated}
            >
              {t("terminate")}
            </Button>
          </div>
        </div>
      </Modal>
      <ConfirmTerminationModal
        open={showConfirmModal}
        setOpen={setShowConfirmModal}
        country={selectedCountry?.country_name ?? undefined}
        onConfirm={() => {
          if (selectedCountry && onSubmit) {
            onSubmit(selectedCountry.country_code, reasonIdSelected);
            setShowConfirmModal(false);
            setOpen(false);
          }
        }}
      />
    </>
  );
};
