import { ServiceType } from "@/lib/api/customer/serviceType";
import { ListCustomer } from "@/lib/api/customer/types";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { useRouter } from "@/i18n/navigation";
import { useTranslations } from "next-intl";

interface IModalCompletedProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  country?: string;
  onConfirm?: () => void;
  customer?: ListCustomer;
  isLoading?: boolean;
}

export const CreateTerminationModal = ({
  open,
  setOpen,
  country,
  onConfirm,
  customer,
  isLoading,
}: IModalCompletedProps) => {
  const router = useRouter();

  const euContract = customer?.contracts?.filter((contract) => contract.type === "EU_LICENSE");
  const actionGuideContract = customer?.contracts?.filter((contract) => contract.type === "ACTION_GUIDE");
  const directContract = customer?.contracts?.filter((contract) => contract.type === "DIRECT_LICENSE");

  const t = useTranslations("CustomerProfileServicesContract");
  const c = useTranslations("common");
  const handleOpen = () => {
    setOpen(!open);
  };

  const getContractTitle = () => {
    if (euContract?.length) return "EU License";
    if (actionGuideContract?.length) return "Action Guide";
    if (directContract?.length) return "Direct License";
    return "License Service";
  };

  const getTerminationCountries = () => {
    const countries = new Set<string>();
    for (const contract of customer?.contracts || []) {
      for (const license of contract.licenses || []) {
        if (!license.termination) {
          countries.add(license.country_name);
        }
      }
    }
    return countries.size > 0 ? Array.from(countries).join(", ") : country ?? "All Countries";
  };

  return (
    <Modal
      open={open}
      className="z-50 w-full"
      style={{ maxWidth: "600px", borderRadius: "52px", maxHeight: "100vh", backgroundColor: "#F0F0EF" }}
      onOpenChange={handleOpen}
    >
      <div className="p-5 h-[260px] overflow-auto flex flex-col justify-center gap-2">
        <div className="flex flex-row w-full justify-between">
          <div className="flex flex-col  gap-2 justify-start">
            <p className="text-[28px] text-primary font-bold">{t("terminationProcessConfirmed")}</p>
          </div>
        </div>
        <div className="mt-2 flex flex-col gap-4">
          {ServiceType.LicensingServices ? (
            <div className="flex flex-col gap-2">
              <p className="text-paragraph-regular text-tonal-dark-cream-20">
                {t("terminationFor")}{" "}
                <span className="text-primary text-paragraph font-bold">
                  {getContractTitle()}, {getTerminationCountries()}
                </span>{" "}
                {t("hasStarted")}
              </p>
              <p className="text-paragraph-regular text-tonal-dark-cream-20">{t("customerWillBeNotified")}</p>
            </div>
          ) : (
            <>
              <p className="text-paragraph-regular text-tonal-dark-cream-20">
                {t("terminationFor")}{" "}
                <span className="text-primary text-paragraph font-bold">
                  {t("actionGuide")} {country}
                </span>
                {t("customerActionGuideNotification")}
              </p>
            </>
          )}
        </div>

        <div className=" flex justify-end">
          <Button
            trailingIcon={<East />}
            color="dark-blue"
            variant="filled"
            size="medium"
            onClick={onConfirm}
            disabled={isLoading}
          >
            {isLoading ? c("loading") : c("ok")}
          </Button>
        </div>
      </div>
    </Modal>
  );
};
