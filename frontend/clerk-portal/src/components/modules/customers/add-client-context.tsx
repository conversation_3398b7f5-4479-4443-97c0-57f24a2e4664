"use client";

import React, { createContext, useContext, useState } from "react";

interface ClientData {
  companyName?: string;
  countryCode?: string;
  addressLine?: string;
  city?: string;
  zipCode?: string;
  streetAndNumber?: string;
  documentType?: string;
  taxNumber?: string;
  salutation?: string;
  firstName?: string;
  surname?: string;
  phone?: string;
  emails?: string[];
  phones?: string[];
  emailLogin?: string;
  countries?: string[];
}

type ClientDataType = ClientData | null;

interface InfoAddClientContext {
  infoClient: ClientDataType;
  setInfoClient: React.Dispatch<React.SetStateAction<ClientDataType>>;
  infoClientsList: ClientDataType[];
  setInfoClientsList: React.Dispatch<React.SetStateAction<ClientDataType[]>>;
}

const InfoAddClientContext = createContext<InfoAddClientContext | null>(null);

export const InfoAddClientContextProvider = ({ children }: { children: React.ReactNode }) => {
  const [infoClient, setInfoClient] = useState<ClientDataType>(null);
  const [infoClientsList, setInfoClientsList] = useState<ClientDataType[]>([]);

  return (
    <InfoAddClientContext.Provider
      value={{
        infoClient,
        setInfoClient,
        infoClientsList,
        setInfoClientsList,
      }}
    >
      {children}
    </InfoAddClientContext.Provider>
  );
};

export const useInfoAddClient = () => {
  const context = useContext(InfoAddClientContext);

  if (!context) {
    throw new Error("context must be used inside the provider");
  }

  return context;
};
