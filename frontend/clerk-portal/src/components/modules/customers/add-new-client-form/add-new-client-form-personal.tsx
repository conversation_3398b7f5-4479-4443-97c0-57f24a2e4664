"use client";

import Divider from "@/components/_common/divider";
import { CustomRadio } from "@/components/_common/forms/customRadio/custom-radio";
import { PhoneInput } from "@/components/_common/forms/phone-input";
import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Check, Delete } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { useFieldArray, useFormContext, useWatch } from "react-hook-form";
import { AddNewClientFormSchemaData } from "./add-new-client-form-provider";
import { useTranslations } from "next-intl";

export function AddNewClientFormPersonal() {
  const {
    control,
    register,
    setValue,
    resetField,
    setError,
    clearErrors,
    formState: { errors },
  } = useFormContext<AddNewClientFormSchemaData>();

  const {
    salutation: salutationWatch,
    firstName: firstName<PERSON><PERSON>,
    surname: surname<PERSON><PERSON>,
    phone: phoneWatch,
    phones: phonesWatch,
    mobile: mobileWatch,
    emailLogin: emailLoginWatch,
  } = useWatch({ control });

  const {
    append: appendToPhones,
    remove: removeFromPhones,
    fields: phonesFields,
  } = useFieldArray({
    name: "phones",
  });

  function handleAddPhone() {
    const initialValue = " ";
    appendToPhones(initialValue);
  }

  function handleRemovePhone(index: number) {
    removeFromPhones(index);
  }

  const t = useTranslations("AddNewClient");

  return (
    <div className=" w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:px-8 md:py-7">
      <div className="flex items-center gap-2 mb-2">
        <p className="text-primary font-medium text-xl">{t("personalData")}</p>
        <QuestionTooltip>
          <QuestionTooltipDescription>
            Lorem ipsum dolor sit, amet consectetur adipisicing elit. Dolorem rem placeat tenetur dignissimos nisi,
            excepturi maxime ut enim debitis eveniet in repudiandae sit possimus impedit voluptatibus cum? Praesentium,
            officia accusantium.
          </QuestionTooltipDescription>
        </QuestionTooltip>
      </div>
      <p className="text-[#808FA9] font-light text-sm mb-8">{t("mandatory")}</p>
      <div className="flex flex-col gap-6 w-full">
        <div>
          <div className="mb-2">
            <p className="text-primary">{t("salutation")} *</p>
            {errors.salutation?.message && <p className="text-error">{errors.salutation?.message}</p>}
          </div>
          <div className="flex gap-4">
            <CustomRadio
              checked={salutationWatch === "Mr."}
              onChange={() => {
                setValue("salutation", "Mr.");
                clearErrors(`salutation`);
              }}
              label={"Mr."}
            />
            <CustomRadio
              checked={salutationWatch === "Mrs."}
              onChange={() => {
                setValue("salutation", "Mrs.");
                clearErrors(`salutation`);
              }}
              label={"Mrs."}
            />
          </div>
        </div>
        <div className="grid md:grid-cols-2 w-full gap-8">
          <Input
            label={t("firstName")}
            placeholder={t("firstName")}
            {...register("firstName")}
            variant={errors.firstName && "error"}
            errorMessage={errors.firstName?.message}
            rightIcon={
              !errors.firstName && firstNameWatch && <Check width={20} height={20} className="fill-tonal-green-40" />
            }
          />
          <Input
            label={t("surname")}
            placeholder={t("surname")}
            {...register("surname")}
            variant={errors.surname && "error"}
            errorMessage={errors.surname?.message}
            rightIcon={
              !errors.surname && surnameWatch && <Check width={20} height={20} className="fill-tonal-green-40" />
            }
          />
        </div>
        <div className="grid lg:grid-cols-2 w-full gap-8">
          <div className="space-y-2">
            <p className="text-primary">{t("Phone")} *</p>
            <PhoneInput
              name="phone"
              defaultValue={phoneWatch}
              valueSetter={(value) => setValue("phone", value || ``)}
              errorSetter={(valid) =>
                valid ? clearErrors("phone") : setError("phone", { message: t("invalidPhone") })
              }
              isError={!!errors.phone}
            />
            {!!errors.phone && (
              <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
                  {errors.phone.message}
                </span>
              </div>
            )}
          </div>
          <div className="space-y-2">
            <p className="text-primary">{t("mobile")}</p>
            <PhoneInput
              name="mobile"
              defaultValue={mobileWatch}
              valueSetter={(value) => setValue("mobile", value)}
              errorSetter={(valid) =>
                valid ? clearErrors("mobile") : setError("mobile", { message: t("invalidPhone") })
              }
              isError={!!errors.mobile}
              required={false}
            />
            {!!errors.mobile && (
              <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
                  {errors.mobile.message}
                </span>
              </div>
            )}
          </div>
        </div>
        <div className="mt-10">
          <Input
            label={t("loginInfo")}
            placeholder={t("loginInfo")}
            {...register("emailLogin")}
            variant={errors.emailLogin && "error"}
            errorMessage={errors.emailLogin?.message}
            rightIcon={
              !errors.emailLogin && emailLoginWatch && <Check width={20} height={20} className="fill-tonal-green-40" />
            }
          />
        </div>
      </div>

      <Divider />

      <div className="space-y-6 w-full">
        <p className="text-primary">{t("otherNumbers")}</p>

        {phonesFields.map((phone, index) => {
          return (
            <div className="flex gap-6 items-center" key={phone.id}>
              <div className="lg:w-1/2 w-full">
                <div className="space-y-2">
                  <p className="text-primary">{t("Phone")}</p>
                  <div className="space-y-2">
                    <div className="flex items-center gap-4">
                      <PhoneInput
                        name={`phones.${index}`}
                        defaultValue={phonesWatch?.[index]}
                        valueSetter={(value) => {
                          value
                            ? setValue(`phones.${index}`, value)
                            : resetField(`phones.${index}`, { keepError: true, defaultValue: "" });
                        }}
                        errorSetter={(valid) =>
                          valid
                            ? clearErrors(`phones.${index}`)
                            : setError(`phones.${index}`, { message: t("invalidPhone") })
                        }
                        isError={Boolean(!!errors.phones && errors.phones[index])}
                      />
                      <Button
                        color="dark-blue"
                        size="iconSmall"
                        variant="text"
                        trailingIcon={<Delete style={{ fill: "inherit" }} />}
                        type="button"
                        onClick={() => handleRemovePhone(index)}
                      />
                    </div>
                    {!!errors.phones && errors.phones[index] && (
                      <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                        <span slot="errorMessage" className="font-centra text-sm text-tonal-red-40">
                          {errors.phones[index]!.message}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
      <button
        type="button"
        onClick={handleAddPhone}
        className="cursor-pointer font-medium mt-4 text-support-blue hover:text-support-blue/70"
      >
        {t("addPhone")} →
      </button>
    </div>
  );
}
