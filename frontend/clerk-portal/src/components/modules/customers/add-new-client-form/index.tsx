"use client";

import { useRouter } from "@/i18n/navigation";
import { useState } from "react";
import { useFormContext } from "react-hook-form";
import { AddNewClientFormCompany } from "./add-new-client-form-company";
import { AddNewClientFormPersonal } from "./add-new-client-form-personal";
import { AddNewClientFormSchemaData } from "./add-new-client-form-provider";
import { CheckCustomerDataModal } from "./check-customer-data-modal";
import { createCustomerWithcountries } from "@/lib/api/customer";
import { enqueueSnackbar } from "notistack";

export function AddNewClientForm() {
  const router = useRouter();

  const { handleSubmit, setError } = useFormContext<AddNewClientFormSchemaData>();
  const [infoClient, setInfoClient] = useState<AddNewClientFormSchemaData>();
  const [isOpenCheckCustomerDataModal, setIsOpenCheckCustomerDataModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  async function handleFormSubmit(data: AddNewClientFormSchemaData) {
    if (!data.taxNumber && !data.vatId) {
      if (data.documentType === `TAX`) return setError(`taxNumber`, { message: `Required field` });
      else return setError(`vatId`, { message: `Required field` });
    }

    if (data?.service !== `DIRECT_LICENSE` && data?.countries?.length < 1)
      return setError(`countries`, { message: `There must be at least 1 country selected` });

    setInfoClient(data);
    setIsOpenCheckCustomerDataModal(true);
  }

  const handleClickConfirmation = async () => {
    if (!infoClient) return;

    const payload = {
      company: {
        name: infoClient.companyName,
        address: {
          country_code: infoClient.countryCode,
          address_line: infoClient.addressLine,
          city: infoClient.city,
          zip_code: infoClient.zipCode,
          street_and_number: infoClient.streetAndNumber,
          additional_address: infoClient.additionalAddressLine || "",
        },
        vat: infoClient.vatId || null,
        tin: infoClient.taxNumber || null,
        emails: infoClient.emails,
      },
      customer: {
        email: infoClient.emailLogin,
        salutation: infoClient.salutation,
        first_name: infoClient.firstName,
        last_name: infoClient.surname,
        phones: [infoClient.phone, infoClient.mobile || "", ...(infoClient.phones || [])],
      },
      service_type: infoClient.service,
      country_codes: infoClient.countries,
    };
    setIsLoading(true);

    const res: any = await createCustomerWithcountries(payload);

    setIsLoading(false);
    if (!res.data) {
      const status = res?.response?.status;
      const msg = res?.response?.data?.message?.toLowerCase();

      if (status === 409 || msg?.includes(`user with email`)) {
        if (msg?.includes(`email`)) {
          setError(`emailLogin`, { message: `Email already in use` });
        }

        if (msg?.includes(`document`) || msg?.includes(`tax`) || msg?.includes(`vat`)) {
          if (infoClient.documentType === `TAX`) {
            setError(`taxNumber`, { message: `Tax already in use` });
          } else {
            setError(`vatId`, { message: `Vat already in use` });
          }
        }
      }

      setIsOpenCheckCustomerDataModal(false);
      return enqueueSnackbar("Error saving data", { variant: "error" });
    }

    const baseUrl = `${window.location.protocol}//${window.location.host}`;
    router.push(`${baseUrl}/en/customers?client-added=true`);
  };

  return (
    <>
      <form id="add-new-client-form" onSubmit={handleSubmit(handleFormSubmit)}>
        <div className="flex flex-col gap-8">
          <AddNewClientFormCompany />
          <AddNewClientFormPersonal />
        </div>
      </form>
      <CheckCustomerDataModal
        isOpen={isOpenCheckCustomerDataModal}
        infoClient={infoClient}
        onClickConfirmation={handleClickConfirmation}
        onOpenChange={() => setIsOpenCheckCustomerDataModal((curr) => !curr)}
        isLoading={isLoading}
      />
    </>
  );
}
