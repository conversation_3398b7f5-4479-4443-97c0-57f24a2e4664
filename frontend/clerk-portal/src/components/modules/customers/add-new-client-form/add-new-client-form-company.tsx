"use client";

import { CountryIcon } from "@/components/_common/country-icon";
import Divider from "@/components/_common/divider";
import { AutocompleteAddressInput } from "@/components/_common/forms/address-input";
import { CustomRadio } from "@/components/_common/forms/customRadio/custom-radio";
import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { Combobox } from "@/components/ui/combobox";
import { AddressSuggestionDetails } from "@/lib/api/address-suggestions";
import { validateVatId } from "@/lib/api/company";
import { COUNTRIES, EU_COUNTRY_CODES } from "@/utils/countries";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Check, Delete } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { useState } from "react";
import { useFieldArray, useFormContext, useWatch } from "react-hook-form";
import { CgSpinnerAlt } from "react-icons/cg";
import { AddNewClientFormSchemaData } from "./add-new-client-form-provider";
import { useTranslations } from "next-intl";

export function AddNewClientFormCompany() {
  const {
    control,
    register,
    getValues,
    setValue,
    clearErrors,
    setError,
    resetField,
    formState: { errors },
  } = useFormContext<AddNewClientFormSchemaData>();

  const {
    documentType: documentTypeWatch,
    countryCode: countryCodeWatch,
    companyName: companyNameWatch,
    zipCode: zipCodeWatch,
    city: cityWatch,
    streetAndNumber: streetAndNumberWatch,
    addressLine: addressLineWatch,
  } = useWatch({ control });

  const [vatInfoMessage, setVatInfoMessage] = useState<string | null>(null);
  const [loadingValidatVat, setLoadingValidatVat] = useState(false);
  const [isFocusedVat, setIsFocusedVat] = useState(false);
  const [addressInputKey, setAddressInputKey] = useState(0);

  const t = useTranslations("AddNewClientFormCompany");

  function setDocumentType(type: "VAT" | "TAX") {
    if (loadingValidatVat) return;
    if (type === "VAT") {
      resetField("taxNumber");
      setValue("documentType", "VAT");
      setValue("taxNumber", undefined);

      clearErrors("taxNumber");
    }
    if (type === "TAX") {
      resetField("vatId");
      setValue("documentType", "TAX");
      setValue("vatId", undefined);
      setVatInfoMessage("");
      clearErrors("vatId");
    }

    clearErrors(`documentType`);
  }

  function handleSelectCountry(country: { value: string; label: string } | null) {
    setValue("city", "");
    setValue("zipCode", "");
    setValue("streetAndNumber", "");
    setValue("addressLine", "");
    setValue("countryCode", "");

    setAddressInputKey((prevKey) => prevKey + 1);

    if (!country) return;

    const countryName = country.value;

    const foundCountry = COUNTRIES.find((c) => c.name === countryName);

    if (!foundCountry) return;

    const isEuCountry = EU_COUNTRY_CODES.find((code) => code === foundCountry.code);

    setValue("countryCode", foundCountry.code);
    clearErrors(`countryCode`);

    const documentType = isEuCountry ? "VAT" : "TAX";

    setDocumentType(documentType);
  }

  function handleSelectAddress(address: AddressSuggestionDetails) {
    const data = {
      addressLine: address.formattedAddress,
      city: address.city || undefined,
      zipCode: address.postalCode || undefined,
      streetAndNumber: ``,
    };

    if (address.route) {
      const streetAndNumber = [address.route];

      if (address.streetNumber) streetAndNumber.push(address.streetNumber);

      data.streetAndNumber = streetAndNumber.join(", ");
    }

    if (data.addressLine) setValue("addressLine", data.addressLine);
    if (data.city) setValue("city", data.city);
    if (data.zipCode) setValue("zipCode", data.zipCode);
    if (data.streetAndNumber) setValue("streetAndNumber", data.streetAndNumber);
  }

  async function handleSelectVatId(vatId: string) {
    if (documentTypeWatch !== `VAT` || loadingValidatVat) return;

    setLoadingValidatVat(true);

    const formattedVatId = vatId.replace(/\s/g, "").trim();

    const vatValidationResponse = await validateVatId({
      vat_id: formattedVatId,
      country_code: countryCodeWatch?.toUpperCase(),
      company_name: companyNameWatch,
      company_zipcode: zipCodeWatch,
      company_city: cityWatch,
      company_street: streetAndNumberWatch,
    });

    if (!vatValidationResponse.valid && documentTypeWatch === "VAT") {
      setError("vatId", { type: "manual", message: vatValidationResponse.code || t("invalidVAT") });
      setLoadingValidatVat(false);
      return;
    }

    clearErrors("zipCode");
    setValue("zipCode", zipCodeWatch || ``);
    setValue("vatId", formattedVatId);
    setLoadingValidatVat(false);
  }

  const {
    append: appendToEmails,
    remove: removeFromEmails,
    fields: emailsFields,
  } = useFieldArray({
    name: "emails",
  });

  function handleAddEmail() {
    const initialValue = " ";
    appendToEmails(initialValue);
  }

  function handleRemoveEmail(index: number) {
    removeFromEmails(index);
  }

  const selectCountryInfo = COUNTRIES.find((c) => c.code === countryCodeWatch);

  const isTaxAndVatEnabled = !!selectCountryInfo?.name && !!zipCodeWatch && !!cityWatch && !!streetAndNumberWatch;

  return (
    <div className="w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:px-8 md:py-7">
      <div className="flex items-center gap-2 mb-2">
        <p className="text-primary font-medium text-xl">{t("companyInformation")}</p>
        <QuestionTooltip>
          <QuestionTooltipDescription>
            Lorem ipsum dolor sit, amet consectetur adipisicing elit. Dolorem rem placeat tenetur dignissimos nisi,
            excepturi maxime ut enim debitis eveniet in repudiandae sit possimus impedit voluptatibus cum? Praesentium,
            officia accusantium.
          </QuestionTooltipDescription>
        </QuestionTooltip>
      </div>
      <p className="text-[#808FA9] font-light text-sm mb-8">*{t("mandatoryFields")}</p>
      <div className="space-y-6 w-full">
        <div className="w-full ">
          <Input
            label={t("companyName") + " *"}
            placeholder={t("companyName")}
            {...register("companyName")}
            errorMessage={errors.companyName?.message}
            rightIcon={
              !errors.companyName &&
              getValues("companyName") && <Check width={20} height={20} className="fill-tonal-green-40" />
            }
            variant={errors.companyName && "error"}
          />
        </div>

        <div className="grid md:grid-cols-2 w-full gap-8">
          <div className="space-y-2">
            <p className="text-primary">{t("country")} *</p>
            <Combobox
              items={COUNTRIES.map((c) => ({ label: c.name, value: c.name, flag: c.flag }))}
              placeholder={t("country")}
              emptyText={t("noCountryFound")}
              searchText={t("searchCountry")}
              value={selectCountryInfo?.name}
              onSelect={handleSelectCountry}
              invalid={!!errors.countryCode}
              renderItem={(item) => (
                <div className="flex items-center gap-3 text-primary">
                  <CountryIcon country={{ name: item.label, flag: item.flag }} className="size-6" />
                  {item.label}
                </div>
              )}
            />
            {!!errors.countryCode && (
              <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
                  {errors.countryCode.message}
                </span>
              </div>
            )}
          </div>
          <AutocompleteAddressInput
            key={addressInputKey}
            defaultValue={addressLineWatch}
            countryCode={countryCodeWatch}
            onChangeAddressLine={(addressLine) => {
              clearErrors("city");
              clearErrors("zipCode");
              clearErrors("streetAndNumber");

              if (!addressLine) {
                setError("addressLine", { message: t("additionalAddressLine") });
              } else {
                clearErrors("addressLine");
              }
            }}
            onSelectAddress={handleSelectAddress}
            isError={!!errors.addressLine}
            errorMessage={errors.addressLine?.message}
          />
        </div>

        <div className="grid md:grid-cols-2 w-full gap-8">
          <Input
            label={t("city") + " *"}
            placeholder={t("city")}
            rightIcon={
              !errors.city && getValues("city") && <Check width={20} height={20} className="fill-tonal-green-40" />
            }
            errorMessage={errors.city?.message}
            {...register("city")}
            variant={errors.city && "error"}
            enabled={!!countryCodeWatch && !!addressLineWatch}
          />
          <Input
            label={t("zipCode") + " *"}
            placeholder={t("zipCode")}
            errorMessage={errors.zipCode?.message}
            {...register("zipCode")}
            rightIcon={
              !errors.zipCode &&
              getValues("zipCode") && <Check width={20} height={20} className="fill-tonal-green-40" />
            }
            variant={errors.zipCode && "error"}
            enabled={!!countryCodeWatch && !!addressLineWatch}
          />
        </div>

        <div className="grid md:grid-cols-2 w-full gap-8">
          <Input
            label={t("streetAndNumber") + " *"}
            placeholder={t("streetAndNumber")}
            rightIcon={
              !errors.streetAndNumber &&
              getValues("streetAndNumber") && <Check width={20} height={20} className="fill-tonal-green-40" />
            }
            errorMessage={errors.streetAndNumber?.message}
            {...register("streetAndNumber")}
            variant={errors.streetAndNumber && "error"}
            enabled={!!countryCodeWatch && !!addressLineWatch}
          />
          <Input
            label={t("additionalAddressLine")}
            placeholder={t("additionalAddressLine")}
            errorMessage={errors.additionalAddressLine?.message}
            rightIcon={
              !errors.additionalAddressLine &&
              getValues("additionalAddressLine") && <Check width={20} height={20} className="fill-tonal-green-40" />
            }
            {...register("additionalAddressLine", {
              setValueAs: (value) => {
                return value === "" ? undefined : value;
              },
            })}
            variant={errors.additionalAddressLine && "error"}
          />
        </div>

        <div className="grid md:grid-cols-2 w-full gap-8">
          <div>
            <CustomRadio
              checked={documentTypeWatch === "VAT"}
              label={t("vatId")}
              onChange={() => setDocumentType("VAT")}
              disabled={!isTaxAndVatEnabled}
            />
            <Input
              label=""
              placeholder={t("vatId")}
              enabled={isTaxAndVatEnabled && documentTypeWatch === "VAT"}
              variant={
                documentTypeWatch === "VAT" && errors.vatId
                  ? "error"
                  : documentTypeWatch !== "VAT"
                    ? "disabled"
                    : "enabled"
              }
              rightIcon={
                (loadingValidatVat && <CgSpinnerAlt size={20} className="animate-spin text-primary" />) ||
                (!errors.vatId && !isFocusedVat && getValues("vatId") && (
                  <Check width={20} height={20} className="fill-tonal-green-40" />
                ))
              }
              onFocus={() => setIsFocusedVat(true)}
              errorMessage={documentTypeWatch === "VAT" && errors.vatId?.message}
              {...register("vatId", {
                onBlur: (event) => {
                  setIsFocusedVat(false);
                  handleSelectVatId(event?.target?.value);
                },
              })}
            />
          </div>
          <div>
            <CustomRadio
              checked={documentTypeWatch === "TAX"}
              onChange={() => setDocumentType("TAX")}
              label={t("taxNumber")}
              disabled={!isTaxAndVatEnabled}
            />
            <Input
              label=""
              placeholder={t("taxNumber")}
              errorMessage={documentTypeWatch === "TAX" && errors.taxNumber?.message}
              enabled={isTaxAndVatEnabled && documentTypeWatch === "TAX"}
              variant={
                documentTypeWatch === "TAX" && errors.taxNumber
                  ? "error"
                  : documentTypeWatch !== "TAX"
                    ? "disabled"
                    : "enabled"
              }
              rightIcon={
                !errors.taxNumber &&
                getValues("taxNumber") && <Check width={20} height={20} className="fill-tonal-green-40" />
              }
              {...register("taxNumber")}
            />
            {vatInfoMessage && <p className="text-success text-sm mt-2">{vatInfoMessage}</p>}
          </div>
        </div>
      </div>
      <Divider />

      <div className="space-y-6 w-full">
        <p className="text-primary">Add e-mails to receive the invoice and informations</p>

        {emailsFields.map((email, index) => (
          <div className="flex gap-6 items-center" key={`${index}`}>
            <div className="lg:w-1/2 w-full">
              <div className="space-y-2">
                <div className="flex items-center gap-4">
                  <Input
                    {...register(`emails.${index}`)}
                    defaultValue={emailsFields[index]}
                    label={t("email")}
                    placeholder={t("email")}
                    type="email"
                    variant={!!errors.emails && errors.emails[index] ? "error" : "enabled"}
                  />
                  <Button
                    color="dark-blue"
                    size="iconSmall"
                    variant="text"
                    className="mt-8"
                    trailingIcon={<Delete style={{ fill: "inherit" }} />}
                    type="button"
                    onClick={() => handleRemoveEmail(index)}
                  />
                </div>
                {!!errors.emails && errors.emails[index] && (
                  <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                    <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
                      {errors.emails[index]!.message}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
      <p
        onClick={handleAddEmail}
        className="cursor-pointer font-medium mt-4 text-support-blue hover:text-support-blue/70"
      >
        {t("addEmail")} →
      </p>
    </div>
  );
}
