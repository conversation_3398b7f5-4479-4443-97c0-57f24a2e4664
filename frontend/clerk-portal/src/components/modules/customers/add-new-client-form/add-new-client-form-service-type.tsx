"use client";

import { Select } from "@arthursenno/lizenzero-ui-react/Select";
import { ChangeEvent, useEffect, useState } from "react";
import { AddNewClientFormSchemaData } from "./add-new-client-form-provider";
import { useFormContext } from "react-hook-form";
import { useTranslations } from "next-intl";

const SERVICE_TYPE = [
  { label: `License Service`, value: `EU_LICENSE` },
  { label: `Action Guide`, value: `ACTION_GUIDE` },
  { label: `Direct License`, value: `DIRECT_LICENSE` },
] as const;

type ServiceType = (typeof SERVICE_TYPE)[number];

export function AddNewClientFormServiceType() {
  const t = useTranslations("CustomerProfileHeader");
  const { setValue, watch } = useFormContext<AddNewClientFormSchemaData>();
  const service = watch(`service`);

  useEffect(() => {
    setValue(`service`, SERVICE_TYPE[0].value);
  }, []);

  const handleSelectService = (event: ChangeEvent<HTMLSelectElement>) => {
    if (!event) return;

    setValue(`service`, event?.target?.value);
  };

  return (
    <div className="w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:px-8 md:py-7">
      <div className="flex flex-col w-full gap-6">
        <p className="text-primary font-medium text-xl mb-2"></p>
        <Select
          label={t("selectServiceType")}
          options={SERVICE_TYPE as any}
          defaultValue={SERVICE_TYPE[0].label}
          value={service}
          onChange={handleSelectService}
        />
      </div>
    </div>
  );
}
