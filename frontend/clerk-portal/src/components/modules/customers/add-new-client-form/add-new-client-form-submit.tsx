import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East } from "@arthursenno/lizenzero-ui-react/Icon";
import { useFormContext } from "react-hook-form";

export function AddNewClientFormSubmit() {
  const {
    formState: { isSubmitting, errors },
  } = useFormContext();

  const hasErrors = Object.keys(errors).length > 0;

  const buttonColor = hasErrors ? "red" : "yellow";
  const disabled = isSubmitting || hasErrors;
  const label = isSubmitting ? "Loading..." : "Add";

  return (
    <div className="w-full flex justify-end">
      <Button
        type="submit"
        color={buttonColor}
        disabled={disabled}
        variant="filled"
        size="small"
        form="add-new-client-form"
        trailingIcon={!isSubmitting && <East />}
        className="w-2/4"
      >
        {label}
      </Button>
    </div>
  );
}
