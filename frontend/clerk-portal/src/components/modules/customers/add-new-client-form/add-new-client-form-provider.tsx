"use client";

import {
  ADDRESS_REGEX,
  SPECIAL_CHARS_COMPANY_NAME_REGEX,
  SPECIAL_CHARS_NUMERIC_REGEX,
  ZIP_CODE_REGEX,
} from "@/utils/regex";
import { zodResolver } from "@hookform/resolvers/zod";
import { ReactNode } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";

const addNewClientFormSchema = z.object({
  companyName: z
    .string()
    .min(1, "Required field")
    .regex(SPECIAL_CHARS_COMPANY_NAME_REGEX, "Special characters are not allowed for this field"),
  countryCode: z.string().min(1),
  addressLine: z
    .string({
      required_error: "Please enter a valid address",
      invalid_type_error: "Please enter a valid address",
    })
    .min(1, { message: "Please enter a valid address" })
    .regex(ADDRESS_REGEX, "Special characters are not allowed for this field"),
  city: z
    .string({
      required_error: "Please enter a valid city",
      invalid_type_error: "Please enter a valid city",
    })
    .min(1, { message: "Please enter a valid city" })
    .regex(ADDRESS_REGEX, "Special characters are not allowed for this field"),
  zipCode: z
    .string({
      required_error: "Please enter a valid zip code",
      invalid_type_error: "Please enter a valid zip code",
    })
    .min(1, { message: "Please enter a valid zip code" })
    .regex(ZIP_CODE_REGEX, "Special characters are not allowed for this field"),
  streetAndNumber: z
    .string({
      required_error: "Please enter a valid street and number",
      invalid_type_error: "Please enter a valid street and number",
    })
    .min(1, {
      message: "Please enter a valid street and number",
    })
    .regex(ADDRESS_REGEX, "Special characters are not allowed for this field"),
  additionalAddressLine: z
    .string()
    .regex(ADDRESS_REGEX, "Special characters are not allowed for this field")
    .optional(),
  documentType: z.enum(["TAX", "VAT"]),
  vatId: z.string().optional(),
  taxNumber: z
    .string()
    .min(1, "Required field")
    .regex(/^[a-zA-Z0-9ßÀ-ÿ\s]+$/, "Special characters are not allowed for this field")
    .optional(),
  salutation: z.string().min(1),
  firstName: z
    .string()
    .min(2, "Must have at least 2 characters")
    .regex(SPECIAL_CHARS_NUMERIC_REGEX, "Special characters are not allowed for this field")
    .refine((s: string) => !s.includes(" "), "Spaces are not allowed for this field"),
  surname: z
    .string()
    .min(2, "Must have at least 2 characters")
    .regex(SPECIAL_CHARS_NUMERIC_REGEX, "Special characters are not allowed for this field"),
  phone: z.string().min(5, "Required field"),
  mobile: z.string().optional(),
  emails: z.array(z.string().trim().email("Invalid e-mail")),
  countries: z.array(z.string().trim()),
  phones: z.array(z.string().trim().min(1, "Required field")),
  emailLogin: z.string().email(),
  service: z.string(),
});

export type AddNewClientFormSchemaData = z.infer<typeof addNewClientFormSchema>;

interface AddNewClientProviderProps {
  children: ReactNode;
}

export function AddNewClientFormProvider({ children }: AddNewClientProviderProps) {
  const methods = useForm<AddNewClientFormSchemaData>({
    resolver: zodResolver(addNewClientFormSchema),
    mode: "all",
  });

  return <FormProvider {...methods}>{children}</FormProvider>;
}
