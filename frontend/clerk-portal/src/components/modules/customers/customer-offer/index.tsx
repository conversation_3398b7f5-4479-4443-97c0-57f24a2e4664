"use client";

import Breadcrumb from "@/components/_common/breadcrumb/breadcrumb";
import Container from "@/components/_common/container/container";
import { StatusType } from "@/components/modules/country/components/task-type";
import StatusFilter, { StatusFilterType } from "@/components/modules/customers/components/filters/status-filter";
import { OfferDoc } from "@/components/modules/customers/components/offer/offer-doc";
import { OfferInfos } from "@/components/modules/customers/components/offer/offer-infos";
import { OfferService } from "@/components/modules/customers/components/offer/offer-service";
import { CustomerProfileCompany } from "@/components/modules/customers/customer-profile/customer-profile-company";
import { CustomerProfileContact } from "@/components/modules/customers/customer-profile/customer-profile-contact";
import CustomerProfileHeader from "@/components/modules/customers/customer-profile/customer-profile-header";
import { useCustomer } from "@/components/modules/customers/customer-profile/use-customer";
import { formatCustomerNumber } from "@/utils/format-customer-number";
import { useTranslations } from "next-intl";

const STATUS_FILTERS: StatusFilterType[] = [
  { label: "2023", value: "2023" },
  { label: "2024", value: "2024" },
  { label: "2025", value: "2025" },
];

const MOCK_COUNTRIES_SELECT = [
  {
    name: `Brazil`,
    flag: "https://cdn.kcak11.com/CountryFlags/countries/br.svg",
  },
  {
    name: "Germany",
    flag: "https://cdn.kcak11.com/CountryFlags/countries/de.svg",
  },
];

const offerStatus: StatusType = `Accepted`;

export function CustomerOffer() {
  const t = useTranslations("CustomerProfileHeader");
  const c = useTranslations("common");
  const { customerId, customer, refetch } = useCustomer();

  const paths = [
    { label: "Dashboard", href: "/" },
    { label: `Customer #${formatCustomerNumber(customerId)}`, href: "" },
  ];

  return (
    <Container>
      <Breadcrumb paths={paths} />
      {customer && (
        <>
          <CustomerProfileHeader customer={customer} />
          <div className="flex justify-between mb-8">
            <div className="mt-8 flex flex-col gap-10">
              <div className="w-[704px] min-h-screen bg-[#F7F5F2] rounded-3xl px-8 pt-9 pb-10">
                <div className="flex items-center justify-between">
                  <p className="text-title-3 font-bold text-[#183362]">{t("countries")}</p>
                  <StatusFilter filters={STATUS_FILTERS} />
                </div>
                <OfferService name="Licensing Services" countries={MOCK_COUNTRIES_SELECT} />
                <OfferService name="Action Guide" countries={MOCK_COUNTRIES_SELECT} />
                <OfferService name="Direct Licensing" countries={MOCK_COUNTRIES_SELECT} showAddBtn={false} />
                <div className="flex justify-center mt-8">
                  <p className="text-small-paragraph-regular text-tonal-dark-cream-50">{c("end")}</p>
                </div>
              </div>

              <OfferDoc />
            </div>

            <div className="mt-8 flex flex-col gap-4 ">
              <div className="w-[460px]">
                <OfferInfos countries={MOCK_COUNTRIES_SELECT} initialShowItems={true} offerStatus={offerStatus} />
              </div>
              <CustomerProfileContact customer={customer} refetch={refetch} />
              <CustomerProfileCompany customer={customer} refetch={refetch} />
            </div>
          </div>
        </>
      )}
    </Container>
  );
}
