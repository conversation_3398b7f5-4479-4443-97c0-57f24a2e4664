module.exports = {
  presets: [require("@arthursenno/lizenzero-ui-react/tailwind.config.js")],
  content: ["./src/**/*.{js,jsx,ts,tsx}", "./node_modules/@arthursenno/lizenzero-ui-react/**/*.{js,jsx,ts,tsx}"],
  darkMode: ["media", "class"],
  theme: {
    lineHeight: {
      none: "normal",
      DEFAULT: "normal",
    },
    extend: {
      screens: {
        xsm: "320px",
      },
      keyframes: {
        slideDownAndFade: {
          from: {
            opacity: 0,
            transform: "translateY(-2px)",
          },
          to: {
            opacity: 1,
            transform: "translateY(0)",
          },
        },
        slideLeftAndFade: {
          from: {
            opacity: 0,
            transform: "translateX(2px)",
          },
          to: {
            opacity: 1,
            transform: "translateX(0)",
          },
        },
        slideUpAndFade: {
          from: {
            opacity: 0,
            transform: "translateY(2px)",
          },
          to: {
            opacity: 1,
            transform: "translateY(0)",
          },
        },
        slideRightAndFade: {
          from: {
            opacity: 0,
            transform: "translateX(-2px)",
          },
          to: {
            opacity: 1,
            transform: "translateX(0)",
          },
        },
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
      },
      animation: {
        slideDownAndFade: "slideDownAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1)",
        slideLeftAndFade: "slideLeftAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1)",
        slideUpAndFade: "slideUpAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1)",
        slideRightAndFade: "slideRightAndFade 400ms cubic-bezier(0.16, 1, 0.3, 1)",
        fadeIn: "fadeIn 0.5s ease-in",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      colors: {},
    },
  },

  variants: {
    extend: {
      fontFamily: {
        centra: ["CentraNo2", "sans-serif"],
      },
    },
  },

  plugins: [require("tailwindcss-animate")],
};
