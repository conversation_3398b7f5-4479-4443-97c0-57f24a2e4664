import createNextIntlPlugin from "next-intl/plugin";

/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "standalone",
  images: {
    remotePatterns: [
      { hostname: "upload.wikimedia.org" },
      { hostname: "miro.medium.com" },
      { hostname: "liz-generic-files.s3.us-east-2.amazonaws.com" },
    ],
  },
};

const withNextIntl = createNextIntlPlugin();

export default withNextIntl(nextConfig);
