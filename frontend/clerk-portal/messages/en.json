{"isDefault": true, "languageCode": "en", "languageName": "English", "flagImageUrl": "https://cdn.kcak11.com/CountryFlags/countries/gb.svg", "common": {"year": "Year", "ok": "Ok", "backToCustomer": "Back to customer", "countries": "Countries", "add": "Add", "backToDash": "Back to dashboard", "uploadExcel": "Upload a Excel", "uploadTemplate": "Upload a template", "orDrag": "or drag it here", "loading": "Loading...", "button": "<PERSON><PERSON>", "pdf": "PDF", "continue": "Continue", "backToLogin": "Back to login", "save": "Save", "thankYou": "Thank you!", "to": "to", "close": "Close", "other": "Other", "back": "Back", "noTasks": "No tasks", "saving": "Saving...", "end": "End of the list", "check": "Check", "cancel": "Cancel", "creditCard": "Credit Card", "paypal": "PayPal", "bankTransfer": "Bank Transfer", "klarna": "<PERSON><PERSON><PERSON>", "eps": "EPS", "ideal": "iDEAL", "areYouSure": "Are you sure?", "confirm": "Confirm", "yes": "Yes", "no": "No", "download": "Download", "downloadOffer": "Download offer", "clientOffer": "Client offer", "noFilesUploaded": "No files uploaded", "edit": "Edit"}, "nav": {"dashboard": "Dashboard", "countries": "Countries", "customers": "Customers", "invoices": "Invoices", "logout": "Logout"}, "Monday": {"name": "Monday", "followedCountries": "Your followed countries", "customer": "Customer", "tasksDone": "Tasks done", "noTasksFound": "No tasks found.", "noTasks": "No tasks"}, "Licensing": {"title": "Licensing Service", "subText": "Add countries"}, "Offers": {"title": "Offers overview", "enrolled": "Enrolled in", "serviceType": "Service type", "status": "Status", "license": "EU License", "download": "Download offer"}, "AddNewClient": {"title": "Add new client", "newClients": "Add new clients", "information": "Add a new client information", "addBulk": "Add in bulk", "personalData": "Personal data", "mandatory": "Mandatory Fields", "customerList": "Customer list", "salutation": "Salutation", "firstName": "First name", "surname": "Surname", "invalidPhone": "Invalid phone number.", "loginInfo": "Login information", "otherNumbers": "Add other contact phone numbers", "Phone": "Phone", "addPhone": "Add phone", "mobile": "Mobile", "confirmInformations": "Confirm informations", "countriesSelected": "Countries selected", "clientAdded": "Client added successfully"}, "Invoices": {"title": "Invoices", "transactions": "Transactions", "searchPlaceholder": "Search by name"}, "PhoneInput": {"noCountry": "No country found", "searchCountry": "Search country..."}, "Login": {"credentials": "Log in with your credentials", "email": "E-mail", "emailPlaceholder": "Enter your email", "password": "Password", "passwordPlaceholder": "Enter your password", "invalidEmail": "Invalid email", "invalidPassword": "Invalid password", "enterLetter": "Enter a letter", "enterNumber": "Enter a number", "enterSpecialChar": "Enter a special character", "failedInvalid": "Invalid email or password", "forgotPassword": "Forgot password?"}, "ForgotPassword": {"emailPrompt": "Please enter the e-mail you’re using for your account", "title": "Password Recovery", "email": "E-mail", "emailPlaceholder": "Enter your email", "unregisteredEmail": "The email is not registered", "invalidEmail": "E-mail Invalid", "loading": "Loading...", "button": "Reset password", "characters": "Enter at least 8 characters", "charactersNumber": "Enter a number", "searchForUser": "Search for user", "charactersSpecial": "Enter a special character", "charactersLetter": "Enter a letter", "invalidPassword": "Invalid password", "invalidPasswordPrompt": "Please enter a valid password", "passwordStrength": "Password strength", "newPassword": "New password", "successTitle": "Password changed successfully!", "instructions": "We’ve sent password reset instructions to your e-mail address. If no e-mail is received within ten minutes, check if the submitted address is correct."}, "RecoverPassword": {"title": "Password recovery", "subtitle": "Please enter the e-mail you're using for your account", "email": {"label": "Email", "placeholder": "Enter your email", "error": "Please enter a valid email"}, "password": {"label": "New Password", "placeholder": "New Password", "error": "Please enter a valid password"}, "passwordStrength": {"weak": "Weak", "medium": "Medium", "strong": "Strong", "lessThan6": "Less than 6 characters", "letter": "At least one letter", "specialChar": "At least one special character", "number": "At least one number", "label": "Password strength"}, "button": "Save", "back": "Back to login", "confirmation": {"title": "Password changed successfully!", "subtitle": "Email:", "button": "Back to login"}}, "CustomerCountry": {"company": "Company", "contact": "Contact", "licenseYear": "License year", "autoRenew": "Automatic renewal", "terminationDate": "Termination date", "clerkControl": "Clerk Control", "fileDownloaded": "File downloaded successfully", "errorDownloadingFile": "Error downloading file.", "fileDeletedSuccessfully": "File deleted successfully", "errorDeletingFile": "Error deleting file.", "fileUploadedSuccessfully": "File uploaded successfully", "errorUploadingFile": "Error uploading file. Please try again.", "proofOfRegistration": "Proof of registration", "uploadDocument": "Upload document"}, "ClaimManagementActions": {"details": "Details", "reissue": "Re-issue", "cancel": "Cancel", "invoice": "Invoice", "refund": "Refund", "print": "Print", "view": "View", "claimManagement": "Claim management", "currentBalance": "Current open balance", "totalPayed": "Total payed"}, "ReissueModal": {"specialCharacters": "Special characters are not allowed for this field", "validAddress": "Please enter a valid address", "validCity": "Please enter a valid city", "validZip": "Please enter a valid zip code", "validStreetAndNumber": "Please enter a valid street and number", "required": "Required Field", "areYouSure": "Are you sure?", "byChanging": "By changing this information the invoice", "autoCancel": "will be automatic cancelled and a new one will be created.", "loading": "Loading", "back": "Back", "generateInvoice": "Generate Invoice", "companyName": "Company Name", "country": "Country", "selectCountry": "Select a country", "noCountry": "No country found", "searchCountry": "Search country...", "city": "City", "zipCode": "ZIP code", "streetAndNumber": "Street and number", "additionalAddress": "Additional address line", "vatId": "VAT ID", "taxNumber": "Tax number", "continue": "Continue", "invoiceGenerated": "Invoice generated successfully", "close": "Close"}, "TransactionTable": {"clientID": "Client ID", "otherInfo": "Other info", "otherInfo2": "Other info 2", "status": "Status", "invoiceId": "Invoice ID", "transaction": "Transaction", "date": "Date", "IBAN": "IBAN", "remaining": "Remaining", "amount": "Amount", "external": "External", "reference": "Reference", "company": "Company", "name": "Name", "caption": "A list of your recent transactions.", "assigned": "Assigned", "unassigned": "Unassigned", "refund": "Refund", "debitCharge": "Debit Charge", "authorize": "Authorize rep. & Third-party invoices", "errorDownloadInvoice": "Error downloading invoice.", "invoiceDownload": "Invoice downloaded successfully."}, "RefundInvoiceModal": {"dunning": "Dunning Charge", "banktransfer": "Bank Transfer", "invoice": "Invoice", "payment": "Payment", "failedProcess": "Failed to process refund:", "refund": "Refund", "refundSubtitle": "To request a refound please provide this information:", "refundType": "Type of refund", "change": "Change", "amountToPay": "Set the amount to be payed", "notPayToTheCustomer": "Not pay out the sum to the customer", "additionalInfo": "Additional information", "addComments": "Add comments", "revisedRefundInfo": "Revise refund information before continue", "refundAmount": "Refund amount", "originalPayment": "Original payment", "authorize": "By clicking on “Confirm Refund” you will be authorizing this action.", "confirmRefund": "Confirm Refund", "refundComplete": "Refund complete", "refundCompleteSubtitle": "Your refund has been completed successfully.", "volumereports": "Volume reports", "thirdPartyInvoices": "Third party invoices", "terminations": "Terminations", "selectType": "Please select a refund type", "selectPaymentMethod": "Please select a payment method", "amountRequired": "Amount is required", "mustBeValidNumber": "Must be a valid number", "amountMustBeGreaterThanZero": "Amount must be greater than 0", "provideInformation": "To request a refund please provide this information:", "originalPaymentMethod": "Original payment method", "addNewBank": "Add new bank account", "errorToProcess": "Error to process refund"}, "InvoiceOrCustomerSelect": {"placeholder": "Invoice ID or customer name", "noResults": "No results found"}, "BalanceSection": {"totalReceived": "Total received", "totalAssignedTransactions": "Total assigned transactions", "totalUnassignedInvoice": "Total unassigned invoice"}, "kanban": {"registration": "Registration", "volumeReports": "Volume Reports", "thirdPartyInvoices": "Third Party Invoices", "terminations": "Terminations"}, "AssignInvoiceModal": {"null": "Transaction ID is null", "invoiceSuccess": "Invoice assigned successfully", "invoiceError": "Failed to assign invoice", "assignInvoice": "Assign Invoice", "assignSubtitle": "Search for invoices and customers to assign to this transactions.", "transactionAmount": "Transaction amount", "setValue": "Set value to be assign", "suggestions": "Suggestions", "potentialMatches": "See potencial matches for this transaction.", "reloadResults": "Reload results", "dateIssued": "Date issued", "dueDate": "Due date", "amount": "Amount", "cleanResults": "Clean results"}, "EditCustomerContactModal": {"required": "Required Field", "noSpecial": "Special characters are not allowed for this field", "invalidEmail": "Invalid email", "emailInUse": "This email is already in use", "error": "An error occurred when trying to save the data", "contactInformation": "Contact Information", "editContactInfo": "Edit contact information", "fullnameRequired": "Full name *", "fullNamePlaceholder": "Full name", "emailRequired": "Email *", "emailPlaceholder": "Email", "phoneMobile": "Phone/Mobile", "invalidPhone": "Invalid phone number.", "addPhone": "Add phone", "informationUpdated": "Information updated"}, "EditCustomerCompanyModal": {"required": "Required Field", "noSpecial": "Special characters are not allowed for this field", "validCity": "Please enter a valid city", "validZipCode": "Please enter a valid zip code", "validStreetAndNumber": "Please enter a valid street and number", "validAddress": "Please enter a valid address", "vatinUse": "This VAT ID is already in use", "error": "An error occurred when trying to save the data", "companyInformation": "Company Information", "editCompanyInfo": "Edit company information", "companyNameRequired": "Company name *", "companyNamePlaceholder": "Company name", "countryRequired": "Country *", "cityRequired": "City *", "cityPlaceholder": "City", "zipCodeRequired": "Zip code *", "zipCodePlaceholder": "Zip code", "streetAndNumberRequired": "Street and number *", "streetAndNumberPlaceholder": "Street and number", "additionalAddressLine": "Additional address line", "vat": "VAT", "informationUpdated": "Information updated"}, "CustomerProfileServicesContract": {"fileDownloaded": "File downloaded successfully", "fileDownloadFailed": "Error downloading file.", "fileDeleted": "File deleted successfully", "fileDeleteFailed": "Error deleting file.", "fileUploaded": "File uploaded successfully", "fileUploadFailed": "Error uploading file.", "contractTerminationRequest": "Contract termination request", "contractTerminationDescription": "The customer have requested the termination of the Direct Licensing contract.", "terminationCasePending": "Termination case pending with dual system", "terminationCasePendingDescription": "Terminate this customer's country contract with the dual system and then upload the proof for each country.", "terminationProcessConclude": "Termination process conclude", "terminationProcessConcludeDescription": "Contract termination was in", "terminationCaseComplete": "Termination case complete", "terminationCaseCompleteDescription": "Contract termination will be in", "noCountriesForLicensingServices": "No countries for licensing services", "terminationRequestedOn": "Termination requested on", "contractTerminationOn": "Contract termination was in", "contractTerminationWillBeIn": "Contract termination will be in", "terminationPendingOnDualSystem": "Termination pending on dual system", "terminationCasePendingWithDualSystem": "Terminate this customer's country contract with the dual system and then upload the proof for each country.", "terminationProcessConfirmed": "Termination process confirmed", "terminationFor": "The termination process for", "hasStarted": "has started please follow the instructions in the next screen", "customerWillBeNotified": "The customer will be notified that the contract it`s in cancellation process in the dual system.", "actionGuide": "Action guide", "customerActionGuideNotification": "has started. The customer will be notified that the contract it´s cancelled.", "noCountriesToTerminate": "No countries to terminate", "select": "Select a country to terminate contract", "uploadTerminationProof": "Upload termination proof", "selectReason": "Select reason", "allCountriesTerminated": "All the countries have started the termination process", "terminate": "Terminate"}, "ActionModal": {"companyNameRequired": "Company name is required", "countryRequired": "Country is required", "cityRequired": "City is required", "zipCodeRequired": "Zip code is required", "federalStateRequired": "Federal state is required", "mandatoryFields": "Mandatory fields", "streetAndNumber": "Street and number", "additionalAddressLine": "Additional address line", "zipCode": "Zip code", "city": "City", "country": "Country", "federalState": "Federal state", "vat": "VAT", "vatNumber": "VAT number", "tin": "TIN", "tinNumber": "TIN number", "companyName": "Company name", "changeInformationPre": "By changing this information the invoice", "changeInformationPost": "will be automatic cancelled and a new one will be created.", "generateInvoice": "Generate invoice", "unableToProcessReissue": "Unable to process reissue", "invoiceGeneratedSuccessfully": "Invoice generated successfully"}, "DetailsModal": {"details": "Details", "IDInvoice": "ID invoice", "type": "Type", "info": "Info", "admin": "Admin", "openBalance": "Open balance", "registeredCountry": "Registered country", "claim": "<PERSON><PERSON><PERSON>", "otherInfo": "Other info", "otherInfo2": "Other info 2"}, "AddRequiredInfoModal": {"document": "Document", "documentDescription": "Document to be signed and uploaded by the customer", "fileInput": "File input", "fileInputDescription": "Request a file to be uploaded by the customer", "image": "Image", "imageDescription": "Request an image (png or jpeg) to be uploaded by the customer", "textField": "Text Field", "textFieldDescription": "Information inputed by the customer", "numberField": "Number Field", "numberFieldDescription": "Number inputed by the customer", "unexpected": "An unexpected error occurred", "newInfo": "New information", "requestNewInfo": "Request a new information to the license process.", "selectInfoType": "Select information type", "general": "General", "specific": "Specific for this country", "title": "Title", "titleOfImage": "Title of image", "infoTitle": "Information title", "additionalInfo": "Additional information", "addDetails": "Add details to your request", "nameRequired": "Name is required", "fileIsRequired": "File is required", "requiredInformationCreated": "Required information created successfully", "failedToCreateRequiredInformation": "Failed to create required information", "back": "Back", "titlePlaceholder": "Title of document", "questionToBeAnswered": "Question to be answered", "questionToBeAnsweredPlaceholder": "Question to be answered", "fileTitle": "Title of file"}, "CountriesGrid": {"resultsFound": "Results found", "noResults": "No results for", "inCountries": "in countries"}, "CountryCard": {"openTasks": "Open tasks:", "representative": "Representative", "licensedCustomers": "Licensed customers:", "unlicensedCustomers": "Unlicensed customers:", "representatives": "Representatives"}, "SelectRepresentativeModal": {"successSelect": "The representative(s) have been successfully assigned to the country", "errorSelect": "Failed to assign representative(s) to the country", "selectPrompt": "Select a representative"}, "CountryRequired": {"basicInfo": "Basic Information to License"}, "AddNewClientFormCompany": {"invalidVAT": "Invalid VAT ID", "companyInformation": "Company Information", "mandatoryFields": "Mandatory fields", "enterValidAddress": "Please enter a valid address", "streetAndNumber": "Street and number", "zipCode": "ZIP code", "additionalAddressLine": "Additional address line", "vatId": "VAT ID", "taxNumber": "Tax number", "emailPrompt": "Add e-mails to receive the invoice and informations", "email": "E-mail", "addEmail": "Add new e-mail", "companyName": "Company name", "country": "Country", "selectACountry": "Select a country", "noCountryFound": "No country found", "searchCountry": "Search country...", "city": "City"}, "AddNewClientFormCountries": {"title": "Select countries", "subtitle": "You can assess additional countries later at any time", "selected": "Countries selected"}, "Step1": {"selectedInvalidCountries": "Selected invalid countries", "selectCountry": "Select a country by searching for his name", "countriesSelected": "Countries selected"}, "CalculatorCountry": {"enterInformation": "Please enter the information to field the pre-information forms", "commitmentAssessment": "Commitment assessment", "loadingCommitment": "Loading commitment...", "mustAnswerAll": "You must answer all the questions", "editAnswers": "Edit answers", "result": "Result:", "licensingRequired": "Licensing required", "notObligated": "Not obligated to license", "pleaseProvideEstimateQuantity": "Please provide the estimate quantity so we are able to estimate the future costs", "bottleTopsFilmChocolate": "Bottle tops, film for chocolate and tubes for skincare products: packaging made from aluminium is mostly used to package food, cosmetics or pharmaceutical products. Enter the estimated total weight of packaging that you will place on the German market in the respective licence year.", "loadingPackaging": "Loading packaging services..."}, "CountriesOffersList": {"registrationFee": "Registration fee", "handlingFee": "Handling fee Sales Packaging", "handlingFeeDescription": "Handling Fee", "saveChanges": "Save changes", "editPrice": "Edit price"}, "LicensingSelectPriceListModal": {"errorFetchingPriceList": "Error fetching price list", "pleaseSelectPriceList": "Please select a price list", "selectPriceList": "Select price list", "selectPriceListDescription": "Select a price list for this customer", "loadingPriceList": "Loading price list...", "confirmAction": "Confirm action", "addingNew": "Adding new countries to this customer will generate an adicional cost of", "toTheCustomer": "to the customer", "invoiceWillBeGenerated": "An invoice will be automatic generated so the user can pay.", "pleaseConfirm": "Please confirm this action by clicking on the “confirm” button", "addedWithSuccess": "Country added with success", "search": "Search", "searchByCompany": "Search by company"}, "OfferCommitment": {"commitmentAssessment": "Commitment assessment", "seeAnswers": "See the answers for the commitment assessment for this country", "isCzech": "Is your company from Czech Republic?*", "sellMoreThan300": "Does your company plan on selling more than 300kg of packaging in a year?*"}, "OfferDocuments": {"contractDocuments": "Contract documents", "uploadAdditional": "Upload additional contract documents if necessary", "uploadDocuments": "Upload Documents"}, "OfferModalEdit": {"editOffer": "Edit offer", "areYouSure": "Are you sure you want to change price to this offer?", "sendOffer": "Send offer", "confirmActionSendignOffer": "By clicking in continue you will be confirming the action of sending this offer to this client.", "send": "Send", "sendOfferAsPdf": "Send offer as PDF additionally", "sendOfferToClient": "Send offer to client", "uploadOffer": "Upload offer", "showOfferHistory": "Show offer history", "generateOffer": "Generate offer", "euLicense": "EU License", "paymentConditions": "Payment conditions", "total": "Total", "offerGenerated": "Offer generated", "noOffersAvailable": "No offers available for confirmation", "monthly": "Monthly"}, "OfferUpload": {"uploadOffer": "Upload offer", "offerInfo": "Offer information", "reviewDetail": "Review offer details before confirming.", "offerAddedSuccessfully": "Offer added successfully"}, "Termination": {"errorToConfirm": "Error to confirm termination. Please try again.", "confirmed": "Termination confirmed successfully", "fileDownloaded": "File downloaded successfully", "fileDownloadFailed": "Failed to download file.", "contractTermination": "Contract termination", "licenseAllCountries": "License Service, All Countries", "actionAllCountries": "Action Guide, All Countries", "directAllYears": "Direct Licensing, All Years", "countryContractTermination": "Country contract termination", "backToCustomer": "Back to customer profile", "contractRequest": "Contract termination request", "countryRequest": "Country termination request", "terminationInfo": "Termination information", "date": "Date of request", "terminationDate": "Termination date", "contactInfo": "Contact information", "fullName": "Full name", "phoneMobile": "Phone/Mobile", "email": "Email", "reason": "Reasons for termination", "attachment": "Attachments", "continue": "To continue with the termination process confirm here", "confirm": "Confirm termination"}, "CustomerCountryCommitmentAssessment": {"viewAnswers": "View answers", "commitmentAssessment": "Commitment assessment", "seeCustomersAnswers": "See the customer's answers.", "pdf": "PDF", "edit": "Edit", "seeCustomerAnswers": "See the answers for the commitment assessment for this country", "isCzech": "Is your company from Czech Republic?*", "sellMoreThan300": "Does your company plan on selling more than 300kg of packaging in a year?*", "license23Or24": "Which year would your company like to license 2023 or 2024?*", "salesPackaging": "Sales packaging", "b2bPackaging": "B2B packaging", "result": "Result:", "info": "Please provide the estimate quantity so we are able to estimate the future costs", "licensingRequired": "Licensing required", "notObligated": "Not obligated to license"}, "CustomerCountryHeader": {"backToCustomer": "Back to customer", "clerkControl": "Clerk control"}, "CustomerCountryLastActivities": {"lastActivities": "Last activities", "declareVolumeReport": "Declare volume reports", "done": "Done", "uploadLizenzeroContract": "Upload Lizenzero contract"}, "CustomerCountryLicense": {"licensingInformation": "Licensing information", "registrationNumber": "Registration number", "licensingDate": "Licensing date", "licensingRenew": "Licensing renew", "commitmentAssessment": "Commitment assessment", "viewAnswers": "View answers", "enterRegistrationNumber": "Enter registration number"}, "CustomerServicesControl": {"terminationConfirm": "Termination confirmed successfully", "error": "Error to confirm termination", "servicesControl": "Services control", "terminationDate": "Termination date"}, "CustomerProfileHeader": {"countries": "Countries", "enrolledIn": "Enrolled in", "serviceType": "Service type", "status": "Status", "company": "Company", "country": "Country", "licenseYear": "License year", "terminationDate": "Termination date", "customerStatus": "Customer status", "address": "Address", "searchCountry": "Search country", "selectServiceType": "Select service type"}, "CustomerProfileContract": {"downloadSuccess": "File downloaded successfully", "downloadFailed": "Error downloading file. Please try again.", "contract": "Contract", "contractSince": "Contract Since", "contractUntil": "Contract Until", "autoRenewed": "Auto renewed", "contactInfo": "Contact Information", "fullName": "Full Name", "phoneMobile": "Phone/Mobile", "email": "Email", "companyData": "Company Data", "companyName": "Company Name", "location": "Location", "address": "Address", "vat": "VAT", "taxNumber": "Tax Number", "contact": "Contact", "contactList": "Contact list for the company", "name": "Name", "customerNumber": "Customer Number"}, "AddThirdPartyInvoiceModal": {"invoiceCreated": "Third party invoice created successfully", "thirdPartyInvoiceFailed": "Error creating third party invoice.", "failedToUpload": "Failed to upload file.", "addInvoice": "Add invoice", "uploadInvoiceDocument": "Upload the invoice document", "pleaseUploadDocument": "Please upload a document to continue.", "invoiceIssuer": "Invoice Issuer", "thirdPartyDualSystem": "Third party dual system", "otherThirdParty": "Other third party", "priceMustBe": "Price must be greater than 0", "invoiceAmount": "Invoice Amount", "dueDateRequired": "Due date is required", "invoiceDueDate": "Invoice Due Date", "selectDueDate": "Select due date", "creatingInvoice": "Creating invoice...", "failedToCreateInvoice": "Failed to create invoice."}, "RequiredInformationLucid": {"answerCopied": "Answer copied successfully", "submitted": "Submitted", "waiting": "Waiting", "success": "Success", "warning": "Warning", "lucidNumber": "LUCID Number"}, "RequiredInformationItem": {"documentApproved": "Document approved successfully", "documentApprovedFailed": "Failed to approve document.", "fileDownloaded": "File downloaded successfully", "fileDownloadFailed": "Failed to download. Please try again.", "deleteFile": "File deleted successfully", "deleteFileFailed": "Unable to delete file. Please try again.", "deleteAnswer": "Answer deleted successfully", "deleteAnswerFailed": "Unable to delete answer. Please try again.", "copyAnswer": "Answer copied successfully", "requiredInformation": "Required information"}, "QuantityReportItem": {"reportFrequency": "Report frequency", "monthly": "Monthly", "annually": "Annually", "quarterly": "Quarterly", "dataNotFound": "Data not found", "reportProblem": "Report problem", "exceededAmountPayed": "Exceeded amount payed", "purchaseMadeIn": "Purchase made in", "amountRefunded": "Amount refunded", "minimumWeight": "The minimum weight is 0.", "minimumPrice": "The minimum price is 0.", "errorOcurred": "An error occurred while updating the volume report.", "currentVolume": "Current volume", "newVolume": "New volume", "updateVolume": "Update volume", "quantityReport": "Quantity report", "licenseYear": "License year", "enterValue": "Enter value"}, "volumeReporting": {"title": "Volume Reporting for the licence year 2023", "licenseYear": "License year", "orderNumber": "Order number", "timeAndDate": "Time and date", "amount": "Amount", "order": "Order", "typeOfMaterial": "Type of material", "volume": "Volume", "currentVolume": "Current volume", "newVolume": "New volume"}, "DirectLicenseReportingTable": {"exportedXml": "Volumes XML exported successfully", "forLicenseYear": "Volume Reporting for the licence year", "licenseYear": "License year", "orderNumber": "Order number", "timeAndDate": "Time and date", "amount": "Amount", "order": "Order", "typeOfMaterial": "Type of material", "volume": "Volume", "downloadCurrent": "Download current volumes for", "download": "Download volumes for"}, "SaasStorefront": {"saasStatus": "Saas Status", "registration": "Registration", "volumeReports": "Volume Reports", "thirdPartyInvoices": "Third Party Invoices", "termination": "Termination", "noThirdPartyInvoice": "No third party invoice", "noVolumeReport": "No volume report", "documents&Information": "Documents & Information"}, "CustomerCountryPriceList": {"dataSaved": "Data saved successfully", "errorSavingData": "Error saving data", "priceList": "Price List", "standard2022": "Standard 2022 Rates", "standard2023": "Standard 2023 Rates", "standard2024": "Standard 2024 Rates", "set": "Set", "registrationFee": "Registration Fee", "handlingFee": "Handling Fee", "variableHandlingFee": "Variable Handling Fee", "authorize": "Authorize representative & Third-party costs", "authorizeRepresentative": "Authorize representative", "selectRepresentative": "Select representative", "thirdpartyCosts": "Third-party costs", "authorizeThirdpartyCosts": "Authorize Third-party costs", "addCosts": "Add Costs", "delete": "Delete?", "saveChanges": "Save your changes?", "areYouSure": "Are you sure you want to "}, "DeclineQuantityReportModal": {"fillAllFields": "Please fill all mandatory fields to continue", "writeAComment": "Please write a comment to continue", "declineQuantityReportButton": "Decline quantity report", "selectReasons": "Select one or more reasons why you didn't accepted this report.", "addComments": "Add comments", "writeComment": "Write a comment", "declineReport": "Decline report", "addCommentsoptional": "Add comments (optional)", "informationDeclined": "Information declined successfully", "declineReportFailed": "Failed to decline report", "declining": "Declining...", "declineDocument": "Decline document", "selectAtLeastOneReason": "Select at least one reason"}}