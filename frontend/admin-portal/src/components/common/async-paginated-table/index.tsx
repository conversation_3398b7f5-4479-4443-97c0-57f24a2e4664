import { cn } from "@/utils/cn";
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  useReactTable,
  SortingState,
  getSortedRowModel,
} from "@tanstack/react-table";
import { Skeleton } from "@/components/ui/skeleton";
import { Lightbulb, Sort, SortDown, SortUp } from "@interzero/oneepr-react-ui/Icon";
import { useEffect, useState } from "react";

interface IAsyncPaginatedTable<T extends object> {
  data: T[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  columns: ColumnDef<T, any>[];
  currentPage: number;
  pages: number;
  pageSize?: number;
  isLoading?: boolean;
  onPageChange: (page: number) => void;
  noResultsMessage: string;
  showHeaderOnNoResults?: boolean;
  maxWidth?: string | number;
  useBuiltInSort?: boolean;
  beforePaginationContent?: React.ReactNode;
  /**
   * The non-clickabe columns should have the id "actions" to be excluded from the onRowClick event.
   */
  onRowClick?: (row: T) => void;
}

export default function AsyncPaginatedTable<T extends object>({
  columns,
  data,
  currentPage,
  pages,
  pageSize = 10,
  isLoading = false,
  onPageChange,
  noResultsMessage,
  showHeaderOnNoResults = false,
  maxWidth,
  useBuiltInSort = false,
  beforePaginationContent = null,
  onRowClick,
}: IAsyncPaginatedTable<T>) {
  const [totalPages, setTotalPages] = useState(pages);
  const [sorting, setSorting] = useState<SortingState>([]);

  useEffect(() => {
    if (!isLoading && pages > 0) {
      setTotalPages(pages);
    }
  }, [pages, isLoading]);

  const table = useReactTable<T>({
    columns,
    data,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    state: {
      sorting,
      pagination: {
        pageIndex: currentPage - 1,
        pageSize: data.length,
      },
    },
  });

  if (!isLoading && data.length === 0) {
    if (!showHeaderOnNoResults) {
      return (
        <div className="flex flex-col items-center justify-center mt-8">
          <Lightbulb className="size-10 fill-tonal-dark-cream-40 text-tonal-dark-cream-40" />
          <div className="text-tonal-dark-cream-40 p-4">{noResultsMessage}</div>
        </div>
      );
    }

    return (
      <div className="flex flex-col">
        <div style={{ maxWidth }} className="overflow-auto rounded-3xl">
          <table className="w-full text-primary">
            <thead className="bg-tonal-cream-90">
              {table.getHeaderGroups().map((headerGroup) => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map((header) => (
                    <th key={header.id} className="px-6 py-4 text-left text-sm font-normal text-nowrap">
                      {flexRender(header.column.columnDef.header, header.getContext())}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody className="bg-white">
              <tr>
                <td colSpan={columns.length} className="text-center py-8">
                  <div className="flex flex-col items-center justify-center">
                    <Lightbulb className="size-10 fill-tonal-dark-cream-40 text-tonal-dark-cream-40" />
                    <div className="text-tonal-dark-cream-40 p-4">{noResultsMessage}</div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col">
      <div style={{ maxWidth }} className="overflow-auto rounded-3xl">
        <table className={cn("w-full text-primary", isLoading && "min-h-80")}>
          <thead className="bg-tonal-cream-90">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header, index) => (
                  <th
                    data-first={index === 0}
                    key={header.id}
                    className="py-3 px-2 text-left h-12 text-sm font-normal text-nowrap data-[first=true]:pl-6"
                    onClick={
                      useBuiltInSort && header.column.getCanSort() ? header.column.getToggleSortingHandler() : undefined
                    }
                  >
                    <div className="flex items-center gap-1">
                      {flexRender(header.column.columnDef.header, header.getContext())}
                      {useBuiltInSort && header.column.getCanSort() && header.column.columnDef.enableSorting && (
                        <div className="ml-1 cursor-pointer">
                          {header.column.getIsSorted() === "asc" ? (
                            <SortUp className="size-4" />
                          ) : header.column.getIsSorted() === "desc" ? (
                            <SortDown className="size-4" />
                          ) : (
                            <Sort className="size-4" />
                          )}
                        </div>
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            ))}
          </thead>

          <tbody className="bg-white">
            {isLoading
              ? Array.from({ length: pageSize }).map((_, idx) => (
                  <tr key={idx} className="h-[52px]">
                    {columns.map((_, cellIdx) => (
                      <td key={cellIdx} className={cn("py-4 px-2", cellIdx === 0 && "pl-5")}>
                        <Skeleton className="h-4 w-[80%]" />
                      </td>
                    ))}
                  </tr>
                ))
              : table.getRowModel().rows.map((row) => (
                  <tr key={row.id} className="ml-3 h-[52px] animate-fadeIn">
                    {row.getVisibleCells().map((cell) => (
                      <td
                        data-first={cell.column.getIndex() === 0}
                        key={cell.id}
                        className={cn(
                          "py-4 px-2 data-[first=true]:pl-6",
                          onRowClick && cell.column.id !== "actions" && "cursor-pointer"
                        )}
                        onClick={() => {
                          if (cell.column.id !== "actions" && onRowClick) {
                            onRowClick(row.original);
                          }
                        }}
                      >
                        {flexRender(cell.column.columnDef.cell, cell.getContext())}
                      </td>
                    ))}
                  </tr>
                ))}
          </tbody>
        </table>
      </div>
      {beforePaginationContent}
      {totalPages >= 1 && (
        <div className="flex items-center gap-2 text-primary mx-auto mt-6">
          {currentPage > 3 && (
            <>
              <span role="button" onClick={() => onPageChange(1)} className="px-2 pb-1 pt-2 rounded cursor-pointer">
                1
              </span>
              {currentPage > 4 && <span className="px-2">...</span>}
            </>
          )}

          {Array.from({ length: totalPages })
            .map((_, i) => i + 1)
            .filter((page) => page >= currentPage - 2 && page <= currentPage + 2)
            .map((pageNumber) => (
              <span
                key={pageNumber}
                role="button"
                onClick={() => !isLoading && onPageChange(pageNumber)}
                className={cn(
                  "px-2 pb-1 pt-2 rounded cursor-pointer",
                  pageNumber === currentPage && "bg-primary text-white",
                  isLoading && "opacity-50"
                )}
              >
                {pageNumber}
              </span>
            ))}

          {currentPage < totalPages - 2 && (
            <>
              {currentPage < totalPages - 3 && <span className="px-2">...</span>}
              <span
                role="button"
                onClick={() => onPageChange(totalPages)}
                className="px-2 pb-1 pt-2 rounded cursor-pointer"
              >
                {totalPages}
              </span>
            </>
          )}
        </div>
      )}
    </div>
  );
}
