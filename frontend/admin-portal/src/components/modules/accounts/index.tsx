"use client";

import { ModuleTitle } from "@/components/common/module-title";
import { AccountCircle } from "@interzero/oneepr-react-ui/Icon";
import { AccountsTable } from "./accounts-table";
import { ModuleContent } from "@/components/common/module-content";
import { useQuery } from "@tanstack/react-query";
import { getCustomers } from "@/lib/api/customer";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/utils/cn";
import Image from "next/image";

export function AccountsModule() {
  const { data: customersData, isLoading } = useQuery({
    queryKey: ["customers-total-count"],
    queryFn: () => getCustomers({ page: 1, limit: 1 }),
  });

  const totalAccountsCard = {
    value: customersData?.count || 0,
    subtitle: "Customer accounts\ncreated in total",
    bgColor: "bg-tonal-green-90",
    titleColor: "text-tonal-dark-green-30",
    subtitleColor: "text-tonal-dark-green-30",
  };

  // Hidden for later use
  // const cards = [
  //   {
  //     title: "Create accounts",
  //     value: summary?.created.current,
  //     growthRate: summary?.created.growthRate,
  //     labelColor: "text-tonal-blue-40",
  //     bgColor: "bg-[#EBF4FF]",
  //     iconColor: "fill-tonal-blue-40",
  //   },
  //   {
  //     title: "Active accounts",
  //     value: summary?.active.current,
  //     growthRate: summary?.active.growthRate,
  //     labelColor: "text-tonal-dark-green-30",
  //     bgColor: "bg-tonal-green-90",
  //     iconColor: "fill-tonal-dark-green-30",
  //   },
  //   {
  //     title: "Terminated accounts",
  //     value: summary?.terminated.current,
  //     growthRate: summary?.terminated.growthRate,
  //     labelColor: "text-tonal-red-50",
  //     bgColor: "bg-[#FEF0EC]",
  //     iconColor: "fill-tonal-red-50",
  //   },
  // ];

  // Hidden for later use
  // const lastMonthName = (() => {
  //   const date = new Date();
  //   date.setMonth(date.getMonth() - 1);
  //   return date.toLocaleString("default", { month: "long" });
  // })();

  return (
    <ModuleContent>
      <ModuleTitle
        icon={AccountCircle}
        title="Account List"
        description="Find and manage all customer accounts in the platform."
      />
      {/* Original three cards - hidden for later use */}
      {/* <div className="w-full grid grid-cols-1 lg:grid-cols-3 gap-6 mb-16">
        {isLoading
          ? Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className={cn("flex flex-col gap-4 p-5 rounded-xl min-h-[212px]", cards[index].bgColor)}>
                <Skeleton className="h-6 w-11/12" />
                <Skeleton className="h-16 w-28" />
                <Skeleton className="h-3 w-36" />
                <Skeleton className="h-3 w-11/12" />
              </div>
            ))
          : cards.map((card) => (
              <div key={card.title} className={cn(`flex flex-col gap-4 p-5 rounded-xl animate-fadeIn ${card.bgColor}`)}>
                <h3 className="text-xl font-bold text-primary">{card.title}</h3>
                <div className="flex flex-col gap-2">
                  <h4 className={cn("text-6xl line-height-[78px] font-bold", card.labelColor)}>{card.value}</h4>
                  <p className="text-sm text-primary">Total {card.title}</p>
                </div>
                {card.growthRate ? (
                  <span
                    className={cn(
                      "flex items-center gap-1",
                      card.growthRate > 0 ? card.labelColor : "text-tonal-red-50"
                    )}
                  >
                    {card.growthRate > 0 ? (
                      <KeyboardArrowUp width={24} className={card.iconColor} />
                    ) : (
                      <KeyboardArrowDown width={24} className="fill-tonal-red-50" />
                    )}
                    {card.growthRate}% {card.growthRate > 0 ? "more" : "less"} than {lastMonthName}
                  </span>
                ) : (
                  <span className="text-primary"></span>
                )}
              </div>
            ))}
      </div> */}
      <div className="w-full mb-16">
        {isLoading ? (
          <div
            className={cn(
              "flex flex-col gap-4 p-8 rounded-[40px] min-h-[160px] relative overflow-hidden",
              totalAccountsCard.bgColor
            )}
          >
            <Skeleton className="h-16 w-20" />
            <Skeleton className="h-5 w-64" />
          </div>
        ) : (
          <div
            className={cn(
              "flex flex-col justify-center gap-1 px-10 min-h-[157px] rounded-[40px] animate-fadeIn relative overflow-hidden",
              totalAccountsCard.bgColor
            )}
          >
            <h3 className={cn("text-[52px] leading-16 font-bold", totalAccountsCard.titleColor)}>
              {totalAccountsCard.value}
            </h3>
            <p className={cn("text-lg leading-6 whitespace-pre-line", totalAccountsCard.subtitleColor)}>
              {totalAccountsCard.subtitle}
            </p>
            {/* Decorative leaf icon */}
            <div className="absolute bottom-0 right-0 h-full flex items-end justify-end overflow-hidden">
              <Image
                src="/assets/images/Blatt-Siegel.svg"
                alt="Leaf seal decoration"
                className="w-auto h-auto"
                draggable="false"
                width={168}
                height={168}
              />
            </div>
          </div>
        )}
      </div>

      <AccountsTable />
    </ModuleContent>
  );
}
