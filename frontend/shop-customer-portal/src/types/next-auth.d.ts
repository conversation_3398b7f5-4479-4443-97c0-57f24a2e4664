import { UserTypes } from "@/utils/user";
import { DefaultSession } from "next-auth";
import "next-auth/jwt";

declare module "next-auth" {
  interface Session extends DefaultSession {
    user: User;
  }

  interface User {
    id: string;
    role: UserTypes;
    has_password: boolean;
    email: string;
    name: string;
    first_name: string;
    last_name: string;
    expires_at: number;
    access_token: string;
    refresh_token: string;
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    role: UserTypes;
    has_password: boolean;
    email: string;
    name: string;
    first_name: string;
    last_name: string;
    sub: string;
    expires_at: number;
    access_token: string;
    refresh_token: string;
  }
}
