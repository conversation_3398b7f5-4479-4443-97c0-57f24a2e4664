import { api } from "@/lib/api";
import { NextRequest } from "next/server";

export const dynamic = "force-dynamic";

async function getServicePriceLists(service_type: string, year?: number) {
  try {
    const response = await api.get("/admin/price-lists", {
      params: {
        service_type,
        license_year: year || undefined,
      },
    });

    if (!response.data || response.status !== 200) throw new Error();

    const priceLists = response.data;

    if (!priceLists.length) throw new Error();

    return priceLists;
  } catch (err: any) {
    return null;
  }
}

async function getCountryPriceLists(countryCode: string, year?: number) {
  try {
    const response = await api.get(`/admin/service-setups/${countryCode}/price-lists`);

    if (!response.data || response.status !== 200) throw new Error();

    const countryPriceLists = response.data;

    if (!countryPriceLists.length) throw new Error();

    const priceLists = countryPriceLists.map((countryPriceList: any) => countryPriceList.price_list);

    if (!year || isNaN(Number(year))) return priceLists;

    return priceLists.filter((priceList: any) => priceList.year === Number(year));
  } catch (err: any) {
    return null;
  }
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const serviceType = searchParams.get("service-type");
  const year = searchParams.get("year");
  const countryCode = searchParams.get("country-code");

  if (!serviceType) return new Response(null, { status: 400 });

  if (serviceType === "EU_LICENSE") {
    if (!countryCode) return new Response(null, { status: 400 });

    const priceLists = await getCountryPriceLists(countryCode, Number(year));

    if (!priceLists || !priceLists.length) return new Response(null, { status: 404 });

    return Response.json(priceLists);
  }

  const priceLists = await getServicePriceLists(serviceType, Number(year));

  if (!priceLists) return new Response(null, { status: 404 });

  return Response.json(priceLists);
}
