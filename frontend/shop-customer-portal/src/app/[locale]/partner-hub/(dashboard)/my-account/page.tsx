import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { PartnerInformationFormProvider } from "@/components/modules/partner-hub/components/partner-information-form/partner-information-provider";
import { PartnerAccount } from "@/components/modules/partner-hub/my-account";
import { SaasBreadcrumb } from "@/components/modules/saas/components/saas-breadcrumb";
import { SaasContainer } from "@/components/modules/saas/components/saas-container";

export default function MyAccount() {
  return (
    <>
      <SaasContainer>
        <SaasBreadcrumb
          paths={[
            { label: "Hub", href: "/partner-hub" },
            { label: "My Account", href: "/partner-hub/my-account" },
          ]}
        />
        <TitleAndSubTitle showIcon title="My Account" subText="Here you will find all your information." />
        <PartnerInformationFormProvider>
          <PartnerAccount />
        </PartnerInformationFormProvider>
      </SaasContainer>
    </>
  );
}
