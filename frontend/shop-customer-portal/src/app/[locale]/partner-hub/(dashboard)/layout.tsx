import { CountryDropdown } from "@/components/_common/country-dropdown";
import { Footer } from "@/components/_common/footer";
import { ProfileDropdown } from "@/components/_common/header/profile-dropdown";
import { LogoutMenuItem } from "@/components/modules/partner-hub/components/logout-button";
import { SidebarLink } from "@/components/modules/partner-hub/components/sidebar-link";
import { Locale, localeList } from "@/i18n/locales";
import { Link, redirect } from "@/i18n/navigation";
import { getPartnerByUserId } from "@/lib/api/partner";
import { getServerUser } from "@/utils/get-server-user";
import { LizenzeroLogo } from "@arthursenno/lizenzero-ui-react/Figure";
import { Dashboard, File, Layers, Menu, PeopleAlt } from "@arthursenno/lizenzero-ui-react/Icon";
import { ReactNode } from "react";
import { IoMdClose } from "react-icons/io";

interface PartnerHubLayoutProps {
  children: ReactNode;
  params: {
    locale: Locale;
  };
}

export default async function PartnerHubLayout({ children, params }: PartnerHubLayoutProps) {
  const locales = localeList.map((l) => ({
    name: l.languageName,
    code: l.languageCode,
    flag: l.flagImageUrl,
  }));

  const user = (await getServerUser())!;

  const partner = await getPartnerByUserId(user.id);

  if (!partner) return redirect({ href: "/partner-hub/review/credentials", locale: params.locale });

  return (
    <div className="h-screen overflow-auto [&:has(>div>header_input:checked)]:overflow-hidden">
      <div className="min-h-screen w-full flex items-stretch">
        <aside className="overflow-auto h-full z-[60] lg:z-10 bg-background transition-transform -translate-x-full lg:translate-x-0 [&:has(~div>header_#partner-hub-sidebar-toggle:checked)]:translate-x-0 absolute lg:relative flex lg:w-[284px] w-full flex-col">
          <div className="w-full h-3 bg-tonal-dark-green-30"></div>
          <div className="p-6 flex flex-col gap-10 items-start">
            <div className="flex gap-4 items-center justify-between w-full">
              <LizenzeroLogo className="h-[30px]" />
              <label
                className="cursor-pointer lg:hidden text-tonal-dark-cream-10 rounded-full"
                htmlFor="partner-hub-sidebar-toggle"
              >
                <IoMdClose className="fill-primary w-6 h-6" />
              </label>
            </div>
            <div className="flex flex-col gap-1">
              <h1 className="overflow-hidden whitespace-nowrap text-ellipsis text-[20px] leading-6 font-bold text-tonal-dark-cream-10">
                {user.first_name} {user.last_name}
              </h1>
              <p className="overflow-hidden whitespace-nowrap text-ellipsis text-small-paragraph-regular text-tonal-dark-cream-30">
                {user.email}
              </p>
            </div>
          </div>
          <div className="pb-6">
            <LogoutMenuItem />
          </div>
          <hr className="w-full text-tonal-dark-cream-80" />
          <nav className="py-6 overflow-auto">
            <ul>
              <li>
                <SidebarLink href="/partner-hub">
                  <div className="flex gap-4 items-center">
                    <Dashboard height={24} width={24} className="fill-[currentColor]" /> Hub
                  </div>
                </SidebarLink>
              </li>
              <li>
                <SidebarLink href="/partner-hub/commissions">
                  <div className="flex gap-4 items-center">
                    <Layers height={24} width={24} className="fill-[currentColor]" /> My Commissions
                  </div>
                </SidebarLink>
              </li>
              <li>
                <SidebarLink href="/partner-hub/marketing-materials">
                  <div className="flex gap-4 items-center">
                    <File height={24} width={24} className="fill-[currentColor]" /> Marketing Materials
                  </div>
                </SidebarLink>
              </li>
              <li>
                <SidebarLink href="/partner-hub/my-account">
                  <div className="flex gap-4 items-center">
                    <PeopleAlt height={24} width={24} className="fill-[currentColor]" /> My Account
                  </div>
                </SidebarLink>
              </li>
            </ul>
          </nav>
        </aside>
        <span className="hidden lg:block bg-tonal-dark-cream-80 w-[1px]"></span>
        <div className={'flex-1 [&:has(>main>[data-dashboard="true"])]:bg-[#f7f5f2] w-full overflow-x-hidden h-full'}>
          <header className="mx-auto  pt-6 pb-4 px-4 flex flex-wrap gap-4 items-center justify-between max-w-[928px]">
            <Link href={`/${params.locale}/partner-hub`}>
              <LizenzeroLogo className="h-7" />
            </Link>
            <div className="flex gap-4 lg:gap-10 flex-wrap items-center">
              <CountryDropdown localeList={locales} />
              <ProfileDropdown />
              <label
                className="cursor-pointer lg:hidden text-tonal-dark-cream-10 rounded-full"
                htmlFor="partner-hub-sidebar-toggle"
              >
                <Menu className="fill-primary w-6 h-6" />
              </label>
              <input hidden type="checkbox" id="partner-hub-sidebar-toggle" />
            </div>
          </header>
          <hr className="w-full text-tonal-dark-cream-80" />
          <main>{children}</main>
        </div>
      </div>
      <div className="z-50 relative">
        <Footer />
      </div>
    </div>
  );
}
