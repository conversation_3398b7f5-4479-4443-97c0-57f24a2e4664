import { MyCommissions } from "@/components/modules/partner-hub/my-commissions";

export type CommissionsSearchParams = {
  inviteShareProduct?: string;
  performancePeriod?: string;
  performanceProduct?: string;
  performanceInvite1?: string;
  performanceInvite2?: string;
  listingProducts?: string | string[];
  listingSearch?: string;
  listingLeads?: string | string[];
  listingPage?: string;
};

interface MyCommissionsPageProps {
  searchParams: CommissionsSearchParams;
}

export default async function MyCommissionsPage({ searchParams }: MyCommissionsPageProps) {
  return <MyCommissions searchParams={searchParams} />;
}
