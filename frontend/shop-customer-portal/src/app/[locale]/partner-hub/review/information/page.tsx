import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { PartnerInformationFormProvider } from "@/components/modules/partner-hub/components/partner-information-form/partner-information-provider";
import { PartnerReviewHeader } from "@/components/modules/partner-hub/first-login/partner-review-header";
import { ReviewInformationForm } from "@/components/modules/partner-hub/first-login/review-information";

const paths = [
  {
    label: "Hub",
    href: "/partner-hub",
  },
  {
    label: "Review Credentials",
    href: "/partner-hub/review/credentials",
  },
  {
    label: "Review Information",
    href: "/partner-hub/review/information",
  },
];

export default function PartnerHubReviewInformation() {
  return (
    <>
      <PartnerReviewHeader paths={paths} />
      <div className="w-full px-4">
        <div className="max-w-7xl w-full mx-auto py-24">
          <TitleAndSubTitle
            showIcon
            title="Review Information"
            subText="Review your company and contact information."
          />
          <PartnerInformationFormProvider>
            <ReviewInformationForm />
          </PartnerInformationFormProvider>
        </div>
      </div>
    </>
  );
}
