import { Locale } from "@/i18n/locales";
import { redirect } from "@/i18n/navigation";
import { getPartnerByUserId } from "@/lib/api/partner";
import { getServerUser } from "@/utils/get-server-user";
import { UserTypes } from "@/utils/user";
import { ReactNode } from "react";

interface ReviewPartnerLayoutProps {
  children: ReactNode;
  params: { locale: Locale };
}

export default async function ReviewPartnerLayout({ children, params }: ReviewPartnerLayoutProps) {
  const user = await getServerUser();

  if (!user) return redirect({ href: "/partner-hub/auth/login", locale: params.locale });

  if (user.role !== UserTypes.PARTNER) return redirect({ href: "/partner-hub/auth/login", locale: params.locale });

  const partner = await getPartnerByUserId(user?.id);

  if (partner) return redirect({ href: "/partner-hub", locale: params.locale });

  return children;
}
