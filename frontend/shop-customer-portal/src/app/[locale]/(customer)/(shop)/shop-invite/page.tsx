"use client";

import { useQueryFilter } from "@/hooks/use-query-filter";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { useRouter } from "@/i18n/navigation";
import { useCustomer } from "@/hooks/use-customer";

import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";
import { useEffect } from "react";
import { CgSpinnerAlt } from "react-icons/cg";

export default function ShopInvitePage() {
  const router = useRouter();
  const { updateCart } = useShoppingCart();
  const t = useTranslations("shop.common.shopInvite");
  const { customer } = useCustomer();

  const { paramValues } = useQueryFilter(["partner_id", "customer_id", "coupon"]);

  useEffect(() => {
    (async () => {
      try {
        if (paramValues.coupon) {
          await updateCart({
            coupon: paramValues.coupon,
            coupon_type: `LINK`,
            coupon_url_link: window.location.href,
          });
        }

        if (paramValues.partner_id) {
          await updateCart({
            affiliate_partner_id: +paramValues.partner_id,
            affiliate_link: window.location.href,
            affiliate_type: `AFFILIATE_LINK`,
          });
        } else if (paramValues.customer_id) {
          await updateCart({
            affiliate_customer_id: +paramValues.customer_id,
            affiliate_link: window.location.href,
            affiliate_type: `AFFILIATE_LINK`,
          });
        }

        if (customer && customer?.id === Number(paramValues?.customer_id)) {
          return enqueueSnackbar(t("link.error"), { variant: "error" });
        }

        enqueueSnackbar(t("link.success"), { variant: "success" });
      } catch (err) {
        enqueueSnackbar(t("link.error"), { variant: "error" });
      } finally {
        router.push(`/redirect`);
      }
    })();
  }, []);

  return (
    <div className="w-full h-screen flex justify-center items-center">
      <CgSpinnerAlt size={32} className="animate-spin text-primary" />
    </div>
  );
}
