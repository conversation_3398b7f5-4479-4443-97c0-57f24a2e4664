"use client";

import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { ShopLicenseStepper } from "@/components/modules/shop/components/shop-stepper";
import { JourneyPurchase } from "@/components/modules/shop/journeys/components/journey-purchase";
import { Plant } from "@arthursenno/lizenzero-ui-react/Icon";
import { useDirectLicensePath } from "@/hooks/path/use-direct-license-path";
import { useTranslations } from "next-intl";

export default function DirectLicensePurchase() {
  const t = useTranslations("shop.directLicense.purchase");
  return (
    <>
      <ShopBreadcrumb paths={useDirectLicensePath()} Icon={Plant} />
      <ShopLicenseStepper step={4} />
      <ShopContent>
        <TitleAndSubTitle title={t("title")} subTitle={t("subTitle")} />
        <JourneyPurchase journey="DIRECT_LICENSE" />
      </ShopContent>
    </>
  );
}
