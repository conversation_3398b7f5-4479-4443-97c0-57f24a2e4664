"use client";

import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { ShopLicenseStepper } from "@/components/modules/shop/components/shop-stepper";
import { JourneyBilling } from "@/components/modules/shop/journeys/components/journey-billing";
import { useDirectLicensePath } from "@/hooks/path/use-direct-license-path";
import { ShoppingBasket } from "@arthursenno/lizenzero-ui-react/Icon";
import { useTranslations } from "next-intl";

export default function BillingPage() {
  const t = useTranslations("shop.directLicense.billing");
  return (
    <>
      <ShopBreadcrumb paths={useDirectLicensePath()} Icon={ShoppingBasket} />
      <ShopLicenseStepper step={3} />
      <ShopContent>
        <TitleAndSubTitle title={t("title")} subTitle={t("subTitle")} />
        <JourneyBilling />
      </ShopContent>
    </>
  );
}
