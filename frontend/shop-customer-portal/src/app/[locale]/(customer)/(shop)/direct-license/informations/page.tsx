"use client";

import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";

import { ResetPasswordModal } from "@/components/_common/modals/reset-password-modal";
import { ShoppingBasket } from "@arthursenno/lizenzero-ui-react/Icon";

import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { ShopInformationBanner } from "@/components/modules/shop/components/shop-information-banner";
import { ShopLicenseStepper } from "@/components/modules/shop/components/shop-stepper";
import { JourneyInformationForm } from "@/components/modules/shop/journeys/components/journey-information-form";
import { JourneyInformationFormProvider } from "@/components/modules/shop/journeys/components/journey-information-form/journey-information-form-provider";
import { JourneyInformationFormSubmit } from "@/components/modules/shop/journeys/components/journey-information-form/journey-information-form-submit";
import { JourneyResume } from "@/components/modules/shop/journeys/components/journey-resume";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useTranslations } from "next-intl";
import { useDirectLicensePath } from "@/hooks/path/use-direct-license-path";
import { ShopTrustpilot } from "@/components/modules/shop/components/shop-trustpilot";

export default function DirectLicenseInformations() {
  const isMobile = useMediaQuery("(max-width: 768px)");
  const t = useTranslations("shop.directLicense.informations");
  return (
    <>
      <ShopBreadcrumb paths={useDirectLicensePath()} Icon={ShoppingBasket} />
      <ShopLicenseStepper step={2} />
      <ShopContent>
        <TitleAndSubTitle title="Enter your informations" subTitle="You are purchasing our Direct Service" />
        <JourneyInformationFormProvider>
          <div className="grid grid-cols-1 md:grid-cols-12 gap-6 w-full mb-20">
            <div className="md:col-span-7">
              <JourneyInformationForm isDirectLicense />
            </div>
            <div className="flex flex-col gap-6 md:col-span-5 space-y-8 md:space-y-16">
              <ShopInformationBanner />
              <JourneyResume />
              <div className="flex flex-col gap-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-10">
                  <div className="hidden md:block"></div>
                  <JourneyInformationFormSubmit />
                </div>
                <ShopTrustpilot />
              </div>
            </div>
          </div>
        </JourneyInformationFormProvider>
      </ShopContent>
      <ResetPasswordModal />
    </>
  );
}
