"use client";

import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { ResetPasswordModal } from "@/components/_common/modals/reset-password-modal";
import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { ShopLicenseStepper } from "@/components/modules/shop/components/shop-stepper";
import { JourneyInformationForm } from "@/components/modules/shop/journeys/components/journey-information-form";
import { JourneyInformationFormProvider } from "@/components/modules/shop/journeys/components/journey-information-form/journey-information-form-provider";
import { JourneyInformationFormSubmit } from "@/components/modules/shop/journeys/components/journey-information-form/journey-information-form-submit";
import { JourneyResume } from "@/components/modules/shop/journeys/components/journey-resume";
import { Padlock, ShoppingBasket } from "@arthursenno/lizenzero-ui-react/Icon";
import { SelectCountriesMapModal } from "@/components/_common/modals/select-countries-map-modal";
import { ShopTrustpilot } from "@/components/modules/shop/components/shop-trustpilot";
import { ShopBanner, IconBanner } from "@arthursenno/lizenzero-ui-react/Banner";
import { useQuickJourneyPath } from "@/hooks/path/use-quick-journey-path";
import { useTranslations } from "next-intl";

export default function Information() {
  const t = useTranslations("shop.common.journey.information");
  return (
    <>
      <ShopBreadcrumb paths={useQuickJourneyPath()} Icon={ShoppingBasket} />
      <ShopLicenseStepper step={2} />
      <ShopContent>
        <TitleAndSubTitle title="Enter your informations" subTitle="You are purchasing our License Service" />
        <SelectCountriesMapModal />
        <JourneyInformationFormProvider>
          <div className="grid grid-cols-1 md:grid-cols-12 gap-6 w-full mb-20">
            <div className="md:col-span-7">
              <JourneyInformationForm />
            </div>
            <div className="flex flex-col gap-6 md:col-span-5 space-y-8 md:space-y-16">
              <JourneyResume />
              <div className="flex flex-col gap-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-10">
                  <div className="hidden md:block"></div>
                  <JourneyInformationFormSubmit />
                </div>
                <ShopTrustpilot />
              </div>
              <ShopBanner title="" style={{ width: "100%" }}>
                <IconBanner
                  className="text-white "
                  icon={() => <Padlock width={24} height={24} className="fill-tonal-dark-blue-80" />}
                />

                <div className="">
                  <p className="font-bold text-base">{t("banner.title")}</p>
                  <span className="w-full text-sm ">{t("banner.description")}</span>
                </div>
              </ShopBanner>
            </div>
          </div>
        </JourneyInformationFormProvider>
      </ShopContent>
      <ResetPasswordModal />
    </>
  );
}
