"use client";

import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { ShopLicenseStepper } from "@/components/modules/shop/components/shop-stepper";
import { formatCustomerNumber } from "@/utils/format-customer-number";

import { useCustomer } from "@/hooks/use-customer";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { CheckCircleOutline, Download, Plant, RequestQuote } from "@arthursenno/lizenzero-ui-react/Icon";
import Image from "next/image";
import { useRouter } from "@/i18n/navigation";
import { enqueueSnackbar } from "notistack";

import { useLongJourneyPath } from "@/hooks/path/use-long-journey-path";

import { useTranslations } from "next-intl";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useMutation } from "@tanstack/react-query";
import { downloadFile } from "@/utils/download-file";
import { CgSpinnerAlt } from "react-icons/cg";
import { downloadCustomerFileByRelation } from "@/lib/api/file";

const RenderCheckTitle = ({
  title,
  body,
  checked,
  disabled,
  last,
}: {
  title: string;
  body?: React.ReactNode;
  checked?: boolean;
  disabled?: boolean;
  last?: boolean;
}) => {
  let className: string =
    "w-10 h-10 rounded-full bg-[#ABC7FD] outline outline-offset-[-12px] outline-[3px] outline-grey-blue flex justify-center items-center";

  if (checked) {
    className = className
      .replace("bg-[#ABC7FD]", "bg-success")
      .replace("outline", "")
      .replace("outline-offset-[-12px]", "")
      .replace("outline-[3px]", "")
      .replace("outline-grey-blue", "");
  }

  if (disabled) {
    className = className.replace("bg-[#ABC7FD]", "bg-[#E1E0DD]").replace("outline-grey-blue", "outline-on-surface-01");
  }

  return (
    <div className="w-full flex flex-row items-start gap-7 h-full">
      <div className="flex flex-col items-center gap-2 h-full">
        <div className={className}>{checked && <CheckCircleOutline className="w-6 h-6 fill-white" />}</div>
        {!last && (
          <div
            className={`h-20 md:h-[calc(100%-3rem)] rounded-full ${
              disabled ? "bg-tonal-dark-cream-80" : "bg-tonal-dark-cream-60"
            }  w-0.5`}
          />
        )}
      </div>
      <div className="w-full h-full">
        <p className={`${!disabled && "font-bold"} text-xl text-grey-blue leading-none mt-2`}>{title}</p>
        {body}
      </div>
    </div>
  );
};

export default function Conclusion() {
  const router = useRouter();
  const { customer } = useCustomer();
  const t = useTranslations("shop.longJourney.conclusion");

  const { paramValues } = useQueryFilter(["order_id"]);
  const orderId = paramValues?.order_id;

  const downloadInvoiceMutation = useMutation({
    mutationFn: async () => {
      if (!orderId) throw new Error("Invoice not found");

      const downloadedFile = await downloadCustomerFileByRelation("order_id", orderId);

      downloadFile({ buffer: downloadedFile, fileName: `invoice-${orderId}.pdf` });
    },
  });

  async function handleInvoiceDownload() {
    downloadInvoiceMutation.mutate(undefined, {
      onSuccess: () => {
        enqueueSnackbar(t("invoice.download.success"), { variant: "success" });
      },
      onError: () => {
        enqueueSnackbar(t("invoice.download.error"), { variant: "error" });
      },
    });
  }

  return (
    <>
      <ShopBreadcrumb paths={useLongJourneyPath()} Icon={Plant} />
      <ShopLicenseStepper step={4} />
      <ShopContent>
        <div className="w-full mb-5 min-h-screen">
          <div className="w-full flex flex-col md:flex-row gap-6 justify-center">
            <div className="w-full md:w-2/5 flex flex-col items-center">
              <div className="flex-none h-72 md:h-80 w-[104%] rounded-[40px] pb-3 px-3 bg-background z-10 sticky">
                <div className="w-full h-full px-7 py-8 md:p-10 bg-success-container rounded-[40px] flex flex-col justify-center">
                  <div className="absolute h-[50px] top-0 left-6">
                    <svg
                      className="-mt-[1px]"
                      width="172"
                      height="52"
                      viewBox="0 0 172 52"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        id="Vector"
                        d="M171.893 -5.89338e-06C157.034 0.0312076 143.654 8.39006 136.455 21.3884C126.019 40.2191 105.955 51.1867 87.1606 51.1268C64.6684 51.0547 47.4584 38.9924 36.0992 20.5952C28.41 8.13509 15.0455 0.329479 0.405344 0.360234"
                        fill="white"
                      />
                    </svg>
                    <div className="absolute left-1/2 -translate-x-1/2 top-0 -translate-y-1/2">
                      <Image className="" src="/assets/images/leaf_seal.png" alt="leaf seal" width={74} height={74} />
                    </div>
                  </div>
                  <p className="text-success font-bold text-4xl md:text-5xl leading-[normal]">{t("thankYou")}</p>
                  <p className="text-grey-blue font-bold text-xl mt-4">
                    {t("customerNumber")}: <span className="block md:inline">{formatCustomerNumber(customer?.id)}</span>
                  </p>
                </div>
              </div>
              <div className="w-full px-7 py-8 pt-20 md:p-10 md:pt-20 bg-tonal-dark-blue-96 rounded-b-[40px] -mt-12 sticky -z-0 h-full">
                <div className="flex items-center gap-2 mb-2">
                  <RequestQuote className="w-7 h-7 fill-grey-blue" />
                  <p className="text-xl md:text-2xl text-grey-blue font-bold mt-2">{t("invoice.download.title")}</p>
                </div>
                <p className="text-grey-blue text-base">{t("invoice.download.clickHere")}</p>
                <div className="w-full md:w-8/12 mt-6">
                  <Button
                    color="yellow"
                    variant="filled"
                    size="medium"
                    className="w-full md:w-auto"
                    onClick={handleInvoiceDownload}
                    disabled={downloadInvoiceMutation.isPending}
                    trailingIcon={
                      downloadInvoiceMutation.isPending ? <CgSpinnerAlt className="animate-spin" /> : <Download />
                    }
                  >
                    {t("invoice.download.button")}
                  </Button>
                </div>
              </div>
            </div>
            <div className="w-full md:w-3/5">
              <div className="w-full h-full p-10 bg-surface-02 rounded-[40px]">
                <p className="text-grey-blue text-2xl font-bold">{t("nextSteps")}</p>
                <div className="mt-6 p-2 flex flex-col gap-7" style={{ height: "calc(100% - 2.5rem)" }}>
                  <div className="flex-1">
                    <RenderCheckTitle title={t("createAccount")} checked />
                  </div>
                  <div className="flex-1">
                    <RenderCheckTitle
                      title={t("uploadDocumentation.title")}
                      body={
                        <div>
                          <p className="text-base text-tonal-dark-cream-30 mt-3">
                            {t("uploadDocumentation.description")}
                          </p>
                          <div className="w-full md:w-1/2 mt-6">
                            <Button
                              color="dark-blue"
                              variant="filled"
                              size="small"
                              onClick={() => router.push("/saas/eu-license")}
                            >
                              {t("uploadDocumentation.button")}
                            </Button>
                          </div>
                        </div>
                      }
                      disabled
                    />
                  </div>
                  <div className="h-10">
                    <RenderCheckTitle title={t("startReporting")} last />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ShopContent>
    </>
  );
}
