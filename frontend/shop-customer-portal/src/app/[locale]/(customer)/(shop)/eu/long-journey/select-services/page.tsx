"use client";

import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { JourneySelectServices } from "@/components/modules/shop/journeys/components/journey-select-services";
import { ShoppingCart } from "@arthursenno/lizenzero-ui-react/Icon";
import { useTranslations } from "next-intl";
import { useLongJourneyPath } from "@/hooks/path/use-long-journey-path";

export default function ServiceSelection() {
  const t = useTranslations("shop.longJourney.selectServices");

  return (
    <>
      <ShopBreadcrumb paths={useLongJourneyPath()} Icon={ShoppingCart} />
      <ShopContent className="!pb-0">
        <TitleAndSubTitle title={t("title")} subTitle={t("subTitle")} subText={t("subText")} />
      </ShopContent>
      <JourneySelectServices />
    </>
  );
}
