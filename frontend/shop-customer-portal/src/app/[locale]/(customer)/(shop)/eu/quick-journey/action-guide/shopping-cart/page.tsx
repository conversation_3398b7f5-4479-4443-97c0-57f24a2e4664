"use client";

import { ExitIntentModal } from "@/components/_common/modals/exit-intent-modal";
import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { ShopInterestProducts } from "@/components/modules/shop/components/shop-interest-products";
import { ShopLicenseStepper } from "@/components/modules/shop/components/shop-stepper";
import { JourneySelectCountriesModal } from "@/components/modules/shop/journeys/components/journey-select-countries-modal";

import { JourneyShoppingCart } from "@/components/modules/shop/journeys/components/journey-shopping-cart";
import { useActionGuidePath } from "@/hooks/path/use-action-guide-path";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { ShoppingBasket } from "@arthursenno/lizenzero-ui-react/Icon";
import { useSession } from "next-auth/react";
import { useTranslations } from "next-intl";
import { useEffect } from "react";

export default function ShoppingCart() {
  const { shoppingCart } = useShoppingCart();
  const t = useTranslations("shop.quickJourney.actionGuide.shoppingCart");

  const actionGuideItems = shoppingCart.items.filter((i) => i.service_type === "ACTION_GUIDE");

  const { data: session } = useSession();

  const { changeParam } = useQueryFilter(["select-countries"]);

  useEffect(() => {
    if (!actionGuideItems.length) changeParam("select-countries", "true");
  }, []);

  return (
    <>
      <ShopBreadcrumb paths={useActionGuidePath()} Icon={ShoppingBasket} />
      <ShopLicenseStepper step={1} />
      <ShopContent>
        <TitleAndSubTitle title={t("title")} subTitle={t("subTitle")} />
        <JourneyShoppingCart />
      </ShopContent>
      <ShopInterestProducts addedProduct="license" />
      <ExitIntentModal />
      <JourneySelectCountriesModal />
    </>
  );
}
