"use client";

import { ExitIntentModal } from "@/components/_common/modals/exit-intent-modal";
import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { ShopInterestProducts } from "@/components/modules/shop/components/shop-interest-products";
import { ShopLicenseStepper } from "@/components/modules/shop/components/shop-stepper";
import { JourneyShoppingCart } from "@/components/modules/shop/journeys/components/journey-shopping-cart";
import { useLongJourneyPath } from "@/hooks/path/use-long-journey-path";
import { ShoppingBasket } from "@arthursenno/lizenzero-ui-react/Icon";

import { useTranslations } from "next-intl";
export default function ShoppingCart() {
  const t = useTranslations("shop.longJourney.shoppingCart");

  return (
    <>
      <ShopBreadcrumb paths={useLongJourneyPath()} Icon={ShoppingBasket} />
      <ShopLicenseStepper step={1} />
      <ShopContent>
        <TitleAndSubTitle title={t("title")} subTitle={t("subTitle")} />
        <JourneyShoppingCart />
      </ShopContent>
      <ShopInterestProducts addedProduct="license" />
      <ExitIntentModal />
    </>
  );
}
