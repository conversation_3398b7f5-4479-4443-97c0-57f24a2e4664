"use client";
import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { ShoppingBasket } from "@arthursenno/lizenzero-ui-react/Icon";
import { JourneyCreateAccount } from "@/components/modules/shop/journeys/components/journey-create-account";
import { useLongJourneyPath } from "@/hooks/path/use-long-journey-path";

import { useTranslations } from "next-intl";

export default function CreateAnAccount() {
  const t = useTranslations("shop.longJourney.createAccount");
  return (
    <>
      <ShopBreadcrumb paths={useLongJourneyPath()} Icon={ShoppingBasket} />
      <ShopContent>
        <TitleAndSubTitle subText={t("subText")} title={t("title")} subTitle={t("subTitle")} />
        <JourneyCreateAccount />
      </ShopContent>
    </>
  );
}
