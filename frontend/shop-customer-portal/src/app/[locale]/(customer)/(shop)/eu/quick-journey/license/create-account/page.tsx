/* eslint-disable react/no-unescaped-entities */
/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";
import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";

import { ShopBreadcrumb } from "@/components/modules/shop/components/shop-breadcrumb";
import { ShopContent } from "@/components/modules/shop/components/shop-content";
import { ShoppingBasket } from "@arthursenno/lizenzero-ui-react/Icon";

import { JourneyCreateAccount } from "@/components/modules/shop/journeys/components/journey-create-account";
import { useQuickJourneyPath } from "@/hooks/path/use-quick-journey-path";
import { useTranslations } from "next-intl";

export default function CreateAnAccount() {
  const t = useTranslations("shop.quickJourney.license.createAccount");
  return (
    <>
      <ShopBreadcrumb paths={useQuickJourneyPath()} Icon={ShoppingBasket} />
      <ShopContent>
        <TitleAndSubTitle title={t("title")} subTitle={t("subTitle")} />
        <JourneyCreateAccount />
      </ShopContent>
    </>
  );
}
