"use client";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Icons } from "@/components/ui/icons";
import { PasswordInput } from "@/components/ui/password-input";
import { useCustomer } from "@/hooks/use-customer";
import { useLogout } from "@/hooks/use-logout";
import { useRouter } from "@/i18n/navigation";
import { TypeResendToken } from "@/lib/api/account/types";
import { patchEmail, patchVerifyEmail } from "@/lib/api/auth";
import { EMAIL_REGEX } from "@/utils/regex";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Check, East, EditCircle } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { enqueueSnackbar } from "notistack";
import { VerifyAccountModal } from "@/components/_common/modals/verify-account-modal";

const emailSchema = z.object({
  newEmail: z.string().regex(EMAIL_REGEX, { message: "Invalid email" }),
  newEmailConfirmation: z.string().regex(EMAIL_REGEX, { message: "Invalid email" }),
  password: z.string(),
});

type EmailFormValues = z.infer<typeof emailSchema>;

const initialValues = {
  newEmail: "",
  newEmailConfirmation: "",
  password: "",
};

export function EmailForm() {
  const [editingLock, setEditingLock] = useState(true);
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmit, setIsSubmit] = useState(false);

  const { customer, invalidateCustomer } = useCustomer();
  const router = useRouter();

  const { logout } = useLogout();

  const {
    handleSubmit,
    register,
    getValues,
    setError,
    clearErrors,
    reset,
    watch,
    formState: { errors },
  } = useForm<EmailFormValues>({
    resolver: zodResolver(emailSchema),
    defaultValues: initialValues,
  });

  const hasErrors = Object.keys(errors).length > 0;

  const newEmail = watch("newEmail");
  const newEmailConfirmation = watch("newEmailConfirmation");
  const password = watch("password");

  const isNotValidConfirmEmail = newEmail && newEmailConfirmation && newEmail !== newEmailConfirmation;

  function cancelEditing() {
    clearErrors();
    reset();
    setEditingLock(true);
    setIsSubmit(false);
  }

  async function submit(data: EmailFormValues) {
    if (!customer) return;

    setIsLoading(true);

    const res: any = await patchEmail(customer.user_id, {
      newEmail: data.newEmail,
      oldEmail: customer?.email || "",
      password: data.password,
    });

    if (res?.data) {
      setEditingLock(true);
      setOpen(true);
      setIsSubmit(true);
    }

    const status = res?.response?.status;

    if (status === 401) {
      setError("password", { message: "Wrong password" });
    }

    if (status === 409) {
      setError("newEmail", { message: "Email already in use" });
    }

    setIsLoading(false);
  }

  async function handleConfirmToken(token: string) {
    if (!customer) return { error: true };

    const res: any = await patchVerifyEmail(customer.user_id, {
      token,
    });

    if (res.data) {
      invalidateCustomer();

      setEditingLock(true);
      setOpen(false);
      setIsSubmit(false);
      reset();

      enqueueSnackbar("Email edited successfully", { variant: "success" });
    }

    const status = res?.response?.status;

    if (status === 409) {
      setError("newEmail", { message: "Email already in use" });
      setOpen(false);
      setEditingLock(false);
      setIsSubmit(false);
    }

    return {
      error: !res.data,
    };
  }

  const handleResendToken = async () => {
    if (!customer) return { error: true };

    const res: any = await patchEmail(customer.user_id, {
      newEmail: newEmail,
      oldEmail: customer.email || "",
      password,
    });

    return {
      error: !res.data,
    };
  };

  async function handleSignout() {
    await logout();
    router.push("/en/auth/forgot-password");
  }

  if (!customer) return null;

  return (
    <form onSubmit={handleSubmit(submit)}>
      <div className="w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:py-7 md:px-8">
        <div className="flex gap-6 items-center mb-8">
          <p className="text-primary font-medium text-xl">Change Email</p>
          {isSubmit && (
            <button className="text-support-blue text-sm font-bold" onClick={() => setOpen(true)} type="button">
              Validate token to {newEmail}
            </button>
          )}
        </div>
        {editingLock ? (
          <div className="grid grid-cols-2 gap-6">
            <Input
              key={`email`}
              name="email"
              label="E-mail"
              value={customer.email}
              placeholder="your email"
              enabled={false}
              variant={"disabled"}
            />

            <Button
              color="light-blue"
              size="small"
              variant="text"
              trailingIcon={<EditCircle />}
              onClick={() => setEditingLock(false)}
              className="h-fit w-fit self-end mb-3"
            >
              Change email
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-6 w-full">
            <Input
              label="New E-mail"
              {...register("newEmail")}
              placeholder="New email"
              variant={errors.newEmail && "error"}
              rightIcon={
                !errors.newEmail &&
                emailSchema.shape.newEmail.safeParse(getValues("newEmail")).success &&
                getValues("newEmail") && <Check width={20} height={20} className="fill-tonal-green-40" />
              }
              errorMessage={errors.newEmail?.message}
            />

            <Input
              label="Confirm E-mail"
              {...register("newEmailConfirmation")}
              placeholder="New email"
              rightIcon={
                !errors.newEmailConfirmation &&
                emailSchema.shape.newEmailConfirmation.safeParse(getValues("newEmailConfirmation")).success &&
                getValues("newEmailConfirmation") && <Check width={20} height={20} className="fill-tonal-green-40" />
              }
              variant={(errors.newEmailConfirmation || isNotValidConfirmEmail) && "error"}
              errorMessage={
                (errors.newEmailConfirmation && errors.newEmailConfirmation.message) ||
                (isNotValidConfirmEmail && "The email doesnt match")
              }
            />

            <div className="space-y-2">
              <PasswordInput
                label="Password"
                {...register("password")}
                placeholder="Your password"
                variant={errors.password && "error"}
              />

              {!!errors.password && (
                <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                  <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
                    {errors.password.message}
                  </span>
                  <button className="text-support-blue text-sm font-bold" onClick={handleSignout}>
                    Forgot password?
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {!editingLock && (
        <div className="flex flex-row gap-6 justify-end w-full mt-8">
          <p className="text-error">{errors.root?.message}</p>
          <Button onClick={cancelEditing} color="dark-blue" size="medium" variant="outlined" type="button">
            Cancel
          </Button>

          <Button
            color="yellow"
            trailingIcon={<East />}
            size="medium"
            variant="filled"
            type="submit"
            disabled={hasErrors || isNotValidConfirmEmail || isLoading}
          >
            {isLoading ? "Loading..." : "Save"}
          </Button>
        </div>
      )}
      <VerifyAccountModal
        open={open}
        onOpenChange={setOpen}
        email={newEmail}
        typeResendToken={TypeResendToken.LOGIN}
        onConfirmToken={handleConfirmToken}
        onResendToken={handleResendToken}
        showClearBtn
      />
    </form>
  );
}
