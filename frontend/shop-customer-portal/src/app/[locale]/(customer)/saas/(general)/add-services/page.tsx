"use client";

import { <PERSON> } from "@/i18n/navigation";

import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { SaasContainer } from "@/components/modules/saas/components/saas-container";
import { AdditionalServiceCard } from "@/components/modules/saas/general/additional-services/additional-service-card";
import { BenefitCommunicationBanner } from "@/components/modules/saas/general/additional-services/benefit-communication-banner";
import { Spinner } from "@/components/ui/loader";
import { useRouter } from "@/i18n/navigation";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East, KeyboardArrowLeft } from "@arthursenno/lizenzero-ui-react/Icon";
import { useState } from "react";
import { SaasBreadcrumb } from "@/components/modules/saas/components/saas-breadcrumb";
import { useTranslations } from "next-intl";

const paths = [
  {
    label: "Dashboard EU",
    href: "/dashboard/licenses",
  },
  {
    label: "Add Services",
    href: "#",
  },
];

const actionGuideItems = ["Action guide service", "Action guide service", "Action guide service"];

const workshopItems = ["Workshop license service", "Workshop license service", "Workshop license service"];

export default function AddServices() {
  const router = useRouter();
  const globalT = useTranslations("modules.saas.global");
  const t = useTranslations("modules.saas.pages.general.additionalServices");

  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  function navigateToPurchase() {
    setIsLoading(true);
    if (selectedServices.includes("EU_LICENSE") && selectedServices.includes("ACTION_GUIDE")) {
      router.push("/eu/long-journey/select-countries");
    } else if (selectedServices.includes("EU_LICENSE")) {
      router.push("/eu/quick-journey/license/calculator");
    } else if (selectedServices.includes("ACTION_GUIDE")) {
      router.push("/eu/quick-journey/action-guide/shopping-cart");
    }
    setIsLoading(false);
  }

  function handleAddService(service: string) {
    setSelectedServices((prev) => {
      if (!prev.includes(service)) {
        return [...prev, service];
      }
      return prev;
    });
  }

  function handleRemoveService(service: string) {
    setSelectedServices((prev) => prev.filter((s) => s !== service));
  }

  return (
    <>
      <SaasBreadcrumb paths={paths} />
      <SaasContainer containerClassName="pb-0">
        <Link href="/saas/eu-license">
          <button className="flex items-center gap-2 md:gap-4 mb-10 text-primary md:text-support-blue font-bold text-base md:text-xl">
            <KeyboardArrowLeft className="fill-primary md:fill-support-blue size-5 md:size-6" />
            <span className="mt-1">{globalT("buttons.backToDashboard.label")}</span>
          </button>
        </Link>
        <TitleAndSubTitle title={t("title")} subText={t("description")} />
      </SaasContainer>
      <SaasContainer containerClassName="bg-surface-02 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <AdditionalServiceCard
            title={t("services.euLicense.title")}
            description={t("services.euLicense.description")}
            onAdd={() => handleAddService("EU_LICENSE")}
            onRemove={() => handleRemoveService("EU_LICENSE")}
            added={selectedServices.includes("EU_LICENSE")}
          />
          <AdditionalServiceCard
            title={t("services.actionGuide.title")}
            description={t("services.actionGuide.description")}
            items={actionGuideItems}
            onAdd={() => handleAddService("ACTION_GUIDE")}
            onRemove={() => handleRemoveService("ACTION_GUIDE")}
            added={selectedServices.includes("ACTION_GUIDE")}
          />
          <AdditionalServiceCard
            title={t("services.workshops.title")}
            description={t("services.workshops.description")}
            items={workshopItems}
            isDisabled
          />
        </div>
      </SaasContainer>
      <SaasContainer className="py-16">
        <div className="flex flex-col md:flex-row justify-between items-center md:items-start gap-6">
          <BenefitCommunicationBanner />

          <Button
            trailingIcon={<East />}
            color="yellow"
            variant="filled"
            size="medium"
            disabled={selectedServices.length <= 0 || isLoading}
            onClick={navigateToPurchase}
            className="w-full md:w-auto"
          >
            {isLoading ? <Spinner /> : globalT("buttons.continue.label")}
          </Button>
        </div>
      </SaasContainer>
    </>
  );
}
