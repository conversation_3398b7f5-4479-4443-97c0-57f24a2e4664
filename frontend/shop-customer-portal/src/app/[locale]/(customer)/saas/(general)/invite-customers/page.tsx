"use client";
import PaginatedTable from "@/components/_common/tables/paginated-table";
import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { SaasBreadcrumb } from "@/components/modules/saas/components/saas-breadcrumb";
import { SaasContainer } from "@/components/modules/saas/components/saas-container";
import { InviteShare, InviteStats, InviteVoucher } from "@/components/modules/saas/general/invite";
import ActiveVoucher from "@/components/modules/saas/general/invite/active-voucher";
import RedeemModal from "@/components/modules/saas/general/invite/redeem-modal";
import { useComissionColumns } from "@/hooks/table-columns/invite/comissionColumns";
import { useVoucherColumns } from "@/hooks/table-columns/invite/voucherColumns";
import { Link } from "@/i18n/navigation";
import { getCommission } from "@/lib/api/commission";
import { Coupon } from "@/lib/api/coupon/types";
import { getCustomerById } from "@/lib/api/customer";
import { InviteCode, InviteComission } from "@/lib/api/invite/types";
import { useCustomer } from "@/hooks/use-customer";
import { formatCurrency } from "@/utils/formatCurrency";
import { FileCopy, KeyboardArrowDown, KeyboardArrowUp } from "@arthursenno/lizenzero-ui-react/Icon";
import { useQuery } from "@tanstack/react-query";
import dayjs from "dayjs";
import { useEffect, useState } from "react";

const paths = [
  { label: "Dashboard EU", href: "/saas/eu-license" },
  { label: "Invite Customers", href: "#" },
];

type CouponObj = { coupon: Coupon };

export default function InviteCustomers() {
  const columns = useComissionColumns();
  const voucherColumns = useVoucherColumns();
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [showPreviousVouchers, setShowPreviousVouchers] = useState<boolean>(false);
  const { customer } = useCustomer();

  const [userCodes, setUserCodes] = useState<InviteCode[]>([]);

  const [startDate, setStartDate] = useState(undefined);
  const [endDate, setEndDate] = useState(undefined);
  const [filteredUserComissionData, setFilteredUserComissionData] = useState<InviteComission[]>([]);

  const {
    data: userComissionData,
    isPending: loading,
    refetch: refetchComission,
  } = useQuery({
    queryKey: ["comission-table", { user_id: customer?.user_id }],
    queryFn: async () => {
      const res = await getCommission({
        user_id: customer?.user_id,
      });

      if (!res) return null;

      return res.map((item) => ({
        ...item,
        id: item.id,
        comission_date: item.created_at,
        product: item.service_type,
        commission: item.commission_value,
        order_id: +item.order_id,
        lead_source: item.type,
        start_date: item.created_at,
      }));
    },
  });

  const {
    data: customerInviteToken,
    isPending: inviteLoading,
    refetch: refetchInvites,
  } = useQuery({
    queryKey: ["customer-coupons", customer?.id],
    queryFn: async () => {
      const res = await getCustomerById(customer!.id);

      const couponFind = res.coupons?.find((item: any) => item.coupon.type === "CUSTOMER");
      const couponSystem = res.coupons?.filter((item: any) => item.coupon.type === "SYSTEM");
      const coupon = couponFind?.coupon;

      return {
        ...coupon,
        share_link: `/shop-invite?customer_id=${customer?.id}`,
        coupon_system: couponSystem,
      };
    },
    enabled: !!customer?.id,
  });

  const handleReload = () => {
    refetchComission();
    refetchInvites();
  };

  const handleStartDateChange = (date: any) => {
    setStartDate(date);
  };

  const handleEndDateChange = (date: any) => {
    setEndDate(date);
  };

  const handleModalToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleShowPreviousVouchersToggle = () => {
    setShowPreviousVouchers(!showPreviousVouchers);
  };

  useEffect(() => {
    if (!userComissionData?.length) return;

    const filterDataByDate = () => {
      if (startDate && endDate) {
        const filtered = userComissionData.filter((item) => {
          const comissionDate = dayjs(item.comission_date);
          return comissionDate.isAfter(startDate) && comissionDate.isBefore(endDate);
        });
        setFilteredUserComissionData(filtered);
      } else {
        setFilteredUserComissionData(userComissionData);
      }
    };

    filterDataByDate();
  }, [startDate, endDate, userComissionData]);

  const userComissionPrice = (userComissionData || []).reduce(
    (total, item) => (item.used ? total : total + (Number(item.commission) || 0)),
    0
  );

  const voucherSelect: CouponObj =
    customerInviteToken?.coupon_system?.find((item: CouponObj) => item?.coupon?.is_active) ||
    customerInviteToken?.coupon_system?.[0];

  return (
    <>
      <SaasBreadcrumb paths={paths} />
      <SaasContainer>
        <TitleAndSubTitle
          title="Invite Customers"
          subText="Here you will find the details about your bills and payments"
        />

        {!!userCodes.length && (
          <div className="flex flex-col gap-6 col-span-5 bg-surface-02 p-10 rounded-[40px] mt-9">
            {userCodes.filter((e) => e.isActive).length > 0 ? (
              <h3 className="text-primary text-2xl font-bold">Active voucher</h3>
            ) : (
              <h3 className="text-primary text-2xl font-bold">No active voucher found</h3>
            )}
            {userCodes
              .filter((e) => e.isActive)
              .map((item) => {
                const copyToClipboard = () => {
                  navigator.clipboard.writeText(item.label);
                  alert("Copied code to clipboard");
                };

                return (
                  <div key={item.id} className="flex flex-row gap-6">
                    <div className="flex items-center justify-between gap-2 px-4 py-2 bg-background rounded-[40px]">
                      <span className="text-primary font-bold">{item.label}</span>
                      <FileCopy
                        onClick={copyToClipboard}
                        className="fill-primary bg-tertiary rounded-full size-9 p-1 cursor-pointer"
                      />
                    </div>

                    <div className="flex flex-col">
                      <span className="text-on-tertiary">
                        Amount: <span className="font-bold">{formatCurrency(item.value)}</span>
                      </span>
                      <span className="text-on-surface-01">Status: {item.isActive ? "Active" : "Used"}</span>
                    </div>
                  </div>
                );
              })}

            <div onClick={handleShowPreviousVouchersToggle} className="flex flex-row gap-2 cursor-pointer w-fit">
              <span className="text-on-tertiary font-bold">Previously vouchers</span>
              {showPreviousVouchers ? (
                <KeyboardArrowUp className="fill-support-blue size-5" />
              ) : (
                <KeyboardArrowDown className="fill-support-blue size-5" />
              )}
            </div>
            {showPreviousVouchers && <PaginatedTable itemsPerPage={5} columns={voucherColumns} data={userCodes} />}
          </div>
        )}
        {voucherSelect?.coupon && (
          <ActiveVoucher
            code={voucherSelect?.coupon?.code}
            status={voucherSelect?.coupon?.is_active ? `Active` : `Deactivate`}
            price={voucherSelect?.coupon?.value}
            vouchers={customerInviteToken?.coupon_system?.map((item: CouponObj) => item.coupon)}
          />
        )}

        <div className="grid grid-cols-1 lg:grid-cols-9 gap-6 mt-9">
          <InviteShare
            customerInviteToken={customerInviteToken}
            isLoading={inviteLoading}
            quantityUsed={userComissionData?.length}
          />
          <InviteVoucher
            price={userComissionPrice}
            guestUsers={userComissionData?.length || 0}
            handleClick={handleModalToggle}
          />
        </div>

        <p className="text-on-surface-01 text-xs my-[41px]">
          *Incentive payments will only be made if a contract is concluded via the referral link / voucher and if the
          generated invoice has been paid in full and not cancelled after X weeks (number of weeks can be flexibly
          defined
        </p>

        <InviteStats />
      </SaasContainer>
      <SaasContainer>
        <div className="flex flex-col gap-6">
          <h3 className="text-primary text-[32px] font-bold">Clients recruited ({userComissionData?.length || 0})</h3>

          <span className="text-primary">
            Your reward will be cashed once this user finishes the registration process.
          </span>

          <div className="flex gap-2 items-center flex-row justify-end text-primary">
            <input
              className="text-support-blue"
              type="date"
              id="start-date"
              value={startDate}
              onChange={(e) => handleStartDateChange(e.target.value)}
            />

            <span>to</span>

            <input
              className="text-support-blue"
              type="date"
              id="end-date"
              value={endDate}
              onChange={(e) => handleEndDateChange(e.target.value)}
            />
          </div>

          <PaginatedTable data={filteredUserComissionData} columns={columns} isLoading={loading} />

          <Link href={"https://www.lizenzero.de"}>
            <span className="text-primary font-bold cursor-pointer">See terms and conditions</span>
          </Link>
        </div>
      </SaasContainer>
      <RedeemModal isOpen={isOpen} toggleOpen={handleModalToggle} price={userComissionPrice} onReload={handleReload} />
    </>
  );
}
