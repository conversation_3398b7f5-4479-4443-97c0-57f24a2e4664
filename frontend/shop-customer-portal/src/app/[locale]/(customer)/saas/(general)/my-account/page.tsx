import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { SaasBreadcrumb } from "@/components/modules/saas/components/saas-breadcrumb";
import { SaasContainer } from "@/components/modules/saas/components/saas-container";
import { CompanyDataForm } from "./_forms/company-form";
import { ConsentForm } from "./_forms/consent-form";
import { EmailForm } from "./_forms/email-form";
import { PasswordForm } from "./_forms/password-form";
import { PersonalDataForm } from "./_forms/personal-form";

const path = [
  {
    label: "Dashboard",
    href: "/saas",
  },
  {
    label: "My Account",
    href: "#",
  },
];

export default function MyAccount() {
  return (
    <>
      <SaasBreadcrumb paths={path} />
      <SaasContainer>
        <TitleAndSubTitle title="My Account" subText="Here you will find all the details about your account" />
        <div className="w-full flex flex-col gap-6 pb-32">
          <PersonalDataForm />
          <CompanyDataForm />
          <EmailForm />
          <PasswordForm />
          <ConsentForm />
        </div>
      </SaasContainer>
    </>
  );
}
