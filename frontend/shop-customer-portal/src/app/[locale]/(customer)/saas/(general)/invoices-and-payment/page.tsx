import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { SaasBreadcrumb } from "@/components/modules/saas/components/saas-breadcrumb";
import { SaasContainer } from "@/components/modules/saas/components/saas-container";
import { InvoicesDefaultPaymentMethod } from "@/components/modules/saas/general/invoices-and-payment/invoices-default-payment-method";
import { InvoiceList } from "@/components/modules/saas/general/invoices-and-payment/invoice-list";
import { StripeProvider } from "@/components/providers/stripe-provider";

const path = [
  {
    label: "Dashboard EU",
    href: "../../dashboards",
  },
  {
    label: "Invoices & Payment",
    href: "#",
  },
];

export default function InvoicePayment() {
  return (
    <>
      <SaasBreadcrumb paths={path} />
      <SaasContainer>
        <TitleAndSubTitle
          title="Invoices & Payment"
          subText="Here you will find all the details about your bills and payments"
        />
        <StripeProvider>
          <div className="space-y-10">
            <InvoiceList />
            <InvoicesDefaultPaymentMethod />
          </div>
        </StripeProvider>
      </SaasContainer>
    </>
  );
}
