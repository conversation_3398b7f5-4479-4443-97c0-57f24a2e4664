"use client";

import { Checkbox } from "@/components/_common/checkbox";
import { CountryIcon } from "@/components/_common/country-icon";
import { Divider } from "@/components/_common/divider";
import { CustomRadio } from "@/components/_common/forms/customRadio/custom-radio";
import {
  additionalAddressLine,
  city,
  companyName,
  countryCode,
  documentType,
  emails,
  lucidNumber,
  mobile,
  streetAndNumber,
  taxNumber,
  vatId,
  zipCode,
} from "@/components/_common/forms/schemas";
import { FormInputIcon } from "@/components/_common/input-status-icon";
import { TooltipIcon } from "@/components/_common/tooltipIcon";
import { Combobox } from "@/components/ui/combobox";
import { useCustomer } from "@/hooks/use-customer";
import { updateCompany, validateVatId } from "@/lib/api/company";
import { COUNTRIES, EU_COUNTRY_CODES } from "@/utils/consts/countries";
import { joinStrings } from "@/utils/join-strings";
import { ADDRESS_REGEX, CITY_REGEX, COMPANY_NAME_REGEX, ZIP_CODE_REGEX } from "@/utils/regex";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Delete, East, EditCircle } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";
import { useEffect, useState } from "react";
import { Controller, useFieldArray, useForm, useWatch } from "react-hook-form";

import { useVatErrorTranslator } from "@/utils/vat-error-translator";
import { z } from "zod";

export const companyFormSchema = z.object({
  companyName,
  countryCode,
  city,
  zipCode,
  streetAndNumber,
  additionalAddressLine,
  documentType,
  vatId,
  taxNumber,
  mobile,
  emails,
  lucidNumber,
  usePersonalDataOnBilling: z.boolean().default(true),
  billing: z
    .object({
      fullName: z
        .string({
          required_error: "Required",
        })
        .trim(),
      countryCode: z
        .string({
          required_error: "Required",
        })
        .trim(),
      countryName: z
        .string({
          required_error: "Required",
        })
        .trim(),
      companyName: z
        .string({
          required_error: "Required",
        })
        .trim()
        .max(50, "Maximum length is 50 characters")
        .refine((value) => COMPANY_NAME_REGEX.test(value), {
          message: "Special characters are not allowed for this field.",
        })
        .optional(),
      streetAndNumber: z
        .string({
          required_error: "Required",
        })
        .trim()
        .max(50, "Maximum length is 50 characters")
        .refine((value) => ADDRESS_REGEX.test(value), {
          message: "Special characters are not allowed for this field.",
        }),
      city: z
        .string()
        .trim()
        .regex(CITY_REGEX, "Special characters are not allowed for this field.")
        .min(1, { message: "Minimum length is 1 character" }),
      zipCode: z
        .string({
          required_error: "Required",
        })
        .trim()
        .max(10, {
          message: "Maximum length is 10 characters",
        })
        .refine((value) => ZIP_CODE_REGEX.test(value), {
          message: "Special characters are not allowed for this field.",
        }),
    })
    .optional(),
});

type CompanyFormData = z.infer<typeof companyFormSchema>;

export function CompanyDataForm() {
  const { customer, invalidateCustomer } = useCustomer();
  const t = useTranslations("shop.common.journey.information.company");
  const globalT = useTranslations("global");
  const getVatApiErrorMessage = useVatErrorTranslator();

  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<CompanyFormData>({
    resolver: zodResolver(companyFormSchema),
    mode: "all",
  });

  const emailField = useFieldArray({
    name: "emails",
    control: form.control,
    keyName: "key",
  });

  const countryCode = useWatch({ control: form.control, name: "countryCode" });
  const documentType = useWatch({ control: form.control, name: "documentType" });
  const hasVatId = useWatch({ control: form.control, name: "vatId" });

  async function submit(data: CompanyFormData) {
    try {
      if (!customer || !customer.company) return;

      setIsLoading(true);
      await form.trigger();

      if (data.documentType === "VAT" && !data.vatId) {
        form.setError("vatId", { type: "manual", message: "Required field" });
        return;
      }

      if (data.documentType === "TAX" && !data.taxNumber) {
        form.setError("taxNumber", { type: "manual", message: "Required field" });
        return;
      }

      const companyResponse = await updateCompany(customer.company.id, {
        customer_id: customer.id,
        name: data.companyName,
        vat: data.vatId,
        tin: data.taxNumber,
        lucid: data.lucidNumber,
        emails: data.emails.map((email) => email.email),
        address: {
          country_code: data.countryCode,
          address_line: joinStrings([data.streetAndNumber, data.city, data.countryCode, data.zipCode]),
          city: data.city,
          zip_code: data.zipCode,
          street_and_number: data.streetAndNumber,
          additional_address: data.additionalAddressLine || "",
        },
        billing: data.usePersonalDataOnBilling
          ? undefined
          : {
              full_name: data.billing?.fullName || "",
              country_code: data.billing?.countryCode || "",
              country_name: data.billing?.countryName || "",
              company_name: data.billing?.companyName || "",
              street_and_number: data.billing?.streetAndNumber || "",
              city: data.billing?.city || "",
              zip_code: data.billing?.zipCode || "",
            },
      });

      if (!companyResponse.success) {
        setIsLoading(false);

        if (companyResponse.type === "VAT_IN_USE") {
          form.setError("vatId", {
            type: "manual",
            message: globalT("inputs.vatId.errors.alreadyInUse"),
          });
          return;
        }

        if (companyResponse.type === "TAX_IN_USE") {
          form.setError("taxNumber", {
            type: "manual",
            message: globalT("inputs.taxNumber.errors.alreadyInUse"),
          });
          return;
        }

        if (companyResponse.type === "DOCUMENT_IN_USE") {
          form.setError("companyName", {
            type: "manual",
            message: globalT("inputs.companyName.errors.alreadyInUse"),
          });
          return;
        }

        if (companyResponse.type === "LUCID_IN_USE") {
          form.setError("lucidNumber", {
            type: "manual",
            message: globalT("inputs.lucid.errors.alreadyRegistered"),
          });
          return;
        }

        return;
      }

      invalidateCustomer();

      enqueueSnackbar("Company data updated successfully", { variant: "success" });

      setIsEditing(true);
    } catch (error) {
      enqueueSnackbar("An error occurred while updating the user data", { variant: "error" });
      form.reset();
      setIsEditing(false);
    } finally {
      setIsLoading(false);
    }
  }

  function handleSelectCountry(country: { value: string; label: string } | null) {
    form.setValue("city", "");
    form.setValue("zipCode", "");
    form.setValue("streetAndNumber", "");
    form.setValue("countryCode", "");

    if (!country) return;

    const countryName = country.value;

    const foundCountry = COUNTRIES.find((c) => c.name === countryName);

    if (!foundCountry) return;

    const isEuCountry = EU_COUNTRY_CODES.find((code) => code === foundCountry.code);

    form.setValue("countryCode", foundCountry.code);
    form.clearErrors("countryCode");

    const documentType = isEuCountry ? "VAT" : "TAX";

    setDocumentType(documentType);
  }

  async function handleSelectVatId(vatId: string) {
    const countryCode = form.getValues("countryCode");
    const companyName = form.getValues("companyName");
    const zipCode = form.getValues("zipCode");
    const cityCode = form.getValues("city");
    const streetAndNumber = form.getValues("streetAndNumber");

    if (!countryCode || !companyName || !zipCode || !cityCode || !streetAndNumber) return;

    const formattedVatId = vatId.replace(/\s/g, "").trim();

    const vatValidationResponse = await validateVatId({
      vat_id: formattedVatId,
      country_code: countryCode,
      company_name: companyName,
      company_zipcode: zipCode,
      company_city: cityCode,
      company_street: streetAndNumber,
    });

    if (!vatValidationResponse?.is_valid) {
      const errorMessage = vatValidationResponse?.error
        ? getVatApiErrorMessage(vatValidationResponse.error)
        : globalT("inputs.vatId.errors.invalid");

      form.setError("vatId", { message: errorMessage });
      return;
    }

    form.clearErrors("zipCode");
    form.setValue("zipCode", zipCode);
    form.setValue("vatId", formattedVatId);
  }

  function setDocumentType(type: "VAT" | "TAX") {
    if (!isEditing) return;

    if (type === "VAT") {
      form.resetField("taxNumber");
      form.setValue("documentType", "VAT");
      form.setValue("taxNumber", undefined);
    }
    if (type === "TAX") {
      form.resetField("vatId");
      form.setValue("documentType", "TAX");
      form.setValue("vatId", undefined);
    }
  }

  function handleSelectBillingCountry(country: { value: string; label: string } | null) {
    form.setValue("billing.city", "");
    form.setValue("billing.zipCode", "");
    form.setValue("billing.streetAndNumber", "");
    form.setValue("billing.countryCode", "");

    if (!country) return;

    const countryName = country.value;

    const foundCountry = COUNTRIES.find((c) => c.name === countryName);

    if (!foundCountry) return;

    form.setValue("billing.countryCode", foundCountry.code);
    form.clearErrors("billing.countryCode");
  }

  function handleOnChangeCustomBilling(usePersonalDataOnBilling: boolean) {
    if (!customer?.company || !customer?.company.billing) return;

    if (!usePersonalDataOnBilling) {
      form.setValue("billing", {
        fullName: customer.company.billing.full_name,
        city: customer.company.billing.city,
        zipCode: customer.company.billing.zip_code,
        streetAndNumber: customer.company.billing.street_and_number,
        countryCode: customer.company.billing.country_code,
        countryName: customer.company.billing.country_name,
        companyName: customer.company.billing.company_name,
      });
      return;
    }

    form.setValue("billing", undefined);
  }

  function resetForm() {
    if (!customer) return;

    form.reset({
      companyName: customer.company?.name,
      countryCode: customer.company?.address?.country_code,
      city: customer.company?.address?.city,
      zipCode: customer.company?.address?.zip_code,
      streetAndNumber: customer.company?.address?.street_and_number,
      additionalAddressLine: customer.company?.address?.additional_address,
      documentType: customer.company?.vat ? "VAT" : "TAX",
      vatId: customer.company?.vat || undefined,
      taxNumber: customer.company?.tin || undefined,
      lucidNumber: customer.company?.lucid || undefined,
      usePersonalDataOnBilling: customer.company.billing?.is_custom || true,
      billing: customer.company.billing?.is_custom
        ? {
            fullName: customer.company.billing.full_name,
            city: customer.company.billing.city,
            zipCode: customer.company.billing.zip_code,
            streetAndNumber: customer.company.billing.street_and_number,
            countryCode: customer.company.billing.country_code,
            countryName: customer.company.billing.country_name,
            companyName: customer.company.billing.company_name,
          }
        : undefined,
      emails: customer.company.emails.map((email) => ({ id: email.id, email: email.email })),
    });
  }

  useEffect(() => {
    resetForm();
  }, [customer]);

  const isSubmitted = form.formState.isSubmitted;
  const errors = form.formState.errors;
  const selectedCountry = COUNTRIES.find((c) => c.code === countryCode);
  const isAddressComplete =
    !!selectedCountry && !!form.getValues("city") && !!form.getValues("streetAndNumber") && !!form.getValues("zipCode");
  const usePersonalDataOnBilling = useWatch({ control: form.control, name: "usePersonalDataOnBilling" });
  const billing = useWatch({ control: form.control, name: "billing" });
  const billingSelectedCountry = COUNTRIES.find((c) => c.code === billing?.countryCode);

  return (
    <form onSubmit={form.handleSubmit(submit)}>
      <div className="w-full rounded-[32px] items-start bg-surface-02 flex flex-col px-4 py-6 md:py-7 md:px-8">
        <div className="flex w-full flex-row justify-between align-middle">
          <div className="flex flex-row gap-4 items-center mb-2">
            <p className="text-primary font-medium text-xl">Company Information</p>
            <TooltipIcon info="Company information form" />
          </div>

          {!isEditing && (
            <Button
              color="light-blue"
              size="small"
              variant="text"
              trailingIcon={<EditCircle />}
              onClick={() => setIsEditing(true)}
            >
              Edit
            </Button>
          )}
        </div>

        <p className="text-[#808FA9] font-light text-sm mb-8">*Mandatory Fields</p>

        <div className="space-y-6 w-full">
          <div className="w-full ">
            <Input
              {...form.register("companyName")}
              label="Company Name *"
              placeholder="Company Name"
              errorMessage={errors.companyName?.message}
              rightIcon={<FormInputIcon control={form.control} name="companyName" />}
              variant={!isEditing ? "disabled" : errors.companyName && "error"}
              enabled={isEditing}
            />
          </div>

          <div className="grid md:grid-cols-2 w-full gap-8">
            <div className="space-y-2">
              <p className="text-primary">Country *</p>
              <Combobox
                key={"partner"}
                items={COUNTRIES.map((c) => ({ label: c.name, value: c.name, flag_url: c.flag_url }))}
                placeholder="Select a country"
                emptyText="No country found"
                searchText="Search country..."
                value={selectedCountry?.name}
                onSelect={handleSelectCountry}
                invalid={!!errors.countryCode}
                disabled={!isEditing}
                renderItem={(item) => (
                  <div className="flex items-center gap-3">
                    <CountryIcon
                      data-disabled={!isEditing}
                      country={{ name: item.label, flag_url: item.flag_url }}
                      className="size-6 data-[disabled=true]:grayscale"
                    />
                    {item.label}
                  </div>
                )}
              />
              {!!errors.countryCode && (
                <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                  <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
                    {errors.countryCode.message}
                  </span>
                </div>
              )}
            </div>

            <Input
              {...form.register("city")}
              label="City *"
              placeholder="City"
              rightIcon={<FormInputIcon control={form.control} name="city" />}
              errorMessage={errors.city?.message}
              variant={errors.city && "error"}
              enabled={!!selectedCountry && isEditing}
            />
          </div>

          <div className="grid md:grid-cols-2 w-full gap-8">
            <Input
              {...form.register("streetAndNumber")}
              label="Street and number *"
              placeholder="Street and number"
              rightIcon={<FormInputIcon control={form.control} name="streetAndNumber" />}
              errorMessage={errors.streetAndNumber?.message}
              variant={errors.streetAndNumber && "error"}
              enabled={!!selectedCountry && isEditing}
            />
            <Input
              {...form.register("zipCode")}
              label="ZIP Code *"
              placeholder="ZIP Code"
              errorMessage={errors.zipCode?.message}
              rightIcon={<FormInputIcon control={form.control} name="zipCode" />}
              variant={errors.zipCode && "error"}
              enabled={!!selectedCountry && isEditing}
            />
          </div>

          <div className="w-full gap-8">
            <Input
              {...form.register("additionalAddressLine")}
              label="Additional address line"
              placeholder="Additional address line"
              errorMessage={errors.additionalAddressLine?.message}
              rightIcon={<FormInputIcon control={form.control} name="additionalAddressLine" />}
              variant={errors.additionalAddressLine && "error"}
              enabled={isEditing}
            />
          </div>

          <div className="grid md:grid-cols-2 w-full gap-8">
            <div>
              <CustomRadio
                checked={documentType === "VAT"}
                label={"VAT ID"}
                onChange={() => setDocumentType("VAT")}
                disabled={!isAddressComplete || !isEditing}
              />
              <Input
                label=""
                placeholder="VAT ID"
                enabled={isAddressComplete && documentType === "VAT"}
                variant={
                  !isEditing
                    ? "disabled"
                    : documentType === "VAT" && errors.vatId
                      ? "error"
                      : documentType !== "VAT"
                        ? "disabled"
                        : "enabled"
                }
                rightIcon={<FormInputIcon control={form.control} name="vatId" />}
                errorMessage={documentType === "VAT" && errors.vatId?.message}
                {...form.register("vatId", {
                  onBlur: (e) => handleSelectVatId(e.target.value),
                })}
              />
              {hasVatId && documentType === "VAT" && !errors.vatId && (
                <div className="bg-surface-03 mt-2 rounded-2xl px-6 py-2">
                  <span className="text-on-surface-03">19% VAT is being charged</span>
                </div>
              )}
            </div>

            <div>
              <CustomRadio
                checked={documentType === "TAX"}
                onChange={() => setDocumentType("TAX")}
                label={"TAX Number"}
                disabled={!isAddressComplete || !isEditing}
              />

              <Input
                label=""
                placeholder="TAX Number"
                errorMessage={documentType === "TAX" && errors.taxNumber?.message}
                enabled={isAddressComplete && documentType === "TAX"}
                variant={
                  !isEditing
                    ? "disabled"
                    : documentType === "TAX" && errors.taxNumber
                      ? "error"
                      : documentType !== "TAX"
                        ? "disabled"
                        : "enabled"
                }
                rightIcon={<FormInputIcon control={form.control} name="taxNumber" />}
                {...form.register("taxNumber")}
              />
            </div>
          </div>
        </div>

        <Divider />

        <div className="space-y-6 w-full">
          <p className="text-primary">Add e-mails to receive the invoice and informations</p>
          {!!emailField.fields.length &&
            emailField.fields.map((email, index) => (
              <div className="flex gap-6 items-center" key={`${email.id}`}>
                <div className="lg:w-1/2 w-full">
                  <div className="space-y-2">
                    <div className="flex items-center gap-4">
                      <Input
                        {...form.register(`emails.${index}`)}
                        defaultValue={emailField.fields[index] ?? ""}
                        label="E-mail "
                        placeholder="E-mail"
                        type="email"
                        enabled={isEditing}
                        variant={!!errors.emails && errors.emails[index] ? "error" : "enabled"}
                      />
                      {isEditing && (
                        <Button
                          color="dark-blue"
                          size="iconSmall"
                          variant="text"
                          className="mt-8"
                          trailingIcon={<Delete style={{ fill: "inherit" }} />}
                          type="button"
                          onClick={() => emailField.remove(index)}
                        />
                      )}
                    </div>
                    {!!errors.emails && errors.emails[index] && (
                      <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                        <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
                          {errors.emails[index]!.message}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
        </div>

        {isEditing && (
          <Button color="light-blue" size="medium" variant="text" onClick={() => emailField.append({ email: "" })}>
            Add new e-mail →
          </Button>
        )}

        <div className="mt-6">
          <Controller
            name="usePersonalDataOnBilling"
            control={form.control}
            render={({ field }) => (
              <Checkbox
                label="Use personal data info on Billing Information"
                checked={field.value}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                  handleOnChangeCustomBilling(e.target.checked);
                  field.onChange(e);
                }}
                disabled={!isEditing}
              />
            )}
          />
        </div>

        {!usePersonalDataOnBilling && (
          <div className="w-full">
            <div className="flex items-center pt-5">
              <h1 className="text-grey-blue font-bold text-2xl  ">Billing Information</h1>
              <TooltipIcon info="Billing Information" />
            </div>
            <p className="text-tonal-dark-cream-50 text-sm py-5 font-light">*Mandatory Fields</p>
            <div className="flex flex-col gap-y-3 ">
              <div className="grid md:grid-cols-2 w-full gap-6">
                <Input
                  label="Full Name *"
                  placeholder="Full Name"
                  errorMessage={errors.billing?.fullName && errors.billing?.fullName.message}
                  variant={errors.billing?.fullName && "error"}
                  rightIcon={<FormInputIcon control={form.control} name="billing.fullName" />}
                  {...form.register("billing.fullName")}
                  enabled={isEditing}
                />

                <div className="space-y-2">
                  <p className="text-primary">Company Country *</p>
                  <Combobox
                    items={COUNTRIES.map((c) => ({ label: c.name, value: c.name, flag_url: c.flag_url }))}
                    placeholder="Select a country"
                    emptyText="No country found"
                    searchText="Search country..."
                    value={billingSelectedCountry?.name}
                    onSelect={handleSelectBillingCountry}
                    invalid={!!errors.billing?.countryCode}
                    disabled={!isEditing}
                    renderItem={(item) => (
                      <div className="flex items-center gap-3">
                        <CountryIcon
                          data-disabled={!isEditing}
                          country={{ name: item.label, flag_url: item.flag_url }}
                          className="size-6 data-[disabled=true]:grayscale"
                        />
                        {item.label}
                      </div>
                    )}
                  />
                  {!!errors.billing?.countryCode && (
                    <div className="flex justify-start items-center mt-2.5 space-x-2 ">
                      <span slot="errorMessage" className="font-centra text-sm  text-tonal-red-40">
                        {errors.billing?.countryCode.message}
                      </span>
                    </div>
                  )}
                </div>
              </div>
              <div className="grid md:grid-cols-2 w-full gap-6">
                <Input
                  {...form.register("billing.companyName")}
                  label="Company Name"
                  placeholder="Company Name"
                  errorMessage={errors.billing?.companyName && errors.billing?.companyName.message}
                  variant={errors.billing?.companyName && "error"}
                  rightIcon={<FormInputIcon control={form.control} name="billing.companyName" />}
                  enabled={isEditing}
                />
                <Input
                  {...form.register("billing.streetAndNumber")}
                  label="Street and Number *"
                  placeholder="Street and Number"
                  errorMessage={errors.billing?.streetAndNumber && errors.billing?.streetAndNumber.message}
                  rightIcon={<FormInputIcon control={form.control} name="billing.streetAndNumber" />}
                  enabled={isEditing}
                />
              </div>
              <div className="grid md:grid-cols-2 w-full gap-6">
                <Input
                  {...form.register("billing.zipCode")}
                  label="ZIP Code *"
                  placeholder="ZIP Code"
                  errorMessage={errors.billing?.zipCode && errors.billing?.zipCode.message}
                  rightIcon={<FormInputIcon control={form.control} name="billing.zipCode" />}
                  variant={errors.billing?.zipCode && "error"}
                  enabled={isEditing}
                />
                <Input
                  {...form.register("billing.city")}
                  label="City *"
                  placeholder="City"
                  errorMessage={errors.billing?.city && errors.billing?.city.message}
                  variant={errors.billing?.city && "error"}
                  rightIcon={<FormInputIcon control={form.control} name="billing.city" />}
                  enabled={isEditing}
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {isEditing && (
        <div className="flex flex-row gap-6 justify-end w-full mt-8">
          {!isLoading && (
            <Button
              onClick={() => setIsEditing(false)}
              color="dark-blue"
              size="medium"
              variant="outlined"
              type="button"
            >
              Cancel
            </Button>
          )}
          <Button
            color="yellow"
            trailingIcon={<East />}
            size="medium"
            variant="filled"
            type="submit"
            disabled={!!Object.keys(errors).length || isLoading}
          >
            {isLoading ? "Loading..." : "Save"}
          </Button>
        </div>
      )}
    </form>
  );
}
