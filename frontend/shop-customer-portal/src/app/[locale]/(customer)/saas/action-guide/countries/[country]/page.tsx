"use client";

import { Checkbox } from "@/components/_common/checkbox";
import { CountryIcon } from "@/components/_common/country-icon";
import { StrapiBlockRenderer } from "@/components/_common/strapi-block-renderer";
import { SaasBreadcrumb } from "@/components/modules/saas/components/saas-breadcrumb";
import { SaasContainer } from "@/components/modules/saas/components/saas-container";
import { CommitmentAssessment } from "@/components/modules/saas/general/commitment-assessment";
import { useCustomer } from "@/hooks/use-customer";
import { useStrapiActionGuide } from "@/hooks/use-strapi-action-guide";
import { getCustomerCommitment } from "@/lib/api/commitment";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Clear, Edit } from "@arthursenno/lizenzero-ui-react/Icon";
import { BlocksContent } from "@strapi/blocks-react-renderer";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { BsExclamationCircleFill, BsStarFill } from "react-icons/bs";
import { CgSpinnerAlt } from "react-icons/cg";

const path = [
  {
    label: "Dashboard Action Guide",
    href: "/saas/action-guide",
  },
  {
    label: "My countries",
    href: "#",
  },
];

export default function MyCountries({ params }: { params: { country: string } }) {
  const { customer } = useCustomer();
  const { tasks, generalInformation, checkTask, modules, loading, error } = useStrapiActionGuide(params.country);

  const {
    data: commitment,
    isLoading,
    isError,
    refetch,
  } = useQuery({
    queryKey: ["my-country-customer-commitment", params],
    queryFn: async () => {
      return await getCustomerCommitment({
        country_code: params.country,
        year: new Date().getFullYear(),
        customer_email: customer?.email,
      });
    },
    enabled: !!customer,
  });

  const [selectedInfo, setSelectedInfo] = useState<number>(0);
  const [content, setContent] = useState<BlocksContent>([]);
  const [isEditCommitment, setIsEditCommitment] = useState(false);

  const actionGuideContract = customer?.contracts.find((c) => c.type === "ACTION_GUIDE");
  const commitments = commitment?.data?.[0]?.commitment;
  const packagingServices = commitment?.data?.[0]?.service_setup?.packaging_services;
  const packagingServicesExist = packagingServices && !!packagingServices?.length;
  const isRequired = packagingServices?.some((item) => item.obliged);

  const handleSelectInfo = (id: number) => {
    const info = generalInformation?.find((info) => info.id === id);
    setSelectedInfo(info!.id);
    setContent(info!.content);
  };

  const handleCheckTask = (id: string) => {
    checkTask(id);
  };

  useEffect(() => {
    const firstInfo = generalInformation?.[0];
    if (firstInfo) {
      setSelectedInfo(firstInfo.id);
      setContent(firstInfo.content);
    }
  }, [generalInformation]);

  useEffect(() => {
    if (error) {
      console.error(error);
    }
  }, [error]);

  if (!actionGuideContract) return null;

  const actionGuide = actionGuideContract.action_guides.find((ag) => ag.country_code === params.country);

  if (!actionGuide) return null;

  if (loading)
    return (
      <div className="w-full h-screen flex justify-center items-center">
        <CgSpinnerAlt size={32} className="animate-spin text-primary" />
      </div>
    );

  return (
    <>
      <SaasBreadcrumb paths={path} />
      <div id="country-header" className="pb-14">
        <SaasContainer className="flex flex-col gap-10">
          <div className="flex flex-row justify-between items-center">
            <div className="flex flex-row gap-4 items-center">
              <CountryIcon
                country={{ name: actionGuide.country_name, flag_url: actionGuide.country_flag }}
                className="size-12"
              />
              <p className="text-[40px] text-primary font-bold mt-2">{actionGuide.country_name}</p>
            </div>

            {tasks?.find((task) => task.done === false) && (
              <div className="flex flex-row gap-2 items-center text-error">
                <BsExclamationCircleFill />
                <span> Open to-dos</span>
              </div>
            )}
          </div>

          <div className="flex flex-row justify-between items-center">
            <div className="flex flex-col gap-2">
              <span className="text-primary">Commitment Assessment</span>
              <Licensing
                is_required={isRequired}
                isLoading={isLoading}
                packaging_services_exist={packagingServicesExist}
              />
            </div>

            <div className="flex flex-col gap-2">
              <span className="text-primary">Packaging Amount</span>
              <span className="text-primary font-bold text-2xl">100.00 kg</span>
            </div>

            <div className="flex flex-col gap-2">
              <span className="text-primary">Expected costs</span>
              <span className="text-primary font-bold text-2xl">200 - 500 €</span>
            </div>

            <div className="flex flex-col gap-2">
              <span className="text-primary">Last info update</span>
              <span className="text-primary font-bold text-2xl">23.01.23</span>
            </div>
          </div>
        </SaasContainer>
      </div>

      {generalInformation.length > 0 && (
        <div id="general-information" className="bg-white py-14">
          <SaasContainer className="flex flex-col gap-10">
            <p className="text-[40px] text-primary font-bold mt-2">General Information</p>

            <div className="flex flex-row flex-wrap gap-4">
              {generalInformation?.map((info) => (
                <div
                  key={`general-info-${info.id}`}
                  onClick={() => handleSelectInfo(info.id)}
                  className={`flex flex-row gap-2 rounded-2xl px-4 py-2 items-center cursor-pointer ${
                    selectedInfo === info.id ? "bg-primary" : "bg-secondary"
                  }`}
                >
                  {mountIcon(info.icon, selectedInfo === info.id)}
                  <p className={selectedInfo === info.id ? "text-white" : "text-primary"}>{info.label}</p>
                </div>
              ))}
            </div>

            <div>
              <StrapiBlockRenderer content={content} />
            </div>
          </SaasContainer>
        </div>
      )}

      <div className="bg-surface-03 py-14 h-full">
        <SaasContainer>
          <div id="commitment-assessment" className="flex flex-col gap-4 bg-white rounded-2xl p-8 mb-6">
            <div className="flex flex-row justify-between items-center">
              <div className="flex justify-between items-center gap-4">
                <p className="text-2xl text-primary font-bold mt-2">Commitment Assessment</p>

                {!isEditCommitment && (
                  <Button color="light-blue" variant="text" size="small" onClick={() => setIsEditCommitment(true)}>
                    <Edit className="fill-support-blue size-5" />
                  </Button>
                )}
              </div>
              {isEditCommitment ? (
                <Button color="light-blue" variant="text" size="small" onClick={() => setIsEditCommitment(false)}>
                  <Clear className="fill-primary size-6" />
                </Button>
              ) : (
                <Licensing
                  is_required={isRequired}
                  isLoading={isLoading}
                  packaging_services_exist={packagingServicesExist}
                />
              )}
            </div>
            {isEditCommitment ? (
              <div>
                <p className="text-tonal-dark-cream-20 text-sm">
                  Answer this questions to define if you are obligated to license in this country
                </p>
                <p className="text-tonal-dark-cream-20 text-sm ml-10 my-6">
                  Please enter the informatios to field the pre-Information forms
                </p>
                <CommitmentAssessment
                  countryCode={params.country}
                  commitments={commitments}
                  onSave={() => {
                    refetch();
                  }}
                  onSubmit={() => {
                    setIsEditCommitment(false);
                    refetch();
                  }}
                />
              </div>
            ) : (
              <>
                <p className="text-sm text-primary">
                  After reviewing the details that you have provided, your company is required by law to license the
                  household packaging placed on the market in Norway.
                </p>

                <p className="text-sm text-primary">
                  To meet your product responsibilities and therefore your legal obligations, you are required to
                  license your household packaging with a recycling system in Norway.
                </p>
              </>
            )}
          </div>

          <div id="modules" className="grid grid-cols-2 gap-6">
            {!!tasks.length && (
              <div className="flex flex-col gap-8 px-8 py-10 bg-white rounded-[24px]">
                <div className="flex flex-row gap-2 justify-between">
                  <p className="text-2xl text-primary font-bold">Tasks</p>
                </div>

                <div className="flex flex-col gap-3">
                  {tasks.map((task) => (
                    <Checkbox
                      key={`task-${task.id}`}
                      checked={task.done}
                      onChange={() => handleCheckTask(task.id)}
                      label={task.name}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        </SaasContainer>
      </div>
    </>
  );
}

interface LicensingProps {
  isLoading: boolean;
  is_required?: boolean;
  packaging_services_exist?: boolean;
}

function Licensing({ isLoading, is_required, packaging_services_exist }: LicensingProps) {
  if (isLoading)
    return (
      <div className="px-4 py-2 flex justify-center items-center">
        <CgSpinnerAlt size={16} className="animate-spin text-primary" />
      </div>
    );

  if (!packaging_services_exist) return <p className="text-primary font-bold">--</p>;

  if (!is_required)
    return (
      <div className="rounded-[12px] px-4 py-2 bg-tonal-dark-green-30 text-sm text-white font-bold w-fit">
        Not obligated to license
      </div>
    );

  return (
    <div className="rounded-[12px] px-4 py-2 bg-on-surface-04 text-sm text-white font-bold w-fit">
      Licensing required
    </div>
  );
}

function mountIcon(icon: string, selected: boolean) {
  switch (icon) {
    case "star":
      return <BsStarFill className={selected ? "fill-white" : "fill-primary"} />;
    default:
      return null;
  }
}
