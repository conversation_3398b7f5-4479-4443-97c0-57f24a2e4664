@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

body {
  color: rgb(var(--foreground-rgb));
  background: white;
}

::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #ede9e4;
  border-radius: 20px;
}

::-webkit-scrollbar-thumb {
  background: #002652;
  border-radius: 20px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.mapboxgl-popup-content {
  padding: 0 !important;
}

.mapboxgl-control-container {
  display: none !important;
}

.mapboxgl-map > :last-child {
  height: 0px !important;
}

.select-style {
  border: none;
  border-radius: 4px;
  overflow: hidden;
}

.select-style select {
  border: none;
  box-shadow: none;
  background: transparent;
  background-image: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

input::-ms-reveal,
input::-ms-clear {
  display: none;
}
