import { ReactNode, useEffect, useRef, useState } from "react";
import { useJourney } from "./use-journey";
import { deleteCartCookie, useShoppingCart } from "./use-shopping-cart";
import { usePathname, useRouter } from "@/i18n/navigation";
import { useQueryFilter } from "./use-query-filter";
import { LoadingScreen } from "@/components/_common/loading-screen";
import { AxiosError } from "axios";

export function usePageRules() {
  const router = useRouter();
  const pathname = usePathname();
  const firstRender = useRef(true);

  const { shoppingCart, updateCart, isUpdatingCart } = useShoppingCart();
  const journey = useJourney();

  const isInRedirect = pathname.includes("redirect");
  const isInShopInvite = pathname.includes("shop-invite");

  const { paramValues } = useQueryFilter(["edit"]);

  const isEditing = paramValues?.edit === "true";

  const [isCheckingRules, setIsCheckingRules] = useState(true);

  useEffect(() => {
    (async () => {
      if (isUpdatingCart) return;

      if (isInShopInvite) return setIsCheckingRules(false);

      if (isInRedirect) {
        if (shoppingCart.journey && shoppingCart.journey_step) {
          return journey.redirectToStep(shoppingCart.journey_step);
        }
      }

      if (!journey || !journey.currentJourney) return router.push("/auth/login");

      if (!journey.currentJourneyStep) return journey.redirectToFirstStep();

      if (journey.canSkip && !isEditing) return journey.redirectToNextStep();

      if (shoppingCart.journey_step === journey.currentJourneyStep.key) {
        if (isInRedirect) return journey.redirectToStep(journey.currentJourneyStep.key);

        setIsCheckingRules(false);
        return;
      }

      if (journey.isMissingRule) {
        if (journey.missingRules.includes("AUTHENTICATION")) return router.push("/auth/login");

        if (journey.missingRules.includes("CART_ITEMS")) return journey.redirectToFirstStep();

        if (journey.missingRules.includes("COMMITMENT")) return journey.redirectToFirstStep();

        if (journey.missingRules.includes("PASSWORD")) return journey.redirectToStep("SET_PASSWORD");

        if (journey.missingRules.includes("COMPANY_ID")) return journey.redirectToStep("COMPANY_INFORMATIONS");

        if (journey.missingRules.includes("PAYMENT_METHOD")) return journey.redirectToStep("BILLING");

        if (shoppingCart.journey_step) return journey.redirectToStep(shoppingCart.journey_step);

        return journey.redirectToFirstStep();
      }

      if (
        firstRender.current ||
        shoppingCart.journey !== journey.currentJourney.type ||
        shoppingCart.journey_step !== journey.currentJourneyStep.key
      ) {
        await updateCart({ journey: journey.currentJourney.type, journey_step: journey.currentJourneyStep.key });
      }

      firstRender.current = false;
      setIsCheckingRules(false);
    })();
  }, [shoppingCart, journey]);

  return {
    isCheckingRules,
  };
}

export function JourneyRuleContainer({ children }: { children: ReactNode }) {
  const { isCheckingRules } = usePageRules();

  if (isCheckingRules) return <LoadingScreen />;

  return children;
}
