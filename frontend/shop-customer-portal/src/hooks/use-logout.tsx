import { tokenManager } from "@/lib/next-auth/local-token-manager";
import { signOut } from "next-auth/react";
import { deleteCartCookie } from "./use-shopping-cart";

export function useLogout() {
  async function handleLogout(email?: string) {
    deleteCartCookie();
    tokenManager.setAccessToken(null);

    const url = new URL(window.location.href);
    if (email) url.searchParams.set("email", email);
    await signOut({ redirect: true, callbackUrl: url.toString() });
  }

  return { logout: handleLogout };
}
