import { InviteCode } from "@/lib/api/invite/types";
import { formatCurrency } from "@/utils/formatCurrency";
import { createColumnHelper } from "@tanstack/react-table";
import dayjs from "dayjs";

export function useVoucherColumns() {
  const columnHelper = createColumnHelper<InviteCode[]>();

  return [
    columnHelper.accessor("label", {
      header: "Code",
      cell: (info) => info.getValue(),
    }),
    columnHelper.accessor("value", {
      header: "Ammount",
      cell: (info) => formatCurrency(info.getValue<number>()),
    }),
    columnHelper.accessor("useDate", {
      header: "Used On",
      cell: (info) => (info.getValue() ? dayjs(info.getValue<string>()).format("DD.MM.YY") : "-"),
    }),
    columnHelper.accessor("isActive", {
      header: "Order Number",
      cell: (info) => (info.getValue() ? "Active" : "Used"),
    }),
  ];
}
