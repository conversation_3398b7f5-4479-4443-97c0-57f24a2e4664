import { InviteComission } from "@/lib/api/invite/types";
import { formatCurrency } from "@/utils/formatCurrency";
import { createColumnHelper } from "@tanstack/react-table";
import dayjs from "dayjs";

export function useComissionColumns() {
  const columnHelper = createColumnHelper<InviteComission[]>();

  return [
    columnHelper.accessor("comission_date", {
      header: "Comission Date",
      cell: (info) => dayjs(info.getValue<string>()).format("DD.MM.YY"),
    }),
    columnHelper.accessor("product", {
      header: "Product",
      cell: (info) => info.getValue(),
    }),
    columnHelper.accessor("commission", {
      header: "Comission",
      cell: (info) => formatCurrency(info.getValue<number>()),
    }),
    columnHelper.accessor("order_id", {
      header: "Order Number",
      cell: (info) => info.getValue(),
    }),
    columnHelper.accessor("lead_source", {
      header: "Lead Source",
      cell: (info) => info.getValue(),
    }),
  ];
}
