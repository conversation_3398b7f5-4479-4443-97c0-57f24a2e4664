"use client";

import { ReactNode, createContext, useContext, useState } from "react";

interface SidebarContextProps {
  open: boolean;
  setOpen: (v: boolean) => void;
}
const SidebarContext = createContext<SidebarContextProps>({} as SidebarContextProps);

export const useSidebar = () => useContext(SidebarContext);

export const SidebarProvider = ({ children }: { children: ReactNode }) => {
  const [open, setOpen] = useState(false);

  return <SidebarContext.Provider value={{ open, setOpen }}>{children}</SidebarContext.Provider>;
};
