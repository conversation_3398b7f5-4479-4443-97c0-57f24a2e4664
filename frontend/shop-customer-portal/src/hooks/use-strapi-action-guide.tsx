import { strapi } from "@/lib/strapi/strapi";
import { StrapiActionGuide, StrapiGeneralInformation } from "@/lib/strapi/types";
import { useEffect, useState } from "react";

export interface ActionGuideTask {
  id: string;
  name: string;
  done: boolean;
}

export const useStrapiActionGuide = (countryCode: string) => {
  const [data, setData] = useState<StrapiActionGuide | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>();
  const [tasks, setTasks] = useState<ActionGuideTask[]>([]);
  const [generalInformation, setGeneralInformation] = useState<StrapiGeneralInformation[]>([]);
  const [modules, setModules] = useState<any[]>([]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const res = await strapi.getActionGuideByCountryCode(countryCode);

      if (!res) throw new Error("Failed to fetch action guide from Strapi");

      setData(res);

      const taskItems = res.attributes.modules
        .find((module) => module.__component === "action-guide.tasks")
        .TaskItem.reduce((acc: ActionGuideTask[], item: any) => {
          acc.push({
            id: item.id,
            name: item.name,
            done: false,
          });
          return acc;
        }, []);

      const generalInformationItems = res.attributes.generalInformation;

      setTasks(taskItems);
      setGeneralInformation(generalInformationItems);
      setModules(res.attributes.modules);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const checkTask = (id: string) => {
    setTasks((prevTasks) => prevTasks.map((task) => (task.id === id ? { ...task, done: !task.done } : task)));
  };

  useEffect(() => {
    fetchData();
  }, []);

  return {
    actionGuideDetails: data,
    tasks,
    checkTask,
    generalInformation,
    modules,
    loading,
    error,
  };
};
