import { getCustomerByUserId } from "@/lib/api/customer";
import { queryClient } from "@/lib/react-query";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";

export function useCustomer() {
  const session = useSession();

  const user = session.data?.user;

  const customerQuery = useQuery({
    queryKey: ["customer", user?.id],
    queryFn: async () => {
      const foundCustomer = await getCustomerByUserId(Number(user!.id));

      if (!foundCustomer) return null;

      const { companies, ...customer } = foundCustomer;

      return {
        ...customer,
        company: companies[0] || null,
        hasActiveContract: customer.contracts.some((contract) => contract.status === "ACTIVE"),
      };
    },
    enabled: !!user?.id,
  });

  function invalidateCustomer() {
    queryClient.invalidateQueries({ queryKey: ["customer", user?.id] });
  }

  const customer = customerQuery.data || null;

  return {
    customer,
    invalidateCustomer,
    isLoading: customerQuery.isLoading,
  };
}
