import { getAccessed<PERSON><PERSON><PERSON>, useShoppingCart } from "./use-shopping-cart";

import { useCustomer } from "@/hooks/use-customer";
import { usePathname, useRouter } from "@/i18n/navigation";
import { JOURNEY_STEPS, JourneyRule, JOURNEYS, JourneyStep, JourneyType } from "@/utils/journeys";
import { useSession } from "next-auth/react";

export function useJourney() {
  const router = useRouter();
  const pathname = usePathname();

  const session = useSession();
  const { customer } = useCustomer();
  const { shoppingCart } = useShoppingCart();

  const customerCommitments = shoppingCart.customer_commitments;

  const customerRules = (() => {
    const currentRules: JourneyRule[] = [];

    if (session.status === "authenticated") currentRules.push("AUTHENTICATION");
    if (session.data?.user.has_password) currentRules.push("PASSWORD");
    if (!!shoppingCart?.items.length) currentRules.push("CART_ITEMS");
    if (
      !!shoppingCart?.items.length &&
      shoppingCart.items
        .filter((item) => item.service_type === "EU_LICENSE")
        .every((item) => !!customerCommitments.find((c) => c.country_code === item.country_code))
    ) {
      currentRules.push("COMMITMENT");
    }
    if (customer?.id) currentRules.push("CUSTOMER_ID");
    if (customer?.company?.id) currentRules.push("COMPANY_ID");
    if (shoppingCart?.payment?.payment_method_type) {
      const paymentMethodId = shoppingCart.payment.payment_method_id;
      const paymentMethodType = shoppingCart.payment.payment_method_type;

      if (paymentMethodType === "CREDIT_CARD") {
        if (paymentMethodId) currentRules.push("PAYMENT_METHOD");
      } else {
        currentRules.push("PAYMENT_METHOD");
      }
    }

    return currentRules;
  })();
  const isInRedirect = pathname.includes("/redirect");

  const journey = (() => {
    const accessedJourneyType = getAccessedJourney(pathname);

    if (isInRedirect) {
      if (!shoppingCart?.journey) return null;

      const journey = JOURNEYS[shoppingCart?.journey as JourneyType];

      if (!journey) return null;

      return journey;
    }

    if (!accessedJourneyType) return null;

    const journey = JOURNEYS[accessedJourneyType];

    if (!journey) return null;

    return journey;
  })();

  const journeyStep = (() => {
    if (!journey) return null;

    if (isInRedirect) {
      if (!shoppingCart?.journey_step) return null;

      const journeyStep =
        JOURNEYS[shoppingCart?.journey as JourneyType].steps[shoppingCart?.journey_step as JourneyStep];

      if (!journeyStep) return null;

      return { ...journeyStep, key: shoppingCart?.journey_step as JourneyStep };
    }

    let journeyStepKey: JourneyStep | null = null;

    for (const step of Object.keys(journey.steps) as JourneyStep[]) {
      if (`${journey.basePath}${JOURNEY_STEPS[step]}` === pathname) {
        journeyStepKey = step;
        break;
      }
    }

    if (!journeyStepKey) return null;

    const journeyStep = journey.steps[journeyStepKey as JourneyStep];

    return { ...journeyStep, key: journeyStepKey };
  })();

  const missingRules = journeyStep ? getMissingRules(journeyStep.key, customerRules) : [];
  const isMissingRule = !!missingRules.length;

  const canSkip =
    !isMissingRule &&
    !!journeyStep?.skip_rules?.length &&
    !!(journeyStep?.skip_rules || []).some((rule) => customerRules.includes(rule));

  const nextStep = journeyStep ? getNextStep(journeyStep.key) : null;
  const previousStep = journeyStep ? getPreviousStep() : null;

  function getMissingRules(step: JourneyStep, rules: JourneyRule[]) {
    if (!journey) return [];

    const journeyStep = journey.steps[step];

    const journeyStepRules = journeyStep?.rules || [];

    const missingRules = journeyStepRules.filter((rule) => !rules.includes(rule));

    return missingRules;
  }

  function redirectToFirstStep() {
    if (!journey) return;
    const journeyFirstStep = Object.keys(journey.steps)[0] as JourneyStep;

    router.push(`${journey.basePath}${JOURNEY_STEPS[journeyFirstStep]}`);
  }

  function redirectToPreviousStep() {
    if (!journey) return;
    if (!journeyStep) return;

    const journeySteps = Object.keys(journey.steps) as JourneyStep[];

    const currentStepIndex = journeySteps.indexOf(journeyStep.key);

    if (currentStepIndex === 0) return;

    const previousStep = journeySteps[currentStepIndex - 1];

    if (!previousStep) return;

    router.push(`${journey.basePath}${JOURNEY_STEPS[previousStep]}`);
  }

  function redirectToNextStep() {
    if (!journey) return;
    if (!journeyStep) return;

    const journeySteps = Object.keys(journey.steps) as JourneyStep[];

    const currentStepIndex = journeySteps.indexOf(journeyStep.key);

    if (currentStepIndex === journeySteps.length - 1) return;

    const nextStep = journeySteps[currentStepIndex + 1];

    if (!nextStep) return;

    // TODO: Check if the next step is a skip rule

    router.push(`${journey.basePath}${JOURNEY_STEPS[nextStep]}`);
  }

  function redirectToStep(step: JourneyStep) {
    if (!journey) return;

    if (!journey.steps[step as JourneyStep]) return;

    router.push(`${journey.basePath}${JOURNEY_STEPS[step as JourneyStep]}`);
  }

  function redirectToJourney(journeyType: JourneyType, step?: JourneyStep) {
    const journey = JOURNEYS[journeyType];

    if (!journey) return;

    if (!step) {
      const journeyFirstStep = Object.keys(journey.steps)[0] as JourneyStep;

      return router.push(`${journey.basePath}${JOURNEY_STEPS[journeyFirstStep]}`, { scroll: true });
    }

    const journeyStep = journey.steps[step as JourneyStep];

    if (!journeyStep) return;

    router.push(`${journey.basePath}${JOURNEY_STEPS[step as JourneyStep]}`, { scroll: true });
  }

  function getPreviousStep() {
    if (!journey) return null;
    if (!journeyStep) return null;

    const journeySteps = Object.keys(journey.steps) as JourneyStep[];

    const currentStepIndex = journeySteps.indexOf(journeyStep.key);

    if (currentStepIndex === 0) return null;

    const previousStep = journeySteps[currentStepIndex - 1];

    if (!previousStep) return null;

    return previousStep;
  }

  function getNextStep(currentStepKey: JourneyStep | null) {
    if (!journey) return null;
    if (!currentStepKey) return null;

    const journeySteps = Object.keys(journey.steps) as JourneyStep[];

    const currentStepIndex = journeySteps.indexOf(currentStepKey);

    if (currentStepIndex === journeySteps.length - 1) return null;

    const nextStepKey = journeySteps[currentStepIndex + 1];

    if (!nextStepKey) return null;

    const nextJourneyStep = journey.steps[nextStepKey];

    if (!nextJourneyStep) return null;

    const canSkipNextJourneyStep =
      !!nextJourneyStep.skip_rules?.length &&
      !!(nextJourneyStep.skip_rules || []).some((rule) => customerRules.includes(rule));

    if (!canSkipNextJourneyStep)
      return {
        nextStep: nextStepKey,
        nextStepMissingRules: getMissingRules(nextStepKey, customerRules),
      };

    return getNextStep(nextStepKey);
  }

  return {
    currentJourney: journey,
    currentJourneyStep: journeyStep,
    customerRules,
    missingRules,
    isMissingRule,
    canSkip,
    previousStep,
    nextStep: nextStep?.nextStep || null,
    redirectToFirstStep,
    redirectToPreviousStep,
    redirectToNextStep,
    redirectToStep,
    redirectToJourney,
  };
}
