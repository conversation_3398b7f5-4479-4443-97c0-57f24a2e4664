import { ContractType } from "@/lib/api/contracts/types";
import { PriceList } from "@/lib/api/shoppingCart/types";
import { useQuery } from "@tanstack/react-query";

interface UsePricingProps {
  serviceType: ContractType;
  year?: number;
  countryCode?: string;
}

export function usePricing({ serviceType, year, countryCode }: UsePricingProps) {
  const { data: priceLists, isLoading } = useQuery({
    queryKey: ["prices", serviceType, year, countryCode],
    queryFn: async () => {
      const response = await fetch(`/api/prices?service-type=${serviceType}&year=${year}&country-code=${countryCode}`);

      if (!response.ok) throw new Error();

      const data = await response.json();

      return data as PriceList[];
    },
  });

  return { priceLists, isLoading };
}
