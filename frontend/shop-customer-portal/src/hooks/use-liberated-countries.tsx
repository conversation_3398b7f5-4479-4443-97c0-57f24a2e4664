import { api } from "@/lib/api";
import { Country } from "@/lib/api/country/types";
import { useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";

// TODO: i18n
export function useLiberatedCountries() {
  const t = useTranslations("shop.common.liberatedCountries");
  const {
    data: liberatedCountries,
    isLoading: loading,
    error,
  } = useQuery({
    queryKey: ["liberated-countries"],
    queryFn: getLiberatedCountries,
  });

  async function getLiberatedCountries() {
    const { data, status } = await api.get("/admin/countries/published");

    if (status >= 400) throw { message: t("error") };

    const countries = data as Country[];

    if (!countries) throw t("error");

    return countries;
  }

  return {
    liberatedCountries: liberatedCountries || [],
    loading,
    error: error?.message || t("error"),
  };
}
