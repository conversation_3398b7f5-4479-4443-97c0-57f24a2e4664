import { getPartnerByUserId } from "@/lib/api/partner";
import { useQuery } from "@tanstack/react-query";
import { useSession } from "next-auth/react";

export function usePartner() {
  const session = useSession();

  const user = session.data?.user;

  const partnerQuery = useQuery({
    queryKey: ["partner", user?.id],
    queryFn: () => getPartnerByUserId(user!.id),
    enabled: !!user?.id,
  });

  return {
    partner: partnerQuery.data || null,
    isLoading: partnerQuery.isLoading,
  };
}
