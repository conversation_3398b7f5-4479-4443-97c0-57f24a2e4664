"use client";

import { cn } from "@/lib/utils";
import { Check } from "@arthursenno/lizenzero-ui-react/Icon";
import { CardCvcElement } from "@stripe/react-stripe-js";
import { StripeCardCvcElementChangeEvent } from "@stripe/stripe-js";
import { useTranslations } from "next-intl";
import { useState } from "react";

interface CardCvcProps {
  isValid?: boolean;
}

export function CardCvcInput({ isValid }: CardCvcProps) {
  const [isComplete, setIsComplete] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const t = useTranslations("shop.common.journey.billing.cvc");

  const handleCVCChange = (event: StripeCardCvcElementChangeEvent) => {
    setIsComplete(event.complete);

    if (event?.error) {
      setErrorMessage(t("invalid"));
      return;
    }

    setErrorMessage("");
  };

  const isCvcValid = isValid !== undefined ? isValid : isComplete;

  return (
    <div className="flex flex-col w-full">
      <label className="text-primary text-base font-centra mb-2">{t("label")}</label>
      <div>
        <div className="relative">
          <CardCvcElement
            options={{
              placeholder: "000",
            }}
            onChange={handleCVCChange}
            className={cn("block w-full border rounded-2xl p-4 text-tonal-dark-cream-10 focus:outline-primary", {
              "pr-10 bg-background placeholder-tonal-dark-cream-60 border-tonal-dark-cream-80": !errorMessage,
              "bg-tonal-red-90 placeholder-total-dark-cream-10 border-on-error-container": !!errorMessage,
            })}
          />

          {isCvcValid && <Check width={20} height={20} className="fill-tonal-green-40 absolute top-4 right-4" />}
        </div>
        {errorMessage && (
          <div className="flex justify-start items-center mt-2.5 space-x-2 ">
            <span slot="errorMessage" className={`font-centra text-sm  text-tonal-red-40`}>
              {errorMessage as string}
            </span>
          </div>
        )}
      </div>
    </div>
  );
}
