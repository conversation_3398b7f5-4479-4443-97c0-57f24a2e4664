"use client";

import { cn } from "@/lib/utils";
import { Check } from "@arthursenno/lizenzero-ui-react/Icon";
import { CardNumberElement } from "@stripe/react-stripe-js";
import { StripeCardNumberElementChangeEvent } from "@stripe/stripe-js";
import { useTranslations } from "next-intl";
import { useState } from "react";

interface CardNumberProps {
  isValid?: boolean;
}

export function CardNumberInput({ isValid }: CardNumberProps) {
  const [isComplete, setIsComplete] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const t = useTranslations("shop.common.journey.billing.cardNumber");

  function handleCardNumberChange(event: StripeCardNumberElementChangeEvent) {
    setIsComplete(event.complete);

    if (event?.error) {
      setErrorMessage(t("invalid"));
      return;
    }

    setErrorMessage("");
  }

  const isCardNumberValid = isValid !== undefined ? isValid : isComplete;

  return (
    <div className="flex flex-col w-full">
      <label className="text-primary text-base font-centra mb-2">{t("label")}</label>
      <div>
        <div className="relative">
          <CardNumberElement
            options={{
              showIcon: true,
              iconStyle: "default",
              placeholder: "9999 9999 9999 9999",
              style: {
                base: {
                  "::placeholder": {
                    color: "#6c757d",
                  },
                },
                invalid: {
                  color: "#dc3545",
                },
                complete: {
                  color: "#28a745",
                  iconColor: "#28a745",
                  ":-webkit-autofill": {
                    color: "#28a745",
                  },
                },
              },
            }}
            onChange={handleCardNumberChange}
            id="cardNumber"
            className={cn("pl-4 block w-full border rounded-2xl p-4 text-tonal-dark-cream-10 focus:outline-primary", {
              "pr-10 bg-background placeholder-tonal-dark-cream-60 border-tonal-dark-cream-80": !errorMessage,
              "bg-tonal-red-90 placeholder-total-dark-cream-10 border-on-error-container": !!errorMessage,
            })}
          />
          {isCardNumberValid && <Check width={20} height={20} className="fill-tonal-green-40 absolute top-4 right-4" />}
        </div>
        {errorMessage && (
          <div className="flex justify-start items-center mt-2.5 space-x-2 ">
            <span slot="errorMessage" className={`font-centra text-sm  text-tonal-red-40`}>
              {errorMessage}
            </span>
          </div>
        )}
      </div>
    </div>
  );
}
