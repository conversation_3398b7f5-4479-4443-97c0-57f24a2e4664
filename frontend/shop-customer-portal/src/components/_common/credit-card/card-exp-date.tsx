"use client";

import { cn } from "@/lib/utils";
import { CalendarToday, Check, CheckCircle } from "@arthursenno/lizenzero-ui-react/Icon";
import { InputProps } from "@arthursenno/lizenzero-ui-react/Input/Input";
import { CardExpiryElement } from "@stripe/react-stripe-js";
import { StripeCardExpiryElementChangeEvent } from "@stripe/stripe-js";
import { useTranslations } from "next-intl";
import { ForwardRefRenderFunction, forwardRef, useState } from "react";
import { Control, Controller, FieldValues } from "react-hook-form";

interface CardExpDateProps {
  isValid?: boolean;
}

const typeInputlasses: { error: string; enabled: string } = {
  enabled: "bg-background placeholder-tonal-dark-cream-60 border-tonal-dark-cream-80",

  error: "bg-tonal-red-90 placeholder-total-dark-cream-10 border-on-error-container",
};

export function CardExpDateInput({ isValid }: CardExpDateProps) {
  const [isComplete, setIsComplete] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const t = useTranslations("shop.common.journey.billing.expirationDate");

  const handleExpiryChange = (event: StripeCardExpiryElementChangeEvent) => {
    setIsComplete(event.complete);

    if (event?.error) {
      setErrorMessage(t("pastDate"));
      return;
    }

    setErrorMessage("");
  };

  const isExpiryValid = isValid !== undefined ? isValid : isComplete;

  return (
    <div className="flex flex-col w-full">
      <label className="text-primary text-base font-centra mb-2 text-nowrap">{t("label")}</label>
      <div>
        <div className="relative">
          <CardExpiryElement
            options={{ placeholder: t("placeholder") }}
            onChange={handleExpiryChange}
            className={cn("block w-full border rounded-2xl p-4 text-tonal-dark-cream-10 focus:outline-primary", {
              "pr-10 bg-background placeholder-tonal-dark-cream-60 border-tonal-dark-cream-80": !errorMessage,
              "bg-tonal-red-90 placeholder-total-dark-cream-10 border-on-error-container": !!errorMessage,
            })}
          />

          {isExpiryValid && <Check width={20} height={20} className="fill-tonal-green-40 absolute top-4 right-4" />}
        </div>
        {errorMessage && (
          <div className="flex justify-start items-center mt-2.5 space-x-2 ">
            <span slot="errorMessage" className={`font-centra text-sm  text-tonal-red-40`}>
              {errorMessage as string}
            </span>
          </div>
        )}
      </div>
    </div>
  );
}
