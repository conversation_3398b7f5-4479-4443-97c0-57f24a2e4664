import Image from "next/image";
import { ComponentProps } from "react";

import { cn } from "@/lib/utils";
import { BaseCountry } from "@/lib/api/country/types";
interface CountryIconProps extends ComponentProps<"div"> {
  country: Omit<BaseCountry, "id" | "code">;
}

export function CountryIcon({ country, ...props }: CountryIconProps) {
  return (
    <div
      data-tag="country-icon"
      {...props}
      className={cn("size-6 rounded-full overflow-hidden flex-none", props.className)}
    >
      <Image
        className="w-full h-full object-cover"
        src={country.flag_url}
        alt={`${country.name} flag`}
        width={240}
        height={240}
      />
    </div>
  );
}
