import { cn } from "@/lib/utils";
import { CONTAINS_LETTER_REGEX, CONTAINS_NUMBER_REGEX, CONTAINS_NON_ALPHANUMERIC_REGEX } from "@/utils/regex";

interface PasswordStrengthBarProps {
  password?: string | null;
  showRequirements?: boolean;
  className?: string;
}

interface PasswordRequirement {
  label: string;
  regex: RegExp;
  met: boolean;
}

export function PasswordStrengthBar({ password, className, showRequirements = false }: PasswordStrengthBarProps) {
  if (!password) return null;

  const requirements: PasswordRequirement[] = [
    {
      label: "At least 6 characters",
      regex: /.{6,}/,
      met: password.length >= 6,
    },
    {
      label: "At least one letter",
      regex: CONTAINS_LETTER_REGEX,
      met: CONTAINS_LETTER_REGEX.test(password),
    },
    {
      label: "At least one number",
      regex: CONTAINS_NUMBER_REGEX,
      met: CONTAINS_NUMBER_REGEX.test(password),
    },
    {
      label: "At least one special character",
      regex: CONTAINS_NON_ALPHANUMERIC_REGEX,
      met: CONTAINS_NON_ALPHANUMERIC_REGEX.test(password),
    },
  ];

  const metRequirements = requirements.filter((req) => req.met).length;
  const totalRequirements = requirements.length;
  const strengthPercentage = (metRequirements / totalRequirements) * 100;

  function getStrengthColor(type: "TEXT" | "BACKGROUND") {
    if (strengthPercentage === 100) return type === "TEXT" ? "text-success" : "bg-success";
    if (strengthPercentage >= 75) return type === "TEXT" ? "text-tonal-blue-40" : "bg-tonal-blue-40";
    if (strengthPercentage >= 50) return type === "TEXT" ? "text-tonal-yellow-40" : "bg-tonal-yellow-40";
    return type === "TEXT" ? "text-error" : "bg-error";
  }

  function getStrengthText() {
    if (strengthPercentage === 100) return "Strong";
    if (strengthPercentage >= 75) return "Good";
    if (strengthPercentage >= 50) return "Fair";
    return "Weak";
  }

  return (
    <div className={cn("space-y-2", className)}>
      <div className="text-tonal-dark-cream-30 text-sm">
        Password strength: <span className={getStrengthColor("TEXT")}>{getStrengthText()}</span>
      </div>
      <div className="w-full bg-tonal-dark-cream-80 rounded-full h-2">
        <div
          className={cn("h-2 rounded-full transition-all duration-300", getStrengthColor("BACKGROUND"))}
          style={{ width: `${strengthPercentage}%` }}
        />
      </div>
      {showRequirements && (
        <div className="space-y-1">
          {requirements.map((requirement, index) => (
            <div key={index} className="flex items-center gap-2 text-xs">
              <div
                className={cn("size-2 rounded-full", requirement.met ? "bg-tonal-green-40" : "bg-tonal-dark-cream-70")}
              />
              <span className={requirement.met ? "text-success" : "text-tonal-dark-cream-70"}>{requirement.label}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
