"use client";

import { NotificationAdd } from "@arthursenno/lizenzero-ui-react/Icon";
import * as DropdownMenu from "@radix-ui/react-dropdown-menu";

export function NotificationsDropdown() {
  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger className="" asChild>
        <div className="flex items-center gap-1 cursor-pointer">
          <NotificationAdd width={32} className="fill-primary hover:fill-support-blue cursor-pointer" />
        </div>
      </DropdownMenu.Trigger>

      <DropdownMenu.Portal>
        <DropdownMenu.Content
          className="w-[310px] bg-background py-3 rounded-2xl focus:outline-none z-[999999]"
          style={{ boxShadow: "0px 2px 3px 0px rgba(0,0,0,0.3)" }}
          sideOffset={5}
          align="end"
        >
          <h1 className="text-center text-primary px-4 py-6">Empty notifications!</h1>
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
}
