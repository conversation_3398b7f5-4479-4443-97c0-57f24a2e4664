import { useSidebar } from "@/hooks/use-sidebar";

export function SidebarTrigger() {
  const sidebar = useSidebar();

  if (sidebar.open) return null;

  return (
    <button
      className="flex min-[1024px]:hidden group size-6 flex-col justify-center items-center gap-1 border-none cursor-pointer"
      onClick={() => sidebar.setOpen(true)}
      aria-label="Open sidebar"
      title="Open sidebar"
    >
      <div className="h-[3px] w-full bg-primary rounded-full group-hover:bg-support-blue"></div>
      <div className="h-[3px] w-full bg-primary rounded-full group-hover:bg-support-blue"></div>
      <div className="h-[3px] w-full bg-primary rounded-full group-hover:bg-support-blue"></div>
    </button>
  );
}
