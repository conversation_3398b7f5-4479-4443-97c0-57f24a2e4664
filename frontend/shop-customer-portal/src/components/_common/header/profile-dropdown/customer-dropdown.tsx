"use client";

import { SaasSidebarDivider } from "@/components/modules/saas/components/saas-sidebar/saas-sidebar-divider";
import { useLogout } from "@/hooks/use-logout";
import { useCustomer } from "@/hooks/use-customer";
import { KeyboardArrowDown, Logout, PersonAddAlt } from "@arthursenno/lizenzero-ui-react/Icon";
import * as HoverCard from "@radix-ui/react-hover-card";
import { useSession } from "next-auth/react";
import { usePathname, useRouter } from "@/i18n/navigation";

import { CountryIcon } from "../../country-icon";
import { cn } from "@/lib/utils";
import { FaUser } from "react-icons/fa6";

export function CustomerProfileDropdown() {
  const session = useSession();
  const pathname = usePathname();

  const isSaas = pathname.includes("/saas");

  const router = useRouter();

  const { logout } = useLogout();

  const { customer } = useCustomer();

  function handleNavigateToMyAccount() {
    router.push("/saas/my-account");
  }

  if (!session.data || !customer) return null;

  const euLicenseContract = customer?.contracts?.find((contract) => contract.type === "EU_LICENSE") || null;

  return (
    <HoverCard.Root openDelay={100} closeDelay={100}>
      <HoverCard.Trigger className="" asChild>
        <div
          className={cn("flex items-center gap-1 cursor-pointer", {
            "max-sm:hidden": isSaas,
          })}
        >
          <div className="flex items-center justify-center size-10 rounded-full bg-secondary">
            <span className="text-primary font-medium text-md">
              {(customer.first_name[0] || "").toLocaleUpperCase()}
              {(customer.last_name[0] || "").toLocaleUpperCase()}
            </span>
          </div>
          <KeyboardArrowDown className="hidden lg:block w-[24px] fill-primary" />
        </div>
      </HoverCard.Trigger>

      <HoverCard.Portal>
        <HoverCard.Content
          className="w-[310px] bg-background p-6 rounded-2xl focus:outline-none z-[999999]"
          style={{ boxShadow: "0px 2px 3px 0px rgba(0,0,0,0.3)" }}
          sideOffset={5}
          align="end"
        >
          <div className="space-y-3">
            <div className="space-y-1">
              <p className="text-tonal-dark-cream-10 font-bold">
                {customer.first_name} {customer.last_name}
              </p>
              <p className="text-sm text-tonal-dark-cream-30 w-full overflow-hidden text-ellipsis whitespace-nowrap">
                {customer.email}
              </p>
            </div>

            {customer && (
              <p className="text-md font-bold text-tonal-dark-cream-30">
                {customer.company?.name || customer.company_name}
              </p>
            )}

            {!!customer.contracts.length && (
              <p
                onClick={handleNavigateToMyAccount}
                className="text-md font-bold text-support-blue block hover:underline underline-offset-2 cursor-pointer"
              >
                Edit profile
              </p>
            )}
          </div>

          {!!customer?.contracts?.length && (
            <div>
              <SaasSidebarDivider className="my-5" />

              <div className="space-y-5 text-tonal-dark-cream-30">
                <div className="flex items-center justify-between">
                  <p className="text-md">Contract Since</p>
                  <span className="text-md font-bold">
                    {new Date(customer.contracts[0].start_date!).toLocaleDateString()}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <p className="text-md">Contract Until</p>
                  <span className="text-md font-bold">
                    {new Date(customer.contracts[0].end_date!).toLocaleDateString()}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <p className="text-md">Last possible date of termination</p>
                  <span className="text-md font-bold ml-2 text-nowrap">---</span>
                </div>
              </div>

              <SaasSidebarDivider className="my-5" />

              {!!euLicenseContract && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-tonal-dark-cream-30">
                    <p className="text-md">Countries Licensed</p>
                    <span className="text-md font-bold">{euLicenseContract.licenses.length}</span>
                  </div>

                  <div className="flex items-center gap-2">
                    {euLicenseContract.licenses.map((license) => (
                      <CountryIcon
                        key={license.country_code}
                        country={{ flag_url: license.country_flag, name: license.country_name }}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          <SaasSidebarDivider className="my-5" />

          <div onClick={() => logout()} className="group flex items-center gap-2 cursor-pointer">
            <Logout width={24} className="fill-primary" />
            <p className="text-primary font-bold group-hover:underline underline-offset-2">Logout</p>
          </div>
        </HoverCard.Content>
      </HoverCard.Portal>
    </HoverCard.Root>
  );
}
