"use client";

import { SaasSidebarDivider } from "@/components/modules/saas/components/saas-sidebar/saas-sidebar-divider";
import { useLogout } from "@/hooks/use-logout";
import { usePartner } from "@/hooks/use-partner";
import { useRouter } from "@/i18n/navigation";
import { KeyboardArrowDown, Logout } from "@arthursenno/lizenzero-ui-react/Icon";
import * as HoverCard from "@radix-ui/react-hover-card";
import { useSession } from "next-auth/react";

export function PartnerProfileDropdown() {
  const session = useSession();
  const user = session.data?.user;
  const { partner, isLoading } = usePartner();

  const router = useRouter();

  const { logout } = useLogout();

  function handleNavigateToMyAccount() {
    router.push("/partner-hub/my-account");
  }

  if (session.status === "loading" || isLoading) return null;

  const firstName = partner?.first_name || user?.first_name || "";
  const lastName = partner?.last_name || user?.last_name || "";
  const email = partner?.email || user?.email || "";

  return (
    <HoverCard.Root openDelay={100} closeDelay={100}>
      <HoverCard.Trigger className="" asChild>
        <div className="hidden lg:flex items-center gap-1 cursor-pointer">
          <div className="w-10 h-10 rounded-full bg-secondary flex items-center justify-center">
            <span className="text-primary font-medium text-xl">
              {(firstName[0] || "").toLocaleUpperCase()}
              {(lastName[0] || "").toLocaleUpperCase()}
            </span>
          </div>
          <KeyboardArrowDown className="w-[24px] fill-primary" />
        </div>
      </HoverCard.Trigger>

      <HoverCard.Portal>
        <HoverCard.Content
          className="w-[310px] bg-background p-6 rounded-2xl focus:outline-none z-[999999]"
          style={{ boxShadow: "0px 2px 3px 0px rgba(0,0,0,0.3)" }}
          sideOffset={5}
          align="end"
        >
          <div className="space-y-3">
            <div className="space-y-1">
              <p className="text-tonal-dark-cream-10 font-bold">
                {firstName} {lastName}
              </p>
              <p className="text-sm text-tonal-dark-cream-30 w-full overflow-hidden text-ellipsis whitespace-nowrap">
                {email}
              </p>
            </div>

            {partner?.companies[0]?.name && (
              <p className="text-md font-bold text-tonal-dark-cream-30">{partner.companies[0].name}</p>
            )}

            {!!partner && (
              <p
                onClick={handleNavigateToMyAccount}
                className="text-md font-bold text-support-blue block hover:underline underline-offset-2 cursor-pointer"
              >
                Edit profile
              </p>
            )}
          </div>

          <SaasSidebarDivider className="my-5" />

          <div onClick={() => logout()} className="group flex items-center gap-2 cursor-pointer">
            <Logout width={24} className="fill-primary" />
            <p className="text-primary font-bold group-hover:underline underline-offset-2">Logout</p>
          </div>
        </HoverCard.Content>
      </HoverCard.Portal>
    </HoverCard.Root>
  );
}
