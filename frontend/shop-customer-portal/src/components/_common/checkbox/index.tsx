"use client";

import { FaC<PERSON>ckSquare, FaRegSquare } from "react-icons/fa";

interface CheckboxProps {
  label?: string;
  description?: string | React.ReactNode;
  checked?: boolean;
  onChange?: React.ChangeEventHandler<HTMLInputElement>;
  onClick?: React.MouseEventHandler<HTMLInputElement>;
  disabled?: boolean;
}

export function Checkbox({ label, description, checked, onChange, onClick, disabled }: CheckboxProps) {
  return (
    <div
      data-checked={checked}
      data-description={!!description}
      aria-disabled={disabled}
      className="group text-tonal-dark-cream-30 gap-2"
    >
      <label className="flex group-data-[description=false]:items-center gap-2 group-data-[description=false]:gap-4 cursor-pointer">
        <input
          type="checkbox"
          className="hidden"
          checked={checked}
          onChange={onChange}
          onClick={onClick}
          disabled={disabled}
        />
        <FaCheckSquare className="text-primary group-aria-disabled:text-tonal-dark-cream-30 group-data-[checked=true]:block hidden group-data-[description=true]:mt-1" />
        <FaRegSquare className="text-primary group-aria-disabled:text-tonal-dark-cream-30 group-data-[checked=true]:hidden block group-data-[description=true]:mt-1" />
        {label && description && (
          <p
            className="text-gray-700 font-medium *:text-tonal-dark-green-30 *:underline"
            dangerouslySetInnerHTML={{ __html: label }}
          />
        )}
        {label && !description && (
          <p
            className="text-primary *:text-tonal-dark-green-30 *:underline"
            dangerouslySetInnerHTML={{ __html: label }}
          />
        )}
      </label>
      {description && (
        <p
          className="ml-6 font-light text-sm *:text-tonal-dark-green-30 *:underline"
          dangerouslySetInnerHTML={{ __html: description }}
        />
      )}
    </div>
  );
}
