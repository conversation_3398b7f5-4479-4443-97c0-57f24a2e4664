"use client";

/* eslint-disable @typescript-eslint/no-explicit-any */
import { Help } from "@arthursenno/lizenzero-ui-react/Icon";
import * as Tooltip from "@radix-ui/react-tooltip";

interface TooltipIconProps {
  info?: string;
  sizeIcon?: number;
  [key: string]: any;
}

export const TooltipIcon = ({ info, sizeIcon = 20, ...props }: TooltipIconProps) => {
  return (
    <Tooltip.Provider delayDuration={100} skipDelayDuration={500}>
      <Tooltip.Root defaultOpen={false}>
        <Tooltip.Trigger>
          <Help width={sizeIcon} height={sizeIcon} className="fill-[#808FA9]" {...props} />
        </Tooltip.Trigger>
        <Tooltip.Portal>
          <Tooltip.Content
            className="data-[state=instant-open]:data-[side=bottom]:animate-slideDownAndFade rounded-[4px] bg-[black] px-[13px] py-[8px] text-[12px] shadow-[hsl(206_22%_7%_/_35%)_0px_10px_38px_-10px,_hsl(206_22%_7%_/_20%)_0px_10px_20px_-15px] will-change-[transform,opacity]"
            style={{ zIndex: 1000 }}
            sideOffset={5}
          >
            {info}
            <Tooltip.Arrow />
          </Tooltip.Content>
        </Tooltip.Portal>
      </Tooltip.Root>
    </Tooltip.Provider>
  );
};
