import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { InterserohLogo, Lizenzero<PERSON>ogo } from "@arthursenno/lizenzero-ui-react/Figure";
import { East, Facebook, Instagram, Linkedin } from "@arthursenno/lizenzero-ui-react/Icon";
import { paymentMethods } from "./paymentMethods";
import { MAILTO } from "@/utils/system-consts";
import { useTranslations } from "next-intl";

export interface LinkProps {
  label: string;
  href: string;
  target: string;
}

export function Footer() {
  const year = new Date().getFullYear();

  const translations = useTranslations("shop.common.footer");

  return (
    <footer className="bg-surface-01 rounded-t-[32px] md:bg-surface-01 md:rounded-t-[60px] pt-8 px-4 pb-4 md:px-36">
      <div className="flex flex-col gap-8 pb-8">
        <div className="flex justify-between flex-col gap-8 md:flex-row flex-wrap">
          <div className="flex items-center">
            <LizenzeroLogo className="max-w-[160px] w-full" />
            <div className="border-r-[1.16px] border-primary md:mx-6 h-8 mx-4" />
            <InterserohLogo className="max-w-[120px] w-full" />
          </div>
          <div className="flex flex-col gap-4">
            <p className="md:hidden font-bold flex text-primary">{translations("extra.anyQuestions")}</p>

            <div className={`flex gap-4 flex-wrap [&>button]:w-full sm:[&>button]:w-auto`}>
              <a href={MAILTO}>
                <Button trailingIcon={<East />} variant="outlined" color="dark-blue" size="small">
                  {translations("buttons.supportCenter")}
                </Button>
              </a>
              <Button trailingIcon={<East />} variant="filled" color="yellow" size="small">
                {translations("buttons.contactForm")}
              </Button>
            </div>

            <p className="pt-2 text-primary font-small-paragraph-regular md:hidden">{translations("extra.callUs")}</p>
          </div>
        </div>

        <div className={"md:hidden"}>
          <p className="text-primary pb-3">
            <b>{translations("extra.followUs")}</b>
          </p>
          <div className={`flex gap-4`}>
            <Facebook fill="#002652" width="24px" height="24px" />
            <Linkedin fill="#002652" width="24px" height="24px" />
            <Instagram fill="#002652" width="24px" height="24px" />
          </div>
        </div>
      </div>
      <div className="py-6 flex flex-col md:flex-row w-full justify-between">
        <div className="flex gap-4 justify-between sm:flex-row flex-col">
          <a
            href="https://www.interseroh.de/impressum"
            target="_blank"
            rel="noreferrer"
            className="text-primary hover:text-primary-dark"
          >
            {translations("links.termsOfUse")}
          </a>
          <a
            href="https://www.interseroh.de/agb"
            target="_blank"
            rel="noreferrer"
            className="text-primary hover:text-primary-dark"
          >
            {translations("links.agb")}
          </a>
          <a
            href="https://www.interseroh.de/datenschutz"
            target="_blank"
            rel="noreferrer"
            className="text-primary hover:text-primary-dark"
          >
            {translations("links.dataProtection")}
          </a>
        </div>
        <div className="md:hidden border-b my-4 md:my-0 border-[#CECECE] w-full" />
        <p className="flex text-primary">
          {translations("links.interseroh")} {year}
        </p>
      </div>
      <div className="flex flex-wrap w-[calc(100%+2rem)] md:w-[calc(100%+18rem)] gap-y-4 items-center justify-evenly md:justify-between bg-on-primary md:bg-white -mx-4 -mb-4 p-4 md:px-36 md:-mx-36">
        {paymentMethods.map((method, index) => (
          <div className="" key={index}>
            {method.path}
          </div>
        ))}
      </div>
    </footer>
  );
}
