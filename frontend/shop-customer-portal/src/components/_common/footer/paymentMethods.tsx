import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>card<PERSON><PERSON>,
  <PERSON>pal<PERSON>ogo,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  VisaLogo,
} from "@arthursenno/lizenzero-ui-react/Figure";

export const paymentMethods: {
  path: JSX.Element;
  alt: string;
}[] = [
  {
    path: <KlarnaLogo width={60} />,
    alt: "Klarna",
  },
  {
    path: <PaypalLogo width={80} />,
    alt: "PayPal",
  },
  {
    path: <RechnungLogo width={80} />,
    alt: "Rechnung",
  },
  {
    path: <VisaLogo width={60} />,
    alt: "Visa",
  },
  {
    path: <MastercardLogo width={60} />,
    alt: "MasterCard",
  },
  {
    path: <AlipayLogo width={80} />,
    alt: "Alipay",
  },
  {
    path: <EpsLogo width={50} />,
    alt: "EPS",
  },
  {
    path: <IdealLogo width={50} />,
    alt: "iDEAL",
  },
];
