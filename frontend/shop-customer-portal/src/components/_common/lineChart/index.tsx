"use client";
import { ChartConfig, ChartContainer } from "@/components/ui/chart";
import { CartesianGrid, Line, LineChart, XAxis } from "recharts";

interface LizenzeroLineChartProps {
  chartData: any;
  chartConfig: ChartConfig;
}

export function LizenzeroLineChart({ chartConfig, chartData }: LizenzeroLineChartProps) {
  if (!chartConfig || !chartData) return null;

  return (
    <ChartContainer className="h-full w-full" config={chartConfig}>
      <LineChart
        accessibilityLayer
        data={chartData}
        margin={{
          left: 12,
          right: 12,
        }}
      >
        <CartesianGrid vertical={false} />
        <XAxis
          dataKey="month"
          tickLine={true}
          axisLine={true}
          tickMargin={8}
          tickFormatter={(value) => value.slice(0, 3)}
        />
        {Object.keys(chartConfig).map((key, i) => (
          <Line
            key={key}
            dataKey={Object.keys(chartConfig)[i]}
            type="monotone"
            stroke={`var(--color-${Object.keys(chartConfig)[i]})`}
            strokeWidth={2}
            dot={false}
          />
        ))}
      </LineChart>
    </ChartContainer>
  );
}
