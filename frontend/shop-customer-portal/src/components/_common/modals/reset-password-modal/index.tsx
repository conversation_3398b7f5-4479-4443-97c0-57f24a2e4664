"use client";

import { PasswordInput } from "@/components/ui/password-input";
import { useRouter } from "@/i18n/navigation";
import { api } from "@/lib/api";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Error } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";
import { useSnackbar } from "notistack";
import { useState } from "react";
import { useForm, useWatch } from "react-hook-form";
import { z } from "zod";
import { PasswordStrengthBar } from "../../password-strength-bar";

interface ResetPasswordFormData {
  password: string;
}

export function ResetPasswordModal() {
  const t = useTranslations("shop.common.resetPasswordModal");
  const globalT = useTranslations("global");
  const router = useRouter();
  const searchParams = useSearchParams();

  const schema = z.object({
    password: z
      .string()
      .min(6, globalT("validation.minimumLength", { min: 6 }))
      .refine((value) => /[^a-zA-Z0-9]/.test(value), globalT("validation.requiredSpecialCharacter"))
      .refine((value) => /[0-9]/.test(value), globalT("validation.numberRequired")),
  });

  const token = searchParams.get("token");
  const email = searchParams.get("email");

  const { enqueueSnackbar } = useSnackbar();

  const [error, setError] = useState(null);

  async function handleFormSubmit({ password }: ResetPasswordFormData) {
    if (!isValid) return;

    const response = await api.post("/auth/user/reset/password", {
      token,
      password,
      type: "PASSWORD_RESET",
    });
    if (response.status === 500) {
      enqueueSnackbar(response.data, { variant: "error" });
      return;
    }
    if (response.status != 201) {
      setError(response.data?.message);
      return;
    }
    router.push("informations");
  }

  const {
    register,
    handleSubmit,
    watch,
    control,
    formState: { errors, isLoading, isSubmitting, isValid },
  } = useForm<ResetPasswordFormData>({
    resolver: zodResolver(schema),
    mode: "all",
  });

  const password = useWatch({ control: control, name: "password" });

  return (
    <Modal open={!!token} className="z-50 w-full" style={{ maxWidth: "580px" }}>
      <div className="p-5">
        <form className="text-primary" onSubmit={handleSubmit(handleFormSubmit)}>
          <div className="mb-5">
            <h1 className="text-support-blue font-medium text-2xl mb-2">{t("title")}</h1>
            <span className="font-base ">{t("email", { email: email! })}</span>
          </div>
          <div className="space-y-6">
            <div>
              <PasswordInput
                label={`${t("password.label")}*`}
                placeholder={t("password.placeholder")}
                {...register("password")}
              />
              {errors.password && (
                <div className="flex space-x-1 items-center mt-2 justify-start">
                  <Error width={17} height={17} className="fill-tonal-red-50" />
                  <span className="text-tonal-red-50 text-xs font-light mt-1">{errors.password.message}</span>
                </div>
              )}
              {error && (
                <div className="flex space-x-1 items-center mt-2 justify-start">
                  <Error width={17} height={17} className="fill-tonal-red-50" />
                  <span className="text-tonal-red-50 text-xs font-light mt-1">{error}</span>
                </div>
              )}
              <PasswordStrengthBar password={password} />
            </div>
          </div>

          <div className="flex justify-end w-full items-center mt-6 gap-10">
            <Button
              color="dark-blue"
              size="medium"
              variant="outlined"
              type="button"
              onClick={() => router.push("informations")}
            >
              {t("backToLogin")}
            </Button>

            <Button color="yellow" size="medium" variant="filled" disabled={isLoading} type="submit">
              {isLoading || isSubmitting ? t("button.loading") : t("button.label")}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
}
