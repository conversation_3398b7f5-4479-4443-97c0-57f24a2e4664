"use client";

import { CreditCardInformation } from "@/components/_common/credit-card-information";
import { Divider } from "@/components/_common/divider";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { MastercardLogo, VisaLogo } from "@arthursenno/lizenzero-ui-react/Figure";
import { Clear, Download, KeyboardArrowLeft, RadioSelected } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { PaymentElement, useElements, useStripe } from "@stripe/react-stripe-js";
import Image from "next/image";
import { useEffect, useState } from "react";
import { JourneyBillingPaymentMethods } from "@/components/modules/shop/journeys/components/journey-billing/journey-billing-payment-methods";
import { PaymentCompletedModal } from "./payment-completed-modal";

interface ModalInvoiceFlowProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export function ModalInvoiceFlow({ open, setOpen }: ModalInvoiceFlowProps) {
  const [isOpenModalCompleted, setIsOpenModalCompleted] = useState(false);

  const handleFinalizePurchase = () => {
    setOpen(!open);
    setIsOpenModalCompleted(!isOpenModalCompleted);
  };

  return (
    <>
      <ModalInvoice open={open} setOpen={setOpen} onClickFinalizePurchase={handleFinalizePurchase} />
      <PaymentCompletedModal
        title="Purchase completed!"
        open={isOpenModalCompleted}
        setOpen={setIsOpenModalCompleted}
      />
    </>
  );
}

interface ModalInvoiceProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  onClickFinalizePurchase: () => void;
}

function ModalInvoice({ open, setOpen, onClickFinalizePurchase }: ModalInvoiceProps) {
  const [renderEdit, setRenderEdit] = useState(false);
  const [message, setMessage] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const stripe = useStripe();
  const elements = useElements();

  const handleOpen = () => {
    setRenderEdit(false);
    setOpen(!open);
  };

  const handleEdit = () => {
    setRenderEdit((current) => !current);
  };

  useEffect(() => {
    if (!stripe) {
      return;
    }

    const clientSecret = new URLSearchParams(window.location.search).get("payment_intent_client_secret");

    if (!clientSecret) {
      return;
    }

    stripe.retrievePaymentIntent(clientSecret).then(({ paymentIntent }: any) => {
      switch (paymentIntent.status) {
        case "succeeded":
          setMessage("Payment succeeded!");
          break;
        case "processing":
          setMessage("Your payment is processing.");
          break;
        case "requires_payment_method":
          setMessage("Your payment was not successful, please try again.");
          break;
        default:
          setMessage("Something went wrong.");
          break;
      }
    });
  }, [stripe]);

  const handleFinalizePurchase = async () => {
    if (!stripe || !elements) {
      // Stripe.js hasn't yet loaded.
      // Make sure to disable form submission until Stripe.js has loaded.
      return;
    }

    setIsLoading(true);

    const { error } = await stripe.confirmPayment({
      elements,
      confirmParams: {
        return_url: window.location.href,
      },
    });

    if (error.type === "card_error" || error.type === "validation_error") {
      if (error.message) setMessage(error.message);
      setIsLoading(false);
      return;
    }
    onClickFinalizePurchase();
  };

  return (
    <Modal
      open={open}
      className="z-50 w-full"
      style={{ maxWidth: "580px", borderRadius: "52px", maxHeight: "85vh" }}
      onOpenChange={handleOpen}
    >
      <div className="p-5 max-h-[75vh] overflow-auto">
        <div className={`flex ${renderEdit ? "justify-between" : "justify-end"}`}>
          {renderEdit && (
            <button className="text-base font-bold flex items-center gap-2 text-support-blue" onClick={handleEdit}>
              <KeyboardArrowLeft className="size-5 fill-support-blue" />
              Back to review
            </button>
          )}
          <button onClick={handleOpen}>
            <Clear className="size-6 fill-primary" />
          </button>
        </div>
        <p className="text-primary text-[28px] font-bold mt-2">
          {renderEdit ? "Review your payment method" : "Pay open invoice"}{" "}
        </p>
        <p className="mt-4 text-base text-tonal-dark-cream-20">
          {renderEdit
            ? "Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit. Pretium egestas posuere in nulla. Ipsum purus nascetur leo lorem. Vitae."
            : "Download the invoice and check the payment method to conclude the process."}
        </p>
        {renderEdit ? (
          <div className="rounded-[20px] mt-10 bg-white border border-surface-03">
            {/* {license?.cardNumber && (
              <div className="flex flex-row gap-3 items-center p-5">
                <RadioSelected width={24} className="fill-primary" />
                <VisaLogo width={40} />
                <p className="text-primary">**** **** **** {license?.cardNumber?.slice(-4)}</p>
              </div>
            )} */}
            <div className="flex flex-row gap-3 items-center p-5">
              {/* TODO: functional select */}
              {/* <RadioUnselected width={24} className="fill-tonal-dark-cream-60" /> */}
              <RadioSelected width={24} className="fill-primary" />
              <VisaLogo width={40} />
              <MastercardLogo width={40} />
              <p className="text-primary">New Credit Card</p>
            </div>
            <div className="flex flex-row gap-3 items-center p-5">
              <PaymentElement id="payment-element" />
            </div>

            <JourneyBillingPaymentMethods borderLess />
          </div>
        ) : (
          <Infos handleEdit={handleEdit} />
        )}
        {message && <div id="payment-message">{message}</div>}

        <div className="mt-6 flex justify-end">
          <Button color="yellow" variant="filled" size="medium" onClick={() => handleFinalizePurchase()}>
            {isLoading ? "Processing..." : "Finalize Purchase"}
          </Button>
        </div>
      </div>
    </Modal>
  );
}

interface InfosProps {
  handleEdit: () => void;
}

function Infos({ handleEdit }: InfosProps) {
  return (
    <>
      <div className="mt-6">
        <div className="py-4 px-5 rounded-[20px] bg-surface-02 flex items-center justify-between">
          <div className="flex items-center gap-4 flex-1">
            <Image
              src={"https://upload.wikimedia.org/wikipedia/en/thumb/0/03/Flag_of_Italy.svg/220px-Flag_of_Italy.svg.png"}
              width={24}
              height={24}
              alt="Country flag"
              className="size-6 rounded-full"
            />
            <p className="text-base font-bold text-primary">name-invoice.pdf</p>
          </div>
          <div className="flex items-center gap-10">
            <p className="text-base text-primary">150.00 €</p>
            <button className="text-base font-bold flex items-center gap-2 text-support-blue">
              <Download className="size-5 fill-support-blue" />
              Download
            </button>
          </div>
        </div>
      </div>

      <Divider style={{ marginTop: "24px", marginBottom: "24px" }} />

      <div>
        <div className="flex items-center justify-between">
          <p className="text-xl text-grey-blue">Payment Method</p>

          <button className="text-support-blue text-xl font-bold" onClick={handleEdit}>
            Edit
          </button>
        </div>
        <div className="mt-4">
          <CreditCardInformation />
        </div>
      </div>

      <div className="mt-6 flex items-center justify-between p-4 bg-[#CFEBF5] rounded-2xl">
        <p className="text-primary text-base font-bold">Total</p>
        <p className="text-primary text-xl font-bold">400.00 €</p>
      </div>
    </>
  );
}
