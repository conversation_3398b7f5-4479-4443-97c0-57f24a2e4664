import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Clear, East, Plant } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";

interface PaymentCompletedModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  title: string;
}

export function PaymentCompletedModal({ open, setOpen, title }: PaymentCompletedModalProps) {
  const handleOpen = () => {
    setOpen(!open);
  };

  return (
    <Modal
      open={open}
      className="z-50 w-full"
      style={{ maxWidth: "580px", borderRadius: "52px", maxHeight: "85vh" }}
      onOpenChange={handleOpen}
    >
      <div className="p-5 max-h-[75vh] overflow-auto">
        <div className="flex justify-end">
          <button onClick={handleOpen}>
            <Clear className="size-6 fill-primary" />
          </button>
        </div>

        <div className="flex items-center gap-2">
          <Plant className="size-9 fill-success" />
          <p className="text-[28px] text-primary font-bold">{title}</p>
        </div>

        <div className="mt-10 flex justify-end">
          <Button trailingIcon={<East />} color="yellow" variant="filled" size="medium" onClick={handleOpen}>
            Go to Dashboard
          </Button>
        </div>
      </div>
    </Modal>
  );
}
