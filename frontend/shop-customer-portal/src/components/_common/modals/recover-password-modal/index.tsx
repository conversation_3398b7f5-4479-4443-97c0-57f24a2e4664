"use client";

import { Link, usePathname } from "@/i18n/navigation";
import { api } from "@/lib/api";
import { extractShopPath } from "@/utils/extractShopPath";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { CheckCircle, Error } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { useForm } from "react-hook-form";
import { z } from "zod";

interface RecoverPasswordModalProps {
  open: boolean;
  onClose: () => void;
}

interface RecoverPasswordData {
  email: string;
}

export function RecoverPasswordModal({ open, onClose }: RecoverPasswordModalProps) {
  const recoverPasswordSchema = z.object({
    email: z
      .string({
        required_error: "This field is required",
        invalid_type_error: "Invalid type",
        description: "E-mail cannot contain special characters, except for @, ., -, and _",
      })
      .email({
        message: "Invalid email",
      }),
  });

  const form = useForm<RecoverPasswordData>({
    resolver: zodResolver(recoverPasswordSchema),
    mode: "onSubmit",
  });

  const recoverPasswordMutation = useMutation({
    mutationFn: async (email: string) => {
      await api.post("auth/user/request/password", {
        email,
        callbackUrl: `${process.env.NEXT_PUBLIC_DOMAIN}/auth/recover-password`,
      });
    },
  });

  async function handleFormSubmit({ email }: { email: string }) {
    await recoverPasswordMutation.mutateAsync(email, {
      onSuccess: () => {
        enqueueSnackbar("Email sent successfully!", {
          variant: "success",
        });
      },
      onError: () => {
        form.setError("email", {
          message: "Email not found",
        });
        enqueueSnackbar("Email not found", {
          variant: "error",
        });
      },
    });
  }

  function handleOpenChange(open: boolean) {
    if (!open) {
      form.reset();
      onClose();
    }
  }

  return (
    <Modal
      open={open}
      className="z-50 w-full"
      style={{ borderRadius: "52px", maxWidth: "580px" }}
      onOpenChange={handleOpenChange}
    >
      <div className="p-5">
        {!form.formState.isSubmitted && (
          <form className="text-primary" onSubmit={form.handleSubmit(handleFormSubmit)}>
            <div className="mb-5">
              <h1 className="text-support-blue font-medium text-2xl mb-2">Recovery password</h1>
              <span className="font-base ">Please enter the e-mail you’re using for your account</span>
            </div>
            <div className="space-y-6">
              <div>
                <Input
                  className="mt-4 "
                  label="E-mail *"
                  placeholder="E-mail"
                  type="text"
                  variant={form.formState.errors.email ? "error" : "enabled"}
                  {...form.register("email")}
                  rightIcon={form.formState.isValid && <CheckCircle className="fill-success" width={20} height={20} />}
                  classname={form.formState.errors?.email?.message && "bg-tonal-red-50"}
                  // errorMessage={errors.email && errors.email.message}
                />
                {form.formState.errors?.email?.message && (
                  <div className="flex space-x-1 items-center mt-2">
                    <Error className="fill-tonal-red-50" width={20} height={20} />
                    <span className="text-tonal-red-50 text-xs font-light">
                      {form.formState.errors?.email?.message}
                    </span>
                  </div>
                )}
              </div>
            </div>

            <div className="flex justify-end  w-full items-center mt-6">
              <LinkDontHaveAccount onClick={onClose} />
              <Button
                color={!form.formState.isValid || form.formState.errors.email ? "red" : "yellow"}
                size="medium"
                variant="filled"
                disabled={recoverPasswordMutation.isPending || !form.formState.isValid}
              >
                {recoverPasswordMutation.isPending ? "Loading..." : "Continue "}
              </Button>
            </div>
          </form>
        )}
        {form.formState.isSubmitted && (
          <div className="text-primary">
            <div className="mb-5 space-y-4 font-base">
              <h1 className="text-support-blue font-medium text-2xl mb-2">Recovery password</h1>
              <p className=" ">
                We’ve sent password reset instructions to your e-mail address. If no e-mail is received within ten
                minutes, check if the submitted address is correct.
              </p>
              <div className="flex justify-end  w-full items-center mt-6">
                <LinkDontHaveAccount onClick={onClose} />
              </div>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
}

function LinkDontHaveAccount({ onClick }: { onClick?: () => void }) {
  const path = usePathname();

  return (
    <Link href={`${extractShopPath(path)}/create-account`}>
      <span
        className="font-medium text-support-blue mr-5 cursor-pointer"
        onClick={() => {
          onClick && onClick();
        }}
      >
        I don&lsquo;t have an account
      </span>
    </Link>
  );
}
