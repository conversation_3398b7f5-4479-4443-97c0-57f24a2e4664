"use client";

import { Divider } from "@/components/_common/divider";
import { CreateAccountModalSchema, CreateAccountSchema } from "@/components/_common/forms/schemas";
import { LoginModal } from "@/components/_common/modals/login-modal";
import { VerifyEmailModal } from "@/components/_common/modals/verify-email-modal";
import { PasswordStrengthBar } from "@/components/_common/password-strength-bar";
import { PasswordInput } from "@/components/ui/password-input";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { createAccount } from "@/lib/api/account";
import { TypeResendToken } from "@/lib/api/account/types";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { CheckCircle, Clear, East } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSession } from "next-auth/react";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { FaCheckSquare, FaRegSquare } from "react-icons/fa";
import { useExitIntent } from "use-exit-intent";
import { z } from "zod";

export type ExitIntentDataSchemaFormValues = z.infer<typeof CreateAccountSchema>;

export function ExitIntentModal() {
  const session = useSession();
  const isAuthenticated = !!session.data?.user;

  const { updateCart } = useShoppingCart();
  const { registerHandler } = useExitIntent();
  const t = useTranslations("shop.common.exitIntent");
  const globalT = useTranslations("global");

  const { paramValues, changeParam, deleteParam } = useQueryFilter(["exit"]);

  const isOpen = paramValues.exit === "true";

  const [isOpenModalVerifyEmail, setIsOpenModalVerifyEmail] = useState(false);
  const [isOpenModalLogin, setIsOpenModalLogin] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isCheckedDataProtection, setIsCheckedDataProtection] = useState(false);
  const [isCheckedTermsAndConditions, setIsCheckedTermsAndConditions] = useState(false);
  const [isCheckCreateAccount, setIsCheckCreateAccount] = useState(false);

  const {
    handleSubmit,
    register,
    watch,
    setError,
    setValue,
    clearErrors,
    formState: { errors },
  } = useForm<ExitIntentDataSchemaFormValues>({
    mode: "onBlur",
    resolver: zodResolver(CreateAccountModalSchema),
  });

  const companyName = watch("companyName");
  const firstName = watch("firstName");
  const lastName = watch("lastName");
  const email = watch("email");
  const password = watch("password");
  const confirmPassword = watch("confirmPassword");

  const isNotValidConfirmPassword = password && confirmPassword && password !== confirmPassword;

  async function onSubmit(data: ExitIntentDataSchemaFormValues) {
    if (isCheckCreateAccount) {
      if (isNotValidConfirmPassword) {
        return;
      }

      if (!password || !confirmPassword) {
        if (!password) {
          setError("password", {
            message: globalT("validation.required"),
          });
        }

        if (!confirmPassword) {
          setError("confirmPassword", {
            message: globalT("validation.required"),
          });
        }

        return;
      }
    }

    setLoading(true);

    try {
      const res: any = await createAccount({
        email: data.email,
        first_name: data.firstName,
        last_name: data.lastName,
        password: isCheckCreateAccount ? data?.password : null,
        company_name: data?.companyName,
      });

      if ([400, 409].includes(res?.response?.status)) {
        setError("email", {
          message: t("form.email.errors.inUse"),
        });

        return enqueueSnackbar(t("form.email.errors.inUse"), { variant: "error" });
      }

      if (!res?.data) return enqueueSnackbar(t("form.errors.createUser"), { variant: "error" });

      try {
        await updateCart({ email: data.email });
      } catch (error) {
        console.error("🚀 ~ onSubmit ~ error:", error);
      }

      setIsOpenModalVerifyEmail(true);
      deleteParam("exit");
    } catch (error) {
      console.error("🚀 ~ onSubmit ~ error:", error);
    } finally {
      setLoading(false);
    }
  }

  function handleAlreadyHaveAccount() {
    setIsOpenModalLogin(true);
    handleOpenChange(false);
  }

  function handleOpenChange(open: boolean) {
    if (!open) deleteParam("exit");
  }

  function handleIsCheckedDataProtection() {
    setIsCheckedDataProtection(!isCheckedDataProtection);
  }

  function handleIsCheckedTermsAndConditions() {
    setIsCheckedTermsAndConditions(!isCheckedTermsAndConditions);
  }

  function handleIsCheckCreateAccount() {
    if (isCheckCreateAccount) {
      setValue("password", "");
      setValue("confirmPassword", "");
      clearErrors();
    }

    setIsCheckCreateAccount(!isCheckCreateAccount);
  }

  registerHandler({
    id: "openModal",
    handler: () => {
      if (isAuthenticated) return;

      changeParam("exit", "true");
    },
  });

  if (isAuthenticated) return null;

  return (
    <>
      <Modal
        open={isOpen}
        onOpenChange={handleOpenChange}
        style={{ zIndex: 1000, borderRadius: "52px", maxWidth: "672px", height: "600px" }}
        className="py-4 md:py-8 px-7 w-full bg-surface-01 z-[999]"
      >
        <div className="w-full flex justify-end">
          <button
            className="size-8 bg-white rounded-full flex items-center justify-center"
            onClick={() => handleOpenChange(false)}
          >
            <Clear className="fill-primary size-6" />
          </button>
        </div>
        <form className="overflow-auto h-[95%] px-2" onSubmit={handleSubmit(onSubmit)}>
          <div className="flex flex-col gap-4">
            <p className="text-[28px] font-bold text-support-blue">{t("title")}</p>
            <p className="text-base text-tonal-dark-cream-20">{t("subtitle")}</p>
            <p className="text-[#808FA9] text-sm">*{globalT("validation.mandatoryFields")}</p>
          </div>
          <div className="mt-10">
            <Input
              label={`${t("form.email.label")} *`}
              placeholder={t("form.email.placeholder")}
              {...register("email")}
              variant={errors.email ? "error" : "enabled"}
              errorMessage={errors?.email && errors?.email?.message}
              rightIcon={
                !errors?.email &&
                CreateAccountSchema.shape.email.safeParse(email).success &&
                email && <CheckCircle width={20} height={20} className="fill-tonal-green-40" />
              }
            />
            <div className="mt-8">
              <Input
                label={t("form.companyName.label")}
                placeholder={t("form.companyName.placeholder")}
                {...register("companyName")}
                variant={errors.companyName && "error"}
                errorMessage={errors.companyName && errors.companyName.message}
                rightIcon={
                  !errors.companyName &&
                  CreateAccountSchema.shape.companyName.safeParse(companyName).success &&
                  companyName && <CheckCircle width={20} height={20} className="fill-tonal-green-40" />
                }
              />
            </div>
            <div className="mt-6">
              <Input
                label={t("form.firstName.label")}
                placeholder={t("form.firstName.placeholder")}
                {...register("firstName")}
                variant={errors.firstName && "error"}
                errorMessage={errors.firstName && errors.firstName.message}
                rightIcon={
                  !errors.firstName &&
                  CreateAccountSchema.shape.firstName.safeParse(firstName).success &&
                  firstName && <CheckCircle width={20} height={20} className="fill-tonal-green-40" />
                }
              />
            </div>
            <div className="mt-8">
              <Input
                label={t("form.lastName.label")}
                placeholder={t("form.lastName.placeholder")}
                {...register("lastName")}
                variant={errors.lastName && "error"}
                errorMessage={errors.lastName && errors.lastName.message}
                rightIcon={
                  !errors.lastName &&
                  CreateAccountSchema.shape.lastName.safeParse(lastName).success &&
                  lastName && <CheckCircle width={20} height={20} className="fill-tonal-green-40" />
                }
              />
            </div>
          </div>
          <Divider style={{ marginTop: "24px", marginBottom: "24px" }} />
          <div>
            <div>
              <label className="flex flex-row gap-3 items-start cursor-pointer">
                {isCheckCreateAccount ? (
                  <FaCheckSquare className="fill-tonal-dark-blue-30 size-5 flex-none" />
                ) : (
                  <FaRegSquare className="fill-tonal-dark-blue-30 size-5 flex-none" />
                )}
                <input type="checkbox" className="hidden" onChange={handleIsCheckCreateAccount} />

                <div>
                  <p className="text-base/title-1 text-tonal-dark-blue-30 font-bold">{t("form.createAccount.title")}</p>
                  <p className="text-base/title-1 text-on-surface-01">{t("form.createAccount.subtitle")}</p>
                </div>
              </label>
              {isCheckCreateAccount && (
                <div className="flex flex-col gap-8 mt-6">
                  <div className="flex flex-col gap-2">
                    <PasswordInput
                      label={t("form.createAccount.password.label")}
                      placeholder={t("form.createAccount.password.placeholder")}
                      {...register("password")}
                      variant={errors.password && "error"}
                      errorMessage={errors.password && errors.password.message}
                      rightIcon={
                        !errors.password &&
                        CreateAccountSchema?.shape?.password?.safeParse?.(password)?.success &&
                        password && <CheckCircle width={20} height={20} className="fill-tonal-green-40" />
                      }
                    />
                    <PasswordStrengthBar password={password} />
                  </div>
                  <PasswordInput
                    label={t("form.createAccount.confirmPassword.label")}
                    placeholder={t("form.createAccount.confirmPassword.placeholder")}
                    {...register("confirmPassword")}
                    variant={(errors.confirmPassword || isNotValidConfirmPassword) && "error"}
                    errorMessage={
                      (errors.confirmPassword && errors.confirmPassword.message) ||
                      (isNotValidConfirmPassword && t("form.createAccount.confirmPassword.errors.mismatch"))
                    }
                    rightIcon={
                      !errors.confirmPassword &&
                      !isNotValidConfirmPassword &&
                      confirmPassword &&
                      password && <CheckCircle width={20} height={20} className="fill-tonal-green-40" />
                    }
                  />
                </div>
              )}

              <Divider style={{ marginTop: "24px", marginBottom: "16px" }} />

              <label className="flex flex-row gap-3 items-start cursor-pointer">
                {isCheckedDataProtection ? (
                  <FaCheckSquare className="fill-on-tertiary size-5 flex-none" />
                ) : (
                  <FaRegSquare className={`fill-on-tertiary size-5 flex-none`} />
                )}
                <input type="checkbox" className="hidden" onChange={handleIsCheckedDataProtection} />
                <div>
                  <p className="text-base/title-1 text-tonal-dark-cream-30 font-bold">
                    {t("form.createAccount.dataProtection.label")}
                  </p>
                  <p className="text-base/title-1 text-on-surface-01">
                    <a href="#" className="text-tonal-dark-green-30 underline">
                      {t("form.createAccount.dataProtection.acceptance")}
                    </a>
                  </p>
                </div>
              </label>
              <label className="flex flex-row gap-3 items-start cursor-pointer mt-7">
                {isCheckedTermsAndConditions ? (
                  <FaCheckSquare className="fill-on-tertiary size-5 flex-none" />
                ) : (
                  <FaRegSquare className={`fill-on-tertiary size-5 flex-none`} />
                )}
                <input type="checkbox" className="hidden" onChange={handleIsCheckedTermsAndConditions} />

                <div>
                  <p className="text-base/title-1 text-tonal-dark-cream-30 font-bold">
                    {t("form.createAccount.resultByMail.consent")}
                  </p>
                  <p className="text-base/title-1 text-on-surface-01">
                    {t("form.createAccount.resultByMail.acceptance")}
                  </p>
                </div>
              </label>
            </div>
          </div>
          <div className="mt-10">
            <div className="flex justify-end items-center">
              <Button color="light-blue" variant="text" size="small" onClick={handleAlreadyHaveAccount} type="button">
                {t("form.existingAccount.label")}
              </Button>
              <Button
                variant="filled"
                size="small"
                color="yellow"
                disabled={!email || loading || !isCheckedTermsAndConditions || !isCheckedDataProtection}
                trailingIcon={!loading && <East />}
              >
                {loading ? t("form.buttons.loading") : t("form.buttons.continue")}
              </Button>
            </div>
          </div>
        </form>
      </Modal>
      <VerifyEmailModal
        email={email}
        open={isOpenModalVerifyEmail}
        setOpen={setIsOpenModalVerifyEmail}
        typeResendToken={TypeResendToken.CREATE_ACCOUNT}
      />
      <LoginModal open={isOpenModalLogin} onClose={() => setIsOpenModalLogin(false)} />
    </>
  );
}
