"use client";

import { resendTokenEmail } from "@/lib/api/account";
import { TypeResendToken } from "@/lib/api/account/types";
import { MAILTO } from "@/utils/system-consts";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";

interface VerifyEmailModalProps {
  email: string;
  open: boolean;
  typeResendToken: TypeResendToken;
  setOpen?: (open: boolean) => void;
}

export function VerifyEmailModal({ email, open, typeResendToken, setOpen }: VerifyEmailModalProps) {
  const t = useTranslations("shop.common.verifyEmailModal");

  const resendToken = async () => {
    const res = await resendTokenEmail(email, typeResendToken);

    if (res?.data) {
      enqueueSnackbar(t("success.sendToken"), { variant: "success" });
      return;
    }

    enqueueSnackbar(t("errors.sendToken"), { variant: "error" });
  };

  return (
    <Modal
      open={open}
      className="z-50 w-full"
      style={{ maxWidth: "580px", borderRadius: "52px" }}
      onOpenChange={() => {
        setOpen && setOpen(!open);
      }}
    >
      <div className="p-5">
        <div className="text-primary">
          <div className="mb-5 space-y-4 font-base">
            <h1 className="text-support-blue font-medium text-2xl mb-2">{t("title")}</h1>
            <p>
              <span dangerouslySetInnerHTML={{ __html: t.raw("description").replaceAll(`{email}`, email) }} />
            </p>
            <p>{t("ifNotReceived")}</p>
            <p>
              <button className="font-bold" onClick={resendToken}>
                {t("button")}
              </button>{" "}
              <span>{t("moreInformation")}</span>
              <a href={MAILTO}>{t("supportCenter")}</a>
            </p>
          </div>
        </div>
      </div>
    </Modal>
  );
}
