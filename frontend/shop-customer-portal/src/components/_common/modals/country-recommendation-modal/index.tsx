"use client";

import { usePathname, useRouter } from "@/i18n/navigation";
import { recommendCountry } from "@/lib/api/country";
import { useCustomer } from "@/hooks/use-customer";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Cancel, Error, Plant } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";
import { FormEvent, useState } from "react";

import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";

interface CountryRecommendationModalProps {}

export function CountryRecommendationModal({}: CountryRecommendationModalProps) {
  const t = useTranslations("shop.common.countryRecommendationModal");
  const searchParams = useSearchParams();
  const { customer } = useCustomer();
  const pathname = usePathname();
  const router = useRouter();
  const [step, setStep] = useState<"form" | "success">("form");
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const initialRecommendation = searchParams.get("country-recommendation");

  const isModalOpen = initialRecommendation !== null;

  const customerId = customer?.id ? String(customer.id) : undefined;

  async function handleFormSubmit(e: FormEvent<HTMLFormElement>) {
    try {
      e.preventDefault();

      setLoading(true);
      setError(null);

      const formData = new FormData(e.target as HTMLFormElement);
      const recommendation = formData.get("recommendation");

      if (!recommendation) return;

      const recommendationResponse = await recommendCountry(recommendation as string, customerId);

      if (!recommendationResponse) throw "Error while sending recommendation";

      setStep("success");
    } catch {
      setError(t("doesNotExist"));
    } finally {
      setLoading(false);
    }
  }

  function handleCloseModal() {
    const params = new URLSearchParams(searchParams.toString());

    params.delete("country-recommendation");

    const newSearchParams = params.toString();

    router.push(pathname + "?" + newSearchParams);

    setStep("form");
  }

  function handleOnOpenChange(open: boolean) {
    if (!open) handleCloseModal();
  }

  return (
    <Modal
      open={isModalOpen}
      onOpenChange={handleOnOpenChange}
      className="z-50 !rounded-[52px] w-full max-w-[600px] !py-8 !px-9"
    >
      <div className="flex items-center justify-end">
        <Cancel
          onClick={handleCloseModal}
          className="w-8 h-8 fill-tonal-dark-cream-80 hover:fill-tonal-dark-cream-70 rounded-full cursor-pointer"
        />
      </div>

      {step === "form" && (
        <form className="space-y-10" onSubmit={(e) => handleFormSubmit(e)}>
          <div className="space-y-4">
            <h3 className="text-[28px] font-bold text-support-blue">{t("title")}</h3>
            <p className="text-on-tertiary">{t("description")}</p>
          </div>
          <div className="space-y-2">
            <Input
              name="recommendation"
              label={t("recommendation.label")}
              defaultValue={initialRecommendation}
              placeholder={t("recommendation.placeholder")}
              variant={error ? "error" : "filled"}
            />
            {!!error && (
              <p className="text-error flex items-center gap-2">
                {error}
                <QuestionTooltip icon={<Error />}>
                  <QuestionTooltipDescription>{t("tooltip")}</QuestionTooltipDescription>
                </QuestionTooltip>
              </p>
            )}
          </div>
          <div className="flex flex-row justify-end">
            <Button type="submit" color="yellow" size="medium" variant="filled" disabled={loading}>
              {loading ? t("button.loading") : t("button.label")}
            </Button>
          </div>
        </form>
      )}

      {step === "success" && (
        <div>
          <div className="flex flex-row gap-2 items-center mb-4">
            <Plant className="fill-success size-9" />
            <h3 className="text-primary font-bold text-[28px] mt-1">{t("success.title")}</h3>
          </div>
          <p className="text-primary mb-10">{t("success.description")}</p>
          <div className="flex flex-row justify-end">
            <Button onClick={handleCloseModal} variant="filled" size="medium" color="dark-blue">
              {t("success.button")}
            </Button>
          </div>
        </div>
      )}
    </Modal>
  );
}
