"use client";

import { MouseEvent, useCallback } from "react";
import { usePathname } from "@/i18n/navigation";
import { useSearchParams } from "next/navigation";
import { cn } from "@/lib/utils";
import { useRouter } from "@/i18n/navigation";
import { useTranslations } from "next-intl";

interface CountryRecommendationModalTriggerProps {
  recommendation: string;
  className?: string;
}

export function CountryRecommendationModalTrigger({
  recommendation,
  className,
}: CountryRecommendationModalTriggerProps) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const t = useTranslations("shop.common.countryInput");

  const createQueryString = useCallback(
    (name: string, value: string) => {
      const params = new URLSearchParams(searchParams.toString());
      params.set(name, value);

      return params.toString();
    },
    [searchParams]
  );

  const recommendationUrl = pathname + "?" + createQueryString("country-recommendation", recommendation);

  const handleClickRequest = useCallback(
    (e: MouseEvent<HTMLSpanElement>) => {
      e.stopPropagation();

      router.push(recommendationUrl, {
        scroll: false,
      });
    },
    [recommendationUrl, router]
  );

  return (
    <span
      onClick={handleClickRequest}
      className={cn("font-bold text-support-blue underline underline-offset-4 cursor-pointer", className)}
    >
      {t("requestCountry")}
    </span>
  );
}
