import { PasswordInput } from "@/components/ui/password-input";
import { TypeResendToken } from "@/lib/api/account/types";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { CheckCircle } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { signIn } from "next-auth/react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { UserTypes } from "@/utils/user";
import { VerifyEmailModal } from "../verify-email-modal";
import { RecoverPasswordModal } from "../recover-password-modal";

const createAccountSchema = z.object({
  email: z.string().email(),
  password: z
    .string()
    .min(6, "Password must have at least 6 characters")
    .refine((value) => /[^a-zA-Z0-9]/.test(value), "Enter a special character")
    .refine((value) => /[0-9]/.test(value), "Enter a number"),
});

type CreateAccountFormData = z.infer<typeof createAccountSchema>;

interface CreateAccountModalProps {
  open: boolean;
  onClose: () => void;
}

export function CreateAccountModal({ open, onClose }: CreateAccountModalProps) {
  const [submitted, setSubmitted] = useState(false);
  const [openRecover, setOpenRecover] = useState(false);

  const {
    register,
    handleSubmit,
    setError,
    getValues,
    formState: { errors, isLoading, isSubmitting, isValid },
  } = useForm<CreateAccountFormData>({
    resolver: zodResolver(createAccountSchema),
  });

  const submit = async ({ email, password }: { email: string; password: string }) => {
    const res = await signIn("credentials", {
      email: email,
      password: password,
      redirect: false,
      intent: UserTypes.CUSTOMER,
    });
    if (res?.error || !res) {
      setError("email", {
        message: "Invalid email",
      });
      setError("password", {
        message: "Invalid password",
      });
      return;
    }
    setSubmitted(true);
    onClose();
  };

  const Form = () => {
    return (
      <form className="text-primary" onSubmit={handleSubmit(submit)}>
        <div className="mb-5">
          <h1 className="text-support-blue font-medium text-2xl mb-2">Create Account</h1>
          <span className="font-base ">Login with your informations to continue your purchase.</span>
        </div>
        <div className="space-y-6">
          <div>
            <Input
              className="mt-4 "
              label="E-mail *"
              placeholder="E-mail"
              type="email"
              variant={errors.email ? "error" : "enabled"}
              {...register("email", { required: true })}
              rightIcon={isValid && <CheckCircle className="fill-success" width={20} height={20} />}
              // errorMessage={errors.email && errors.email.message}
            />
            {errors.email && (
              <div className="flex space-x-1 items-center mt-2">
                <span className="text-white rounded-full text-xs flex items-center justify-center text-center w-3 h-3 bg-tonal-red-50">
                  !
                </span>
                <span className="text-tonal-red-50 text-xs font-light">{errors.email.message}</span>
              </div>
            )}
          </div>
          <PasswordInput
            label={"Password *"}
            placeholder={"Type your password"}
            {...register("password", { required: true })}
            variant={errors.password ? "error" : "enabled"}
          />
        </div>

        <div className="flex items-center space-x-5 mt-2">
          {errors.password && (
            <div className="flex space-x-1 items-center">
              <span className="text-white rounded-full text-xs flex items-center justify-center text-center w-3 h-3 bg-tonal-red-50">
                !
              </span>
              <span className="text-tonal-red-50 text-xs font-light">{errors.password.message}</span>
            </div>
          )}
          <span
            className="text-xs cursor-pointer font-medium text-tonal-blue-40"
            onClick={() => {
              setOpenRecover(true);
              onClose();
            }}
          >
            Forgot Password?
          </span>
        </div>
        <div className="flex justify-end  w-full items-center mt-6">
          <span className="font-medium text-support-blue mr-5 cursor-pointer">I don&lsquo;t have an account</span>
          <Button color="yellow" size="medium" variant="filled" disabled={isLoading}>
            {isLoading || isSubmitting ? "Loading..." : "Login"}
          </Button>
        </div>
      </form>
    );
  };

  return (
    <>
      <Modal open={open} className="z-50 w-full" style={{ maxWidth: "580px" }}>
        <div className="p-5">
          <Form />
        </div>
      </Modal>
      <VerifyEmailModal email={getValues("email")} open={submitted} typeResendToken={TypeResendToken.CREATE_ACCOUNT} />
      <RecoverPasswordModal
        open={openRecover}
        onClose={() => {
          onClose();
          setOpenRecover(false);
        }}
      />
    </>
  );
}
