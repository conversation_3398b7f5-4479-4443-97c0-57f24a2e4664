"use client";

import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Dispatch, SetStateAction, useEffect, useRef, useState } from "react";

interface TokenInputProps {
  error?: string;
  initialToken?: string;
  setToken: Dispatch<SetStateAction<string>>;
}

export function TokenInput({ error, initialToken, setToken }: TokenInputProps) {
  const input1 = useRef<HTMLInputElement>(null);
  const [input1Value, setInput1Value] = useState("");

  const input2 = useRef<HTMLInputElement>(null);
  const [input2Value, setInput2Value] = useState("");

  const input3 = useRef<HTMLInputElement>(null);
  const [input3Value, setInput3Value] = useState("");

  const input4 = useRef<HTMLInputElement>(null);
  const [input4Value, setInput4Value] = useState("");

  const input5 = useRef<HTMLInputElement>(null);
  const [input5Value, setInput5Value] = useState("");

  const addValue = (value: string, inputNumber: number) => {
    switch (inputNumber) {
      case 1:
        setInput1Value(value[0]);
        if (value.length > 1) {
          for (let index = 0; index < value.length; index++) {
            const char = value[index];
            addValue(char, index + 1);
          }
        }
        break;
      case 2:
        setInput2Value(value[0]);
        if (value.length > 1) {
          for (let index = 0; index < value.length; index++) {
            const char = value[index];
            addValue(char, index + 2);
          }
        }
        break;
      case 3:
        setInput3Value(value[0]);
        if (value.length > 1) {
          for (let index = 0; index < value.length; index++) {
            const char = value[index];
            addValue(char, index + 3);
          }
        }
        break;
      case 4:
        setInput4Value(value[0]);
        if (value.length > 1) {
          for (let index = 0; index < value.length; index++) {
            const char = value[index];
            addValue(char, index + 4);
          }
        }
        break;
      case 5:
        setInput5Value(value[0]);
        break;
      default:
        value[0] && setInput1Value(value[0]);
        value[1] && setInput2Value(value[1]);
        value[2] && setInput3Value(value[2]);
        value[3] && setInput4Value(value[3]);
        value[4] && setInput5Value(value[4]);
    }
  };

  const handleInputChange = (e: any, nextInput: any, prevInput: any, inputNumber: number) => {
    const value: string = e.target.value.toUpperCase();

    addValue(value.replaceAll(` `, ``), inputNumber);

    if (e.target.value && nextInput) {
      nextInput.current.focus();
    } else if (e.target.value === "" && prevInput) {
      prevInput.current.focus();
    }
  };

  useEffect(() => {
    if (initialToken) {
      addValue(initialToken, 0);
    }
  }, [initialToken]);

  useEffect(() => {
    setToken(input1Value + input2Value + input3Value + input4Value + input5Value);
  }, [input1Value, input2Value, input3Value, input4Value, input5Value]);

  return (
    <div className="grid grid-cols-5 gap-5 my-4 text-center text-xl justify-center">
      <Input
        label=""
        placeholder=""
        variant={error ? "error" : "enabled"}
        className="w-full justify-center flex text-center text-2xl"
        type="text"
        ref={input1}
        onChange={(e: any) => handleInputChange(e, input2, null, 1)}
        value={input1Value}
      />
      <Input
        label=""
        variant={error ? "error" : "enabled"}
        placeholder=""
        type="text"
        ref={input2}
        onChange={(e: any) => handleInputChange(e, input3, input1, 2)}
        value={input2Value}
      />
      <Input
        label=""
        placeholder=""
        variant={error ? "error" : "enabled"}
        type="text"
        ref={input3}
        onChange={(e: any) => handleInputChange(e, input4, input2, 3)}
        value={input3Value}
      />
      <Input
        label=""
        placeholder=""
        variant={error ? "error" : "enabled"}
        type="text"
        ref={input4}
        onChange={(e: any) => handleInputChange(e, input5, input3, 4)}
        value={input4Value}
      />
      <Input
        label=""
        placeholder=""
        variant={error ? "error" : "enabled"}
        type="text"
        ref={input5}
        onChange={(e: any) => handleInputChange(e, null, input4, 5)}
        value={input5Value}
      />
    </div>
  );
}
