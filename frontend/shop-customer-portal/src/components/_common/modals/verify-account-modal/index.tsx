import { resendTokenEmail } from "@/lib/api/account";
import { TypeResendToken } from "@/lib/api/account/types";
import { MAILTO } from "@/utils/system-consts";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { CheckCircle, Clear, East, Error } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { signIn } from "next-auth/react";
import { useState } from "react";
import { TokenInput } from "./token-input";

interface VerifyAccountModalProps {
  open: boolean;
  onOpenChange?: (open: boolean) => void;
  email: string;
  typeResendToken: TypeResendToken;
  onResendToken?: () => Promise<{ error: boolean }>;
  onConfirmToken?: (token: string) => Promise<{ error: boolean }>;
  initialToken?: string;
  showClearBtn?: boolean;
}

export function VerifyAccountModal({
  open,
  onOpenChange,
  email,
  typeResendToken,
  onResendToken,
  onConfirmToken,
  initialToken,
  showClearBtn,
}: VerifyAccountModalProps) {
  const [error, setError] = useState<string>("");
  const [loading, setLoading] = useState<boolean>(false);
  const [message, setMessage] = useState<string>("");
  const [token, setToken] = useState("");

  const confirmToken = async () => {
    setLoading(true);
    setMessage("");
    setError("");
    if (!token[0] || !token[1] || !token[2] || !token[3] || !token[4]) {
      setError("Please fill all fields");
      setLoading(false);
      return;
    }

    try {
      if (onConfirmToken) {
        const res = await onConfirmToken(token);

        if (res.error) {
          setError("Invalid token");
        }

        setLoading(false);
        return;
      }

      const res = await signIn("by-email", {
        email,
        token,
        redirect: false,
      });
      if (!res?.ok) {
        setError(res?.error || "Invalid token");
        setLoading(false);
        return;
      }

      // router.push("./informations");
    } catch (error: any) {
      console.error(error);
      setError(error?.message || "Invalid token");
      setLoading(false);
    }
  };

  const resendToken = async () => {
    setLoading(true);

    if (onResendToken) {
      const res = await onResendToken();
      setLoading(false);

      if (res.error) {
        setError("Invalid token");
      }
      return;
    }

    try {
      const res = await resendTokenEmail(email, typeResendToken);

      if (res?.data) {
        setMessage("Token sent successfully");
        setError("");
      }

      setLoading(false);
    } catch (error) {
      console.error(error);
      setError("Invalid token");
      setLoading(false);
    }
  };

  return (
    <Modal
      open={open}
      style={{ borderRadius: "52px", maxWidth: "800px" }}
      onOpenChange={onOpenChange}
      className="bg-surface-01 z-[9999]  min-w-min w-full"
    >
      <div className="justify-center overflow-auto p-1 text-primary">
        <div className="flex justify-between items-center mb-5">
          <h3 className="font-medium text-tonal-blue-40 text-3xl">Verify your account</h3>
          {showClearBtn && (
            <button className="bg-er" onClick={() => onOpenChange?.(false)}>
              <Clear className="fill-primary bg-white size-6" />
            </button>
          )}
        </div>
        <p className="font-light">
          We’ve sent you a six-digit confirmation code to <strong className="font-medium">{email}</strong>.{" "}
        </p>
        <p>Please enter it below to create your account.</p>

        <TokenInput setToken={setToken} error={error} initialToken={initialToken} />

        {message && (
          <div className="flex">
            <CheckCircle width={20} height={20} className="fill-success flex" />

            <span className="text-success">{message}</span>
          </div>
        )}
        {error ? (
          <div className="flex gap-2 items-center text-error">
            <Error width={20} height={20} className="fill-error flex" />
            <span>{error}.</span>
            <InfoResendToken resendToken={resendToken} />
          </div>
        ) : (
          <InfoResendToken resendToken={resendToken} />
        )}
        <div className="w-full flex justify-end mt-5">
          <div className=" ">
            <Button
              disabled={loading}
              color="yellow"
              variant="filled"
              size="small"
              onClick={() => confirmToken()}
              trailingIcon={!loading && <East />}
            >
              {loading ? "Loading..." : "Continue"}
            </Button>
          </div>
        </div>
      </div>
    </Modal>
  );
}

function InfoResendToken({ resendToken }: { resendToken: () => void }) {
  return (
    <span className="text-sm ">
      <strong className="underline cursor-pointer" onClick={resendToken}>
        Send again
      </strong>{" "}
      or find more information in{" "}
      <a href={MAILTO} className="underline">
        Support Center
      </a>
    </span>
  );
}
