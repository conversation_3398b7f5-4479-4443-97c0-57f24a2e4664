"use client";

import { PasswordInput } from "@/components/ui/password-input";
import { Link, usePathname, useRouter } from "@/i18n/navigation";
import { getStatusByEmail, sendVerificationCode } from "@/lib/api/account";
import { TypeResendToken } from "@/lib/api/account/types";
import { extractShopPath } from "@/utils/extractShopPath";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { CheckCircle, East } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { signIn } from "next-auth/react";
import { useTranslations } from "next-intl";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { SimpleLoginSchema } from "../../forms/schemas";
import { UserTypes } from "@/utils/user";
import { z } from "zod";
import { VerifyEmailModal } from "../verify-email-modal";
import { RecoverPasswordModal } from "../recover-password-modal";
import { CreateAccountModal } from "../create-account-modal";

interface LoginModalProps {
  defaultEmail?: string;
  open: boolean;
  onClose: () => void;
  setIsActive?: Dispatch<SetStateAction<boolean>>;
}

type LoginFormData = z.infer<typeof SimpleLoginSchema>;

export function LoginModal({ defaultEmail, open, onClose, setIsActive }: LoginModalProps) {
  const router = useRouter();
  const path = usePathname();
  const t = useTranslations("shop.common.modalLogin");
  const globalT = useTranslations("global");

  const [submitted, setSubmitted] = useState(false);

  const [openMainModal, setOpenMainModal] = useState(open);

  const [openModalCreateAccount, setOpenModalCreateAccount] = useState(false);
  const [openModalVerifyAccount, setOpenModalVerifyAccount] = useState(false);
  const [openRecover, setOpenRecover] = useState(false);

  const [hasPassword, setHasPassword] = useState(false);

  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    setError,
    getValues,
    formState: { errors, isSubmitting, isValid },
  } = useForm<LoginFormData>({
    resolver: zodResolver(SimpleLoginSchema),
  });

  function handleClose() {
    onClose();
  }

  async function submit({ email, password }: LoginFormData) {
    setIsLoading(true);
    if (!hasPassword) {
      const checkStatus: any = await getStatusByEmail(email);

      if (checkStatus?.response?.status === 404) {
        setError("email", { message: t("form.email.error.notFound") });
      }

      if (!checkStatus.data) return setIsLoading(false);

      if (!checkStatus?.data?.has_password) {
        await sendVerificationCode(email);
        setSubmitted(true);
        onClose();
        // setOpenMainModal(false);
        return setIsLoading(false);
      }
    }

    if (!hasPassword) {
      setHasPassword(true);
      return setIsLoading(false);
    }

    if (!password) {
      setError("password", { message: globalT("validation.required") });
      return setIsLoading(false);
    }

    const res = await signIn("credentials", {
      email: email,
      password: password,
      redirect: false,
      intent: UserTypes.CUSTOMER,
    });

    if (res?.error || !res) {
      setError("email", {
        message: t("form.error.credentials"),
      });
      setError("password", {
        message: t("form.error.credentials"),
      });
      return setIsLoading(false);
    }

    setIsLoading(false);
    const isLongJourney = window?.location?.href?.includes("long-journey");
    const isActionGuideJourney = window?.location?.href?.includes("action-guide");

    let endPath = "calculator";

    if (isLongJourney) endPath = `select-countries`;
    if (isActionGuideJourney) endPath = `shopping-cart`;

    router.push(`${extractShopPath(window.location.href)}/${endPath}`);
  }

  useEffect(() => {
    setOpenMainModal(open);
  }, [open]);

  useEffect(() => {
    setIsActive &&
      setIsActive(openMainModal || openModalCreateAccount || openModalVerifyAccount || openRecover || submitted);
  }, [openMainModal, openModalCreateAccount, openModalVerifyAccount, openRecover, submitted]);

  return (
    <>
      <Modal
        open={openMainModal}
        className="z-50 w-full"
        style={{ maxWidth: "580px", borderRadius: "52px" }}
        onOpenChange={handleClose}
      >
        <div className="p-5">
          <form className="text-primary" onSubmit={handleSubmit(submit)}>
            <div className="mb-5">
              <h1 className="text-support-blue font-medium text-2xl mb-2">Already have an account?</h1>
              <span className="font-base text-tonal-dark-cream-20">
                Login with your informations to continue your purchase.
              </span>
            </div>
            <div className="space-y-6">
              <div>
                <Input
                  defaultValue={defaultEmail}
                  className="mt-4 "
                  label="E-mail *"
                  placeholder="E-mail"
                  type="email"
                  {...register("email", { required: "Email is required" })}
                  variant={errors.email ? "error" : "enabled"}
                  rightIcon={isValid && <CheckCircle className="fill-success" width={20} height={20} />}
                  errorMessage={errors.email && errors.email.message}
                />
              </div>
              {hasPassword && (
                <PasswordInput
                  label={"Password *"}
                  placeholder={"Type your password"}
                  {...register("password")}
                  variant={errors.password ? "error" : "enabled"}
                  errorMessage={errors.password && errors.password.message}
                />
              )}
            </div>

            <div className="flex items-center space-x-5 mt-2">
              {hasPassword && (
                <span
                  className="text-xs cursor-pointer font-medium text-tonal-blue-40"
                  onClick={() => {
                    setOpenRecover(true);
                    onClose();
                  }}
                >
                  Forgot Password?
                </span>
              )}
            </div>

            <div className="flex justify-end  w-full items-center mt-6">
              <Link href={`${extractShopPath(path)}/create-account`}>
                <span
                  className="font-medium text-support-blue mr-5 cursor-pointer"
                  onClick={() => {
                    onClose();
                  }}
                >
                  I don&lsquo;t have an account
                </span>
              </Link>
              <Button
                color="yellow"
                size="medium"
                variant="filled"
                disabled={isLoading}
                trailingIcon={!isLoading && !isSubmitting && <East />}
              >
                {isLoading || isSubmitting ? "Loading..." : hasPassword ? "Continue" : "Login"}
              </Button>
            </div>
          </form>
        </div>
      </Modal>
      <VerifyEmailModal
        open={openModalVerifyAccount}
        email={getValues("email")}
        typeResendToken={TypeResendToken.LOGIN}
      />
      <VerifyEmailModal email={getValues("email")} open={submitted} typeResendToken={TypeResendToken.LOGIN} />
      <RecoverPasswordModal
        open={openRecover}
        onClose={() => {
          onClose();
          setOpenRecover(false);
        }}
      />
      <CreateAccountModal open={openModalCreateAccount} onClose={onClose} />
    </>
  );
}
