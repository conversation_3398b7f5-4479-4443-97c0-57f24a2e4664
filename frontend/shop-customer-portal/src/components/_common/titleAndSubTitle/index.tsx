import { BalloonPlus } from "@arthursenno/lizenzero-ui-react/Icon";
import Image, { StaticImageData } from "next/image";
import { cn } from "@/lib/utils";
import { QuestionTooltip, QuestionTooltipDescription } from "../question-tooltip";

interface ITitleAndSubTitleProps {
  title?: string;
  subTitle?: string;
  subText?: string;
  tooltipInfo?: string;
  showIcon?: boolean;
  variation?: "primary" | "red";
  sideImg?: StaticImageData | string;
  className?: string;
}

export const TitleAndSubTitle = ({
  title,
  subTitle,
  tooltipInfo,
  subText,
  showIcon = false,
  variation = "primary",
  sideImg,
  className,
}: ITitleAndSubTitleProps) => {
  const titleColor = (() => {
    switch (variation) {
      case "primary":
        return "text-primary";
      case "red":
        return "text-error";
      default:
        return "text-primary";
    }
  })();

  return (
    <div className={cn("pb-8 md:pb-16 max-w-screen-md", className)}>
      {showIcon && (
        <div className="flex items-start md:items-center mb-6 gap-4 w-full">
          <BalloonPlus className="fill-primary size-6 md:size-9 flex-none" />
          <p className="text-primary text-lg font-medium">{subTitle}</p>
        </div>
      )}

      <div className="flex items-center gap-4">
        <div className="flex flex-row items-center gap-2">
          {sideImg && <Image src={sideImg} alt="europe_union" className="size-10 mb-3" width={40} height={40} />}
          <p className={`${titleColor} md:text-5xl text-3xl font-semibold md:leading-[62.4px]`}>{title}</p>
        </div>
        {tooltipInfo && (
          <QuestionTooltip>
            <QuestionTooltipDescription>{tooltipInfo}</QuestionTooltipDescription>
          </QuestionTooltip>
        )}
      </div>

      <p className="text-primary text-base leading-5 mt-4">{subText}</p>
    </div>
  );
};
