"use client";

import { ChangeEvent, useEffect, useRef, useState, ComponentProps } from "react";

import { Check } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { BiLoader } from "react-icons/bi";
import { FaLocationPin } from "react-icons/fa6";
import {
  AddressSuggestion,
  AddressSuggestionDetails,
  getAddressSuggestionDetails,
  getAddressSuggestions,
} from "@/lib/api/address-suggestions";
import { useTranslations } from "next-intl";

interface AutocompleteAddressInputProps extends ComponentProps<typeof Input> {
  countryCode?: string;
  onChangeAddressLine?: (addressLine: string) => void;
  onSelectAddress?: (address: AddressSuggestionDetails) => void;
  defaultValue?: string;
  isError?: boolean;
}

export function AutocompleteAddressInput({
  countryCode,
  onSelectAddress,
  onChangeAddressLine,
  defaultValue,
  isError,
  ...props
}: AutocompleteAddressInputProps) {
  const [addressLine, setAddressLine] = useState<string | null>(defaultValue || null);
  const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const globalT = useTranslations("global");

  const [isAddressSuggestionsOpen, setIsAddressSuggestionsOpen] = useState(false);
  const [loadingAddresses, setLoadingAddresses] = useState(false);
  const [addresses, setAddresses] = useState<any[]>([]);

  function handleChangeAddressLine(newAddressLine: string) {
    setAddressLine(newAddressLine);

    if (debounceTimeoutRef.current) clearTimeout(debounceTimeoutRef.current);

    debounceTimeoutRef.current = setTimeout(() => {
      onChangeAddressLine?.(newAddressLine);

      if (!countryCode) return;

      if (!newAddressLine) {
        setIsAddressSuggestionsOpen(false);
        setAddresses([]);
        return;
      }

      fetchAddresses(countryCode, newAddressLine);
    }, 500);
  }

  async function fetchAddresses(countryCode: string, addressLine: string) {
    if (!countryCode) return;
    if (!addressLine) return;

    setAddresses([]);
    setLoadingAddresses(true);
    setIsAddressSuggestionsOpen(true);

    const response = await getAddressSuggestions(countryCode, addressLine);

    if (!response.success) {
      setLoadingAddresses(false);
      setIsAddressSuggestionsOpen(false);

      return;
    }

    setIsAddressSuggestionsOpen(!!response.data.length);
    setLoadingAddresses(false);
    setAddresses(response.data);
  }

  function handleSelectAddress({ formattedAddress, placeId }: AddressSuggestion) {
    setAddressLine(formattedAddress);
    setAddresses([]);
    setLoadingAddresses(false);
    setIsAddressSuggestionsOpen(false);

    getAddressSuggestionDetails(placeId).then((response) => {
      if (!response.success) return;

      onSelectAddress?.(response.data);
    });
  }

  function handleOnFocus() {
    if (!addresses.length) return;

    setIsAddressSuggestionsOpen(true);
  }

  function handleOnBlur() {
    setIsAddressSuggestionsOpen(false);
  }

  useEffect(() => {
    setAddressLine(defaultValue || null);
  }, [defaultValue]);

  return (
    <div className="relative">
      <Input
        label={`${globalT("inputs.address.label")} *`}
        placeholder={globalT("inputs.address.placeholder")}
        rightIcon={!isError && addressLine && <Check width={20} height={20} className="fill-tonal-green-40" />}
        errorMessage={""}
        value={addressLine || ``}
        onChange={(e: ChangeEvent<HTMLInputElement>) => handleChangeAddressLine(e.target.value)}
        onFocus={handleOnFocus}
        onBlur={handleOnBlur}
        variant={isError && "error"}
        enabled={!!countryCode}
        {...props}
      />
      {isAddressSuggestionsOpen && (
        <div className="z-50 absolute top-[110%] -bottom-8 left-0 w-full h-max bg-background border-tonal-dark-cream-80 border rounded-md p-2 text-tonal-dark-cream-10">
          {loadingAddresses && (
            <div className="flex items-center gap-1 p-1">
              <BiLoader width={20} height={20} className="fill-tonal-dark-cream-10 animate-spin duration-1000" />
            </div>
          )}
          {!!addresses.length && (
            <div className="space-y-1 max-h-24 overflow-y-auto">
              {addresses.map((address) => (
                <div
                  id={address.id}
                  key={address.id}
                  className="overflow-hidden whitespace-nowrap flex items-center gap-2 hover:bg-tonal-dark-cream-80 rounded-md p-1 cursor-pointer"
                  onMouseDown={() => handleSelectAddress(address)}
                >
                  <FaLocationPin className="fill-tonal-dark-cream-10 size-3 flex-none" />
                  <p className="text-sm text-nowrap overflow-hidden text-ellipsis">{address.formattedAddress}</p>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
