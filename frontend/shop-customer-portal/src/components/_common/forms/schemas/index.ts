import {
  ADDRESS_REGEX,
  CITY_REGEX,
  COMPANY_NAME_REGEX,
  CONTAINS_LETTER_REGEX,
  CONTAINS_NON_ALPHANUMERIC_REGEX,
  CONTAINS_NUMBER_REGEX,
  SPECIAL_CHARS_NUMERIC_REGEX,
  SPECIAL_CHARS_REGEX,
  ZIP_CODE_REGEX,
} from "@/utils/regex";
import { z } from "zod";

// TODO: i18n

export const companyId = z.number().optional();

export const companyName = z
  .string()
  .min(1, "Required field")
  .regex(COMPANY_NAME_REGEX, "Special characters are not allowed for this field");

export const companyNameOpt = companyName.optional().or(z.literal(""));

export const countryCode = z.string().min(1);

export const city = z
  .string({
    required_error: "Please enter a valid city",
    invalid_type_error: "Please enter a valid city",
  })
  .min(1, { message: "Please enter a valid city" })
  .regex(CITY_REGEX, "Special characters are not allowed for this field");

export const cityOpt = city.optional();

export const zipCode = z
  .string({
    required_error: "Please enter a valid zip code",
    invalid_type_error: "Please enter a valid zip code",
  })
  .min(1, { message: "Please enter a valid zip code" })
  .regex(ZIP_CODE_REGEX, "Special characters are not allowed for this field");

export const zipCodeOpt = zipCode.optional();

export const streetAndNumber = z
  .string({
    required_error: "Please enter a valid street and number",
    invalid_type_error: "Please enter a valid street and number",
  })
  .min(1, { message: "Please enter a valid street and number" })
  .regex(ADDRESS_REGEX, "Special characters are not allowed for this field");

export const streetAndNumberOpt = streetAndNumber.optional();

export const additionalAddressLine = z
  .string()
  .regex(ADDRESS_REGEX, "Special characters are not allowed for this field")
  .optional()
  .or(z.literal(""));

export const documentType = z.enum(["TAX", "VAT"]);

export const vatId = z
  .string()
  .min(1, "Required field")
  .regex(/^[a-zA-Z0-9ßÀ-ÿ\s]+$/, "Special characters are not allowed for this field")
  .optional();

export const taxNumber = z
  .string()
  .min(1, "Required field")
  .regex(/^[a-zA-Z0-9ßÀ-ÿ\s]+$/, "Special characters are not allowed for this field")
  .optional();

export const salutation = z.string().min(1);

export const fullName = z
  .string()
  .min(2, { message: `Full name is required` })
  .max(50, { message: `Full name is too long` })
  .regex(SPECIAL_CHARS_REGEX, "Special characters are not allowed for this field.");

export const fullNameOpt = fullName.optional().or(z.literal(""));

export const firstName = z
  .string()
  .min(2, "Must have at least 2 characters")
  .regex(SPECIAL_CHARS_NUMERIC_REGEX, "Special characters are not allowed for this field")
  .refine((s) => !s.includes(" "), "Spaces are not allowed for first name");

export const surname = z
  .string()
  .min(2, "Must have at least 2 characters")
  .regex(SPECIAL_CHARS_NUMERIC_REGEX, "Special characters are not allowed for this field")
  .refine((s) => !!s.trim().length, "Must have at least 2 characters");

export const phone = z.string().min(5, "Required field");

export const mobile = z.string().optional();

export const emails = z.array(
  z.object({
    id: z.string().optional(),
    email: z.string().trim().email("Invalid e-mail"),
  })
);

export const lucidNumber = z.string().optional();

export const phones = z.array(
  z.object({
    id: z.string().optional(),
    phone_number: z.string().trim().min(1, "Required field"),
  })
);

export const email = z.string().email({ message: "Invalid email" });

export const lastName = z
  .string()
  .min(2, "Must have at least 2 characters")
  .regex(SPECIAL_CHARS_NUMERIC_REGEX, "Special characters are not allowed for this field");

export const password = z
  .string()
  .min(6, "Password must have at least 6 characters")
  .regex(CONTAINS_LETTER_REGEX, "Enter a letter")
  .regex(CONTAINS_NUMBER_REGEX, "Enter a number")
  .regex(CONTAINS_NON_ALPHANUMERIC_REGEX, "Enter a special character");

export const passwordOpt = password.optional().or(z.literal(""));

export const passwordLogin = z.string({
  required_error: `Invalid password`,
});

export const confirmPassword = z.string().min(6, { message: "Confirm Password is required" });
export const confirmPasswordOpt = confirmPassword.optional().or(z.literal(""));

export const companyNamePayment = z
  .string({
    required_error: "Please enter a valid company name",
  })
  .regex(SPECIAL_CHARS_REGEX, "Special characters are not allowed for this field.")
  .optional();

export const SimpleCreateAccountSchema = z.object({
  email,
  firstName,
  lastName,
  companyName,
});

export const CreateAccountModalSchema = z.object({
  email,
  firstName,
  lastName,
  companyName,
  password: passwordOpt,
  confirmPassword: confirmPasswordOpt,
});

export const CreateAccountSchema = z.object({
  email,
  firstName,
  lastName,
  companyName,
  password: passwordOpt,
  confirmPassword: confirmPasswordOpt,
});

export const CreateNewPassword = z.object({
  password,
  confirmPassword,
});

export const PersonalSchema = z.object({
  salutation,
  firstName,
  surname,
  phone,
  mobile,
  phones,
});

export const SimpleLoginSchema = z.object({
  email: z.string().email(),
  password: z.string().optional(),
});

export const LoginSchema = z.object({
  email,
  password: passwordLogin,
});

export const PasswordSchema = z.object({
  password,
});

export const EmailSchema = z.object({
  email,
});
