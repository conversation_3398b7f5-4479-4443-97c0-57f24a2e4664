import { IoMdRadioButtonOff, IoMdRadioButtonOn } from "react-icons/io";

const textColor = (type?: string) => {
  if (type === "disabled") {
    return "text-tonal-dark-cream-50";
  }

  if (type === "error") {
    return "text-error";
  }

  return "text-primary";
};

export const CustomRadio = ({
  checked,
  onChange,
  label,
  disabled = false,
  error,
}: {
  checked: boolean;
  onChange: () => void;
  label: string;
  disabled?: boolean;
  error?: boolean;
}) => {
  return (
    <div className="flex items-center">
      <label className="cursor-pointer flex items-center gap-2">
        <input type="radio" className="hidden" checked={checked} onChange={onChange} disabled={disabled} />
        {checked ? (
          <IoMdRadioButtonOn className={textColor((disabled && "disabled") || (error && "error") || "")} size={20} />
        ) : (
          <IoMdRadioButtonOff className={textColor((error && "error") || "disabled")} size={20} />
        )}
        <span
          className={`${
            checked ? (disabled ? "text-tonal-dark-cream-50" : "text-primary") : "text-tonal-dark-cream-50"
          } text-base/none`}
        >
          {label}
        </span>
      </label>
    </div>
  );
};
