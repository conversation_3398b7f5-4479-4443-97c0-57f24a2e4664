import { cn } from "@/lib/utils";
import { Check, Error } from "@arthursenno/lizenzero-ui-react/Icon";
import { Control, FieldPath, useWatch, useFormState } from "react-hook-form";
import { CgSpinnerAlt } from "react-icons/cg";

interface FormInputIconProps<TFieldValues extends Record<string, any>> {
  control: Control<TFieldValues>;
  name: FieldPath<TFieldValues>;
  mode?: "onChange" | "onSubmit";
  loading?: boolean;
  className?: string;
  successClassName?: string;
  errorClassName?: string;
}

export function FormInputIcon<TFieldValues extends Record<string, any>>({
  control,
  name,
  mode = "onChange",
  loading,
  className,
  successClassName,
  errorClassName,
}: FormInputIconProps<TFieldValues>) {
  const fieldValue = useWatch({ control, name });
  const { errors, isSubmitted } = useFormState({ control });
  const fieldError = errors[name as keyof typeof errors] as any;

  if (loading) return <CgSpinnerAlt size={20} className="animate-spin text-primary" />;

  if (mode === "onSubmit" && isSubmitted) {
    if (fieldError) return <Error className={cn("fill-error size-5", className, errorClassName)} />;
    return <Check className={cn("fill-success size-5", className, successClassName)} />;
  }

  if (mode === "onChange" && fieldValue !== undefined && fieldValue !== null && fieldValue !== "") {
    if (fieldError) return <Error className={cn("fill-error size-5", className, errorClassName)} />;
    return <Check className={cn("fill-success size-5", className, successClassName)} />;
  }

  return null;
}
