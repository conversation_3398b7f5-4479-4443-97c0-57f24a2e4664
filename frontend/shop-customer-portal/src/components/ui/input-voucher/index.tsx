import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { ChangeEvent } from "react";
import BtnPlus from "../btn-plus";

interface IInputVoucherProps {
  onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  value?: string;
  onClick?: () => void;
  onBlur?: () => void;
  errorMessage?: string;
  loading?: boolean;
}

export default function InputVoucher({
  onChange,
  value,
  placeholder,
  onClick,
  errorMessage,
  onBlur,
  loading,
}: IInputVoucherProps) {
  return (
    <div className="w-2/10">
      <Input
        value={value}
        style={{ borderRadius: "100px" }}
        placeholder={placeholder}
        rightIcon={<BtnPlus onClick={onClick} disabled={loading || Boolean(errorMessage)} />}
        onChange={onChange}
        variant={errorMessage ? "error" : "enabled"}
        errorMessage={errorMessage}
        onBlur={onBlur}
        enabled={!loading}
      />
    </div>
  );
}
