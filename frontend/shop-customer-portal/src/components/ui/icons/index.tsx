import { cn } from "@/lib/utils";

type IconProps = React.HTMLAttributes<SVGElement>;

export const Icons = {
  aluminum: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M60.6554 42.7932H16V84.6961H60.6554V42.7932Z" />
      <path d="M81.2026 11.3913L75.8875 6.08307H77.8873C79.4919 6.08307 80.793 4.78368 80.793 3.1811V2.90197C80.793 1.29939 79.4919 0 77.8873 0H43.438C41.8334 0 40.5323 1.29939 40.5323 2.90197V3.1811C40.5323 4.78368 41.8334 6.08307 43.438 6.08307H45.4378L40.1227 11.3913C38.9758 12.5367 38.3349 14.0863 38.3349 15.7034V24.6643H21.1031C19.4984 24.6643 18.1974 25.9637 18.1974 27.5663V27.8454C18.1974 29.448 19.4984 30.7474 21.1031 30.7474H22.7655L17.7878 35.7188C16.8288 36.6765 16.2265 37.9181 16.0578 39.2464H60.5976C60.4241 37.9181 59.8266 36.6716 58.8677 35.7188L53.8851 30.7426H55.5475C57.1522 30.7426 58.4533 29.4432 58.4533 27.8406V27.5615C58.4533 25.9589 57.1522 24.6595 55.5475 24.6595H48.2471C51.2781 19.2839 55.2054 16.0354 59.5037 16.0354C69.0882 16.0354 76.8609 32.196 76.8609 52.1344C76.8609 72.0728 70.3074 88.2333 60.7229 88.2333H16.0578C16.2313 89.5568 16.8336 90.7936 17.7878 91.7513L24.2593 98.2145C25.4062 99.3599 26.9579 100 28.577 100H48.0833C49.7024 100 51.254 99.3599 52.4009 98.2145L55.0127 95.6061H70.423C72.0422 95.6061 73.5938 94.9661 74.7407 93.8207L81.2122 87.3574C82.3591 86.212 83 84.6624 83 83.0454V15.7034C83 14.0863 82.3591 12.5367 81.2122 11.3913H81.2026Z" />
    </svg>
  ),
  miscellaneous: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M65.4218 25.6965C62.4771 23.8895 60.8416 20.5636 61.324 17.1554L62.8768 6.16222C63.3362 2.90498 60.8003 0 57.4972 0H45.36C42.0569 0 39.521 2.90956 39.9804 6.16222L40.8992 12.6721C41.1381 14.3511 39.8288 15.8516 38.1244 15.8516H31.7067C21.5034 15.8516 13 23.9901 13 34.1553C13 39.4437 15.2602 44.2106 18.8711 47.5594C20.8052 49.3527 21.9353 51.8596 21.8756 54.4901C21.8664 54.7921 21.8664 55.094 21.8664 55.4005C21.8664 65.0899 25.4543 80.0448 31.1967 92.1543C31.2105 92.1863 31.2289 92.2229 31.2473 92.2549C33.5397 97.0218 38.446 99.9954 43.7521 99.9954H44.6709C46.9081 98.8883 48.4471 96.5918 48.4471 93.9339V80.0677C47.6661 80.2507 46.853 80.3559 46.0123 80.3559C40.2193 80.3559 35.5243 75.6805 35.5243 69.9117C35.5243 67.391 36.4201 65.0807 37.9131 63.2783C37.5869 62.3816 37.4078 61.4163 37.4078 60.4053C37.4078 56.4985 40.0769 53.2138 43.6969 52.2531C43.7291 47.5502 47.5697 43.744 52.3014 43.744C57.0332 43.744 60.8462 47.5273 60.9014 52.2119C64.6087 53.1131 67.3559 56.439 67.3559 60.4053C67.3559 61.4163 67.1767 62.3816 66.8506 63.2783C68.3436 65.0807 69.2394 67.391 69.2394 69.9117C69.2394 75.6805 64.5444 80.3559 58.7514 80.3559C57.0792 80.3559 55.4988 79.9625 54.0931 79.2717V93.9384C54.0931 96.5964 55.6321 98.8883 57.8693 100H59.1143C64.4203 100 69.3267 97.0264 71.6191 92.2595C71.6375 92.2275 71.6513 92.1909 71.6696 92.1588C77.4121 80.0448 81 65.0945 81 55.4051C81 42.5591 74.6971 31.3967 65.4264 25.7057L65.4218 25.6965ZM37.4859 25.6645C37.4859 25.6645 37.4537 25.6828 37.4353 25.6965C31.9318 29.0727 27.4756 34.384 24.7606 40.8253C24.0807 42.4356 21.8847 42.7147 20.8924 41.2782C19.4913 39.2378 18.6736 36.772 18.6827 34.1187C18.7057 27.1101 24.586 21.5152 31.624 21.5152H36.2409C38.4966 21.5152 39.397 24.475 37.4813 25.6645H37.4859Z" />
    </svg>
  ),
  beveragePackaging: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M77.0756 19.3752L64.0904 6.24318V2.2872C64.0904 1.0231 63.0787 0 61.8288 0H58.7848C57.5348 0 56.5232 1.0231 56.5232 2.2872V6.82066L51.5953 11.8043H32.1309C29.5905 11.8043 27.5357 13.8869 27.5357 16.4514V24.1361H32.4322V27.0053H48.6277V24.1361H51.5368C52.7148 24.1361 53.8074 24.7772 54.3874 25.8139L61.0644 37.6955H20V93.3658C20 97.0307 22.9361 100 26.5601 100H54.5043C58.1283 100 61.0644 97.0307 61.0644 93.3658V92.3927H72.4399C76.0639 92.3927 79 89.4234 79 85.7585V24.0678C79 22.3081 78.3076 20.6211 77.0801 19.3752H77.0756ZM40.5299 17.952C45.0037 17.952 48.6277 19.0069 48.6277 20.3119C48.6277 21.617 45.0037 22.6719 40.5299 22.6719C36.0562 22.6719 32.4322 21.617 32.4322 20.3119C32.4322 19.0069 36.0562 17.952 40.5299 17.952Z" />
    </svg>
  ),
  glass: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M75 42.0916C74.9156 36.9305 72.237 32.3964 68.1947 29.6996C66.3912 28.4981 65.3339 26.4679 65.3339 24.3192V1.70138C65.3339 0.762991 64.561 0 63.6104 0H55.5169C54.5662 0 53.7933 0.762991 53.7933 1.70138V24.3455C53.7933 26.4986 52.7094 28.5113 50.9059 29.7215C48.0985 31.6027 45.953 34.3697 44.8824 37.6146C44.5137 36.8647 44.1361 36.1193 43.7363 35.3826C42.5592 33.212 41.9417 30.7871 41.9417 28.3271V14.4617C41.9417 13.0498 40.7823 11.9053 39.352 11.9053H35.9271C34.4967 11.9053 33.3373 13.0498 33.3373 14.4617V28.3271C33.3373 30.7871 32.7199 33.212 31.5427 35.3826C26.5898 44.5166 24 54.7161 24 65.0778V91.3308V91.8264H24.0133C24.2265 97.4172 28.0912 99.4168 33.3818 100V62.3065C33.3818 59.5527 35.6428 57.3164 38.4369 57.3164H38.8633V47.2221C38.8633 46.0645 39.814 45.1261 40.9867 45.1261H44.687C45.8597 45.1261 46.8103 46.0645 46.8103 47.2221V57.3164H47.2367C50.0264 57.3164 52.2919 59.5483 52.2919 62.3065V91.0502C52.2919 97.2857 57.1782 99.9737 62.5887 99.9737H64.6854C68.2302 99.9737 71.5485 98.8204 73.4186 96.2684C73.4275 96.2552 73.4364 96.2464 73.4408 96.2377C73.4808 96.1807 73.5208 96.1193 73.5608 96.0579C73.8095 95.6852 74.0272 95.2949 74.2137 94.8871C74.7024 93.7996 74.9778 92.5236 74.9778 91.0502L74.9956 42.096L75 42.0916Z" />
    </svg>
  ),
  plastics: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M75.7685 14.832H67.3822V9.62358C67.3822 4.31434 63.3219 0 58.3313 0H42.3377C37.3472 0 33.2869 4.31892 33.2869 9.62358V14.832H24.2315C20.24 14.832 17 18.0689 17 22.0668V92.7651C17 96.7585 20.2354 100 24.2315 100H26.894C30.8856 100 34.1255 96.7631 34.1255 92.7651V65.2698C34.1255 58.7685 37.9017 53.1521 43.378 50.4883V49.0303C43.378 46.0823 45.4402 43.6202 48.199 43.0012V35.3629H42.576C41.2791 35.3629 40.2297 34.313 40.2297 33.0155C40.2297 31.7179 41.2791 30.668 42.576 30.668H54.7889C61.7409 30.668 67.3959 36.3257 67.3959 43.2809V43.932C67.3959 45.2295 66.3465 46.2794 65.0496 46.2794C63.7527 46.2794 62.7032 45.2295 62.7032 43.932V43.2809C62.7032 38.9161 59.1516 35.3629 54.7889 35.3629H52.8871V43.0012C55.6459 43.6202 57.7081 46.0823 57.7081 49.0303V50.4883C63.1844 53.1521 66.9606 58.7685 66.9606 65.2698V93.1686C66.9606 96.4697 64.6921 99.2389 61.6309 100H75.7685C79.76 100 83 96.7631 83 92.7651V22.0668C83 18.0735 79.7646 14.832 75.7685 14.832ZM38.0712 14.832V9.62358C38.0712 6.95521 39.9868 4.78199 42.3377 4.78199H58.3359C60.6914 4.78199 62.6024 6.95521 62.6024 9.62358V14.832H38.0712Z" />
    </svg>
  ),
  play: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 27 30"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M0.857422 24.6407V4.3593C0.857422 1.1879 4.37171 -0.722218 7.033 1.00269L22.6786 11.1434C25.1112 12.72 25.1112 16.28 22.6786 17.8566L7.033 27.9973C4.37171 29.7222 0.857422 27.8121 0.857422 24.6407Z" />
    </svg>
  ),
  ferrousMetals: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M88.6272 37.5412C86.7119 36.9865 84.269 36.4065 81.1668 35.8455C74.1083 34.5658 65.4735 33.7526 56.3489 33.4942C59.4511 28.054 61.4984 20.2058 61.4984 17.5708C61.4984 13.3914 56.3489 10 50 10C43.6511 10 38.5016 13.3914 38.5016 17.5708C38.5016 20.2058 40.5489 28.054 43.6511 33.4942C34.5265 33.7526 25.8917 34.5658 18.8332 35.8455C15.731 36.4065 13.2881 36.9928 11.3728 37.5412C9.87189 37.9761 9.87189 40.1194 11.3728 40.5544C13.2881 41.1091 15.731 41.6891 18.8332 42.2501C27.5119 43.8261 38.577 44.6897 49.9937 44.6897C61.4104 44.6897 72.4818 43.8197 81.1542 42.2501C84.2565 41.6891 86.6993 41.1028 88.6147 40.5544C90.1155 40.1194 90.1155 37.9761 88.6147 37.5412H88.6272ZM43.0733 17.5582C43.0733 15.8373 46.1756 14.4442 50.0063 14.4442C53.837 14.4442 56.9392 15.8373 56.9392 17.5582C56.9392 19.2792 53.837 20.6723 50.0063 20.6723C46.1756 20.6723 43.0733 19.2792 43.0733 17.5582ZM50.0063 34.2317C47.9905 34.2317 46.3577 32.5927 46.3577 30.5692C46.3577 28.5457 47.9905 26.9067 50.0063 26.9067C52.0221 26.9067 53.6549 28.5457 53.6549 30.5692C53.6549 32.5927 52.0221 34.2317 50.0063 34.2317Z" />
      <path d="M50.0063 49.884C22.3876 49.884 0 45.0364 0 39.0478V80.0286C0.144436 85.9857 22.4755 90.8081 50 90.8081C77.5245 90.8081 99.8618 85.9857 100 80.0286V39.0478C100 45.0364 77.6124 49.884 49.9937 49.884H50.0063ZM76.3753 75.0928C76.3753 77.0154 74.9121 78.6229 72.9967 78.7805C65.7058 79.3793 57.9314 79.6882 49.9498 79.6882C41.9681 79.6882 34.269 79.3793 27.0095 78.7868C25.1005 78.6292 23.631 77.028 23.631 75.0991V64.8365C23.631 62.6743 25.471 60.9723 27.6124 61.1551C34.0744 61.7036 41.5034 62.044 49.9435 62.044C58.3836 62.044 65.9005 61.7036 72.3813 61.1488C74.529 60.966 76.3627 62.668 76.3627 64.8302V75.0928H76.3753Z" />
    </svg>
  ),
  cardboard: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M57.4374 32.5268H57.4817V57.9868L53.2698 55.543L49.0579 57.9868L44.8459 55.543L40.634 57.9868V32.5268H40.6783L41.9198 22.4184H4.8548L1.61827 28.7501C0.5542 30.8384 0 33.1267 0 35.4816V85.0463C0 88.8897 3.10353 92 6.9386 92H93.0614C96.8965 92 100 88.8897 100 85.0463V35.415C100 33.1267 99.468 30.8606 98.4482 28.8167L95.256 22.4184H56.2181L57.4595 32.5268H57.4374Z" />
      <path d="M91.2658 14.4427L89.2707 10.4438C88.517 8.9553 87.0095 8 85.3469 8H54.4669L55.2427 14.4427H91.2658Z" />
      <path d="M43.6932 8.02222H14.9191C13.2787 8.02222 11.7712 8.9553 11.0175 10.4216L8.95589 14.465H42.9173L43.6932 8.02222Z" />
    </svg>
  ),
  compositePackaging: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M25.0695 81.5126L26.2049 92.9582C26.2049 95.9197 36.8595 100 50.0055 100C63.1515 100 73.8061 95.9197 73.8061 92.9582L74.9415 81.5126C68.8894 83.1578 59.9656 84.1944 50.011 84.1944C40.0565 84.1944 31.1271 83.1524 25.075 81.5126H25.0695Z" />
      <path d="M17 21.8274V24.8821C17 25.9899 17.9866 27.0484 19.7615 28.0026L22.771 58.358C28.7184 60.4036 38.695 61.7418 50 61.7418C61.305 61.7418 71.2816 60.4036 77.229 58.358L80.2385 28.0026C82.0134 27.0484 83 25.9899 83 24.8821V21.8274C75.7573 25.8802 59.6294 27.0922 50 27.0922C40.3706 27.0922 24.2427 25.8802 17 21.8274Z" />
      <path d="M80.1007 13.4145L76.3691 2.61051C76.3691 6.04365 64.5625 8.82966 50 8.82966C35.4374 8.82966 23.6474 6.04914 23.6309 2.61599L19.9048 13.409C18.0362 14.3852 17 15.4656 17 16.6063V17.2206C17.3583 17.8677 19.2709 19.5185 25.7419 21.0486C32.2019 22.5732 40.8171 23.4123 50 23.4123C59.1829 23.4123 67.7981 22.5732 74.2581 21.0486C80.7291 19.524 82.6417 17.8677 83 17.2206V16.6063C83 15.4656 81.9638 14.3852 80.1007 13.409V13.4145Z" />
      <path d="M50 5.22102C42.9557 5.22102 37.3997 4.59033 33.4862 3.79511C32.2019 3.53735 32.2019 1.68915 33.4862 1.42591C37.3997 0.63069 42.9557 0 50 0C57.0443 0 62.6003 0.63069 66.5138 1.42591C67.7981 1.68367 67.7981 3.53186 66.5138 3.79511C62.6003 4.59033 57.0443 5.22102 50 5.22102Z" />
    </svg>
  ),
  calculator: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M76.8894 0H23.1106C15.3437 0 9 6.31324 9 14.0429V85.9571C9 93.6868 15.3437 100 23.1106 100H76.8894C84.6563 100 91 93.6868 91 85.9571V14.0429C91 6.31324 84.6563 0 76.8894 0ZM38.4818 78.5512C37.5465 79.482 36.2655 80.0081 34.9236 80.0081C33.5817 80.0081 32.3211 79.482 31.3655 78.5512C30.4302 77.6204 29.9016 76.3456 29.9016 75.0101C29.9016 73.6746 30.4302 72.4201 31.3655 71.469C32.3008 70.5382 33.5817 70.0121 34.9236 70.0121C36.2655 70.0121 37.5261 70.5382 38.4818 71.469C39.417 72.3998 39.9457 73.6746 39.9457 75.0101C39.9457 76.3456 39.417 77.6002 38.4818 78.5512ZM38.4818 58.5391C37.5465 59.4699 36.2655 59.996 34.9236 59.996C33.5817 59.996 32.3211 59.4699 31.3655 58.5391C30.4302 57.6083 29.9016 56.3335 29.9016 54.998C29.9016 53.6625 30.4302 52.408 31.3655 51.4569C32.3008 50.5261 33.5817 50 34.9236 50C36.2655 50 37.5261 50.5261 38.4818 51.4569C39.417 52.3877 39.9457 53.6625 39.9457 54.998C39.9457 56.3335 39.417 57.5881 38.4818 58.5391ZM53.548 78.5512C52.6127 79.482 51.3318 80.0081 49.9898 80.0081C48.6479 80.0081 47.3873 79.482 46.4317 78.5512C45.4964 77.6204 44.9678 76.3456 44.9678 75.0101C44.9678 73.6746 45.4964 72.4201 46.4317 71.469C47.367 70.5382 48.6479 70.0121 49.9898 70.0121C51.3318 70.0121 52.5924 70.5382 53.548 71.469C54.4833 72.3998 55.0119 73.6746 55.0119 75.0101C55.0119 76.3456 54.4833 77.6002 53.548 78.5512ZM53.548 58.5391C52.6127 59.4699 51.3318 59.996 49.9898 59.996C48.6479 59.996 47.3873 59.4699 46.4317 58.5391C45.4964 57.6083 44.9678 56.3335 44.9678 54.998C44.9678 53.6625 45.4964 52.408 46.4317 51.4569C47.367 50.5261 48.6479 50 49.9898 50C51.3318 50 52.5924 50.5261 53.548 51.4569C54.4833 52.3877 55.0119 53.6625 55.0119 54.998C55.0119 56.3335 54.4833 57.5881 53.548 58.5391ZM68.6142 78.5512C67.6789 79.482 66.398 80.0081 65.056 80.0081C63.7141 80.0081 62.4535 79.482 61.4979 78.5512C60.5626 77.6204 60.034 76.3456 60.034 75.0101C60.034 73.6746 60.5626 72.4201 61.4979 71.469C62.4332 70.5382 63.7141 70.0121 65.056 70.0121C66.398 70.0121 67.6586 70.5382 68.6142 71.469C69.5495 72.3998 70.0781 73.6746 70.0781 75.0101C70.0781 76.3456 69.5495 77.6002 68.6142 78.5512ZM68.6142 58.5391C67.6789 59.4699 66.398 59.996 65.056 59.996C63.7141 59.996 62.4535 59.4699 61.4979 58.5391C60.5626 57.6083 60.034 56.3335 60.034 54.998C60.034 53.6625 60.5626 52.408 61.4979 51.4569C62.4332 50.5261 63.7141 50 65.056 50C66.398 50 67.6586 50.5261 68.6142 51.4569C69.5495 52.3877 70.0781 53.6625 70.0781 54.998C70.0781 56.3335 69.5495 57.5881 68.6142 58.5391ZM69.4072 27.499C68.5125 29.0368 66.8453 30.0081 65.056 30.0081H34.9033C33.1141 30.0081 31.4468 29.057 30.5522 27.499C29.6576 25.9611 29.6576 24.0388 30.5522 22.501C31.4468 20.9632 33.1141 19.9919 34.9033 19.9919H65.056C66.8453 19.9919 68.5125 20.9429 69.4072 22.501C70.3018 24.0591 70.3018 25.9611 69.4072 27.499Z" />
    </svg>
  ),
  questionMarkCalculator: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M23.1106 0H76.8894C84.6563 0 91 6.31324 91 14.0429V85.9571C91 93.6868 84.6563 100 76.8894 100H23.1106C15.3437 100 9 93.6868 9 85.9571V14.0429C9 6.31324 15.3437 0 23.1106 0ZM34.9236 80.0081C36.2655 80.0081 37.5465 79.482 38.4818 78.5512C39.417 77.6002 39.9457 76.3456 39.9457 75.0101C39.9457 73.6746 39.417 72.3998 38.4818 71.469C37.5261 70.5382 36.2655 70.0121 34.9236 70.0121C33.5817 70.0121 32.3008 70.5382 31.3655 71.469C30.4302 72.4201 29.9016 73.6746 29.9016 75.0101C29.9016 76.3456 30.4302 77.6204 31.3655 78.5512C32.3211 79.482 33.5817 80.0081 34.9236 80.0081ZM34.9236 59.996C36.2655 59.996 37.5465 59.4699 38.4818 58.5391C39.417 57.5881 39.9457 56.3335 39.9457 54.998C39.9457 53.6625 39.417 52.3877 38.4818 51.4569C37.5261 50.5261 36.2655 50 34.9236 50C33.5817 50 32.3008 50.5261 31.3655 51.4569C30.4302 52.408 29.9016 53.6625 29.9016 54.998C29.9016 56.3335 30.4302 57.6083 31.3655 58.5391C32.3211 59.4699 33.5817 59.996 34.9236 59.996ZM49.9898 80.0081C51.3318 80.0081 52.6127 79.482 53.548 78.5512C54.4833 77.6002 55.0119 76.3456 55.0119 75.0101C55.0119 73.6746 54.4833 72.3998 53.548 71.469C52.5924 70.5382 51.3318 70.0121 49.9898 70.0121C48.6479 70.0121 47.367 70.5382 46.4317 71.469C45.4964 72.4201 44.9678 73.6746 44.9678 75.0101C44.9678 76.3456 45.4964 77.6204 46.4317 78.5512C47.3873 79.482 48.6479 80.0081 49.9898 80.0081ZM49.9898 59.996C51.3318 59.996 52.6127 59.4699 53.548 58.5391C54.4833 57.5881 55.0119 56.3335 55.0119 54.998C55.0119 53.6625 54.4833 52.3877 53.548 51.4569C52.5924 50.5261 51.3318 50 49.9898 50C48.6479 50 47.367 50.5261 46.4317 51.4569C45.4964 52.408 44.9678 53.6625 44.9678 54.998C44.9678 56.3335 45.4964 57.6083 46.4317 58.5391C47.3873 59.4699 48.6479 59.996 49.9898 59.996ZM65.056 80.0081C66.398 80.0081 67.6789 79.482 68.6142 78.5512C69.5495 77.6002 70.0781 76.3456 70.0781 75.0101C70.0781 73.6746 69.5495 72.3998 68.6142 71.469C67.6586 70.5382 66.398 70.0121 65.056 70.0121C63.7141 70.0121 62.4332 70.5382 61.4979 71.469C60.5626 72.4201 60.034 73.6746 60.034 75.0101C60.034 76.3456 60.5626 77.6204 61.4979 78.5512C62.4535 79.482 63.7141 80.0081 65.056 80.0081ZM65.056 59.996C66.398 59.996 67.6789 59.4699 68.6142 58.5391C69.5495 57.5881 70.0781 56.3335 70.0781 54.998C70.0781 53.6625 69.5495 52.3877 68.6142 51.4569C67.6586 50.5261 66.398 50 65.056 50C63.7141 50 62.4332 50.5261 61.4979 51.4569C60.5626 52.408 60.034 53.6625 60.034 54.998C60.034 56.3335 60.5626 57.6083 61.4979 58.5391C62.4535 59.4699 63.7141 59.996 65.056 59.996ZM50.0593 16C58.1271 16 64 21.2941 64 28.5963C64 32.4909 62.4576 35.5335 59.3136 38.3935C58.808 38.8456 58.2629 39.286 57.7195 39.725C55.7761 41.2951 53.8559 42.8465 53.8559 44.8438V46H45.2542V43.931C45.2542 41.071 47.2119 38.0284 51.0085 35.3509C53.7966 33.4037 55.161 31.3955 55.161 29.3266C55.161 26.7099 53.0847 24.5193 50 24.5193C46.9153 24.5193 44.661 26.9533 44.6017 30.3002H36C36 21.9026 41.6949 16 50.0593 16Z"
      />
    </svg>
  ),
  lightBulb: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M57.093 91.2383C56.0994 91.2383 55.2965 90.4361 55.2965 89.4434C55.2965 88.4507 56.0994 87.6484 57.093 87.6484H59.2977V84.0631H57.093C56.0994 84.0631 55.2965 83.2608 55.2965 82.2682C55.2965 81.2755 56.0994 80.4732 57.093 80.4732H59.4656C59.6017 79.272 59.8149 78.08 60.1143 76.9151H40.8928C41.2104 78.08 41.4463 79.2675 41.6005 80.4732H43.9369C44.9304 80.4732 45.7333 81.2755 45.7333 82.2682C45.7333 83.2608 44.9304 84.0631 43.9369 84.0631H41.8001V87.6484H43.9369C44.9304 87.6484 45.7333 88.4507 45.7333 89.4434C45.7333 90.4361 44.9304 91.2383 43.9369 91.2383H41.8273C42.0542 93.8174 43.8733 95.9297 46.2959 96.6096C46.2959 96.6458 46.2868 96.6775 46.2868 96.7138C46.2868 98.5269 48.1967 100 50.5512 100C52.9057 100 54.8156 98.5269 54.8156 96.7138C54.8156 96.6775 54.8111 96.6458 54.8065 96.6096C57.229 95.9297 59.0482 93.8174 59.2751 91.2383H57.093Z" />
      <path d="M50.1021 25.7864C37.6673 25.9587 27.5144 36.0937 27.3239 48.5178C27.2286 54.9995 29.8054 60.883 34.0289 65.1346C36.4061 67.5279 38.2979 70.3109 39.6226 73.3388H61.3165C62.5777 70.3291 64.4059 67.5641 66.7559 65.2162C70.9477 61.0371 73.5426 55.2579 73.5426 48.8714C73.5426 36.0076 63.0177 25.6051 50.1021 25.7819V25.7864Z" />
      <path d="M49.9977 19.1687C48.3918 19.1687 47.0898 17.8678 47.0898 16.2633V2.90545C47.0898 1.30088 48.3918 0 49.9977 0C51.6037 0 52.9057 1.30088 52.9057 2.90545V16.2678C52.9057 17.8724 51.6037 19.1732 49.9977 19.1732V19.1687Z" />
      <path d="M74.9217 26.217C74.1777 26.217 73.4337 25.9315 72.8667 25.3649C71.7325 24.2317 71.7325 22.3914 72.8667 21.2583L82.3209 11.8122C83.4551 10.679 85.2969 10.679 86.4311 11.8122C87.5652 12.9453 87.5652 14.7856 86.4311 15.9188L76.9768 25.3649C76.4097 25.9315 75.6657 26.217 74.9217 26.217Z" />
      <path d="M97.092 51.4097H83.7182C82.1122 51.4097 80.8102 50.1088 80.8102 48.5042C80.8102 46.8997 82.1122 45.5988 83.7182 45.5988H97.092C98.698 45.5988 100 46.8997 100 48.5042C100 50.1088 98.698 51.4097 97.092 51.4097Z" />
      <path d="M25.0737 26.217C24.3297 26.217 23.5857 25.9315 23.0186 25.3649L13.5644 15.9188C12.4303 14.7856 12.4303 12.9453 13.5644 11.8122C14.6985 10.679 16.5404 10.679 17.6745 11.8122L27.1288 21.2583C28.2629 22.3914 28.2629 24.2317 27.1288 25.3649C26.5617 25.9315 25.8177 26.217 25.0737 26.217Z" />
      <path d="M2.90795 51.4097C1.302 51.4097 0 50.1088 0 48.5042C0 46.8997 1.302 45.5988 2.90795 45.5988H16.2818C17.8878 45.5988 19.1898 46.8997 19.1898 48.5042C19.1898 50.1088 17.8878 51.4097 16.2818 51.4097H2.90795Z" />
    </svg>
  ),
  pencil: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M88.5271 45.2015C84.7612 45.2015 81.7158 48.2652 81.7158 52.0537C81.7158 70.9384 66.4409 86.3052 47.6691 86.3052C28.8974 86.3052 13.6225 70.9384 13.6225 52.0537C13.6225 33.169 28.8974 17.8023 47.6691 17.8023C48.8316 17.8023 50.0036 17.8602 51.1564 17.9762C54.8935 18.3628 58.2367 15.6181 58.621 11.8488C59.0052 8.0893 56.2769 4.72601 52.5302 4.33942C50.9259 4.17512 49.2831 4.08814 47.6691 4.08814C21.3848 4.08814 0 25.6016 0 52.0441C0 78.4865 21.3848 100 47.6691 100C73.9535 100 95.3383 78.4865 95.3383 52.0441C95.3383 48.2555 92.2929 45.1918 88.5271 45.1918V45.2015Z" />
      <path d="M97.6439 3.27631L96.6929 2.31951C93.6187 -0.773171 88.6423 -0.773171 85.5681 2.31951L47.7748 40.3402C46.3145 41.8092 46.3145 44.1771 47.7748 45.6461L54.7013 52.6143C56.1616 54.0833 58.5152 54.0833 59.9755 52.6143L97.6439 14.7192C100.785 11.5589 100.785 6.42698 97.6439 3.25698V3.27631Z" />
      <path d="M40.6369 49.6376L34.0947 64.2505C33.4414 65.7195 34.9401 67.2175 36.4003 66.541L50.8106 59.8338C52.6263 58.9833 53.0586 56.5768 51.6368 55.1561L45.3059 48.7871C43.8841 47.3567 41.4727 47.8013 40.6465 49.6472L40.6369 49.6376Z" />
    </svg>
  ),
  login: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M70.2629 49.4088C67.872 46.8775 64.0943 46.0876 60.9096 47.5151C57.4188 49.0757 53.555 49.9417 49.4904 49.9417C45.4258 49.9417 41.5525 49.0757 38.0713 47.5151C34.8866 46.0876 31.1089 46.8775 28.718 49.4088C23.9361 54.481 21 61.3042 21 68.8031V90.4718C21 94.3259 23.8117 97.6185 27.6372 98.2371C42.1168 100.588 56.8832 100.588 71.3627 98.2371C75.1883 97.6185 78 94.3259 78 90.4718V68.8031C78 61.2947 75.0639 54.481 70.282 49.4088H70.2629Z" />
      <path d="M49.4904 44.2319C61.7656 44.2319 71.7166 34.3303 71.7166 22.116C71.7166 9.90165 61.7656 0 49.4904 0C37.2153 0 27.2642 9.90165 27.2642 22.116C27.2642 34.3303 37.2153 44.2319 49.4904 44.2319Z" />
    </svg>
  ),
  regulator: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M53.9474 76.9737V96.0526C53.9474 98.2895 52.2368 100 50 100C47.7632 100 46.0526 98.2895 46.0526 96.0526V76.9737C40.6579 75.2632 36.8421 70.2632 36.8421 64.4737C36.8421 58.6842 40.6579 53.5526 46.0526 51.9737V3.94737C46.0526 1.71053 47.7632 0 50 0C52.2368 0 53.9474 1.71053 53.9474 3.94737V51.9737C59.3421 53.6842 63.1579 58.6842 63.1579 64.4737C63.1579 70.2632 59.3421 75.3947 53.9474 76.9737ZM50 59.2082C47.1053 59.2082 44.7368 61.5766 44.7368 64.4713C44.7368 67.3661 47.1053 69.7345 50 69.7345C52.8947 69.7345 55.2632 67.3661 55.2632 64.4713C55.2632 61.5766 52.8947 59.2082 50 59.2082Z" />
      <path d="M82.8947 26.9737V3.94737C82.8947 1.71053 84.6053 0 86.8421 0C89.0789 0 90.7895 1.71053 90.7895 3.94737V26.9737C96.1842 28.6842 100 33.6842 100 39.4737C100 45.2632 96.1842 50.3947 90.7895 51.9737V96.0526C90.7895 98.2895 89.0789 100 86.8421 100C84.6053 100 82.8947 98.2895 82.8947 96.0526V51.9737C77.5 50.2632 73.6842 45.2632 73.6842 39.4737C73.6842 33.6842 77.5 28.5526 82.8947 26.9737ZM86.8421 44.7368C89.7368 44.7368 92.1053 42.3684 92.1053 39.4737C92.1053 36.5789 89.7368 34.2105 86.8421 34.2105C83.9474 34.2105 81.5789 36.5789 81.5789 39.4737C81.5789 42.3684 83.9474 44.7368 86.8421 44.7368Z" />
      <path d="M17.1053 3.94737V26.9737C22.5 28.6842 26.3158 33.6842 26.3158 39.4737C26.3158 45.2632 22.5 50.3947 17.1053 51.9737V96.0526C17.1053 98.2895 15.3947 100 13.1579 100C10.9211 100 9.21053 98.2895 9.21053 96.0526V51.9737C3.81579 50.2632 0 45.2632 0 39.4737C0 33.6842 3.81579 28.5526 9.21053 26.9737V3.94737C9.21053 1.71053 10.9211 0 13.1579 0C15.3947 0 17.1053 1.71053 17.1053 3.94737ZM13.1579 44.7368C16.0526 44.7368 18.4211 42.3684 18.4211 39.4737C18.4211 36.5789 16.0526 34.2105 13.1579 34.2105C10.2632 34.2105 7.89474 36.5789 7.89474 39.4737C7.89474 42.3684 10.2632 44.7368 13.1579 44.7368Z" />
    </svg>
  ),
  badge: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M86.4425 39.3459C85.7007 37.6767 85.7007 35.7759 86.4425 34.1067C87.517 31.6908 87.0113 28.8804 85.1585 26.9699C83.8779 25.6495 83.2159 23.8661 83.329 22.0469C83.4953 19.4158 82.0384 16.9445 79.6334 15.7708C77.9668 14.959 76.7228 13.5049 76.1972 11.7574C75.4354 9.23068 73.2067 7.39514 70.5356 7.09846C68.6895 6.89306 67.0096 5.94431 65.9053 4.48045C64.3086 2.36126 61.5709 1.38318 58.9597 2.00263C57.1535 2.42973 55.2441 2.10044 53.694 1.09301C51.4553 -0.364337 48.5414 -0.364337 46.3027 1.09301C44.7526 2.10044 42.8465 2.42973 41.0403 2.00263C38.4257 1.38644 35.6914 2.36126 34.0948 4.48045C32.9904 5.94757 31.3139 6.89632 29.4644 7.09846C26.7933 7.39514 24.5613 9.22742 23.8028 11.7574C23.2773 13.5049 22.0332 14.959 20.3666 15.7708C17.9583 16.9445 16.5047 19.4158 16.671 22.0469C16.7874 23.8661 16.1255 25.6495 14.8415 26.9699C12.9887 28.8804 12.483 31.694 13.5575 34.1067C14.2993 35.7759 14.2993 37.6767 13.5575 39.3459C12.483 41.7618 12.9887 44.5722 14.8415 46.4827C16.1221 47.8031 16.7841 49.5865 16.671 51.4057C16.5047 54.0368 17.9616 56.5081 20.3666 57.6818C22.0332 58.4936 23.2773 59.9477 23.8028 61.6952C24.5646 64.2219 26.7933 66.0574 29.4644 66.3541C31.3105 66.5595 32.9904 67.5083 34.0948 68.9754C35.6914 71.0946 38.4291 72.0727 41.0403 71.4532C42.8465 71.0261 44.7559 71.3554 46.3027 72.3628C48.5414 73.8202 51.4553 73.8202 53.694 72.3628C55.2441 71.3554 57.1501 71.0261 58.9597 71.4532C61.5743 72.0694 64.3086 71.0946 65.9053 68.9754C67.0096 67.5083 68.6861 66.5595 70.5356 66.3541C73.2067 66.0574 75.4388 64.2252 76.1972 61.6952C76.7228 59.9477 77.9668 58.4936 79.6334 57.6818C82.0417 56.5081 83.4953 54.0368 83.329 51.4057C83.2126 49.5865 83.8745 47.8031 85.1585 46.4827C87.0113 44.5722 87.517 41.7618 86.4425 39.3459ZM70.9248 32.62L62.2595 40.8978L64.3052 52.5892C64.6512 54.5715 62.5289 56.0842 60.7127 55.1485L50.0017 49.6289L39.2906 55.1485C37.4744 56.0842 35.3521 54.5715 35.6981 52.5892L37.7438 40.8978L29.0785 32.62C27.6082 31.2148 28.4199 28.7696 30.4523 28.4794L42.4274 26.7743L47.7829 16.1392C48.6911 14.3363 51.3156 14.3363 52.2237 16.1392L57.5792 26.7743L69.5543 28.4794C71.5868 28.7696 72.3951 31.2148 70.9281 32.62H70.9248Z"
      />
      <path d="M41.8193 74.6212C37.8975 75.5471 33.816 74.0931 31.4176 70.911C30.8654 70.1775 30.0172 69.6982 29.0925 69.5939C27.3095 69.3982 25.6663 68.7462 24.2858 67.7518L13.6346 89.8826C13.0791 91.0368 14.0238 92.3441 15.3211 92.2105L24.1261 91.3074C24.7448 91.2454 25.3502 91.5225 25.6929 92.0311L30.5827 99.2657C31.3012 100.332 32.9345 100.214 33.49 99.0603L44.9029 75.3418L44.8484 75.3102C44.7196 75.2358 44.5891 75.1605 44.4638 75.0777C43.6888 74.5723 42.7241 74.406 41.8193 74.6212Z" />
      <path d="M58.1823 74.6197C62.1041 75.5456 66.1856 74.0916 68.584 70.9095L68.5873 70.9063C69.1395 70.1727 69.9877 69.6902 70.9125 69.5891C72.6921 69.3935 74.3387 68.7414 75.7191 67.747L86.367 89.8811C86.9225 91.0353 85.9778 92.3426 84.6805 92.2089L75.8755 91.3059C75.2568 91.2406 74.6513 91.521 74.3087 92.0296L69.4189 99.2642C68.6971 100.33 67.0671 100.213 66.5116 99.0588L55.0987 75.3402C55.1475 75.3109 55.1966 75.2823 55.2458 75.2537C55.3441 75.1964 55.4424 75.1392 55.5378 75.0762C56.3128 74.5708 57.2775 74.4045 58.1823 74.6197Z" />
    </svg>
  ),
  globe: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M68.7566 12.3921C67.1035 8.85851 65.2365 5.87602 63.1993 3.47842V3.48325C62.6207 2.80167 63.2819 1.7914 64.1425 2.03792C69.4567 3.57993 74.4112 5.96786 78.8405 9.05187C79.4483 9.47725 79.317 10.415 78.6072 10.6422C75.9038 11.522 72.9477 12.2857 69.7631 12.9093C69.3498 12.9866 68.9316 12.7691 68.7566 12.3921Z" />
      <path d="M36.921 3.48481C34.8887 5.86791 33.0265 8.84074 31.3783 12.355V12.3598C31.2032 12.7368 30.7851 12.9544 30.3718 12.8722C27.2066 12.2438 24.2651 11.4752 21.5812 10.5954C20.8762 10.3634 20.7401 9.43047 21.3527 9.00509C25.7626 5.95009 30.6878 3.57666 35.9729 2.04432C36.8335 1.79296 37.4996 2.80323 36.921 3.48481Z" />
      <path d="M38.9685 13.1861C42.2837 7.07737 46.2599 3.602 50.0564 3.60052C53.8578 3.602 57.8437 7.08704 61.1589 13.2103C61.3923 13.6405 61.1152 14.177 60.6241 14.2254C57.3033 14.5444 53.8902 14.7184 50.1707 14.7184C46.4512 14.7184 42.8873 14.5347 39.4984 14.2012C39.0074 14.1529 38.7351 13.6163 38.9685 13.1861Z" />
      <path d="M50.0564 3.60052L50.0588 3.60052H50.054L50.0564 3.60052Z" />
      <path d="M65.3477 23.8339C64.8615 22.171 63.2522 21.0979 61.5213 21.2719C57.8164 21.6393 53.9997 21.8327 50.1198 21.8327C46.2398 21.8327 42.2724 21.6345 38.4994 21.2526C36.7637 21.0786 35.1543 22.1469 34.6681 23.8146C32.9032 29.8617 31.6828 37.0449 31.3279 45.0594C31.2938 45.8183 31.9113 46.4467 32.6698 46.4467H67.346C68.1094 46.4467 68.722 45.8135 68.688 45.0594C68.3282 37.0545 67.1127 29.8811 65.3526 23.8387L65.3477 23.8339Z" />
      <path d="M39.3849 85.6888C42.7932 85.3504 46.3765 85.1667 50.1203 85.1667L50.1155 85.1716C53.7815 85.1716 57.2919 85.3456 60.6321 85.6695C61.1232 85.7178 61.4003 86.2495 61.167 86.6846C57.8364 92.8768 53.8204 96.4007 50.0036 96.4007C46.1869 96.4007 42.1806 92.8816 38.8549 86.7039C38.6216 86.2689 38.8938 85.7371 39.3849 85.6888Z" />
      <path d="M36.8592 96.5216C34.8123 94.1143 32.9307 91.1077 31.2727 87.5451C31.0928 87.1632 30.6795 86.9457 30.2614 87.0279C27.0719 87.6611 24.1109 88.4393 21.4076 89.3288C20.7026 89.5608 20.5713 90.4937 21.1791 90.9191C25.6181 94.0176 30.5823 96.4152 35.916 97.9621C36.7766 98.2086 37.4378 97.1983 36.8592 96.5216Z" />
      <path d="M32.7573 53.558H67.2536L67.2487 53.5629C68.0607 53.5629 68.7171 54.2348 68.6782 55.042C68.3184 62.9599 67.1175 70.0609 65.3817 76.0549C64.9004 77.7225 63.2862 78.7957 61.5504 78.6217C57.8358 78.2495 54.0871 78.0561 50.1197 78.0561C46.1523 78.0561 42.2529 78.2543 38.4702 78.641C36.7344 78.815 35.1202 77.7419 34.634 76.0742C32.8885 70.0754 31.6876 62.9647 31.3278 55.0372C31.2938 54.2299 31.9453 53.558 32.7573 53.558Z" />
      <path d="M68.7515 87.5023C67.0838 91.0793 65.2022 94.1005 63.1455 96.5174C62.567 97.1942 63.2331 98.2093 64.0888 97.9579C69.4516 96.4014 74.4401 93.9845 78.8986 90.8618C79.5064 90.4364 79.3702 89.5035 78.6653 89.2715C75.9474 88.3868 72.9669 87.6086 69.7579 86.9802C69.3447 86.898 68.9265 87.1156 68.7515 87.4974V87.5023Z" />
      <path d="M75.8435 45.1615C75.5226 37.495 74.4481 30.1668 72.6977 23.5976L72.7026 23.6024C72.1532 21.5335 73.3882 19.4308 75.474 18.9136C78.4253 18.1837 81.2355 17.3184 83.861 16.3323C85.3586 15.7667 87.0506 16.2211 88.0959 17.4296C94.6354 24.9801 98.9383 34.498 99.9934 44.9681C100.071 45.7561 99.4537 46.4473 98.6563 46.4473H77.1806C76.461 46.4473 75.8727 45.8769 75.8435 45.1615Z" />
      <path d="M75.8419 54.8436C75.5259 62.4714 74.4611 69.7609 72.7253 76.3011C72.1759 78.3652 73.4109 80.4679 75.4967 80.9851C78.4674 81.7199 81.2874 82.59 83.9275 83.5858C85.4299 84.1513 87.1267 83.6969 88.1721 82.4836C94.6678 74.9476 98.9416 65.4636 99.9918 55.0369C100.07 54.2442 99.4521 53.5578 98.6547 53.5578H77.179C76.4594 53.5578 75.8711 54.1282 75.8419 54.8436Z" />
      <path d="M24.159 54.8436C24.4799 62.4908 25.5496 69.7996 27.2902 76.3543L27.2853 76.3495C27.8299 78.4087 26.5998 80.5114 24.5237 81.0335C21.5724 81.7731 18.7621 82.6432 16.1366 83.6438C14.6342 84.2142 12.9374 83.7646 11.8872 82.5513C5.36229 75.0056 1.06422 65.4974 0.00915345 55.0369C-0.0686396 54.249 0.548843 53.5578 1.34622 53.5578H22.822C23.5415 53.5578 24.1299 54.1282 24.159 54.8436Z" />
      <path d="M24.1614 45.1561C24.4823 37.4703 25.5616 30.1228 27.3168 23.5439C27.8663 21.4847 26.6362 19.3771 24.5552 18.8599C21.6185 18.1251 18.8228 17.2598 16.2119 16.2689C14.7095 15.6985 13.0175 16.1481 11.9673 17.3565C5.39379 24.9167 1.06655 34.4636 0.00662125 44.9628C-0.0711718 45.7555 0.54631 46.4419 1.34369 46.4419H22.8194C23.539 46.4419 24.1273 45.8715 24.1565 45.1561H24.1614Z" />
    </svg>
  ),
  lock: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M39.3558 39.9626H78.8777L78.8729 39.9675C81.1539 39.9675 83 41.8288 83 44.1285V95.839C83 98.1387 81.1539 100 78.8729 100H22.1271C19.8461 100 18 98.1387 18 95.839V44.1285C18 41.8288 19.8461 39.9675 22.1271 39.9675H25.3699C26.5636 39.9675 27.5301 38.993 27.5301 37.7896V23.1627C27.5301 10.2412 38.0751 -0.244163 50.9446 0.00432743C63.5242 0.243074 73.4747 10.8648 73.4747 23.5476V29.2386C73.4747 30.6126 72.3729 31.7235 71.01 31.7235H66.274C64.9112 31.7235 63.8093 30.6126 63.8093 29.2386V23.1578C63.8093 15.6252 57.3383 9.52981 50.0989 9.74907C42.8595 9.96833 37.1955 16.1465 37.1955 23.4453V37.7847C37.1955 38.9882 38.1621 39.9626 39.3558 39.9626ZM36.3079 54.0062L48.67 50.3226C49.5157 50.0693 50.4146 50.0693 51.2554 50.3226L63.6175 54.0062C65.5506 54.586 66.8796 56.3741 66.8796 58.4108V67.7121C66.8796 76.4532 60.7179 86.5585 52.0239 91.0362C50.7287 91.7037 49.1919 91.7037 47.9016 91.0362C39.2027 86.5585 33.0458 76.4532 33.0458 67.7121V58.4108C33.0458 56.3741 34.3748 54.5811 36.3079 54.0062Z"
      />
    </svg>
  ),
  speechBubble: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.91586 7H91.0841C96.0095 7 100 10.9638 100 15.8459V62.2993C100 67.186 96.0095 71.1452 91.0841 71.1452H45.1979C43.0957 71.1452 41.3934 72.8341 41.3934 74.9198V89.8429C41.3934 92.6485 37.975 94.0559 35.9751 92.0716L16.8039 73.051C15.576 71.8328 13.9063 71.1452 12.1669 71.1452H8.91586C3.99051 71.1452 0 67.186 0 62.2993V15.8459C0 10.9592 3.99051 7 8.91586 7ZM56.3533 33.4533H67.39C69.2132 33.4533 70.6922 34.9207 70.6922 36.7295V41.404C70.6922 43.2128 69.2132 44.6802 67.39 44.6802H56.3533C55.7115 44.6802 55.1906 45.197 55.1906 45.8338V56.7839C55.1906 58.5927 53.7116 60.0601 51.8884 60.0601H47.177C45.3538 60.0601 43.8748 58.5927 43.8748 56.7839V45.8338C43.8748 45.197 43.3539 44.6802 42.7121 44.6802H31.6754C29.8522 44.6802 28.3732 43.2128 28.3732 41.404V36.7295C28.3732 34.9207 29.8522 33.4533 31.6754 33.4533H42.7121C43.3539 33.4533 43.8748 32.9365 43.8748 32.2997V21.3496C43.8748 19.5408 45.3538 18.0734 47.177 18.0734H51.8884C53.7116 18.0734 55.1906 19.5408 55.1906 21.3496V32.2997C55.1906 32.9365 55.7115 33.4533 56.3533 33.4533Z"
        fill="#002652"
      />
    </svg>
  ),

  leaf: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M62.3415 52.5503C96.3093 48.4962 96.7283 22.5759 99.9701 5.07973C100.176 3.95984 99.2885 2.93965 98.1514 3.00278C76.4493 4.1991 51.6816 8.42942 49.128 46.2099C39.3792 29.8037 23.1634 25.3374 1.43805 29.4348C0.384041 29.6342 -0.251028 30.7142 0.0947684 31.7277C5.49784 47.5624 9.17525 64.0716 41.198 61.5327C38.9803 50.4568 32.6761 44.9537 27.2697 41.5974C26.8208 41.3182 27.15 40.6304 27.6521 40.8032C38.4649 44.5383 49.394 55.7671 47.3326 78.5404C46.8671 78.6368 46.4116 78.7232 45.976 78.8461C37.1748 81.3783 24.8059 87.4197 22.1526 89.7459C17.3281 93.9762 31.4592 95.9501 51.5619 96C51.5985 96 51.6317 96 51.6683 96C51.778 96 51.8878 96 51.9942 96C52.1006 96 52.2136 96 52.32 96C52.3566 96 52.3898 96 52.4264 96C72.5292 95.9501 86.6603 93.9762 81.8357 89.7459C79.1824 87.4164 68.0937 81.7106 58.0124 78.8461C57.5768 78.7232 57.1213 78.6334 56.6558 78.5371C56.5959 78.3244 56.5394 78.115 56.4829 77.9057C54.0357 57.4719 57.4205 28.0059 78.9995 20.0304C79.465 19.8576 79.8175 20.4757 79.4318 20.7914C67.6348 30.4218 62.8435 41.0092 62.3415 52.547V52.5503Z"
      />
    </svg>
  ),
  leafBadge: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M62.1073 2.72603C59.6642 3.30759 57.0877 2.85779 54.9943 1.48569C51.9669 -0.495229 48.0331 -0.495229 45.0057 1.48569C42.9123 2.85779 40.3358 3.30759 37.8927 2.72603C34.3638 1.88551 30.6647 3.21672 28.5068 6.10177C27.0116 8.09632 24.7479 9.39119 22.2496 9.66833C18.6425 10.0727 15.6243 12.5716 14.5983 16.0109C13.8898 18.3916 12.2058 20.3726 9.95598 21.4766C6.70314 23.0713 4.73395 26.438 4.95939 30.0182C5.11582 32.4943 4.21864 34.9205 2.4887 36.7197C-0.0141929 39.3185 -0.699728 43.144 0.754159 46.4334C1.75716 48.7097 1.75716 51.2903 0.754159 53.5666C-0.699728 56.856 -0.0141929 60.6815 2.4887 63.2803C4.21864 65.0795 5.11582 67.5057 4.95939 69.9818C4.73395 73.562 6.70314 76.9241 9.95598 78.5234C12.2058 79.632 13.8852 81.6084 14.5983 83.9891C15.6243 87.433 18.6379 89.9273 22.2496 90.3317C24.7479 90.6088 27.0162 91.9037 28.5068 93.8982C30.6647 96.7878 34.3592 98.1145 37.8927 97.274C40.3358 96.6924 42.9123 97.1422 45.0057 98.5143C48.0331 100.495 51.9669 100.495 54.9943 98.5143C57.0877 97.1422 59.6642 96.6924 62.1073 97.274C65.6362 98.1145 69.3353 96.7833 71.4931 93.8982C72.9884 91.9037 75.2521 90.6088 77.7504 90.3317C81.3575 89.9273 84.3757 87.4284 85.4017 83.9891C86.1102 81.6084 87.7942 79.6274 90.044 78.5234C93.2969 76.9287 95.2661 73.562 95.0406 69.9818C94.8842 67.5057 95.7813 65.0795 97.5113 63.2803C100.014 60.6815 100.7 56.856 99.2458 53.5666C98.2428 51.2903 98.2428 48.7097 99.2458 46.4334C100.7 43.144 100.014 39.3185 97.5113 36.7197C95.7813 34.9205 94.8842 32.4943 95.0406 30.0182C95.2661 26.438 93.2969 23.0759 90.044 21.4766C87.7942 20.368 86.1148 18.3916 85.4017 16.0109C84.3757 12.567 81.3621 10.0727 77.7504 9.66833C75.2521 9.39119 72.9838 8.09632 71.4931 6.10177C69.3353 3.21218 65.6408 1.88551 62.1073 2.72603ZM73.5362 30.7721C72.2235 39.1698 70.5764 49.7067 55.9239 51.5088V51.5071C56.1649 45.8002 58.4647 40.5634 64.1273 35.8C64.3124 35.6439 64.1432 35.3382 63.9198 35.4236C53.5619 39.3685 51.9371 53.9431 53.1118 64.0501C53.1389 64.1537 53.166 64.2572 53.1948 64.3624C53.2193 64.3676 53.2437 64.3728 53.268 64.378C53.466 64.42 53.6598 64.4611 53.8459 64.5153C58.685 65.9321 64.0076 68.7544 65.2812 69.9066C67.5969 71.999 60.814 72.9753 51.1647 73H51.1136H50.9572H50.8008H50.7497C41.1004 72.9753 34.3175 71.999 36.6332 69.9066C37.9068 68.756 43.8439 65.7678 48.0685 64.5153C48.2434 64.4644 48.425 64.4262 48.6106 64.3871C48.6468 64.3795 48.6831 64.3718 48.7196 64.3641C49.7091 53.0999 44.4631 47.5458 39.273 45.6983C39.032 45.6129 38.874 45.9531 39.0895 46.0912C41.6845 47.7513 44.7105 50.4733 45.775 55.9517C31.2221 57.1406 28.8654 49.884 26.4547 42.4611C26.3192 42.0439 26.1835 41.6262 26.0455 41.2094C25.8795 40.7081 26.1843 40.1739 26.6903 40.0753C37.1184 38.0486 44.902 40.2577 49.5815 48.3726C50.8072 29.6855 62.6957 27.5931 73.1127 27.0014C73.6585 26.9701 74.0846 27.4748 73.9857 28.0287C73.828 28.9056 73.6842 29.8252 73.5362 30.7721Z"
      />
    </svg>
  ),
  recycle: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 100 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M32.0132 77.7899H15.471C14.2698 77.7899 13.6588 77.0265 13.4059 76.5811C13.153 76.1358 12.8158 75.2028 13.448 74.1849L20.2124 63.0943L26.5553 66.5296C27.904 67.2718 29.4634 65.9783 28.9998 64.4939L23.268 46.257C22.994 45.3664 22.0668 44.8786 21.1818 45.1119L2.86945 50.2861C1.39435 50.7102 1.16252 52.7247 2.51118 53.4457L8.70661 56.8174L2.32153 67.2718C-0.64974 72.1279 -0.776185 78.0019 1.98436 82.9852C4.74491 87.9686 9.78135 90.9374 15.45 90.9374H31.9921C35.5956 90.9374 38.5247 87.9898 38.5247 84.3636C38.5247 80.7374 35.5956 77.7899 31.9921 77.7899H32.0132Z" />
      <path d="M73.1475 36.8629L78.0153 18.3715C78.4157 16.8871 76.7931 15.6572 75.4866 16.463L69.4597 20.1104L63.7489 9.25308C61.0938 4.20613 56.1417 1.1313 50.4731 1.00407C44.8045 0.876836 39.7048 3.7396 36.8389 8.63811L28.4519 22.9732C26.6186 26.1116 27.6511 30.1407 30.7699 31.9644C31.8025 32.5793 32.9615 32.8762 34.0784 32.8762C36.3121 32.8762 38.5036 31.7099 39.7048 29.6317L48.0918 15.2967C48.7029 14.2576 49.6512 14.1304 50.178 14.1304C50.6838 14.1304 51.6532 14.3212 52.2011 15.3815L58.249 26.875L52.0957 30.6284C50.7891 31.4342 51.1053 33.4276 52.6014 33.7668L71.1245 38.0504C72.0306 38.2625 72.9157 37.7111 73.1475 36.8205V36.8629Z" />
      <path d="M97.6342 67.3143L88.8679 53.1913C86.9502 50.1165 82.9253 49.1622 79.8697 51.0919C76.8141 53.0216 75.8659 57.0719 77.7835 60.1467L86.5498 74.2697C87.182 75.2876 86.8659 76.2206 86.613 76.666C86.3602 77.1113 85.7701 77.8959 84.569 77.8959L71.6303 77.9807L71.1877 70.7496C71.0823 69.2016 69.1858 68.5654 68.1954 69.7317L55.8466 84.2788C55.2566 84.9786 55.3198 86.0389 55.9942 86.6538L70.0498 99.5469C71.1877 100.586 72.9999 99.7166 72.8946 98.1685L72.4521 91.107L84.6533 91.0222C90.3219 90.9798 95.3372 87.9898 98.0556 82.9853C100.795 77.9807 100.627 72.128 97.6342 67.2931V67.3143Z" />
    </svg>
  ),
  menu: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 stroke-secondary", props.className)}
      viewBox="0 0 18 8"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M1 1H17" strokeWidth="2" />
      <path d="M1 7H17" strokeWidth="2" />
    </svg>
  ),
  quote: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 56 39"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M13.2446 39L21.0588 24.0118C23.4631 19.2706 24.515 16.0588 24.515 12.2353C24.515 5.04707 19.2555 0 12.1926 0C5.58062 0 0.0205078 5.04707 0.0205078 12.2353C0.0205078 17.8941 4.5287 22.4824 9.93854 22.4824H10.2391L1.22269 39H13.2446ZM43.75 39L51.5642 24.0118C53.9686 19.2706 55.0205 16.0588 55.0205 12.2353C55.0205 5.04707 49.7609 0 42.6981 0C36.0861 0 30.526 5.04707 30.526 12.2353C30.526 17.8941 35.0342 22.4824 40.444 22.4824H40.7445L31.7282 39H43.75Z"
        fill="#002652"
      />
    </svg>
  ),
  filledStar: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 41 38"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M40.5 14.5225H25.2254L20.5074 0L15.7746 14.5225L0.5 14.5078L12.8701 23.4922L8.1373 38L20.5074 29.0302L32.8627 38L28.1447 23.4922L40.5 14.5225Z"
        fill="#00B67A"
      />
      <path d="M29.2065 26.7767L28.1447 23.4922L20.5074 29.0302L29.2065 26.7767Z" fill="#005128" />
    </svg>
  ),
  halfFilledStar: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      width="41"
      height="38"
      viewBox="0 0 41 38"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M40.5 14.5225H25.2254L20.5074 0L15.7746 14.5225L0.5 14.5078L12.8701 23.4922L8.1373 38L20.5074 29.0302L32.8627 38L28.1447 23.4922L40.5 14.5225Z"
        fill="#00B67A"
      />
      <path d="M29.2065 26.7767L28.1447 23.4922L20.5074 29.0302L29.2065 26.7767Z" fill="#005128" />
      <mask id="mask0_1445_2509" maskUnits="userSpaceOnUse" x="20" y="0" width="21" height="38">
        <rect x="20.5" width="20" height="38" fill="#D9D9D9" />
      </mask>
      <g mask="url(#mask0_1445_2509)">
        <path
          d="M40.5 14.5225H25.2254L20.5074 0L15.7746 14.5225L0.5 14.5078L12.8701 23.4922L8.1373 38L20.5074 29.0302L32.8627 38L28.1447 23.4922L40.5 14.5225Z"
          fill="#CCCCCC"
        />
        <path d="M29.2065 26.7767L28.1447 23.4922L20.5074 29.0302L29.2065 26.7767Z" fill="#808080" />
      </g>
    </svg>
  ),
  minus: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 stroke-secondary", props.className)}
      viewBox="0 0 12 3"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M11 1.5L1 1.5" strokeWidth="2" />
    </svg>
  ),
  plus: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 stroke-secondary", props.className)}
      viewBox="0 0 12 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M6 1.5V11.5" strokeWidth="2" />
      <path d="M11 6.5L1 6.5" strokeWidth="2" />
    </svg>
  ),
  x: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 stroke-secondary", props.className)}
      viewBox="0 0 16 14"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M2.34375 1.34375L13.6575 12.6575" strokeWidth="2" />
      <path d="M2.34375 12.6582L13.6575 1.34449" strokeWidth="2" />
    </svg>
  ),
  chevronDown: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      width="12"
      height="9"
      viewBox="0 0 12 9"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M2 2L6 6L10 2" strokeWidth="3" />
    </svg>
  ),
  copy: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.5 0C4.11929 0 3 1.11929 3 2.5V3H2.5C1.11929 3 0 4.11929 0 5.5V13.5C0 14.8807 1.11929 16 2.5 16H10.5C11.8807 16 13 14.8807 13 13.5V13H13.5C14.8807 13 16 11.8807 16 10.5V2.5C16 1.11929 14.8807 0 13.5 0H5.5ZM12 13H5.5C4.11929 13 3 11.8807 3 10.5V4H2.5C1.67157 4 1 4.67157 1 5.5V13.5C1 14.3284 1.67157 15 2.5 15H10.5C11.3284 15 12 14.3284 12 13.5V13ZM4 2.5C4 1.67157 4.67157 1 5.5 1H13.5C14.3284 1 15 1.67157 15 2.5V10.5C15 11.3284 14.3284 12 13.5 12H5.5C4.67157 12 4 11.3284 4 10.5V2.5Z"
      />
    </svg>
  ),
  facebook: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M36 18.1096C36 8.1069 27.9422 0 18 0C8.05781 0 0 8.1069 0 18.1096C0 27.1503 6.58125 34.6418 15.1875 36V23.3445H10.6172V18.1096H15.1875V14.1199C15.1875 9.58184 17.8734 7.07408 21.9867 7.07408C23.9555 7.07408 26.0156 7.42779 26.0156 7.42779V11.8845H23.7445C21.5086 11.8845 20.8125 13.2816 20.8125 14.7141V18.1096H25.8047L25.0066 23.3445H20.8125V36C29.4188 34.6418 36 27.1503 36 18.1096Z" />
    </svg>
  ),
  instagram: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 20 20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path d="M10.0009 0C7.28508 0 6.94424 0.0118752 5.87756 0.0604169C4.81297 0.109167 4.08629 0.277708 3.45045 0.525C2.79274 0.780417 2.23481 1.12208 1.67898 1.67813C1.12272 2.23396 0.78105 2.79187 0.524797 3.44937C0.276878 4.08542 0.108126 4.81229 0.0602089 5.87646C0.0125 6.94312 0 7.28417 0 10C0 12.7158 0.0120837 13.0556 0.0604175 14.1223C0.109376 15.1869 0.27792 15.9135 0.525006 16.5494C0.780633 17.2071 1.1223 17.765 1.67835 18.3208C2.23398 18.8771 2.7919 19.2196 3.4492 19.475C4.08546 19.7223 4.81234 19.8908 5.87673 19.9396C6.94341 19.9881 7.28403 20 9.99969 20C12.7158 20 13.0556 19.9881 14.1222 19.9396C15.1868 19.8908 15.9143 19.7223 16.5506 19.475C17.2081 19.2196 17.7652 18.8771 18.3208 18.3208C18.8771 17.765 19.2187 17.2071 19.475 16.5496C19.7208 15.9135 19.8896 15.1867 19.9396 14.1225C19.9875 13.0558 20 12.7158 20 10C20 7.28417 19.9875 6.94333 19.9396 5.87667C19.8896 4.81208 19.7208 4.08542 19.475 3.44958C19.2187 2.79187 18.8771 2.23396 18.3208 1.67813C17.7646 1.12188 17.2083 0.780208 16.55 0.525C15.9125 0.277708 15.1854 0.109167 14.1208 0.0604169C13.0541 0.0118752 12.7145 0 9.99781 0H10.0009ZM9.10385 1.80208C9.3701 1.80167 9.66718 1.80208 10.0009 1.80208C12.671 1.80208 12.9874 1.81167 14.0418 1.85958C15.0168 1.90417 15.546 2.06708 15.8985 2.20396C16.3652 2.38521 16.6979 2.60187 17.0477 2.95187C17.3977 3.30187 17.6143 3.63521 17.796 4.10187C17.9329 4.45396 18.096 4.98312 18.1404 5.95812C18.1883 7.01229 18.1987 7.32896 18.1987 9.99771C18.1987 12.6665 18.1883 12.9831 18.1404 14.0373C18.0958 15.0123 17.9329 15.5415 17.796 15.8935C17.6148 16.3602 17.3977 16.6925 17.0477 17.0423C16.6977 17.3923 16.3654 17.609 15.8985 17.7902C15.5464 17.9277 15.0168 18.0902 14.0418 18.1348C12.9876 18.1827 12.671 18.1931 10.0009 18.1931C7.3307 18.1931 7.01424 18.1827 5.96006 18.1348C4.98505 18.0898 4.45588 17.9269 4.10317 17.79C3.6365 17.6087 3.30316 17.3921 2.95316 17.0421C2.60315 16.6921 2.38648 16.3596 2.20481 15.8927C2.06794 15.5406 1.90481 15.0115 1.86044 14.0365C1.81252 12.9823 1.80294 12.6656 1.80294 9.99521C1.80294 7.32479 1.81252 7.00979 1.86044 5.95563C1.90502 4.98063 2.06794 4.45146 2.20481 4.09896C2.38607 3.63229 2.60315 3.29896 2.95316 2.94896C3.30316 2.59896 3.6365 2.38229 4.10317 2.20062C4.45567 2.06312 4.98505 1.90063 5.96006 1.85583C6.88257 1.81417 7.24008 1.80167 9.10385 1.79958V1.80208ZM15.3389 3.4625C14.6764 3.4625 14.1389 3.99937 14.1389 4.66208C14.1389 5.32458 14.6764 5.86208 15.3389 5.86208C16.0014 5.86208 16.5389 5.32458 16.5389 4.66208C16.5389 3.99958 16.0014 3.46208 15.3389 3.46208V3.4625ZM10.0009 4.86458C7.16487 4.86458 4.86547 7.16396 4.86547 10C4.86547 12.836 7.16487 15.1344 10.0009 15.1344C12.837 15.1344 15.1356 12.836 15.1356 10C15.1356 7.16396 12.837 4.86458 10.0009 4.86458ZM10.0009 6.66667C11.8418 6.66667 13.3343 8.15896 13.3343 10C13.3343 11.8408 11.8418 13.3333 10.0009 13.3333C8.15988 13.3333 6.66757 11.8408 6.66757 10C6.66757 8.15896 8.15988 6.66667 10.0009 6.66667Z" />
    </svg>
  ),
  linkedin: (props: IconProps) => (
    <svg
      {...props}
      className={cn("h-5 w-5 fill-secondary", props.className)}
      viewBox="0 0 20 20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4.31348 17.9444V5.81694H0.249539V17.9444H4.31348ZM2.28151 4.16C3.70746 4.16 4.59867 3.24339 4.59867 2.08C4.56302 0.881355 3.70746 0 2.31716 0C0.926864 0 0 0.91661 0 2.08C0 3.20813 0.855567 4.16 2.28151 4.16Z"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M6.55863 17.9444H10.6226V11.1756C10.6226 10.8231 10.6582 10.4353 10.7652 10.1885C11.0504 9.44818 11.7277 8.70784 12.8684 8.70784C14.33 8.70784 14.9361 9.83598 14.9361 11.4577V17.9444H19V10.9994C19 7.26242 17.0037 5.53496 14.2944 5.53496C12.0842 5.53496 11.1217 6.76886 10.5869 7.57971H10.6226V5.817H6.55863C6.62993 6.94513 6.55863 17.9444 6.55863 17.9444Z"
      />
    </svg>
  ),
};
