"use client";

import { useCustomer } from "@/hooks/use-customer";
import { KeyboardArrowRight } from "@arthursenno/lizenzero-ui-react/Icon";
import { Link } from "@/i18n/navigation";

interface BreadcrumbItemsProps {
  paths: Array<{ href: string; label: string }>;
}

export function BreadcrumbItems({ paths }: BreadcrumbItemsProps) {
  const { customer } = useCustomer();

  let urlHome = "https://lizenzero-landing-page.entw.portale.interzero.de/";

  if (customer?.hasActiveContract) {
    urlHome = "/saas";
  }

  return (
    <nav aria-label="breadcrumb" className="z-50 w-full flex flex-row  justify-start">
      <ol className="flex items-center space-x-2">
        {paths.map((path, index) => (
          <li key={`${path.href}_${path.label}`} className="flex items-center space-x-1 text-sm">
            {index === paths.length - 1 ? (
              <span className="text-primary font-bold" aria-hidden>
                {path.label}
              </span>
            ) : (
              <Link href={index === 0 ? urlHome : path.href} className="flex items-center font-bold">
                <span className="text-tonal-dark-cream-50 hover:text-tonal-dark-cream-20 text-sm/none">
                  {path.label}
                </span>
                <KeyboardArrowRight className="ml-2 fill-tonal-dark-cream-50  text-tonal-dark-cream-50 flex-none w-5 h-5" />
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
}
