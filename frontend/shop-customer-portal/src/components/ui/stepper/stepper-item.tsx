interface StepperItemProps {
  step: number;
  title: string;
  checked?: boolean;
}

export function StepperItem({ step, title, checked = false }: StepperItemProps) {
  const isFirstItem = step === 1;

  return (
    <>
      {!isFirstItem && (
        <div className="hidden lg:flex h-[1px] my-auto flex-grow rounded-sm bg-tonal-dark-cream-80 group-data-[checked=true]:bg-[#1B6C64]" />
      )}
      <div className="group flex items-center gap-10 px-4" data-first={isFirstItem} data-checked={checked}>
        <div className="flex flex-col items-center">
          <div className="flex justify-center items-center h-7 w-7 rounded-full bg-tonal-dark-cream-80 group-data-[checked=true]:bg-[#1B6C64]">
            <p className="color text-base leading-[16px] text-tonal-dark-cream-20 group-data-[first=true]:text-white group-data-[checked=true]:text-white">
              {step}
            </p>
          </div>
          <p className="hidden lg:block mt-2 text-center text-nowrap text-tonal-dark-cream-20 group-data-[checked=true]:text-[#1B6C64] group-data-[checked=true]:font-bold">
            {title}
          </p>
        </div>
      </div>
    </>
  );
}
