"use client";

import { ExitIntentModal } from "@/components/_common/modals/exit-intent-modal";
import { JourneyCalculatorFormData } from "@/components/modules/shop/journeys/components/journey-calculator/journey-calculator-provider";
import { GermanyCalculatorFormData } from "@/components/modules/shop/journeys/germany-journey/germany-calculator-provider";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { useShoppingCart } from "@/hooks/use-shopping-cart";
import { ShoppingCartItem } from "@/lib/api/shoppingCart/types";

import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { useSession } from "next-auth/react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useFormContext } from "react-hook-form";

export function SaveProgress() {
  const session = useSession();
  const form = useFormContext();
  const t = useTranslations("shop.common.saveProgress");
  const { shoppingCart, updateCartItems, isUpdatingCart } = useShoppingCart();

  const [isSavingProgress, setIsSavingProgress] = useState(false);
  const { changeParams } = useQueryFilter(["select-countries", "exit"]);

  const isAuthenticated = !!session.data?.user;

  async function handleSaveProgress() {
    setIsSavingProgress(true);

    const formattedItems = shoppingCart.items.map((item) => {
      if (item.service_type === "DIRECT_LICENSE") {
        const packagingService = form.getValues("packagingService") as GermanyCalculatorFormData["packagingService"];

        return {
          ...item,
          packaging_services: [
            {
              ...packagingService,
              fractions: packagingService.fractions || {},
            },
          ],
        };
      }

      const items = (form.getValues("items") || {}) as JourneyCalculatorFormData["items"];

      return {
        ...item,
        packaging_services: Object.values(items[item.country_code]?.packagingServices || {}).filter(
          (v) => !!v
        ) as ShoppingCartItem["packaging_services"],
      };
    });

    await updateCartItems(formattedItems);

    setIsSavingProgress(false);

    if (!isAuthenticated) {
      changeParams({ "select-countries": "false", exit: "true" });
      return;
    }

    setIsSavingProgress(false);
  }

  return (
    <>
      <Button
        disabled={isSavingProgress || isUpdatingCart}
        color="dark-blue"
        variant="outlined"
        size="medium"
        onClick={handleSaveProgress}
        style={{
          textWrap: "nowrap",
        }}
      >
        {isSavingProgress ? t("saving") : t("save")}
      </Button>
      <ExitIntentModal />
    </>
  );
}
