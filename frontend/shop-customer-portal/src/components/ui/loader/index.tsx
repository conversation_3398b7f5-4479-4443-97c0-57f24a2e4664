import React from "react";
import { cn } from "@/lib/utils";
import { CgSpinnerAlt } from "react-icons/cg";

interface SpinnerProps {
  size?: "xs" | "sm" | "md" | "lg";
}

export function Spinner({ size = "md" }: SpinnerProps) {
  return (
    <div
      data-size={size}
      className={cn(
        "flex items-center justify-center rounded-full overflow-hidden",
        size === "xs" && "size-6",
        size === "sm" && "size-8",
        size === "md" && "size-10",
        size === "lg" && "size-16"
      )}
    >
      <CgSpinnerAlt
        className={cn(
          "text-primary animate-spin duration-300",
          size === "xs" && "size-6",
          size === "sm" && "size-8",
          size === "md" && "size-10",
          size === "lg" && "size-16"
        )}
      />
    </div>
  );
}
