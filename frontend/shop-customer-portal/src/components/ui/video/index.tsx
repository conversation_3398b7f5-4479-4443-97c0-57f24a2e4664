"use client";

import { DetailedHTMLProps, VideoHTMLAttributes, useRef, useState } from "react";
import { Icons } from "../icons";
import { cn } from "@/lib/utils";
import { Fullscreen } from "@arthursenno/lizenzero-ui-react/Icon";

interface IVideo {
  className?: string;
  propsVideo?: DetailedHTMLProps<VideoHTMLAttributes<HTMLVideoElement>, HTMLVideoElement>;
  url: string;
}

export function Video({ className, propsVideo, url }: IVideo) {
  const videoRef = useRef<null | HTMLVideoElement>(null);
  const [isPlayed, setIsPlayed] = useState(false);

  const play = () => {
    if (!videoRef.current) return;

    videoRef.current.play();
    setIsPlayed(true);
  };

  const pause = () => {
    if (!videoRef.current) return;

    videoRef.current.pause();
    setIsPlayed(false);
  };

  const fullScreen = () => {
    if (!videoRef.current) return;

    videoRef.current.requestFullscreen();
  };

  return (
    <div className={cn("relative rounded-4xl overflow-hidden", className)}>
      <div className={"flex justify-center items-center size-full relative"}>
        {!isPlayed ? (
          <button
            className="rounded-full bg-white flex justify-center items-center size-20 md:size-28 z-10"
            onClick={play}
          >
            <Icons.play className="fill-primary size-5 md:size-7" />
          </button>
        ) : (
          <button className="size-full z-10" onClick={pause} />
        )}
        <div className="absolute bottom-0 w-full flex justify-end items-start pb-6 pr-6">
          <button className="z-10" onClick={fullScreen}>
            <Fullscreen className="fill-white size-8" />
          </button>
        </div>
      </div>
      <video muted loop ref={videoRef} className="size-full absolute top-0 object-cover z-0" {...propsVideo}>
        <source src={url} type="video/mp4" />
        Your browser does not support the video tag.
      </video>
    </div>
  );
}
