"use client";
import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { SaasBreadcrumb } from "@/components/modules/saas/components/saas-breadcrumb";
import { SaasContainer } from "@/components/modules/saas/components/saas-container";
import { ContractData } from "./contract-data";
import { ContractDocuments } from "./contract-documents";

const paths = [
  { label: "Dashboard", href: "/saas" },
  { label: "Contract Management", href: "#" },
];

export function ContractManagement() {
  return (
    <>
      <SaasBreadcrumb paths={paths} />
      <SaasContainer>
        <TitleAndSubTitle
          showIcon
          title="Contract Management"
          subText="Here you will find all the details and documents for your contract"
        />

        <div className="flex flex-col gap-8">
          <ContractData />
          <ContractDocuments />
        </div>
      </SaasContainer>
    </>
  );
}
