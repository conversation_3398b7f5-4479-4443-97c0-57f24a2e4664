import { formatCustomerNumber } from "@/utils/format-customer-number";

import { useCustomer } from "@/hooks/use-customer";

export function ContractData() {
  const { customer } = useCustomer();

  if (!customer) return null;

  return (
    <div className="px-4 py-6 md:py-7 md:px-8 bg-surface-01 rounded-4xl">
      <h3 className="text-2xl text-primary font-bold mb-8">Contract Data</h3>

      <div className="p-4 rounded-3xl bg-white md:flex flex-col gap-6 overflow-hidden">
        <div className="flex">
          <div className="flex flex-1 flex-col gap-2">
            <span className="text-sm text-on-surface-01">Customer number</span>
            <span className="text-primary">{formatCustomerNumber(customer?.id)}</span>
          </div>
          <div className="flex flex-1 flex-col gap-2">
            <span className="text-sm text-on-surface-01">Company</span>
            <span className="text-primary">{customer?.company?.name}</span>
          </div>
        </div>
        <div className="flex flex-col gap-2">
          <span className="text-sm text-on-surface-01">Address</span>
          <span className="text-primary">{customer?.company?.address.address_line}</span>
        </div>
      </div>
    </div>
  );
}
