"use client";

import { CountryIcon } from "@/components/_common/country-icon";
import { Contract } from "@/lib/api/contracts/types";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { RadioSelected, RadioUnselected } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { Link } from "@/i18n/navigation";
import { useRouter } from "@/i18n/navigation";
import { useSearchParams } from "next/navigation";
import { useState } from "react";
import { CgClose } from "react-icons/cg";

export interface ICountryTerminationProps {
  contracts: Contract[];
}

export function CountryTerminationModal({ contracts }: ICountryTerminationProps) {
  const searchParams = useSearchParams();

  const terminationContractId = searchParams.get("termination_contract_id");

  const router = useRouter();
  const [selectedCountryCode, setSelectedCountryCode] = useState<string | null>(null);
  const [isRedirecting, setIsRedirecting] = useState(false);

  const contract = contracts.find((contract) => contract.id === Number(terminationContractId));

  const countries = (
    (contract?.licenses.length && contract?.licenses.filter((license) => !license.termination)) ||
    (contract?.action_guides.length && contract?.action_guides) ||
    []
  ).map((item) => ({
    id: item.country_id,
    code: item.country_code,
    name: item.country_name,
    flag_url: item.country_flag,
  }));

  const isModalOpen = !!terminationContractId;

  function handleFormSubmit() {
    setIsRedirecting(true);

    const searchParams = new URLSearchParams();

    searchParams.set("contract_id", String(terminationContractId));

    if (selectedCountryCode === "ALL") {
      searchParams.set("general", "true");
    } else {
      searchParams.set("countries", String(selectedCountryCode));
    }

    router.push(`./contract-management/termination?${searchParams.toString()}`);
  }

  return (
    <Modal
      open={isModalOpen}
      style={{ borderRadius: "52px", maxWidth: "500px" }}
      className="bg-surface-01 z-[9999] min-w-min w-full"
    >
      <div className="flex flex-col bg-white gap-6">
        <div className="flex flex-row w-full justify-end">
          <Link href="./contract-management">
            <CgClose className="text-primary cursor-pointer hover:bg-surface-01" />
          </Link>
        </div>

        <p className="text-primary font-bold text-title-2">Select countries for termination</p>
        <p className="text-on-surface-01">
          Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit. Pretium egestas posuere in
          nulla. Ipsum purus nascetur leo lorem. Vitae.
        </p>

        <div className="rounded-[20px] shadow-surface-01-tonal-dark-cream border-2 border-surface-02 flex flex-col gap-1 py-2">
          <label htmlFor="ALL" className="flex items-center gap-2 cursor-pointer px-4 py-3">
            <input
              id="ALL"
              type="checkbox"
              onChange={() => setSelectedCountryCode("ALL")}
              checked={selectedCountryCode === "ALL"}
              className="hidden peer"
            />
            <RadioSelected className="hidden peer-checked:block size-5 fill-primary" />
            <RadioUnselected className="block cursor-pointer peer-checked:hidden size-5 fill-primary peer-data-[invalid=true]:fill-error" />
            <div className="flex items-center gap-3">
              <CountryIcon
                country={{ flag_url: "/assets/images/europe_union.png", name: "All EU Countries Licensed" }}
              />
              <span className="text-primary">All EU Countries Licensed</span>
            </div>
          </label>
          {countries.map((country) => (
            <label
              key={country.code}
              htmlFor={country.code}
              className="flex items-center gap-2 cursor-pointer px-4 py-3"
            >
              <input
                id={country.code}
                type="checkbox"
                onChange={() => setSelectedCountryCode(country.code)}
                checked={country.code === selectedCountryCode}
                className="hidden peer"
              />
              <RadioSelected className="hidden peer-checked:block size-5 fill-primary" />
              <RadioUnselected className="block cursor-pointer peer-checked:hidden size-5 fill-primary peer-data-[invalid=true]:fill-error" />
              <CountryIcon country={{ flag_url: country.flag_url, name: country.name }} className="size-6" />
              <span className="text-primary">{country.name}</span>
            </label>
          ))}
        </div>

        <div className="flex flex-row w-full justify-end">
          <Button
            disabled={!selectedCountryCode || isRedirecting}
            onClick={handleFormSubmit}
            color={"red"}
            variant="filled"
            size="medium"
          >
            Terminate
          </Button>
        </div>
      </div>
    </Modal>
  );
}
