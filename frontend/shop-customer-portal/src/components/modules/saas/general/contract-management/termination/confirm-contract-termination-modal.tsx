"use client";

import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { CgClose } from "react-icons/cg";

export interface ConfirmContractTerminationModalProps {
  isOpen?: boolean;
  toggleModal: () => void;
  onSubmit: () => void;
}

export function ConfirmContractTerminationModal({
  isOpen = false,
  toggleModal,
  onSubmit,
}: ConfirmContractTerminationModalProps) {
  return (
    <Modal open={isOpen} className={"z-50 !rounded-[52px] w-full max-w-[600px] !py-8 !px-9"}>
      <div className="flex flex-col bg-white gap-6">
        <div className="flex flex-row w-full justify-end">
          <CgClose onClick={toggleModal} className="text-primary cursor-pointer hover:bg-surface-01" />
        </div>

        <p className="text-primary font-bold text-3xl">Are you sure you want to terminate this service?</p>
        <p className="text-on-surface-01">
          Lorem ipsum dolor sit amet consectetur. Risus nulla egestas orci non hendrerit. Pretium egestas posuere in
          nulla. Ipsum purus nascetur leo lorem. Vitae.
        </p>

        <div className="flex flex-row w-full justify-end">
          <Button onClick={() => onSubmit()} color="red" variant="filled" size="medium">
            Terminate service
          </Button>
        </div>
      </div>
    </Modal>
  );
}
