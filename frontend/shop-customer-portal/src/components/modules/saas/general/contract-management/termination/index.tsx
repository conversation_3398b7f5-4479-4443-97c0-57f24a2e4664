"use client";

import { CountryIcon } from "@/components/_common/country-icon";
import { Divider } from "@/components/_common/divider";
import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { SaasBreadcrumb } from "@/components/modules/saas/components/saas-breadcrumb";
import { SaasContainer } from "@/components/modules/saas/components/saas-container";

import { getContract } from "@/lib/api/contracts";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Delete, File, RadioSelected, RadioUnselected, Upload } from "@arthursenno/lizenzero-ui-react/Icon";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Link } from "@/i18n/navigation";
import { useRouter } from "@/i18n/navigation";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { ConfirmContractTerminationModal } from "./confirm-contract-termination-modal";
import { uploadFile } from "@/lib/api/file";

import { useCustomer } from "@/hooks/use-customer";
import { enqueueSnackbar } from "notistack";
import { createTermination } from "@/lib/api/termination";
import { queryClient } from "@/lib/react-query";
import { getReasons } from "@/lib/api/reasons";
import { Skeleton } from "@/components/ui/skeleton";

const paths = [
  { label: "Dashboard", href: "/saas" },
  { label: "Contract Management", href: "/saas/contract-management" },
  { label: "Terminate Contract", href: "#" },
];

export function ContractTermination() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { customer } = useCustomer();

  const contractId = Number(searchParams.get("contract_id"));
  const countriesParam = searchParams.get("countries");
  const countryCodes = countriesParam ? countriesParam.split(",").map((code) => code.trim().toLocaleUpperCase()) : [];
  const isGeneralTermination = searchParams.get("general");

  useEffect(() => {
    if (!contractId) return router.push("/saas/contract-management");
  }, []);

  const { data: contract } = useQuery({
    queryKey: ["contract", contractId],
    queryFn: () => getContract(contractId),
    enabled: !!contractId,
  });

  const { mutate: terminateContract, isPending: isTerminatingContract } = useMutation({
    mutationFn: async () => {
      setIsConfirmModalOpen(false);
      if (!customer || !contractId || !contract || !checkedReasons.length) return;

      let terminationFileId: string | undefined = undefined;

      if (hasExtraordinaryChecked) {
        if (!extraordinaryTerminationDocument) {
          enqueueSnackbar("Please upload extraordinary termination document.", { variant: "error" });
          return;
        }

        if (extraordinaryTerminationDocument.size > 25 * 1024 * 1024) {
          enqueueSnackbar("The file size must be less than 25MB.", { variant: "error" });
          return;
        }

        try {
          const file = await uploadFile({
            file: extraordinaryTerminationDocument,
            type: "CONTRACT_TERMINATION",
          });

          if (!file) throw new Error();

          terminationFileId = file.id;
        } catch (err) {
          enqueueSnackbar("Failed to upload extraordinary termination document. Please try again.", {
            variant: "error",
          });
          return;
        }
      }

      try {
        const response = await createTermination({
          contract_id: contractId,
          country_codes: countryCodes.length > 0 ? countryCodes : undefined,
          reason_ids: checkedReasons,
          termination_file_id: terminationFileId,
        });

        if (!response.success) throw new Error();

        enqueueSnackbar("Contract terminated successfully", { variant: "success" });

        queryClient.invalidateQueries({ queryKey: ["contracts", customer.id] });

        router.push("/saas/contract-management");
      } catch (err) {
        enqueueSnackbar("Failed to terminate contract. Please try again.", { variant: "error" });
        return;
      }
    },
  });

  const reasonsQuery = useQuery({
    queryKey: ["reasons", { type: "TERMINATION" }],
    queryFn: () => getReasons({ type: "TERMINATION" }),
  });

  const [checkedReasons, setCheckedReasons] = useState<number[]>([]);

  const hasExtraordinaryChecked = checkedReasons.includes(
    Number(reasonsQuery.data?.find((reason) => reason.value === "EXTRAORDINARY_TERMINATION")?.id)
  );
  const [extraordinaryTerminationDocument, setExtraordinaryTerminationDocument] = useState<File | null>(null);

  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);

  function toggleConfirmModal() {
    setIsConfirmModalOpen(!isConfirmModalOpen);
  }

  const countries = (() => {
    if (!contract) return null;

    if (contract.type === "EU_LICENSE" || contract.type === "DIRECT_LICENSE") {
      const contractCountries = contract.licenses.map((license) => ({
        id: license.contract_id,
        code: license.country_code,
        name: license.country_name,
        flag_url: license.country_flag,
      }));

      if (isGeneralTermination) return contractCountries;

      return contractCountries.filter((country) => countryCodes.includes(country.code));
    }

    if (contract.type === "ACTION_GUIDE") {
      const contractCountries = contract.action_guides.map((actionGuide) => ({
        id: actionGuide.contract_id,
        code: actionGuide.country_code,
        name: actionGuide.country_name,
        flag_url: actionGuide.country_flag,
      }));

      if (isGeneralTermination) return contractCountries;

      return contractCountries.filter((country) => countryCodes.includes(country.code));
    }

    return null;
  })();

  const title = (() => {
    if (!contract) return null;

    if (contract.type === "EU_LICENSE") {
      if (isGeneralTermination || (countries && countries.length > 1)) return "EU License Contract";

      const country = contract.licenses.find((license) => license.country_code === countryCodes[0]);

      if (!country) return "EU License Contract";

      return country.country_name;
    }

    if (contract.type === "DIRECT_LICENSE") {
      return "Direct License Contract";
    }

    if (contract.type === "ACTION_GUIDE") {
      if (isGeneralTermination || (countries && countries.length > 1)) return "Action Guide Contract";

      const country = contract.action_guides.find((actionGuide) => actionGuide.country_code === countryCodes[0]);

      if (!country) return "Action Guide Contract";

      return country.country_name;
    }

    return null;
  })();

  async function handleFormSubmit() {
    terminateContract();
  }

  function handleExtraordinaryTerminationDocumentInputChange(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0];

    if (!file) return;

    setExtraordinaryTerminationDocument(file);
  }

  const isSubmitButtonDisabled =
    !checkedReasons.length || (!extraordinaryTerminationDocument && hasExtraordinaryChecked) || isTerminatingContract;

  return (
    <>
      <SaasBreadcrumb paths={paths} />
      <SaasContainer>
        <TitleAndSubTitle
          showIcon
          title="Terminate Contract"
          subText="For completing the termination process, please fill this form."
          variation="red"
        />
        {!!contractId && !!contract && (
          <div className="px-8 py-10 bg-surface-02 rounded-4xl">
            <div className="flex flex-row justify-between">
              <div className="flex flex-row items-center gap-2">
                <h3 className="text-2xl text-primary font-bold">{title}</h3>
              </div>

              <div className="flex flex-row gap-2">
                {!!countries && countries.map((country) => <CountryIcon key={country.id} country={country} />)}
              </div>
            </div>

            <Divider />

            <p className="text-xl text-tonal-dark-cream-20 font-bold mb-6">
              Select a reason why you don't want to renew the contract
            </p>

            <div className="flex flex-col gap-5">
              {reasonsQuery.isLoading &&
                Array.from({ length: 5 }).map((_, index) => (
                  <div className="flex flex-col gap-2" key={index}>
                    <Skeleton className="w-full h-10 rounded-lg" />
                  </div>
                ))}
              {reasonsQuery.data?.map((reason) => (
                <div className="flex flex-col gap-2" key={reason.id}>
                  <label htmlFor={String(reason.id)} className="flex items-center gap-2 cursor-pointer">
                    <input
                      id={String(reason.id)}
                      type="checkbox"
                      disabled={isTerminatingContract}
                      onChange={(e) =>
                        setCheckedReasons((prev) => {
                          if (e.target.checked) return [...prev, reason.id];

                          return prev.filter((id) => id !== reason.id);
                        })
                      }
                      checked={checkedReasons.includes(reason.id)}
                      className="hidden peer"
                    />
                    <RadioSelected className="hidden peer-checked:block size-5 fill-primary" />
                    <RadioUnselected className="block cursor-pointer peer-checked:hidden size-5 fill-primary peer-data-[invalid=true]:fill-error" />
                    <span className="text-on-tertiary">{reason.title}</span>
                  </label>
                  {reason.value === "EXTRAORDINARY_TERMINATION" && hasExtraordinaryChecked && (
                    <div className="space-y-2 pl-6 w-1/2">
                      <p className="text-tonal-dark-cream-40 italic text-sm">
                        In order to terminate earlier, submit proof below.*
                      </p>
                      {extraordinaryTerminationDocument ? (
                        <div className="flex flex-row justify-between w-full py-4 border-b border-tonal-dark-cream-80">
                          <div className="flex flex-row items-center gap-2 w-full">
                            <File className="fill-primary w-5 h-5 flex-none" />
                            <span className="text-primary overflow-hidden whitespace-nowrap text-ellipsis">
                              {extraordinaryTerminationDocument.name}
                            </span>
                            <Delete
                              onClick={() => setExtraordinaryTerminationDocument(null)}
                              className="fill-primary w-5 h-5 cursor-pointer flex-none"
                            />
                          </div>
                        </div>
                      ) : (
                        <div className="flex flex-row gap-2">
                          <input
                            id="extraordinary-termination-document"
                            type="file"
                            hidden
                            accept=".pdf"
                            onChange={handleExtraordinaryTerminationDocumentInputChange}
                          />
                          <Button
                            variant="text"
                            color="light-blue"
                            size="small"
                            leadingIcon={<Upload />}
                            onClick={() => document.getElementById("extraordinary-termination-document")?.click()}
                          >
                            Upload Document
                          </Button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
              <span className="text-primary bg-[#EBF4FF] px-2 py-[10px] rounded-lg text-sm w-fit">
                If you conclued this action your contract will be terminated in:{" "}
                <span className="text-primary font-bold ml-2">{new Date(contract.end_date).toLocaleDateString()}</span>
              </span>
            </div>

            <Divider />

            <div className="flex flex-row gap-4">
              <Link href="/saas/contract-management">
                <Button color="yellow" size="medium" variant="filled">
                  Back to contract
                </Button>
              </Link>
              <Button
                disabled={isSubmitButtonDisabled}
                onClick={toggleConfirmModal}
                color={checkedReasons.length ? "red" : "dark-blue"}
                size="medium"
                variant={"outlined"}
              >
                {isTerminatingContract ? "Terminating..." : "Terminate contract"}
              </Button>
            </div>
          </div>
        )}
      </SaasContainer>
      <ConfirmContractTerminationModal
        isOpen={isConfirmModalOpen}
        toggleModal={toggleConfirmModal}
        onSubmit={handleFormSubmit}
      />
    </>
  );
}
