"use client";
import { CountryIcon } from "@/components/_common/country-icon";
import { Divider } from "@/components/_common/divider";
import { TooltipIcon } from "@/components/_common/tooltipIcon";
import { Link, useRouter } from "@/i18n/navigation";
import { getContracts } from "@/lib/api/contracts";
import { ActionGuide, Contract, ContractStatus } from "@/lib/api/contracts/types";
import { UploadedFile } from "@/lib/api/file/types";
import { License } from "@/lib/api/license/types";
import { revokeTermination } from "@/lib/api/termination";
import { queryClient } from "@/lib/react-query";
import { cn } from "@/lib/utils";
import { useCustomer } from "@/hooks/use-customer";
import { formatDateToDDMMYYYY } from "@/utils/formatDateToDDMMYYYY";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Download, File, KeyboardArrowDown } from "@arthursenno/lizenzero-ui-react/Icon";
import { useMutation, useQuery } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { BsCircleFill } from "react-icons/bs";
import { CgSpinnerAlt } from "react-icons/cg";

import { CountryTerminationModal } from "./country-termination-modal";
import { formatFileSize } from "@/utils/filesize";
import { downloadCustomerFile } from "@/lib/api/file";
import { downloadFile } from "@/utils/download-file";
function getContractStatusLabel(status: ContractStatus) {
  if (status === "TERMINATED") return "Terminated";
  if (status === "TERMINATION_PROCESS") return "In termination proccess";
  return "Annual renewal";
}

export function ContractDocuments() {
  const { customer } = useCustomer();
  const router = useRouter();

  const { data: contracts, isLoading } = useQuery<Contract[]>({
    queryKey: ["contracts", customer!.id],
    queryFn: () => getContracts({ customer_id: customer!.id }),
    enabled: !!customer!.id,
  });

  const { mutate: handleRevokeTermination, isPending: isRevoking } = useMutation({
    mutationFn: async ({ terminationId, endDate }: { terminationId: number; endDate: string }) => {
      const currentDate = new Date();
      const contractEndDate = new Date(endDate);

      if (contractEndDate < currentDate) {
        enqueueSnackbar("You can’t revoke the termination after the contract cancellation date", {
          variant: "error",
        });
        throw new Error("Contract end date has passed");
      }

      try {
        const response = await revokeTermination(terminationId);
        enqueueSnackbar("Termination revoked successfully", { variant: "success" });
        queryClient.invalidateQueries({ queryKey: ["contracts", customer!.id] });
        return response;
      } catch (error) {
        enqueueSnackbar("Failed to revoke termination. Please try again.", { variant: "error" });
        throw error;
      }
    },
  });

  const euLicenseContract = contracts?.find((contract) => contract.type === "EU_LICENSE");
  const directLicenseContract = contracts?.find((contract) => contract.type === "DIRECT_LICENSE");
  const actionGuideContract = contracts?.find((contract) => contract.type === "ACTION_GUIDE");

  return (
    <>
      <div className="flex flex-col gap-8 px-4 py-6 md:py-7 md:px-8 bg-surface-01 rounded-4xl">
        <h3 className="text-2xl text-primary font-bold">Contract Documents</h3>
        {isLoading && (
          <div className="w-full flex justify-center items-center">
            <CgSpinnerAlt size={32} className="animate-spin text-primary" />
          </div>
        )}
        {!!directLicenseContract && (
          <div className="px-4 py-6 md:py-6 md:px-6 rounded-4xl bg-white">
            <ContractItem contract={directLicenseContract} />
            {directLicenseContract.status === "ACTIVE" && (
              <div className="flex flex-col md:flex-row gap-3">
                <Button
                  onClick={() =>
                    router.push(
                      `./contract-management/termination?contract_id=${directLicenseContract.id}&general=true`
                    )
                  }
                  color="red"
                  size="small"
                  variant="outlined"
                  className="md:w-auto w-full"
                >
                  Terminate DE contract
                </Button>
              </div>
            )}
          </div>
        )}
        {!!euLicenseContract && (
          <div className="px-4 py-6 md:py-6 md:px-6 rounded-4xl bg-white">
            <ContractItem contract={euLicenseContract} />
            <Divider initialMarginDisabled className="border-[2px] mb-8 mt-3" />
            {euLicenseContract.status === "ACTIVE" && (
              <div className="flex flex-col md:flex-row gap-3">
                <Link href={`./contract-management/termination?contract_id=${euLicenseContract.id}&general=true`}>
                  <Button color="red" size="small" variant="outlined" className="md:w-auto w-full">
                    General Termination
                  </Button>
                </Link>
                <Link href={`./contract-management?termination_contract_id=${euLicenseContract.id}`}>
                  <Button color="red" size="small" variant="outlined" className="md:w-auto w-full">
                    Country Termination
                  </Button>
                </Link>
              </div>
            )}
          </div>
        )}

        {!!actionGuideContract && (
          <div className="px-4 py-6 md:py-6 md:px-6 rounded-4xl bg-white">
            <ContractItem contract={actionGuideContract} />
            {actionGuideContract.status === "ACTIVE" && (
              <div className="flex flex-col mt-8 md:flex-row gap-3">
                <Link href={`./contract-management/termination?contract_id=${actionGuideContract.id}&general=true`}>
                  <Button color="red" size="small" variant="outlined">
                    General Termination
                  </Button>
                </Link>
                <Link href={`./contract-management?termination_contract_id=${actionGuideContract.id}`}>
                  <Button color="red" size="small" variant="outlined">
                    Country Termination
                  </Button>
                </Link>
              </div>
            )}
          </div>
        )}
      </div>
      {!!contracts && !!contracts.length && <CountryTerminationModal contracts={contracts} />}
    </>
  );
}

interface ContractItemProps {
  contract: Contract;
}

function ContractItem({ contract }: ContractItemProps) {
  const { customer } = useCustomer();

  const downloadFileMutation = useMutation({
    mutationFn: async (file: UploadedFile) => {
      const downloadedFile = await downloadCustomerFile(file.id);

      downloadFile({ buffer: downloadedFile, fileName: file.original_name });
    },
  });

  function downloadContractFile(file: UploadedFile) {
    downloadFileMutation.mutate(file, {
      onSuccess: () => {
        enqueueSnackbar("File downloaded successfully", { variant: "success" });
      },
      onError: () => {
        enqueueSnackbar("Failed to download file", { variant: "error" });
      },
    });
  }

  const { mutate: handleRevokeTermination, isPending: isRevoking } = useMutation({
    mutationFn: async ({ terminationId, endDate }: { terminationId: number; endDate: string }) => {
      const currentDate = new Date();
      const contractEndDate = new Date(endDate);

      if (contractEndDate < currentDate) {
        enqueueSnackbar("You can’t revoke the termination after the contract cancellation date", {
          variant: "error",
        });
        throw new Error("Contract end date has passed");
      }

      try {
        const response = await revokeTermination(terminationId);
        enqueueSnackbar("Termination revoked successfully", { variant: "success" });
        queryClient.invalidateQueries({ queryKey: ["contracts", customer!.id] });
        return response;
      } catch (error) {
        enqueueSnackbar("Failed to revoke termination. Please try again.", { variant: "error" });
        throw error;
      }
    },
  });

  const subItemKey = contract.type === "ACTION_GUIDE" ? "action_guides" : "licenses";

  return (
    <>
      <div style={{ filter: contract.status !== "ACTIVE" ? "grayscale(100%)" : "none" }}>
        <div className="flex flex-col md:flex-row justify-between w-full md:items-center mb-2">
          <div className="flex flex-row gap-1 items-center mb-4 md:mb-0">
            <File className="size-6 fill-primary" />
            <h4
              className={cn(
                "flex-1 font-bold text-primary mr-2",
                contract.status === "TERMINATION_PROCESS" && "text-on-surface-01"
              )}
            >
              {contract.title}
            </h4>
            <TooltipIcon info={`Section for ${contract.title}`} />
          </div>

          {contract.status !== "TERMINATED" && (
            <div
              className={cn(
                "flex items-center gap-2",
                contract.status === "ACTIVE" ? "text-success" : "text-on-surface-01"
              )}
            >
              <BsCircleFill className="size-3" />
              <span className="font-semibold text-sm">
                {contract.status === "ACTIVE" && "Annual renewal"}
                {contract.status === "TERMINATION_PROCESS" && "In termination proccess"}
              </span>
            </div>
          )}
        </div>

        <div className="flex justify-between items-center mb-4">
          <div className="flex flex-col text-xs text-on-surface-01">
            <span>Start date: {formatDateToDDMMYYYY(contract.start_date)}</span>
            {!!contract.termination && contract.termination.status === "REQUESTED" && (
              <span>Termination request date: {formatDateToDDMMYYYY(contract.termination.requested_at)}</span>
            )}
            {!!contract.termination &&
              contract.termination.status !== "REQUESTED" &&
              contract.termination.completed_at && (
                <span>Termination date: {formatDateToDDMMYYYY(contract.end_date)}</span>
              )}
          </div>
          <div>
            {!!contract.termination && (
              <Button
                color="gray"
                size="small"
                variant="text"
                className="underline underline-offset-2"
                onClick={() =>
                  !!contract.termination &&
                  handleRevokeTermination({ terminationId: contract.termination.id, endDate: contract.end_date })
                }
                disabled={isRevoking}
                trailingIcon={isRevoking && <CgSpinnerAlt className="animate-spin" />}
              >
                {isRevoking ? `Revoking...` : "Revoke termination"}
              </Button>
            )}
          </div>
        </div>
        {!!contract.files && !!contract.files.length && (
          <>
            <Divider initialMarginDisabled className="mt-0 mb-4" />
            <table className="w-full table-auto mb-6">
              <tbody>
                {contract.files?.map((contractFile) => (
                  <tr key={contractFile.id} className="text-sm text-on-tertiary py-3">
                    <td>
                      <span className="font-bold">{contractFile.original_name}</span>
                      <div className="flex md:hidden flex-row gap-4">
                        <span className="font-medium text-on-surface-01">{contractFile.size}</span>
                        <span className="font-medium text-on-surface-01">{contractFile.created_at}</span>
                      </div>
                    </td>
                    <td>
                      <span className="font-medium hidden md:block text-on-surface-01">
                        {formatFileSize(Number(contractFile.size))}
                      </span>
                    </td>
                    <td>
                      <span className="font-medium hidden md:block text-on-surface-01">
                        {formatDateToDDMMYYYY(contractFile.created_at)}
                      </span>
                    </td>
                    <td>
                      <Button
                        variant="text"
                        color="dark-blue"
                        size="iconSmall"
                        disabled={
                          downloadFileMutation.isPending && downloadFileMutation.variables.id === contractFile.id
                        }
                        onClick={() => downloadContractFile(contractFile)}
                        leadingIcon={
                          downloadFileMutation.isPending && downloadFileMutation.variables.id === contractFile.id ? (
                            <CgSpinnerAlt className="animate-spin" />
                          ) : (
                            <Download />
                          )
                        }
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </>
        )}
        {!!contract.termination && !!contract.termination.files && !!contract.termination.files.length && (
          <>
            <Divider initialMarginDisabled className="mt-0 mb-4" />
            <div className="space-y-2 mb-3">
              <p className="text-tonal-dark-cream-20 italic">Extraordinary termination</p>
              <table className="w-full table-auto mb-6">
                <tbody>
                  <tr className="text-sm text-on-tertiary">
                    <td>
                      <span>{contract.termination.files[0].original_name}</span>
                      <div className="flex md:hidden flex-row gap-4">
                        <span className="font-medium text-on-surface-01">{contract.termination.files[0].size}</span>
                        <span className="font-medium text-on-surface-01">
                          {contract.termination.files[0].created_at}
                        </span>
                      </div>
                    </td>
                    <td>
                      <span className="font-medium hidden md:block text-on-surface-01">
                        {formatFileSize(Number(contract.termination.files[0].size))}
                      </span>
                    </td>
                    <td>
                      <span className="font-medium hidden md:block text-on-surface-01">
                        {formatDateToDDMMYYYY(contract.termination.files[0].created_at)}
                      </span>
                    </td>
                    <td>
                      <Button
                        variant="text"
                        color="dark-blue"
                        size="iconSmall"
                        disabled={
                          downloadFileMutation.isPending &&
                          downloadFileMutation.variables.id === contract.termination.files[0]?.id
                        }
                        onClick={() =>
                          !!contract.termination?.files?.length && downloadContractFile(contract.termination.files[0])
                        }
                        leadingIcon={
                          downloadFileMutation.isPending &&
                          downloadFileMutation.variables.id === contract.termination.files[0]?.id ? (
                            <CgSpinnerAlt className="animate-spin" />
                          ) : (
                            <Download />
                          )
                        }
                      />
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </>
        )}
      </div>
      {contract.type !== "DIRECT_LICENSE" && (
        <>
          <Divider initialMarginDisabled className="mb-4" />
          <details className="group" open>
            <summary className="flex items-center gap-2 cursor-pointer text-sm font-semibold hover:underline underline-offset-2 text-support-blue">
              <KeyboardArrowDown className="fill-support-blue size-6 group-open:rotate-180 transition-transform duration-300" />
              Countries ({contract[subItemKey].length}/{contract[subItemKey].length})
            </summary>
            <div className="flex flex-col gap-6 pl-6 mt-4">
              {contract[subItemKey].map((contractItem) => (
                <ContractSubItem
                  key={contractItem.id}
                  contract={contract}
                  item={contractItem}
                  onRevokeTermination={(terminationId) =>
                    handleRevokeTermination({ terminationId, endDate: contract.end_date })
                  }
                  isRevoking={isRevoking}
                />
              ))}
            </div>
          </details>
        </>
      )}
    </>
  );
}

interface ContractSubItemProps {
  item: License | ActionGuide;
  contract: Contract;
  onRevokeTermination: (id: number) => void;
  isRevoking: boolean;
}

function ContractSubItem({ item, contract, onRevokeTermination, isRevoking }: ContractSubItemProps) {
  const downloadFileMutation = useMutation({
    mutationFn: async (file: UploadedFile) => {
      const downloadedFile = await downloadCustomerFile(file.id);

      downloadFile({ buffer: downloadedFile, fileName: file.original_name });
    },
  });

  function downloadContractFile(file: UploadedFile) {
    downloadFileMutation.mutate(file, {
      onSuccess: () => {
        enqueueSnackbar("File downloaded successfully", { variant: "success" });
      },
      onError: () => {
        enqueueSnackbar("Failed to download file", { variant: "error" });
      },
    });
  }

  const termination = contract?.termination || item?.termination || null;
  const isContractTermination = !!contract?.termination;
  const terminationExtraordinaryFiles = (termination?.files || []).filter(
    (file) => file.type === "CONTRACT_TERMINATION"
  );
  const terminationDate = (() => {
    if (isContractTermination) return contract.end_date;

    if ("end_date" in item && item.end_date) return item.end_date;

    return contract.end_date;
  })();
  // TODO: Cost Condition Sheet
  const itemFiles = [] as UploadedFile[];

  return (
    <div style={{ filter: item.contract_status !== "ACTIVE" ? "grayscale(100%)" : "none" }}>
      <div className="flex flex-col md:flex-row justify-between w-full md:items-center mb-2">
        <div className="flex flex-row gap-2 items-center mb-4 md:mb-0">
          <CountryIcon country={{ name: item.country_name, flag_url: item.country_flag }} />
          <h4
            className={cn(
              "text-base flex-1 font-bold text-primary mr-2",
              item.contract_status === "TERMINATION_PROCESS" && "text-on-surface-01"
            )}
          >
            Country Contract ({item.country_name})
          </h4>
          <TooltipIcon info={`Section for ${`Country Contract (${item.country_name})`}`} />
        </div>

        <div
          className={cn(
            "flex items-center gap-2",
            item.contract_status === "ACTIVE" ? "text-success" : "text-on-surface-01"
          )}
        >
          <BsCircleFill className="size-3" />
          <span className="font-semibold text-sm">{getContractStatusLabel(item.contract_status)}</span>
        </div>
      </div>

      <div className="flex justify-between items-center">
        <div className="flex flex-col text-xs text-on-surface-01 space-y-1">
          <span>Start date: {formatDateToDDMMYYYY(contract.start_date)}</span>
          {!!termination && termination.status !== "COMPLETED" && (
            <span>Termination request date: {formatDateToDDMMYYYY(termination.requested_at)}</span>
          )}
          {!!termination && termination.status !== "REQUESTED" && termination.completed_at && (
            <span>Termination date: {formatDateToDDMMYYYY(terminationDate)}</span>
          )}
        </div>
        <div>
          {termination && !contract.termination && (
            <Button
              color="gray"
              size="small"
              variant="text"
              className="underline underline-offset-2"
              onClick={() => onRevokeTermination(termination.id)}
              disabled={isRevoking}
              trailingIcon={isRevoking && <CgSpinnerAlt className="animate-spin" />}
            >
              {isRevoking ? `Revoking...` : "Revoke termination"}
            </Button>
          )}
        </div>
      </div>
      {!!itemFiles?.length && (
        <>
          <Divider initialMarginDisabled className="my-3" />
          <table className="w-full table-auto mb-6">
            <tbody>
              {itemFiles.map((file: UploadedFile) => {
                return (
                  <tr key={file.id} className="text-sm text-on-tertiary">
                    <td>
                      <span className="font-bold">{file.original_name}</span>
                      <div className="flex md:hidden flex-row gap-4">
                        <span className="font-medium text-on-surface-01">{file.size}</span>
                        <span className="font-medium text-on-surface-01">{file.created_at}</span>
                      </div>
                    </td>
                    <td>
                      <span className="font-medium hidden md:block text-on-surface-01">
                        {formatFileSize(Number(file.size))}
                      </span>
                    </td>
                    <td>
                      <span className="font-medium hidden md:block text-on-surface-01">
                        {formatDateToDDMMYYYY(file.created_at)}
                      </span>
                    </td>
                    <td>
                      <Button
                        variant="text"
                        color="dark-blue"
                        size="iconSmall"
                        disabled={downloadFileMutation.isPending && downloadFileMutation.variables.id === file.id}
                        onClick={() => !!contract.termination?.files?.length && downloadContractFile(file)}
                        leadingIcon={
                          downloadFileMutation.isPending && downloadFileMutation.variables.id === file?.id ? (
                            <CgSpinnerAlt className="animate-spin" />
                          ) : (
                            <Download />
                          )
                        }
                      />
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </>
      )}
      {!!termination && !isContractTermination && !!terminationExtraordinaryFiles.length && (
        <>
          <Divider initialMarginDisabled className="mt-0 mb-4" />
          <div className="space-y-2 mb-3">
            <p className="text-tonal-dark-cream-20 italic">Extraordinary termination</p>
            <table className="w-full table-auto mb-6">
              <tbody>
                {terminationExtraordinaryFiles.map((file) => (
                  <tr key={file.id} className="text-sm text-on-tertiary">
                    <td>
                      <span>{file.original_name}</span>
                      <div className="flex md:hidden flex-row gap-4">
                        <span className="font-medium text-on-surface-01">{file.size}</span>
                        <span className="font-medium text-on-surface-01">{file.created_at}</span>
                      </div>
                    </td>
                    <td>
                      <span className="font-medium hidden md:block text-on-surface-01">
                        {formatFileSize(Number(file.size))}
                      </span>
                    </td>
                    <td>
                      <span className="font-medium hidden md:block text-on-surface-01">
                        {formatDateToDDMMYYYY(file.created_at)}
                      </span>
                    </td>
                    <td>
                      <Button
                        variant="text"
                        color="dark-blue"
                        size="iconSmall"
                        disabled={downloadFileMutation.isPending && downloadFileMutation.variables.id === file.id}
                        onClick={() => !!contract.termination?.files?.length && downloadContractFile(file)}
                        leadingIcon={
                          downloadFileMutation.isPending && downloadFileMutation.variables.id === file?.id ? (
                            <CgSpinnerAlt className="animate-spin" />
                          ) : (
                            <Download />
                          )
                        }
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </>
      )}
    </div>
  );
}
