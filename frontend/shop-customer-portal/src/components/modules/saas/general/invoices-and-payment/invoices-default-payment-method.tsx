"use client";

import { <PERSON><PERSON><PERSON> } from "@/components/_common/card-brand";
import { CardCvcInput } from "@/components/_common/credit-card/card-cvc";
import { CardExpDateInput } from "@/components/_common/credit-card/card-exp-date";
import { CardNumberInput } from "@/components/_common/credit-card/card-number";
import { TooltipIcon } from "@/components/_common/tooltipIcon";
import { JourneyBillingPaymentMethods } from "@/components/modules/shop/journeys/components/journey-billing/journey-billing-payment-methods";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { useCustomer } from "@/hooks/use-customer";
import {
  createPaymentMethod,
  deletePaymentMethod,
  getCustomerActivePaymentMethods,
  getPaymentCustomer,
  updateDefaultPaymentMethod,
} from "@/lib/api/payment";
import { PaymentMethodType } from "@/lib/api/payment/types";
import { queryClient } from "@/lib/react-query";
import { SPECIAL_CHARS_REGEX } from "@/utils/regex";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import {
  AlipayLogo,
  EpsLogo,
  IdealLogo,
  KlarnaLogo,
  MastercardLogo,
  PaypalLogo,
  VisaLogo,
} from "@arthursenno/lizenzero-ui-react/Figure";
import { Check, East, RadioSelected, RadioUnselected } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { AlertDialogAction, AlertDialogCancel } from "@radix-ui/react-alert-dialog";
import { CardNumberElement, useElements, useStripe } from "@stripe/react-stripe-js";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { useForm } from "react-hook-form";

import { z } from "zod";

const defaultPaymentMethodFormSchema = (t: any) =>
  z.object({
    card: z
      .object({
        firstName: z
          .string()
          .min(2, t("validation.minimumLength", { min: 2 }))
          .refine((value) => SPECIAL_CHARS_REGEX.test(value), {
            message: t("validation.specialCharacters"),
          })
          .optional()
          .or(z.literal("")),

        surname: z
          .string()
          .min(2, t("validation.minimumLength", { min: 2 }))
          .refine((value) => SPECIAL_CHARS_REGEX.test(value), {
            message: t("validation.specialCharacters"),
          })
          .optional()
          .or(z.literal("")),
        cardNumber: z.string().min(1, { message: "Please enter a valid card number" }).optional().or(z.literal("")),
        cvc: z.string().min(1, { message: "Please enter a valid cvc" }).optional().or(z.literal("")),
        expirationDate: z
          .string()
          .min(1, { message: "Please enter a valid expiration date" })
          .optional()
          .or(z.literal("")),
      })
      .optional(),
  });

type DefaultPaymentMethodFormData = z.infer<ReturnType<typeof defaultPaymentMethodFormSchema>>;

export function InvoicesDefaultPaymentMethod() {
  const t = useTranslations("shop.common.journey.billing");
  const globalT = useTranslations("global");

  const { customer } = useCustomer();

  const [defaultPaymentMethod, setDefaultPaymentMethod] = useState<PaymentMethodType | string>();
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethodType | string>("CREDIT_CARD");
  const [isEdit, setIsEdit] = useState(false);

  const form = useForm<DefaultPaymentMethodFormData>({
    resolver: zodResolver(defaultPaymentMethodFormSchema(globalT)),
    mode: "all",
  });

  const stripe = useStripe();
  const elements = useElements();

  const paymentCustomerQuery = useQuery({
    queryKey: ["payment-customer"],
    queryFn: async () => {
      if (!customer?.id) return null;

      const paymentCustomer = await getPaymentCustomer(customer.id);

      if (paymentCustomer.default_payment_method) {
        setDefaultPaymentMethod(paymentCustomer.default_payment_method);
        setPaymentMethod(paymentCustomer.default_payment_method);
      }

      return paymentCustomer;
    },
    enabled: !!customer?.id,
  });

  const customerPaymentMethodsQuery = useQuery({
    queryKey: ["customer-payment-methods"],
    queryFn: async () => {
      if (!customer?.id) return null;

      const customerPaymentMethods = await getCustomerActivePaymentMethods(customer.id);

      if (!customerPaymentMethods || !customerPaymentMethods.length) return null;

      return customerPaymentMethods;
    },
    enabled: !!customer?.id,
  });

  const removePaymentMethodMutation = useMutation({
    mutationFn: deletePaymentMethod,
  });

  const updateDefaultPaymentMethodMutation = useMutation({
    mutationFn: (newDefaultPaymentMethod: string | null) =>
      updateDefaultPaymentMethod(customer!.id, newDefaultPaymentMethod),
  });

  function handleSetPaymentMethod(method: PaymentMethodType | string) {
    setPaymentMethod(method);
  }

  async function handleRemoveCard(id: string) {
    removePaymentMethodMutation.mutate(id, {
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: ["customer-payment-methods"] });
        queryClient.invalidateQueries({ queryKey: ["payment-customer"] });
        queryClient.invalidateQueries({ queryKey: ["modal-customer-payment-methods"] });
        queryClient.invalidateQueries({ queryKey: ["modal-payment-customer"] });
        enqueueSnackbar("Card removed successfully", { variant: "success", autoHideDuration: 1000 });
      },
      onError: () => {
        enqueueSnackbar("Failed to remove card. Please try again.", { variant: "error", autoHideDuration: 1000 });
      },
    });
  }

  async function handleSaveDefaultPaymentMethod(data: DefaultPaymentMethodFormData) {
    if (!customer || !stripe || !elements) return;

    if (paymentMethod === "invoice") {
      return;
    }

    let newDefaultPaymentMethod = paymentMethod;
    if (paymentMethod === "CREDIT_CARD") {
      if (!data.card?.firstName) form.setError("card.firstName", { message: "Please enter a valid first name" });
      if (!data.card?.surname) form.setError("card.surname", { message: "Please enter a valid surname" });

      try {
        const {
          paymentMethod: stripePaymentMethod,
          error,
          ...rest
        } = await stripe.createPaymentMethod({
          type: "card",
          card: elements.getElement(CardNumberElement)!,
          billing_details: {
            name: `${data.card?.firstName} ${data.card?.surname}`,
          },
        });

        if (error || !stripePaymentMethod) throw new Error(error?.message || "Failed to set payment method");

        const paymentMethodResponse = await createPaymentMethod({
          customer_id: customer.id,
          platform: "stripe",
          platform_payment_method_id: stripePaymentMethod.id,
          type: "CREDIT_CARD",
          card_last_4: stripePaymentMethod.card?.last4,
          card_brand: stripePaymentMethod.card?.brand,
          card_country: stripePaymentMethod.card?.country || "",
          saved_for_future_purchase: true,
        });

        if (!paymentMethodResponse.success) throw new Error("Failed to create payment method");

        queryClient.invalidateQueries({ queryKey: ["customer-payment-methods"] });
        queryClient.invalidateQueries({ queryKey: ["modal-customer-payment-methods"] });

        const paymentMethodId = paymentMethodResponse.data.id;
        newDefaultPaymentMethod = paymentMethodId;
      } catch (err) {
        console.error("Error:", err);
        enqueueSnackbar("An error occurred while selecting the payment method", { variant: "error" });
        return;
      }
    }

    updateDefaultPaymentMethodMutation.mutate(newDefaultPaymentMethod, {
      onSuccess: () => {
        setDefaultPaymentMethod(newDefaultPaymentMethod);
        setIsEdit(false);
        enqueueSnackbar("Payment method selected", { variant: "success", autoHideDuration: 1000 });
      },
      onError: () =>
        enqueueSnackbar("An error occurred while selecting the payment method", {
          variant: "error",
          autoHideDuration: 1000,
        }),
    });
  }

  const customerDefaultCreditCard = customerPaymentMethodsQuery.data?.find((pm) => pm.id === defaultPaymentMethod);

  const isLoading = customerPaymentMethodsQuery.isLoading || paymentCustomerQuery.isLoading;

  return (
    <div className="rounded-[40px] px-4 py-6 md:px-8 md:py-10 w-full bg-surface-02 mt-14">
      <div className="flex items-center gap-4">
        <p className="text-grey-blue font-bold text-2xl">Payment Method</p>
        {!isLoading && !isEdit && (
          <button
            className="flex items-center gap-2 text-support-blue text-base/none font-bold"
            onClick={() => setIsEdit(true)}
          >
            Edit
          </button>
        )}
      </div>

      {isLoading && (
        <div className="mt-9 w-full bg-white rounded-2xl">
          <div className="px-4 py-5 text-primary font-bold">
            <Skeleton className="w-24 h-8" />
          </div>
          <div className="p-5 flex items-center justify-between">
            <Skeleton className="w-48 h-8" />
            <Skeleton className="w-24 h-8" />
          </div>
        </div>
      )}
      {!isLoading && isEdit && (
        <>
          <div className="mt-9 w-full bg-white rounded-2xl">
            <div className="flex flex-col gap-x-6">
              {customerPaymentMethodsQuery.data?.map((pm, index) => (
                <div
                  key={`saved-payment-method-[${index}]`}
                  className="flex gap-3 items-center justify-between p-5 border-b-2 border-b-surface-01"
                  onClick={() => handleSetPaymentMethod(pm.id)}
                >
                  <div className="flex items-center gap-3">
                    {paymentMethod === pm.id ? (
                      <RadioSelected width={24} className="fill-primary" />
                    ) : (
                      <RadioUnselected width={24} className="fill-on-surface-01" />
                    )}
                    <CardBrand brand={pm.card_brand} />
                    <p className="text-primary">**** {pm.card_last_4}</p>
                  </div>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="text" color="light-blue" size="small">
                        Remove
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete payment method?</AlertDialogTitle>
                        <AlertDialogDescription>
                          By clicking on ”confirm” you are deleting this credit card.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel
                          asChild
                          disabled={
                            removePaymentMethodMutation.variables === pm.id && removePaymentMethodMutation.isPending
                          }
                        >
                          <Button color="dark-blue" variant="outlined" size="medium">
                            Back
                          </Button>
                        </AlertDialogCancel>
                        <AlertDialogAction
                          asChild
                          disabled={
                            removePaymentMethodMutation.variables === pm.id && removePaymentMethodMutation.isPending
                          }
                        >
                          <Button
                            onClick={(e) => {
                              e.preventDefault();
                              handleRemoveCard(pm.id);
                            }}
                            variant="filled"
                            size="medium"
                            color="yellow"
                          >
                            {removePaymentMethodMutation.variables === pm.id && removePaymentMethodMutation.isPending
                              ? "Deleting..."
                              : "Confirm"}
                          </Button>
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              ))}
              <div
                className="flex flex-row gap-3 items-center p-5"
                onClick={() => handleSetPaymentMethod("CREDIT_CARD")}
              >
                {paymentMethod === "CREDIT_CARD" ? (
                  <RadioSelected width={24} className="fill-primary" />
                ) : (
                  <RadioUnselected width={24} className="fill-on-surface-01" />
                )}

                <VisaLogo width={40} />
                <MastercardLogo width={40} />
                <p className="text-primary">{t("creditCard.label")}</p>
              </div>
              {paymentMethod === "CREDIT_CARD" && (
                <div className="flex flex-col gap-2">
                  <div className="flex flex-col md:flex-row gap-2 md:gap-6 px-5">
                    <Input
                      label={t("surname.label")}
                      placeholder={t("surname.placeholder")}
                      errorMessage={form.formState.errors.card?.surname && form.formState.errors.card?.surname.message}
                      variant={form.formState.errors.card?.surname && "error"}
                      rightIcon={
                        !form.formState.errors.card?.surname &&
                        form.getValues("card.surname") && (
                          <Check width={20} height={20} className="fill-tonal-green-40" />
                        )
                      }
                      {...form.register("card.surname")}
                    />
                    <Input
                      label={t("firstName.label")}
                      placeholder={t("firstName.placeholder")}
                      errorMessage={
                        form.formState.errors.card?.firstName && form.formState.errors.card?.firstName.message
                      }
                      variant={form.formState.errors.card?.firstName && "error"}
                      rightIcon={
                        !form.formState.errors.card?.firstName &&
                        form.getValues("card.firstName") && (
                          <Check width={20} height={20} className="fill-tonal-green-40" />
                        )
                      }
                      {...form.register("card.firstName")}
                    />
                  </div>

                  <div className="flex flex-col items-start md:flex-row md:items-start gap-2 px-5">
                    <CardNumberInput />
                    <div className="w-1/4 md:min-w-[20%]">
                      <CardCvcInput />
                    </div>
                    <div className="w-1/4 md:min-w-[20%]">
                      <CardExpDateInput />
                    </div>
                  </div>
                </div>
              )}
              <div
                className="flex flex-row gap-3 items-center p-5 border-t-2 border-t-surface-01"
                onClick={(e) => handleSetPaymentMethod("INVOICE")}
              >
                {paymentMethod === "INVOICE" ? (
                  <RadioSelected width={24} className="fill-primary" />
                ) : (
                  <RadioUnselected width={24} className="fill-tonal-dark-cream-60" />
                )}
                <VisaLogo width={40} />
                <p className="text-grey-blue text-base">{t("invoice.label")}</p>
                <TooltipIcon info={t("invoice.tooltip")} sizeIcon={20} />
              </div>
              <JourneyBillingPaymentMethods
                paymentMethod={paymentMethod as PaymentMethodType}
                handleSetPaymentMethod={handleSetPaymentMethod}
              />
            </div>
          </div>
          <div className="flex justify-end gap-6 items-center mt-8">
            <Button color="dark-blue" variant="outlined" size="medium" onClick={() => setIsEdit(false)}>
              Cancel
            </Button>
            <Button
              color={Boolean(Object.keys(form.formState.errors).length) ? "red" : "yellow"}
              variant="filled"
              size="medium"
              trailingIcon={<East />}
              onClick={form.handleSubmit(handleSaveDefaultPaymentMethod)}
              disabled={Boolean(Object.keys(form.formState.errors).length) || form.formState.isSubmitting}
            >
              {form.formState.isSubmitting ? "Pending..." : "Save"}
            </Button>
          </div>
        </>
      )}
      {!isLoading && !isEdit && (
        <div className="mt-9 w-full bg-white rounded-2xl">
          {customerDefaultCreditCard ? (
            <>
              <div className="px-4 py-5 text-primary font-bold">{t("creditCard.label")}</div>
              <div className="p-5 flex items-center justify-between text-primary">
                <div className="flex items-center gap-2 text-tonal-dark-cream-30">
                  <span className="mt-1">**** **** ****</span>
                  {customerDefaultCreditCard?.card_last_4}
                </div>
                <CardBrand brand={customerDefaultCreditCard?.card_brand} />
              </div>
            </>
          ) : (
            renderDefaultPaymentMethod(defaultPaymentMethod as PaymentMethodType)
          )}
        </div>
      )}
    </div>
  );
}

const DefaultPaymentMethod = ({ label, Logo }: { label: string; Logo: any }) => {
  return (
    <div className="flex flex-row gap-3 items-center p-10">
      <Logo width={40} />
      <p className="text-grey-blue text-base">{label}</p>
      <TooltipIcon info={label} sizeIcon={20} />
    </div>
  );
};

const renderDefaultPaymentMethod = (paymentMethod: PaymentMethodType | null) => {
  switch (paymentMethod) {
    case "INVOICE":
      return <DefaultPaymentMethod label="Invoice" Logo={VisaLogo} />;
    case "PAYPAL":
      return <DefaultPaymentMethod label="Paypal" Logo={PaypalLogo} />;
    case "ALIPAY":
      return <DefaultPaymentMethod label="Alipay" Logo={AlipayLogo} />;
    case "KLARNA":
      return <DefaultPaymentMethod label="Klarna" Logo={KlarnaLogo} />;
    case "EPS":
      return <DefaultPaymentMethod label="EPS" Logo={EpsLogo} />;
    case "IDEAL":
      return <DefaultPaymentMethod label="iDeal" Logo={IdealLogo} />;
    default:
      return (
        <div className="p-5">
          <p className="text-primary">No default payment method selected</p>
        </div>
      );
  }
};
