"use client";

import { <PERSON><PERSON><PERSON> } from "@/components/_common/card-brand";
import { CardCvcInput } from "@/components/_common/credit-card/card-cvc";
import { CardExpDateInput } from "@/components/_common/credit-card/card-exp-date";
import { CardNumberInput } from "@/components/_common/credit-card/card-number";

import { Divider } from "@/components/_common/divider";
import { TooltipIcon } from "@/components/_common/tooltipIcon";
import { JourneyBillingPaymentMethods } from "@/components/modules/shop/journeys/components/journey-billing/journey-billing-payment-methods";
import { Order } from "@/lib/api/orders/types";
import {
  createCheckoutSession,
  createPaymentMethod,
  createPurchaseForExistingOrder,
  getCustomerActivePaymentMethods,
  getPaymentCustomer,
} from "@/lib/api/payment";
import { PaymentMethodType } from "@/lib/api/payment/types";
import { queryClient } from "@/lib/react-query";
import { useCustomer } from "@/hooks/use-customer";
import { formatCurrency } from "@/utils/formatCurrency";
import { SPECIAL_CHARS_REGEX } from "@/utils/regex";
import { STRIPE_CHECKOUT_PAYMENT_METHODS } from "@/utils/system-consts";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import {
  AlipayLogo,
  EpsLogo,
  IdealLogo,
  KlarnaLogo,
  MastercardLogo,
  PaypalLogo,
  VisaLogo,
} from "@arthursenno/lizenzero-ui-react/Figure";
import { Check, Download, East, RadioSelected, RadioUnselected } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { zodResolver } from "@hookform/resolvers/zod";
import { CardNumberElement, useElements, useStripe } from "@stripe/react-stripe-js";
import { useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { CgClose, CgSpinnerAlt } from "react-icons/cg";

import { z } from "zod";

const schema = (t: any) =>
  z.object({
    card: z
      .object({
        firstName: z
          .string()
          .min(2, t("validation.minimumLength", { min: 2 }))
          .refine((value) => SPECIAL_CHARS_REGEX.test(value), {
            message: t("validation.specialCharacters"),
          })
          .optional()
          .or(z.literal("")),

        surname: z
          .string()
          .min(2, t("validation.minimumLength", { min: 2 }))
          .refine((value) => SPECIAL_CHARS_REGEX.test(value), {
            message: t("validation.specialCharacters"),
          })
          .optional()
          .or(z.literal("")),
        cardNumber: z.string().min(1, { message: "Please enter a valid card number" }).optional().or(z.literal("")),
        cvc: z.string().min(1, { message: "Please enter a valid cvc" }).optional().or(z.literal("")),
        expirationDate: z
          .string()
          .min(1, { message: "Please enter a valid expiration date" })
          .optional()
          .or(z.literal("")),
      })
      .optional(),
  });

type PaymentMethodFormData = z.infer<ReturnType<typeof schema>>;

interface InvoicePaymentModalProps {
  order?: Order;
  onClose: () => void;
  handleDownloadOrderInvoice: (orderId: string) => void;
  isDownloadPending?: boolean;
}

export function InvoicePaymentModal({
  order,
  onClose,
  handleDownloadOrderInvoice,
  isDownloadPending,
}: InvoicePaymentModalProps) {
  const t = useTranslations("shop.common.journey.billing");
  const globalT = useTranslations("global");

  const isOpen = !!order;
  const { customer } = useCustomer();
  const [isEdit, setIsEdit] = useState(false);

  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethodType | string>();
  const [defaultPaymentMethod, setDefaultPaymentMethod] = useState<PaymentMethodType | string>();
  const [isLoadinCreateNewCard, setIsLoadinCreateNewCard] = useState(false);

  const customerPaymentMethodsQuery = useQuery({
    queryKey: ["modal-customer-payment-methods"],
    queryFn: async () => {
      if (!customer) return null;

      const customerPaymentMethods = await getCustomerActivePaymentMethods(customer.id);

      if (!customerPaymentMethods || !customerPaymentMethods.length) return null;

      return customerPaymentMethods;
    },
    enabled: !!customer,
  });

  const paymentCustomerQuery = useQuery({
    queryKey: ["modal-payment-customer"],
    queryFn: async () => {
      const paymentCustomer = await getPaymentCustomer(customer!.id);
      if (paymentCustomer.default_payment_method) {
        setDefaultPaymentMethod(paymentCustomer.default_payment_method);
        setSelectedPaymentMethod(paymentCustomer.default_payment_method);
      }
      return paymentCustomer;
    },
    enabled: !!customer,
  });

  const {
    register,
    handleSubmit,
    watch,
    getValues,
    control,
    formState: { errors, isSubmitting, isValid, isSubmitted },
    ...form
  } = useForm<PaymentMethodFormData>({
    resolver: zodResolver(schema(globalT)),
    mode: "all",
  });

  const stripe = useStripe();
  const elements = useElements();

  function handleClose() {
    queryClient.invalidateQueries({ queryKey: ["customer-payment-methods"] });
    queryClient.invalidateQueries({ queryKey: ["payment-customer"] });
    setIsEdit(false);
    onClose();
  }

  async function handleSaveSelectedPaymentMethod() {
    if (!customer || !stripe || !elements) return;
    if (selectedPaymentMethod === "CREDIT_CARD") {
      const firstName = getValues("card.firstName");
      const surname = getValues("card.surname");
      if (!firstName) form.setError("card.firstName", { message: "Please enter a valid first name" });
      if (!surname) form.setError("card.surname", { message: "Please enter a valid surname" });
      if (!firstName || !surname) return;

      try {
        setIsLoadinCreateNewCard(true);
        const {
          paymentMethod: stripePaymentMethod,
          error,
          ...rest
        } = await stripe.createPaymentMethod({
          type: "card",
          card: elements.getElement(CardNumberElement)!,
          billing_details: {
            name: `${firstName} ${surname}`,
          },
        });

        if (error || !stripePaymentMethod) throw new Error(error?.message || "Failed to set payment method");

        const paymentMethodResponse = await createPaymentMethod({
          customer_id: customer.id,
          platform: "stripe",
          platform_payment_method_id: stripePaymentMethod.id,
          type: "CREDIT_CARD",
          card_last_4: stripePaymentMethod.card?.last4,
          card_brand: stripePaymentMethod.card?.brand,
          card_country: stripePaymentMethod.card?.country || "",
          saved_for_future_purchase: true,
        });

        if (!paymentMethodResponse.success) throw new Error("Failed to create payment method");

        queryClient.invalidateQueries({ queryKey: ["customer-payment-methods"] });
        queryClient.invalidateQueries({ queryKey: ["modal-customer-payment-methods"] });

        const paymentMethodId = paymentMethodResponse.data.id;

        setDefaultPaymentMethod(paymentMethodId);
      } catch (err) {
        console.error("Error:", err);
        enqueueSnackbar("An error occurred while selecting the payment method", { variant: "error" });
        return;
      } finally {
        setIsLoadinCreateNewCard(false);
      }
    } else {
      setDefaultPaymentMethod(selectedPaymentMethod);
    }

    setIsEdit(false);
  }

  async function handleFormSubmit(data: PaymentMethodFormData) {
    if (!customer || !stripe || !elements) return;
    const orderId = order?.id;
    if (!orderId) return;
    if (!defaultPaymentMethod) {
      enqueueSnackbar("Please select a payment method", { variant: "error" });
      return;
    }

    if (defaultPaymentMethod === "INVOICE") {
      enqueueSnackbar("Invoice payment is not implemented yet", { variant: "error" });
      return;
    }

    try {
      if (STRIPE_CHECKOUT_PAYMENT_METHODS.includes(defaultPaymentMethod || "")) {
        const sessionId = await createCheckoutSession({
          customer_id: customer.id,
          total: order.amount,
          payment_method_type: defaultPaymentMethod.toLocaleLowerCase(),
          journey: "SAAS",
          currency: customer.currency || "EUR",
        });

        if (!sessionId) throw new Error("Error creating checkout session");

        const { error } = await stripe.redirectToCheckout({ sessionId });

        if (error) {
          console.log("Checkout error:", error.message);
          throw new Error("Checkout error, check console");
        }

        return;
      }

      const response = await createPurchaseForExistingOrder(orderId, defaultPaymentMethod);

      if (response.error) throw new Error(response.error);

      if (response.success) {
        queryClient.invalidateQueries({ queryKey: ["invoices"] });
        enqueueSnackbar("Payment successful", { variant: "success" });
        handleClose();
      }
    } catch (error) {
      console.error("Error:", error);
      enqueueSnackbar("An error occurred.", { variant: "error" });
    }
  }

  const customerDefaultCreditCard = customerPaymentMethodsQuery.data?.find((pm) => pm.id === defaultPaymentMethod);
  const isLoading = customerPaymentMethodsQuery.isLoading || paymentCustomerQuery.isLoading || isSubmitting;

  return (
    <Modal
      open={isOpen}
      className="z-50 w-full"
      style={{ maxWidth: "600px", borderRadius: "52px", overflow: "hidden" }}
    >
      <div className={`flex flex-col p-6 ${isEdit && "max-h-[80vh] overflow-y-hidden"}`}>
        <CgClose onClick={handleClose} className="text-primary cursor-pointer ml-auto" />

        <form className="text-primary overflow-hidden flex flex-col flex-1" onSubmit={handleSubmit(handleFormSubmit)}>
          <div className="flex flex-col gap-4 mb-6">
            <h1 className="text-primary text-[28px] font-bold mb-2">
              {isEdit ? "Change your payment method" : "Pay open invoice"}
            </h1>
            <span className="font-base">
              Download the invoice and check the payment method to conclude the process.
            </span>
          </div>

          {!isEdit && (
            <>
              <div className="flex flex-row justify-between items-center bg-surface-02 p-4 rounded-[20px]">
                <p className="text-primary font-bold">Order Number {order?.id}</p>
                <p className="text-primary font-bold">{formatCurrency(order?.amount)}</p>
                <Button
                  variant="text"
                  color="light-blue"
                  size="iconSmall"
                  disabled={isDownloadPending}
                  leadingIcon={
                    isDownloadPending ? (
                      <CgSpinnerAlt className="animate-spin" />
                    ) : (
                      <Download className="fill-support-blue" />
                    )
                  }
                  onClick={() => handleDownloadOrderInvoice(String(order?.id))}
                >
                  Download
                </Button>
              </div>

              <Divider initialMarginDisabled className="my-6" />

              <div className="flex flex-row items-center justify-between">
                <h2 className="text-xl">Payment Method</h2>
                <button
                  className="flex items-center gap-2 text-support-blue text-base/none font-bold"
                  onClick={() => setIsEdit(true)}
                >
                  Edit
                </button>
              </div>
            </>
          )}

          {isEdit && (
            <div className="bg-white w-full flex-1 overflow-y-auto min-h-0 rounded-2xl border border-surface-03">
              <div className="flex flex-col">
                {customerPaymentMethodsQuery.data?.map((pm, index) => (
                  <div
                    key={`saved-payment-method-[${index}]`}
                    className="flex gap-3 items-center justify-between p-5 border-b-2 border-b-surface-01 cursor-pointer"
                    onClick={() => setSelectedPaymentMethod(pm.id)}
                  >
                    <div className="flex items-center gap-3">
                      {selectedPaymentMethod === pm.id ? (
                        <RadioSelected width={24} className="fill-primary" />
                      ) : (
                        <RadioUnselected width={24} className="fill-on-surface-01" />
                      )}
                      <CardBrand brand={pm.card_brand} />
                      <p className="text-primary">**** {pm.card_last_4}</p>
                    </div>
                  </div>
                ))}
                <div
                  className="flex flex-row gap-3 items-center p-5 cursor-pointer"
                  onClick={() => setSelectedPaymentMethod("CREDIT_CARD")}
                >
                  {selectedPaymentMethod === "CREDIT_CARD" ? (
                    <RadioSelected width={24} className="fill-primary" />
                  ) : (
                    <RadioUnselected width={24} className="fill-on-surface-01" />
                  )}

                  <VisaLogo width={40} />
                  <MastercardLogo width={40} />
                  <p className="text-primary truncate">{t("creditCard.label")}</p>
                </div>

                {selectedPaymentMethod === "CREDIT_CARD" && (
                  <div className="flex flex-col gap-2 p-5 border-t-2 border-t-surface-01">
                    <div className="flex flex-col md:flex-row gap-2 md:gap-6">
                      <Input
                        label={t("surname.label")}
                        placeholder={t("surname.placeholder")}
                        errorMessage={errors.card?.surname && errors.card?.surname.message}
                        variant={errors.card?.surname && "error"}
                        rightIcon={
                          !errors.card?.surname &&
                          getValues("card.surname") && <Check width={20} height={20} className="fill-tonal-green-40" />
                        }
                        {...register("card.surname")}
                      />
                      <Input
                        label={t("firstName.label")}
                        placeholder={t("firstName.placeholder")}
                        errorMessage={errors.card?.firstName && errors.card?.firstName.message}
                        variant={errors.card?.firstName && "error"}
                        rightIcon={
                          !errors.card?.firstName &&
                          getValues("card.firstName") && (
                            <Check width={20} height={20} className="fill-tonal-green-40" />
                          )
                        }
                        {...register("card.firstName")}
                      />
                    </div>

                    <div className="flex flex-col items-start md:flex-row md:items-start gap-2">
                      <CardNumberInput />
                      <div className="w-1/4 md:min-w-[20%]">
                        <CardCvcInput />
                      </div>
                      <div className="w-1/4 md:min-w-[20%]">
                        <CardExpDateInput />
                      </div>
                    </div>
                  </div>
                )}

                <div
                  className="flex flex-row gap-3 items-center p-5 border-t-2 border-t-surface-01 cursor-pointer"
                  onClick={(e) => setSelectedPaymentMethod("INVOICE")}
                >
                  {selectedPaymentMethod === "INVOICE" ? (
                    <RadioSelected width={24} className="fill-primary" />
                  ) : (
                    <RadioUnselected width={24} className="fill-tonal-dark-cream-60" />
                  )}
                  <VisaLogo width={40} />
                  <p className="text-grey-blue text-base truncate">{t("invoice.label")}</p>
                  <TooltipIcon info={t("invoice.tooltip")} sizeIcon={20} />
                </div>

                <div className="overflow-hidden">
                  <JourneyBillingPaymentMethods
                    paymentMethod={selectedPaymentMethod as PaymentMethodType}
                    handleSetPaymentMethod={setSelectedPaymentMethod}
                    borderLess={false}
                  />
                </div>
              </div>
            </div>
          )}

          {!isLoading && !isEdit && (
            <div className="flex flex-col gap-2">
              <div className="mt-6 w-full bg-white rounded-2xl border border-surface-03">
                {customerDefaultCreditCard ? (
                  <>
                    <div className="px-4 py-5 text-primary font-bold">{t("creditCard.label")}</div>
                    <div className="p-5 flex items-center justify-between text-primary">
                      <div className="flex items-center gap-2 text-tonal-dark-cream-30">
                        <span className="mt-1">**** **** ****</span>
                        {customerDefaultCreditCard?.card_last_4}
                      </div>
                      <CardBrand brand={customerDefaultCreditCard?.card_brand} />
                    </div>
                  </>
                ) : (
                  renderDefaultPaymentMethod(defaultPaymentMethod as PaymentMethodType)
                )}
              </div>

              <div className="flex flex-row justify-between items-center p-4 w-full bg-tonal-dark-blue-90 rounded-2xl">
                <p className="text-primary font-bold">Total</p>
                <p className="text-on-tertiary font-bold">{formatCurrency(order?.amount)}</p>
              </div>
            </div>
          )}

          {isEdit ? (
            <div className="flex justify-end gap-6 items-center mt-6">
              <Button color="dark-blue" variant="outlined" size="medium" onClick={() => setIsEdit(false)} type="button">
                Cancel
              </Button>
              <Button
                type="button"
                color="yellow"
                variant="filled"
                size="medium"
                trailingIcon={<East />}
                onClick={handleSaveSelectedPaymentMethod}
                disabled={isLoadinCreateNewCard}
              >
                {isLoadinCreateNewCard ? "Loading..." : " Save"}
              </Button>
            </div>
          ) : (
            <div className="flex justify-end w-full items-center mt-6 gap-10">
              <Button color="yellow" size="medium" variant="filled" disabled={isLoading} type="submit">
                {isLoading || isSubmitting ? "loading" : "Finalize payment"}
              </Button>
            </div>
          )}
        </form>
      </div>
    </Modal>
  );
}

const DefaultPaymentMethod = ({ label, Logo }: { label: string; Logo: any }) => {
  return (
    <div className="flex flex-row gap-3 items-center p-10">
      <Logo width={40} />
      <p className="text-grey-blue text-base">{label}</p>
      <TooltipIcon info={label} sizeIcon={20} />
    </div>
  );
};

const renderDefaultPaymentMethod = (paymentMethod: PaymentMethodType | null) => {
  switch (paymentMethod) {
    case "INVOICE":
      return <DefaultPaymentMethod label="Invoice" Logo={VisaLogo} />;
    case "PAYPAL":
      return <DefaultPaymentMethod label="Paypal" Logo={PaypalLogo} />;
    case "ALIPAY":
      return <DefaultPaymentMethod label="Alipay" Logo={AlipayLogo} />;
    case "KLARNA":
      return <DefaultPaymentMethod label="Klarna" Logo={KlarnaLogo} />;
    case "EPS":
      return <DefaultPaymentMethod label="EPS" Logo={EpsLogo} />;
    case "IDEAL":
      return <DefaultPaymentMethod label="iDeal" Logo={IdealLogo} />;
    default:
      return (
        <div className="p-5">
          <p className="text-primary">No default payment method selected</p>
        </div>
      );
  }
};
