import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East } from "@arthursenno/lizenzero-ui-react/Icon";
import Image from "next/image";

export function PartnerDealsPartners() {
  return (
    <div className="w-full flex justify-center">
      <div className="flex flex-col md:flex-row w-full md:w-4/5 h-80 md:h-144 relative">
        <div className="w-[60vw] md:w-[50vw] bg-grey-blue rounded-[40px] py-2 px-4 md:pt-9 md:pb-10 md:pr-12 flex justify-end absolute left-[-20vw] md:left-[-30vw] z-10 top-5 md:top-24">
          <p className="text-white font-bold text-sm md:text-2xl w-28 md:w-max">NEU: Products with love</p>
        </div>

        <Image
          className="w-full md:w-5/6 h-full rounded-[40px] object-cover"
          src="/assets/images/package.png"
          alt="leaf seal"
          width={300}
          height={576}
        />

        <div className="w-full md:w-2/4 bg-[#AEC6E7] rounded-[40px] h-[57%] md:h-full absolute right-0 flex justify-start md:justify-center items-end md:items-center pb-6 md:pb-0 pl-6 md:pl-0">
          <p className="text-2xl/[36px] md:text-6xl/[78px] text-grey-blue font-bold w-2/4 md:w-9/12">
            Deals from our partners
          </p>
          <div className="p-3 bg-[#AEC6E7] rounded-full absolute bottom-[-30px] right-10 md:bottom-20 md:right-[-50px]">
            <Button trailingIcon={<East />} color="yellow" variant="filled" size="medium">
              Discover Offers
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
