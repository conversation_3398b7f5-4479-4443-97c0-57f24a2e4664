import Image from "next/image";
import { IProductTitle } from "./interfaces";

export function ProductTitle({ title, subTitle }: IProductTitle) {
  return (
    <div className="flex flex-1 flex-col">
      <Image
        className="w-full md:w-3/4"
        src="/assets/images/products_love.png"
        alt="Products love"
        width={320}
        height={55}
      />
      <div className="flex flex-col gap-2 mt-6">
        <p className="text-base md:text-xl font-bold text-grey-blue">{title}</p>
        <p className="text-base text-[#425981]">{subTitle}</p>
      </div>
    </div>
  );
}
