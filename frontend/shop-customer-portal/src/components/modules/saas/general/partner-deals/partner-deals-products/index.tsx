"use client";

import Container from "@/components/_common/container/container";
import { cn } from "@/lib/utils";
import { ShoppingCart } from "@arthursenno/lizenzero-ui-react/Icon";
import { useState } from "react";
import { Product } from "./components/product";

const ALL_BTNS = [
  {
    name: "Marketplace",
    Icon: ShoppingCart,
  },
  {
    name: "Packaging Trade",
    Icon: ShoppingCart,
  },
  {
    name: "Logistic & Fulfillment",
    Icon: ShoppingCart,
  },
  {
    name: "Advice & Associations",
    Icon: ShoppingCart,
  },
  {
    name: "Software",
    Icon: ShoppingCart,
  },
];

export function PartnerDealsProducts() {
  const [menuSelect, setMenuSelect] = useState("Marketplace");

  return (
    <div className="bg-surface-02 w-full mt-10 md:mt-36 rounded-t-[40px] md:rounded-t-[60px] pt-16 px-4 md:px-24 pb-44">
      <Container>
        <div className="flex flex-row w-[calc(100vw-4rem)] md:w-full gap-3 md:gap-4 justify-around overflow-auto pb-4">
          {ALL_BTNS.map(({ name, Icon }) => (
            <button
              className={cn(
                "rounded-[90px] flex-none pt-2 px-8 pb-3 flex flex-row md:flex-col gap-2 justify-center items-center text-base font-bold",
                name === menuSelect ? "bg-grey-blue text-white" : "bg-white md:bg-surface-02 text-grey-blue"
              )}
              key={name}
              onClick={() => setMenuSelect(name)}
            >
              <Icon
                className={cn("size-5 md:size-7 flex-none", name === menuSelect ? "fill-white" : "fill-grey-blue")}
              />
              {name}
            </button>
          ))}
        </div>

        <div className="flex justify-center items-center mt-10 md:mt-16">
          <p className="text-3xl/[41px] md:text-5xl/[62px] font-bold text-grey-blue text-center">
            Your advantages <br /> at marketplace
          </p>
        </div>

        <div className="mt-6 md:mt-16 flex flex-col gap-6 md:gap-12">
          <Product
            position="left"
            file={{
              url: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4",
              type: "VIDEO",
            }}
            cupon={{
              percentageOff: 25,
              subText: "on the premium shop.",
              cupon: "Licenseero25",
            }}
            title="Your marketplace for handmade and DIY materials"
            subTitle="Lorem ipsum dolor sit amet consectetur adipisicing elit. Impedit earum error expedita fuga suscipit fugit sint
          dignissimos similique commodi? Ullam explicabo tempore perferendis quibusdam facere quidem suscipit beatae
          dignissimos totam!"
          />
          <Product
            type="row"
            position="left"
            file={{
              url: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ForBiggerBlazes.mp4",
              type: "VIDEO",
            }}
            cupon={{
              percentageOff: 25,
              subText: "on the premium shop.",
              cupon: "Licenseero25",
            }}
            title="Your marketplace for handmade and DIY materials"
            subTitle="Lorem ipsum dolor sit amet consectetur adipisicing elit. Impedit earum error expedita fuga suscipit fugit sint
          dignissimos similique commodi? Ullam explicabo tempore perferendis quibusdam facere quidem suscipit beatae
          dignissimos totam!"
          />
          <Product
            position="left"
            type="row"
            file={{
              url: "/assets/images/bubbles.png",
              type: "IMAGE",
            }}
            title="Your marketplace for handmade and DIY materials"
            subTitle="Lorem ipsum dolor sit amet consectetur adipisicing elit. Impedit earum error expedita fuga suscipit fugit sint
          dignissimos similique commodi? Ullam explicabo tempore perferendis quibusdam facere quidem suscipit beatae
          dignissimos totam!"
          />
        </div>
      </Container>
    </div>
  );
}
