import { Plant } from "@arthursenno/lizenzero-ui-react/Icon";
import Image from "next/image";

export default function InviteStats() {
  return (
    <div className="flex flex-row w-full relative rounded-[40px] h-[241px] overflow-hidden">
      <Image src={"/assets/images/Planting.png"} fill className="max-w-[52%] hidden md:block" alt="planting" />
      <div className="absolute flex justify-center bg-[#1B6C64] w-full md:w-[55%] right-0 h-full rounded-[40px] px-6">
        <div className="flex flex-col justify-center">
          <span className="text-xl font-bold mb-6">Helping the world little by little</span>

          <div className="flex flex-row gap-2">
            <Plant className="fill-white bg-[#1B6C64] rounded-full size-16 p-2" />
            <div className="flex flex-col justify-end">
              <span className="text-[#D8F2D8] text-[32px] font-bold pb-0">50 m2</span>
              <span className="text-xs">metres of renaturalised moorland </span>
            </div>
          </div>

          <div className="flex flex-row gap-2 items-center">
            <Plant className="fill-white bg-[#1B6C64] rounded-full size-16 p-2" />

            <span className="text-[#D8F2D8] text-[32px] mt-2 font-bold pb-0">34,095</span>
            <span className="text-xs mt-4">trees planted</span>
          </div>
        </div>
      </div>
    </div>
  );
}
