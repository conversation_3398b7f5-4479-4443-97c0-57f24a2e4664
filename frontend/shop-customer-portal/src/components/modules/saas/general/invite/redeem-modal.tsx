"use client";
import { Spinner } from "@/components/ui/loader";
import { createCommissionCoupon, patchUseCommission } from "@/lib/api/commission";
import { useCustomer } from "@/hooks/use-customer";
import { formatCurrency } from "@/utils/formatCurrency";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { FileCopy } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import Image from "next/image";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { CgClose } from "react-icons/cg";

interface RedeemModalProps {
  isOpen: boolean;
  toggleOpen: () => void;
  price: number;
  onReload?: () => void;
}

export default function RedeemModal({ isOpen, toggleOpen, price, onReload }: RedeemModalProps) {
  const { customer } = useCustomer();
  const [step, setStep] = useState<`INITIAL` | `CODE` | `TREE`>(`INITIAL`);
  const [code, setCode] = useState<string>(``);
  const [codeValue, setCodeValue] = useState<number>();
  const [isLoadingPlantTrees, setIsLoadingPlantTrees] = useState(false);
  const [isLoadingCode, setIsLoadingCode] = useState(false);

  const handleClose = () => {
    setStep("INITIAL");
    toggleOpen();
  };

  const handlePlantTrees = async () => {
    if (!customer?.user_id) return;

    setIsLoadingPlantTrees(true);
    try {
      const res: any = await patchUseCommission(customer.user_id);
      if (!res.data) throw new Error(`Error trying to send data`);
      setStep("TREE");
      onReload?.();
    } catch (err) {
      enqueueSnackbar("Error trying to send data", { variant: "error" });
    } finally {
      setIsLoadingPlantTrees(false);
    }
  };

  const handleCreateCode = async () => {
    if (!customer?.user_id) return;

    setIsLoadingCode(true);
    try {
      const res: any = await createCommissionCoupon(customer.user_id);
      if (!res.data) throw new Error(`Error trying to send data`);
      setCodeValue(price);
      setCode(res?.data?.code);
      setStep("CODE");
      onReload?.();
    } catch (err) {
      enqueueSnackbar("Error trying to send data", { variant: "error" });
    } finally {
      setIsLoadingCode(false);
    }
  };

  const handleCopyToClipboard = () => {
    navigator.clipboard.writeText(code);
    alert("Copied code to clipboard");
  };

  const isLoading = isLoadingPlantTrees || isLoadingCode;

  return (
    <Modal
      style={{ borderRadius: "52px", backgroundColor: "#F0F0EF" }}
      className="z-50 w-full max-w-[750px] rounded-[52px] overflow-hidden"
      open={isOpen}
    >
      <div onClick={handleClose} className="bg-white p-2 rounded-full w-fit ml-auto cursor-pointer">
        <CgClose className="text-primary" />
      </div>

      {step === "INITIAL" && (
        <div className="p-6 flex flex-col gap-4 w-full">
          <h3 className="text-primary text-[28px] font-bold">Redeem your credits</h3>
          <p className="text-[#3F3D3C]">
            To redeem your credits you must create a discount code to apply on your next purchase. Be aware that the
            Code only includes the credits value till the day of it’s creation
          </p>
          <p className="text-[#3F3D3C]">Or you can choose an emission offset and plant trees to help our enviroment!</p>
          <div className="flex flex-row gap-6 justify-end mt-4">
            <Button
              onClick={handlePlantTrees}
              variant="outlined"
              color="dark-blue"
              size="medium"
              style={!isLoading ? { color: "#1B6C64", borderColor: "#1B6C64" } : { color: `gray`, borderColor: `gray` }}
              disabled={isLoading}
              trailingIcon={isLoadingPlantTrees && <Spinner size="xs" />}
            >
              Plant a tree!
            </Button>
            <Button
              onClick={handleCreateCode}
              variant="filled"
              size="medium"
              color="dark-blue"
              disabled={isLoading}
              trailingIcon={isLoadingCode && <Spinner size="xs" />}
            >
              Create Code
            </Button>
          </div>
        </div>
      )}

      {step === "CODE" && (
        <div className="flex flex-col gap-4 p-6">
          <h3 className="text-primary text-[28px] font-bold">Redeem your credits</h3>
          <p className="text-[#3F3D3C]">
            This code contains <span className="font-bold">{formatCurrency(codeValue!)}</span>. Use the code to receive
            the discount amount in your next purchase.
          </p>
          <div className="flex items-center justify-between w-fit gap-6 px-4 py-2 bg-background rounded-[40px]">
            <span className="text-primary font-bold">{code}</span>

            <FileCopy
              onClick={handleCopyToClipboard}
              className="fill-primary bg-tertiary rounded-full size-9 p-2 cursor-pointer"
            />
          </div>
          <div className="flex justify-end">
            <Button onClick={handleClose} variant="filled" size="medium" color="dark-blue">
              Close
            </Button>
          </div>
        </div>
      )}

      {step === `TREE` && (
        <div className="flex flex-col gap-4 p-6">
          <div className="flex items-center gap-2">
            <Image alt="tree" src="/assets/images/leaf_seal.png" width={36} height={36} />
            <h3 className="text-tonal-dark-green-30 text-[28px] font-bold">The planet thanks you!</h3>
          </div>
          <p className="text-primary">Here's how you are going to start changing the world</p>
          <TreePlantingData />
          <div className="flex justify-end">
            <Button onClick={handleClose} variant="filled" size="medium" color="yellow">
              Amazing!
            </Button>
          </div>
        </div>
      )}
    </Modal>
  );
}

function TreePlantingData() {
  return (
    <div className="flex items-center justify-between gap-6 h-20">
      <div className="bg-success-container p-4 rounded-[20px] flex flex-1 items-center gap-4 h-full">
        <div className="size-[52px] rounded-full bg-tonal-dark-green-30 flex-none">
          <div className="flex size-full justify-center items-center">
            <Image alt="tree" src="/assets/images/bottle_with_tree.png" width={31} height={31} />
          </div>
        </div>
        <div>
          <p className="text-3xl font-bold text-tonal-dark-green-30">62 m2</p>
          <p className="text-xs text-on-surface-01">metres of renaturalised moorland</p>
        </div>
      </div>

      <div className="bg-success-container p-4 rounded-[20px] flex-1 flex items-center gap-4 h-full">
        <Image alt="tree" src="/assets/images/plant.png" width={40} height={40} />
        <p className="text-[28px] text-tonal-dark-green-30 font-bold">34,095 trees</p>
      </div>
    </div>
  );
}
