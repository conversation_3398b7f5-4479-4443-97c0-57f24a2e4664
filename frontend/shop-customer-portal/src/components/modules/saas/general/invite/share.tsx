import { FileCopy, Link } from "@arthursenno/lizenzero-ui-react/Icon";
import dayjs from "dayjs";

const BASE_URL = typeof window !== "undefined" ? window.location.origin : "";

export default function InviteShare({
  quantityUsed,
  customerInviteToken,
  isLoading,
}: {
  customerInviteToken: any;
  isLoading: boolean;
  quantityUsed?: number;
}) {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="flex flex-col gap-10 col-span-5 bg-[#D8F2D8] p-10 rounded-[40px]">
      <div className="flex flex-col gap-2 text-primary">
        <h3 className="text-[28px] font-bold">Share an Invite</h3>
        <p>Invite people to use Lizenzero to receive a commission.</p>
      </div>

      <hr className="text-on-tertiary" />

      <div className="flex flex-row text-on-tertiary gap-2">
        <span>Uses available to this year ({new Date().getFullYear()}):</span>
        <span className="font-bold text-on-surface-01">
          <span className="text-on-tertiary">{quantityUsed || 0}</span>/100
        </span>
      </div>

      <div className="flex flex-col gap-2">
        <div className="flex items-center justify-between px-4 py-2 bg-background rounded-[40px]">
          <span className="text-primary font-bold">
            {isLoading ? "Loading..." : `${BASE_URL}${customerInviteToken?.share_link}`}
          </span>

          <Link
            className="fill-primary bg-tertiary rounded-full size-9 p-1 cursor-pointer"
            onClick={() => copyToClipboard(`${BASE_URL}${customerInviteToken?.share_link}`)}
          />
        </div>
        <span className="text-sm text-on-surface-01">
          Valid until:{` `}
          {dayjs(customerInviteToken?.end_date).format("DD.MM.YYYY")}
        </span>
      </div>

      <div className="flex flex-col gap-2 w-full">
        <div className="flex items-center justify-between w-full md:w-52 gap-6 px-4 py-2 bg-background rounded-[40px]">
          <span className="text-primary font-bold">{isLoading ? "Loading..." : customerInviteToken?.code}</span>

          <FileCopy
            className="fill-primary bg-tertiary rounded-full size-9 p-2 cursor-pointer"
            onClick={() => copyToClipboard(customerInviteToken?.code)}
          />
        </div>
        <span className="text-sm text-on-surface-01">
          Valid until:{` `}
          {dayjs(customerInviteToken?.end_date).format("DD.MM.YYYY")}
        </span>
      </div>
    </div>
  );
}
