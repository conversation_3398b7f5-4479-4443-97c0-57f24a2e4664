import PaginatedTable from "@/components/_common/tables/paginated-table";
import { StatusBadge } from "@/components/ui/status-badge";
import { TextCopy } from "@/components/ui/text-copy";
import { Coupon } from "@/lib/api/coupon/types";
import { formatCurrency } from "@/utils/formatCurrency";
import { CheckCircle, CheckCircleOutline, KeyboardArrowUp } from "@arthursenno/lizenzero-ui-react/Icon";
import { createColumnHelper } from "@tanstack/react-table";
import dayjs from "dayjs";

const columnHelper = createColumnHelper<Coupon>();

const columns: any[] = [
  columnHelper.accessor("code", {
    header: "Code",
    cell: (info) => {
      return (
        <div className="flex items-center gap-2">
          {info.row.original.is_active ? (
            <CheckCircle className="fill-success size-6" />
          ) : (
            <CheckCircleOutline className="fill-tonal-dark-cream-60 size-6" />
          )}
          <p className="text-base text-tonal-dark-cream-10">{info.row.original.code}</p>
        </div>
      );
    },
  }),
  columnHelper.accessor("value", {
    header: "Amount",
    cell: (info) => formatCurrency(info.getValue<number>()),
  }),
  columnHelper.accessor("used_at", {
    header: "Used on",
    cell: (info) => (info.getValue() ? dayjs(info.getValue<string>()).format("DD.MM.YY") : "-"),
  }),
  columnHelper.accessor("is_active", {
    header: "Status",
    cell: (info) => <Status active={info.getValue()} />,
  }),
];

interface ActiveVoucherProps {
  code: string;
  status: "Active" | "Deactivate";
  price: number;
  vouchers: Coupon[];
}

export default function ActiveVoucher({ code, vouchers, status, price }: ActiveVoucherProps) {
  return (
    <div className="rounded-[40px] p-10 bg-surface-02 flex flex-col gap-6">
      <p className="text-[28px] font-bold text-primary">Active voucher</p>
      <div className="flex gap-4">
        <TextCopy text={code} className="w-2/4" />
        <div className="flex flex-col gap-2 w-2/4">
          <p className="text-tonal-dark-cream-10 text-base">
            Amount: <b className="text-xl">{formatCurrency(price)}</b>
          </p>
          <p className="text-tonal-dark-cream-50 text-sm">Status: {status}</p>
        </div>
      </div>
      <details className="group">
        <summary className="flex items-center gap-2">
          <p className="text-tonal-dark-cream-10 text-base font-bold">Previously vouchers</p>
          <KeyboardArrowUp className="flex-none size-6 fill-tonal-blue-40 group-open:-rotate-180 transition-all duration-300" />
        </summary>

        <div className="mt-6">
          <PaginatedTable data={vouchers} columns={columns} />
        </div>
      </details>
    </div>
  );
}

function Status({ active }: { active: boolean }) {
  return (
    <div
      data-active={active}
      className="px-4 py-2 rounded-xl bg-tonal-dark-cream-80 data-[active='true']:bg-success-container flex justify-center items-center w-min"
    >
      <p data-active={active} className="text-tonal-dark-cream-30 data-[active='true']:text-success font-bold">
        {active ? "Active" : "Used"}
      </p>
    </div>
  );
}
