"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { ReactNode } from "react";
import { FormProvider, useForm } from "react-hook-form";
import { z } from "zod";

const licenseAssessmentFormSchema = z.object({
  countries: z.record(
    z.string(),
    z.object({
      country: z.object({
        id: z.number(),
        code: z.string(),
        flag_url: z.string(),
        name: z.string(),
      }),
      commitment: z.object({
        filled: z.boolean().refine((filled) => !!filled, { message: "Please confirm your answers." }),
        questions: z.record(
          z.string(),
          z.object({
            id: z.string(),
            title: z.string(),
            help_text: z.string().nullable(),
            input_type: z.string(),
            options: z.array(
              z.object({
                id: z.string(),
                option_value: z.string(),
                option_to_value: z.string().nullable(),
                value: z.string(),
              })
            ),
            answer: z.string(),
          })
        ),
        loading: z.boolean(),
      }),
    }),
    {
      required_error: "Select at least one country.",
    }
  ),
});

export type LicenseAssessmentFormData = z.infer<typeof licenseAssessmentFormSchema>;

interface LicenseAssessmentProviderProps {
  children: ReactNode;
}

export function LicenseAssessmentProvider({ children }: LicenseAssessmentProviderProps) {
  const methods = useForm<LicenseAssessmentFormData>({
    resolver: zodResolver(licenseAssessmentFormSchema),
    mode: "all",
  });

  return <FormProvider {...methods}>{children}</FormProvider>;
}
