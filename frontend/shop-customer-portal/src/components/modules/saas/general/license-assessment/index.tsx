"use client";

import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { SaasBreadcrumb } from "@/components/modules/saas/components/saas-breadcrumb";
import { SaasContainer } from "@/components/modules/saas/components/saas-container";
import { LicenseAssessmentProvider } from "./license-assessment-provider";
import { LicenseAssessmentForm } from "./license-assessment-form";
import { useTranslations } from "next-intl";

const paths = [
  { label: "Dashboard", href: "/saas" },
  { label: "License Assessment", href: "#" },
];

export function LicenseAssessment() {
  const t = useTranslations("modules.saas.pages.general.licenseAssessment");

  return (
    <>
      <SaasBreadcrumb paths={paths} />
      <SaasContainer>
        <TitleAndSubTitle showIcon title={t("title")} subText={t("description")} />
        <LicenseAssessmentProvider>
          <LicenseAssessmentForm />
        </LicenseAssessmentProvider>
      </SaasContainer>
    </>
  );
}
