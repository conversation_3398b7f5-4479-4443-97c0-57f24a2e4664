import { Add, RadioSelected, RadioUnselected, Remove } from "@arthursenno/lizenzero-ui-react/Icon";
import { useRef } from "react";
import { useFormContext, useWatch } from "react-hook-form";

import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";

import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { CgSpinnerAlt } from "react-icons/cg";
import { LicenseAssessmentFormData } from "./license-assessment-provider";

interface LicenseAssessmentCommitmentProps {
  countryCode: string;
  commitment: LicenseAssessmentFormData["countries"][number]["commitment"];
}

export function LicenseAssessmentCommitment({ countryCode, commitment }: Required<LicenseAssessmentCommitmentProps>) {
  const criterias = Object.values(commitment.questions || {});

  const commitmentRef = useRef<HTMLDetailsElement>(null);

  const {
    register,
    setValue,
    formState: { errors, isSubmitting },
    getValues,
    control,
  } = useFormContext<LicenseAssessmentFormData>();

  const commitmentErrors =
    !!errors && !!errors.countries && !!errors.countries[countryCode] && errors.countries[countryCode]!.commitment;

  function handleSaveCommitment() {
    setValue(`countries.${countryCode}.commitment.filled`, true, {
      shouldValidate: true,
    });

    if (commitmentRef.current) commitmentRef.current.open = false;
  }

  function handleEditCommitment() {
    setValue(`countries.${countryCode}.commitment.filled`, false);

    if (commitmentRef.current) commitmentRef.current.open = true;
  }

  const controlledCommitment = useWatch({
    control: control,
    name: `countries.${countryCode}.commitment`,
  });

  const allAnswered =
    !!Object.values(controlledCommitment.questions).length &&
    Object.values(controlledCommitment.questions).every((criteria) => !!criteria.answer);

  if (commitment.loading) {
    return (
      <div className="w-full flex justify-center items-center">
        <CgSpinnerAlt size={24} className="animate-spin text-primary" />
      </div>
    );
  }

  return (
    <>
      <details ref={commitmentRef} className="group/commitment" open>
        <summary className="flex flex-col cursor-pointer">
          <div className="flex items-center justify-between py-2 md:py-6">
            <div className="flex items-center gap-4">
              <p
                data-invalid={!!commitmentErrors}
                className="md:text-xl font-bold text-primary data-[invalid=true]:text-error"
              >
                Commitment Assessment
              </p>
              <QuestionTooltip>
                <QuestionTooltipDescription>
                  Lorem ipsum dolor sit amet consectetur. Gravida vitae fermentum faucibus arcu non. In volutpat nisl
                  nunc pellentesque.
                </QuestionTooltipDescription>
              </QuestionTooltip>
            </div>
            <Remove className="hidden group-open/commitment:block size-8 fill-support-blue" />
            <Add className="block group-open/commitment:hidden size-8 fill-support-blue" />
          </div>
        </summary>
        <div className="flex flex-col pb-6">
          <div className="space-y-8 md:space-y-10">
            {criterias.map((criteria) => (
              <div key={criteria.id} className="flex flex-col gap-4">
                <p className="text-base text-tonal-dark-cream-10">{criteria.title}</p>
                {criteria.options.map((option) => (
                  <label
                    key={`${criteria.id}_${option.value}`}
                    className="text-sm md:text-base text-tonal-dark-cream-20 flex gap-2 items-center cursor-pointer has-[input:checked]:cursor-default"
                  >
                    <input
                      {...register(`countries.${countryCode}.commitment.questions.${`criteria_${criteria.id}`}.answer`)}
                      disabled={commitment.filled}
                      value={option.value}
                      type="radio"
                      className="hidden peer"
                      data-invalid={!!commitmentErrors && !!commitmentErrors.questions?.[criteria.id]}
                    />
                    <RadioSelected className="hidden peer-checked:block size-5 fill-primary" />
                    <RadioUnselected className="block cursor-pointer peer-checked:hidden size-5 fill-primary peer-data-[invalid=true]:fill-error" />
                    {criteria.input_type === "SELECT" && <span className="mt-1">{option.option_value}</span>}
                    {criteria.input_type === "YES_NO" && <>{option.option_value === "YES" ? "Yes" : "No"}</>}
                  </label>
                ))}
              </div>
            ))}
          </div>
        </div>
      </details>
      {!commitment.filled && (
        <Button
          type="button"
          variant="text"
          color="light-blue"
          size="medium"
          onClick={handleSaveCommitment}
          disabled={isSubmitting || !allAnswered}
        >
          Save answers
        </Button>
      )}
      {commitment.filled && (
        <Button type="button" variant="text" color="light-blue" size="medium" onClick={handleEditCommitment}>
          Edit answers
        </Button>
      )}
    </>
  );
}
