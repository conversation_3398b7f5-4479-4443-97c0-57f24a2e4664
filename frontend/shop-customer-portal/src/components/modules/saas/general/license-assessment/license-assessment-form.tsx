"use client";

import { CountryIcon } from "@/components/_common/country-icon";
import { CountryInput } from "@/components/_common/forms/country-input";
import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { Icons } from "@/components/ui/icons";
import { useLiberatedCountries } from "@/hooks/use-liberated-countries";
import { useCustomer } from "@/hooks/use-customer";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { CheckCircle, Delete, East, Error, KeyboardArrowUp } from "@arthursenno/lizenzero-ui-react/Icon";
import { useFormContext, useWatch } from "react-hook-form";

import { LicenseAssessmentFormData } from "./license-assessment-provider";
import { MapCountries } from "@/components/_common/map";
import { MapBanner } from "@arthursenno/lizenzero-ui-react/Banner";
import { Divider } from "@/components/_common/divider";
import { CustomerCommitment, getCommitment, submitCommitment } from "@/lib/api/commitment";
import { LicenseAssessmentCommitment } from "./license-assessment-commitment";
import { useRouter } from "@/i18n/navigation";
import {
  createShoppingCart,
  getShoppingCartByEmail,
  updateShoppingCart,
  UpdateShoppingCartParams,
} from "@/lib/api/shoppingCart";
import { ShoppingCartItem } from "@/lib/api/shoppingCart/types";
import { CgSpinnerAlt } from "react-icons/cg";
import { CountryRecommendationModalTrigger } from "@/components/_common/modals/country-recommendation-modal/country-recommendation-modal-trigger";
import { useTranslations } from "next-intl";

export function LicenseAssessmentForm() {
  const { customer } = useCustomer();
  const t = useTranslations("modules.saas.pages.general.licenseAssessment");
  const { liberatedCountries } = useLiberatedCountries();
  const router = useRouter();
  const {
    setValue,
    formState: { errors, isSubmitted, isSubmitting },
    getValues,
    handleSubmit,
    reset,
    control,
  } = useFormContext<LicenseAssessmentFormData>();

  const countries = useWatch({
    name: "countries",
    control,
  });

  const countryList = Object.values(countries || {});

  const euLicenseContract = customer?.contracts.find((c) => c.type === "EU_LICENSE");

  function handleSelectCountry(countryCode: string) {
    const selectedCountry = liberatedCountries.find((country) => country.code === countryCode);

    if (!selectedCountry) return;

    const isAlreadyPurchased = !!euLicenseContract?.licenses.find((i) => i.country_code === countryCode);

    if (isAlreadyPurchased) return;

    const countries = getValues("countries") || {};

    const isAlreadyAdded = countries[countryCode];

    if (isAlreadyAdded) {
      handleRemoveCountry(countryCode);

      return;
    }

    setValue(`countries.${countryCode}`, {
      country: {
        id: Number(selectedCountry.id),
        code: countryCode,
        flag_url: selectedCountry.flag_url,
        name: selectedCountry.name,
      },
      commitment: { filled: false, questions: {}, loading: true },
    });

    getCountryCommitment(countryCode);
  }

  async function getCountryCommitment(countryCode: string) {
    const normalizedCountryCode = countryCode.toUpperCase().trim();

    if (!normalizedCountryCode) return;

    const commitmentResponse = await getCommitment(countryCode);

    if (!commitmentResponse.success) return;

    const commitmentCriterias = commitmentResponse.data;

    const countries = getValues("countries");

    const country = countries[normalizedCountryCode];

    if (!country) return;

    setValue(`countries.${countryCode}`, {
      ...country,
      commitment: {
        filled: false,
        questions: commitmentCriterias.reduce(
          (acc, criteria) => {
            acc[String(`criteria_${criteria.id}`)] = {
              id: String(criteria.id),
              title: criteria.title,
              help_text: criteria.help_text,
              input_type: criteria.input_type,
              answer: "",
              options: criteria.options.map((option) => ({
                id: String(option.id),
                value: option.value,
                option_value: option.option_value,
                option_to_value: option.option_to_value,
              })),
            };
            return acc;
          },
          {} as LicenseAssessmentFormData["countries"][string]["commitment"]["questions"]
        ),
        loading: false,
      },
    });
  }

  function handleRemoveCountry(countryCode: string) {
    const countries = getValues("countries");

    const newCountries = { ...countries };

    delete newCountries[countryCode];

    reset({ countries: newCountries });
  }

  async function handleFormSubmit(data: LicenseAssessmentFormData) {
    if (!customer) return;

    const countries = Object.values(data.countries || {});

    if (!countries.length) return;

    const currentYear = new Date().getFullYear();

    let customerCart = await getShoppingCartByEmail(customer.email);

    if (!customerCart) {
      customerCart = await createShoppingCart({
        email: customer.email,
        journey: "LONG",
      });
    }

    const commitmentSubmissions = await Promise.allSettled(
      countries.map(async (country) => {
        const commitmentAnswers = Object.values(country.commitment.questions || {}).map((q) => ({
          criteria_id: Number(q.id),
          answer: q.answer,
        }));

        const commitmentSubmissionResponse = await submitCommitment({
          customer_email: customer.email,
          year: currentYear,
          country_code: country.country.code.toUpperCase().trim(),
          commitment_answers: commitmentAnswers,
          shopping_cart_id: customerCart?.id,
        });

        if (!commitmentSubmissionResponse.success) return null;

        return commitmentSubmissionResponse.data;
      })
    );

    const successSubmissions = commitmentSubmissions.map((result) => {
      if (result.status !== "fulfilled") return null;

      if (!result.value) return null;

      return result.value;
    }) as CustomerCommitment[];

    const updatedData: UpdateShoppingCartParams = {};

    updatedData.items = successSubmissions.map((submission) => {
      const country = countries.find((c) => c.country.code === submission.country_code)!;

      return {
        country_id: country.country.id,
        country_code: country.country.code,
        country_name: country.country.name,
        country_flag: country.country.flag_url,
        service_type: "EU_LICENSE",
        year: submission.year,
        packaging_services: submission.service_setup.packaging_services
          .filter((p) => p.obliged)
          .map((p) => ({
            id: p.id,
            name: p.name,
            fractions: p.report_set.fractions.reduce(
              (acc, fraction) => {
                acc[fraction.code] = {
                  code: fraction.code,
                  name: fraction.name,
                  weight: 0,
                };
                return acc;
              },
              {} as Record<string, { code: string; name: string; weight: number }>
            ),
          })),
      } as ShoppingCartItem;
    });

    if (customerCart.journey !== "LONG") {
      updatedData.journey = "LONG";
    }

    await updateShoppingCart(customerCart.id, updatedData);

    router.push("/eu/long-journey/obligations");
  }

  const searchCountries = liberatedCountries.filter(
    (c) =>
      !countryList.find((country) => country.country.code === c.code) &&
      !euLicenseContract?.licenses.find((l) => l.country_code === c.code)
  );

  const allFilled = !!countryList.length && countryList.every((country) => country.commitment.filled);
  const isValid = !errors.countries;
  const countryErrors = errors?.countries && Array.isArray(errors.countries) ? errors.countries : [];

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="flex flex-col gap-10">
      <div className="flex flex-col gap-6">
        <MapCountries
          className="w-full aspect-video h-auto"
          onSelectCountry={handleSelectCountry}
          selectedCountries={countryList.map((country) => country.country.code)}
          liberatedCountries={liberatedCountries}
          contractedCountryCodes={euLicenseContract?.licenses.map((i) => i.country_code) || []}
        >
          <div className="absolute top-0 mt-3 ml-3">
            <MapBanner title="Benefit communication." style={{ width: "100%", zIndex: 1000 }}>
              <Error
                width={"6%"}
                height={"6%"}
                className="fill-primary mr-2.5 min-w-6 min-h-6 max-w-[50px] max-h-[50px]"
              />
              <p>
                Can't find the country you're looking for?{" "}
                <CountryRecommendationModalTrigger className="text-primary" recommendation="" />
              </p>
            </MapBanner>
          </div>
        </MapCountries>
        <div className="w-full">
          <div className="flex items-center gap-2">
            {countryList.map((country) => (
              <div key={country.country.code} className="flex items-center gap-2">
                <CountryIcon country={country.country} className="size-6 md:size-10" />
              </div>
            ))}
          </div>
        </div>
        <Divider style={{ margin: 0 }} />
      </div>
      <div className="w-full grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="col-span-1 md:col-span-2">
          <div className="bg-surface-02 py-6 md:py-10 px-4 md:px-6 rounded-[32px] flex flex-col gap-6">
            <div className="hidden md:block">
              <p className="text-grey-blue font-bold text-2xl">{t("selectCountries.title")}</p>
              <span className="text-grey-blue text-sm">{t("selectCountries.description")}</span>
            </div>
            <div className="w-full md:w-2/3">
              <CountryInput
                countries={searchCountries}
                onSelectCountry={({ code }) => handleSelectCountry(code)}
                recommendationEnabled
                contractedCountryCodes={euLicenseContract?.licenses.map((l) => l.country_code) || []}
              />
            </div>
            {!!countryList.length && (
              <p className="text-tonal-dark-cream-30 font-bold text-sm">{t("selectCountries.description")}</p>
            )}
            {allFilled && isValid && (
              <div className="p-4 flex items-center rounded-2xl bg-[#F0FAF0] gap-6">
                <div>
                  <Icons.badge className="w-12 md:w-8 h-12 fill-tonal-dark-green-30" />
                </div>
                <p className="font-bold text-tonal-dark-green-30">
                  Questions answered successfully! Go to the next step to find out your obligations
                </p>
              </div>
            )}
            {!!countryList.length && (
              <div className="space-y-6">
                {countryList.map((country, countryIndex) => (
                  <details
                    key={country.country.code}
                    className="group rounded-4xl bg-background overflow-hidden open:shadow-elevation-02-1"
                  >
                    <summary className="flex items-center justify-between py-5 px-4 md:px-7">
                      <div className="flex items-center gap-4">
                        <Button
                          type="button"
                          variant="text"
                          size="iconSmall"
                          color="gray"
                          onClick={() => handleRemoveCountry(country.country.code)}
                        >
                          <Delete className="size-5 fill-primary" />
                        </Button>
                        <CountryIcon country={country.country} className="size-6 md:size-8" />
                        <p className="text-xl md:text-2xl font-bold text-primary">{country.country.name}</p>
                        {isSubmitted && country.commitment.filled && (
                          <>
                            {!!countryErrors.length &&
                            countryErrors.find(
                              (countryError) => String(countryError?.country?.code) === country.country.code
                            ) ? (
                              <Error className="fill-error w-5 h-5" />
                            ) : (
                              <CheckCircle className="fill-success w-5 h-5" />
                            )}
                          </>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <QuestionTooltip>
                          <QuestionTooltipDescription>
                            Lorem ipsum dolor sit amet consectetur. Gravida vitae fermentum faucibus arcu non.
                          </QuestionTooltipDescription>
                        </QuestionTooltip>
                        <KeyboardArrowUp className="size-8 fill-primary group-open:-rotate-180 transition-all duration-300" />
                      </div>
                    </summary>
                    <div className="px-4 md:px-10 pb-5">
                      <LicenseAssessmentCommitment countryCode={country.country.code} commitment={country.commitment} />
                    </div>
                  </details>
                ))}
              </div>
            )}
          </div>
        </div>
        <div className="col-span-1 flex flex-col items-end">
          <p className="text-primary mb-7">{t("subtitle")}</p>
          <Button
            variant="filled"
            color="yellow"
            size="medium"
            type="submit"
            trailingIcon={isSubmitting ? <CgSpinnerAlt className="animate-spin" /> : <East />}
            disabled={!allFilled || !isValid || isSubmitting}
          >
            {isSubmitting ? "Submitting..." : "Next"}
          </Button>
        </div>
      </div>
    </form>
  );
}
