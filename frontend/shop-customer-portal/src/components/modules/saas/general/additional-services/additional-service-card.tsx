"use client";

import { Divider } from "@/components/_common/divider";
import { cn } from "@/lib/utils";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Add, Check, CheckCircleOutline, Remove } from "@arthursenno/lizenzero-ui-react/Icon";

interface AdditionalServiceCardProps {
  title: string;
  description: string;
  onAdd?: () => void;
  onRemove?: () => void;
  added?: boolean;
  items?: string[];
  isDisabled?: boolean;
}

export function AdditionalServiceCard({
  title,
  description,
  onAdd,
  onRemove,
  added = false,
  items,
  isDisabled = false,
}: AdditionalServiceCardProps) {
  return (
    <div
      className={cn(
        "p-4 md:p-6 flex flex-col flex-1 rounded-3xl items-center justify-between bg-white border",
        added ? "border-[#808FA9]" : "border-transparent"
      )}
    >
      <div className="w-full space-y-6">
        <div className="flex  items-start gap-6 justify-between">
          <div className="flex flex-col gap-1">
            <p className="text-primary text-2xl font-bold">{title}</p>
            <p className="font-medium text-xs text-[#808FA9]">{description}</p>
          </div>
          <div className="flex flex-col justify-end gap-1 mt-1">
            <p className="text-[28px] font-bold text-support-blue text-right text-nowrap">
              {isDisabled ? "---" : "250 €"}
            </p>
            <p className="text-xs font-medium text-tonal-dark-cream-40 text-right">/ per country and year</p>
          </div>
        </div>
        <Divider style={{ marginTop: "16px", marginBottom: "0" }} />
        {!!items && (
          <div className="space-y-2 w-full mt-6">
            <p className="text-base font-bold text-tonal-dark-cream-20">Contains:</p>
            <div className="space-y-2">
              {items.map((item, idx) => (
                <div key={idx} className="flex items-start gap-2">
                  <CheckCircleOutline className="size-4 fill-tonal-dark-cream-20 flex-none" />
                  <p className="text-xs text-tonal-dark-cream-20">{item}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
      <div className="flex-1 flex flex-col justify-end w-full">
        {isDisabled && (
          <div className="mt-4 w-full flex flex-col md:flex-row justify-start gap-6">
            <Button disabled color="dark-blue" variant="filled" size="small" className="w-full text-nowrap">
              Coming soon
            </Button>
          </div>
        )}
        {!isDisabled && added && (
          <div className="mt-4 flex items-center justify-between w-full">
            <div className="flex items-center gap-2">
              <p className="text-on-surface-01 text-sm font-bold">Added!</p>
              <Check className="size-4 fill-on-surface-01" />
            </div>
            <Button
              leadingIcon={<Remove className="fill-support-blue" />}
              color="light-blue"
              variant="text"
              size="small"
              onClick={onRemove}
              disabled={isDisabled}
              className="text-nowrap"
            >
              {isDisabled ? "Added" : "Remove"}
            </Button>
          </div>
        )}
        {!isDisabled && !added && (
          <div className="mt-4 w-full">
            <Button
              className="w-full text-nowrap"
              leadingIcon={<Add />}
              color="dark-blue"
              variant="filled"
              size="small"
              onClick={onAdd}
            >
              Add to cart
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
