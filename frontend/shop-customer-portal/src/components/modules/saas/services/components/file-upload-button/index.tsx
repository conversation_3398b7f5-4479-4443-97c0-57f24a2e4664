"use client";

import { useCallback } from "react";
import { Accept, type FileWithPath, useDropzone } from "react-dropzone";

import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Upload } from "@arthursenno/lizenzero-ui-react/Icon";
import { cn } from "@/lib/utils";
import { Spinner } from "@/components/ui/loader";
import { CgSpinnerAlt } from "react-icons/cg";
import { enqueueSnackbar } from "notistack";

interface FileUploadButtonProps {
  className?: string;
  children: React.ReactNode;
  isLoading?: boolean;
  onUpload: (file: File) => void;
  accept?: Accept;
}

export function FileUploadButton({ isLoading, onUpload, accept, ...props }: FileUploadButtonProps) {
  const onDrop = useCallback(
    async (acceptedFiles: FileWithPath[]) => {
      const file = acceptedFiles[0];
      if (file) onUpload(file);
    },
    [onUpload]
  );

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    maxFiles: 1,
    multiple: false,
    accept,
    maxSize: 26214400, // 25MB in bytes (25 * 1024 * 1024),
    onDropRejected: (error) => {
      enqueueSnackbar("The file size must be less than 25MB.", { variant: "error" });
    },
  });

  const acceptString = Object.entries(accept || {})
    .map(([key, value]) => `${value.join(",")}`)
    .join(",");

  return (
    <div {...getRootProps()} className={cn("inline-flex cursor-pointer", { "cursor-wait": isLoading })}>
      <input {...getInputProps()} accept={acceptString} />
      <Button
        {...props}
        aria-label="Upload file"
        variant="text"
        color="light-blue"
        size="medium"
        leadingIcon={isLoading ? <CgSpinnerAlt className="animate-spin size-5" /> : <Upload />}
        disabled={isLoading}
      >
        {isLoading ? "Uploading..." : props.children}
      </Button>
    </div>
  );
}
