"use client";

import { Link } from "@/i18n/navigation";
import { EditCircle } from "@arthursenno/lizenzero-ui-react/Icon";

import { useCustomer } from "@/hooks/use-customer";

import { formatCustomerNumber } from "@/utils/format-customer-number";

import { CountryIcon } from "@/components/_common/country-icon";
import { formatDateToDDMMYYYY } from "@/utils/formatDateToDDMMYYYY";
import { usePathname } from "@/i18n/navigation";

export function ContractManagementCard() {
  const pathname = usePathname();
  const { customer } = useCustomer();

  const contract = (() => {
    if (pathname.includes("direct-license")) {
      return customer?.contracts.find((contract) => contract.type === "DIRECT_LICENSE");
    }

    return customer?.contracts.find((contract) => contract.type === "EU_LICENSE");
  })();

  const licenses = contract?.licenses || [];

  const totalLicensedCountries = licenses.length.toString().padStart(2, "0");

  return (
    <div id="contract-management" className="col-span-1 md:col-span-5 px-4 py-6 md:p-8 bg-background rounded-3xl">
      <div className="flex items-center gap-3 mb-5">
        <h2 className="text-title-3 text-primary font-bold">Contract Management</h2>
        <Link href="/saas/contract-management">
          <EditCircle className="size-7 fill-support-blue" />
        </Link>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 w-full">
        <div className="w-full md:col-span-1 space-y-2 py-4">
          <label className="text-small-paragraph-regular text-tonal-dark-cream-30">Customer number</label>
          <p className="text-paragraph-regular text-primary">{formatCustomerNumber(customer?.id)}</p>
        </div>
        <div className="w-full md:col-span-1 space-y-2 py-4">
          <label className="text-small-paragraph-regular text-tonal-dark-cream-30">Company</label>
          <p className="text-paragraph-regular text-primary">{customer?.company?.name}</p>
        </div>
        <div className="w-full md:col-span-1 space-y-2 py-4">
          {/* TODO */}
          <label className="text-small-paragraph-regular text-tonal-dark-cream-30">Notice period</label>
          <p className="text-paragraph-regular text-primary">
            {contract?.termination ? formatDateToDDMMYYYY(contract.termination.created_at) : "-------"}
          </p>
        </div>
        <div className="w-full md:col-span-1 space-y-2 py-4">
          <label className="text-small-paragraph-regular text-tonal-dark-cream-30">Termination date</label>
          <p className="text-paragraph-regular text-primary">
            {contract?.termination ? formatDateToDDMMYYYY(contract.termination.requested_at) : "-------"}
          </p>
        </div>
        <div className="w-full md:col-span-2 space-y-2 py-4">
          <label className="text-small-paragraph-regular text-tonal-dark-cream-30">
            Countries Licensed ({totalLicensedCountries})
          </label>
          <div className="flex items-center gap-2">
            {licenses.map((license) => (
              <CountryIcon key={license.id} country={{ flag_url: license.country_flag, name: license.country_name }} />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
