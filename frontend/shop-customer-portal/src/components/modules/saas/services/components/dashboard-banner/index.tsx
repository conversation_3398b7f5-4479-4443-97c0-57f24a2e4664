import Image from "next/image";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { KeyboardArrowRight } from "@arthursenno/lizenzero-ui-react/Icon";
import { useRouter } from "@/i18n/navigation";

export function DashboardBanner() {
  const router = useRouter();

  return (
    <div className="flex flex-row bg-primary w-full max-h-[166px] rounded-4xl items-center gap-6 p-6 md:p-0 mt-5 md:mt-7">
      <Image
        src={"/assets/images/recycling.webp"}
        alt="recycling"
        className="md:block hidden"
        width={269}
        height={166}
      />
      <div className="md:block flex">
        <p className="m-0 md:mb-3 font-bold text-base">
          Only a few more steps are required so that we can license you!
        </p>
        <div className="md:block hidden">
          <Button
            color="yellow"
            size="small"
            variant="filled"
            onClick={() => router.push("/saas/eu-license/required-information")}
          >
            Sign power of attorney now
          </Button>
        </div>
        <div className="md:hidden flex justify-center items-center ml-3">
          <div className="w-9 h-9 bg-tertiary rounded-full flex justify-center items-center">
            <KeyboardArrowRight className="w-5 h-5 fill-primary" />
          </div>
        </div>
      </div>
    </div>
  );
}
