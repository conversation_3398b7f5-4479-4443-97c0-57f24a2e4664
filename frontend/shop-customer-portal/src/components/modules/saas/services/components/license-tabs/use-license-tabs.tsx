"use client";

import { FullLicense, LicensePendency } from "@/lib/api/license/types";
import { useCustomer } from "@/hooks/use-customer";

import { useQuery } from "@tanstack/react-query";
import { getLicenses } from "@/lib/api/license";
import { createContext, useContext, ReactNode } from "react";
import { useQueryFilter } from "@/hooks/use-query-filter";

interface LicenseTabsContextData {
  licenses: (FullLicense & { _status: "OPEN_TO_DOS" | "DONE" })[];
  selectedLicense: (FullLicense & { _status: "OPEN_TO_DOS" | "DONE" }) | null;
  licenseYears: number[];
  selectCountry: (countryCode: string | null) => void;
  isLoading: boolean;
}

const LicenseTabsContext = createContext<LicenseTabsContextData>({} as LicenseTabsContextData);

interface LicenseTabsProviderProps {
  pendencyType?: LicensePendency["type"];
  children: ReactNode;
}

export function LicenseTabsProvider({ pendencyType, children }: LicenseTabsProviderProps) {
  const currentYear = new Date().getFullYear().toString();

  const { paramValues, changeParam, deleteParam } = useQueryFilter(["country-code", "year"]);

  const countryCode = paramValues["country-code"];
  const licenseYear = paramValues.year || currentYear;

  const { customer } = useCustomer();

  const euLicenseContract = customer?.contracts.find((contract) => contract.type === "EU_LICENSE");

  const { data: licenses, isLoading } = useQuery({
    queryKey: ["licenses", { contract_id: euLicenseContract?.id }],
    queryFn: async () => {
      if (!euLicenseContract) return [];

      return getLicenses({ contract_id: euLicenseContract.id });
    },
    enabled: !!euLicenseContract,
  });

  const licensesWithStatus = (() => {
    if (!licenses) return [];

    return licenses.map((license) => ({
      ...license,
      _status: (pendencyType
        ? license.pendencies.find((pendency) => pendency.type === pendencyType)
          ? "OPEN_TO_DOS"
          : "DONE"
        : "DONE") as "OPEN_TO_DOS" | "DONE",
    }));
  })();

  function handleSelectCountryCode(countryCode: string | null) {
    if (countryCode) return changeParam("country-code", countryCode);

    deleteParam("country-code");
  }

  const selectedLicense =
    licensesWithStatus.find(
      (license) => license.country_code === countryCode && license.year === Number(licenseYear)
    ) || null;

  const licenseYears = licensesWithStatus
    .filter((license) => license.country_code === countryCode)
    .map((license) => license.year);

  return (
    <LicenseTabsContext.Provider
      value={{
        licenses: licensesWithStatus,
        selectedLicense,
        licenseYears,
        selectCountry: handleSelectCountryCode,
        isLoading,
      }}
    >
      {children}
    </LicenseTabsContext.Provider>
  );
}

export function useLicenseTabs() {
  const context = useContext(LicenseTabsContext);

  if (!context) {
    throw new Error("useLicenseTabs must be used within a LicenseTabsProvider");
  }

  return context;
}
