"use client";

import { Link } from "@/i18n/navigation";
import { EditCircle } from "@arthursenno/lizenzero-ui-react/Icon";
import { useQuery } from "@tanstack/react-query";
import { usePathname } from "@/i18n/navigation";
import { useEffect, useState } from "react";
import { BiLoader } from "react-icons/bi";

import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { SelectDropdown } from "@/components/_common/select-dropdown";
import { getPackagingServices } from "@/lib/api/packaging-services";
import { PackagingService } from "@/lib/api/packaging-services/types";
import { GERMANY_FRACTIONS } from "@/utils/consts/direct-license";
import { useCustomer } from "@/hooks/use-customer";
import { formatWeight } from "@/utils/format-weight";
import { formatDateToDDMMYYYY } from "@/utils/formatDateToDDMMYYYY";
import NextImage from "next/image";

interface DeclareVolumesCardProps {
  countryCode: string;
}

export function DeclareVolumesCard({ countryCode }: DeclareVolumesCardProps) {
  const { customer } = useCustomer();
  const pathname = usePathname();

  const isDirectLicense = pathname.includes("direct-license");
  const editVolumesHref = isDirectLicense
    ? `./direct-license/declare-volumes`
    : `/saas/eu-license/declare-volumes?country-code=${countryCode}`;

  const contract = isDirectLicense
    ? customer?.contracts.find((c) => c.type === "DIRECT_LICENSE")
    : customer?.contracts.find((c) => c.type === "EU_LICENSE");

  const licenses =
    (isDirectLicense ? contract?.licenses : contract?.licenses.filter((l) => l.country_code === countryCode)) || [];

  const [selectedYear, setSelectedYear] = useState<number | null>(licenses[0]?.year || null);

  const selectedLicense = licenses.find((l) => l.year === selectedYear);

  const { data: packagingServices, isLoading: isLoadingPackagingServices } = useQuery<PackagingService[], Error>({
    queryKey: ["packaging-services-declare-volumes", { license_id: selectedLicense?.id }],
    queryFn: async () => getPackagingServices({ license_id: Number(selectedLicense?.id) }),
    enabled: !!selectedLicense,
  });

  const [selectedPackagingService, setSelectedPackagingService] = useState<PackagingService>();

  useEffect(() => {
    if (packagingServices) setSelectedPackagingService(packagingServices[0]);
  }, [packagingServices]);

  const uniquePackaging = packagingServices?.length === 1;

  const volumeReport = (() => {
    if (!selectedPackagingService) return null;

    if (isDirectLicense) return selectedPackagingService.volume_reports?.[0];

    const lastVolumeReport = selectedPackagingService.volume_reports?.sort(
      (a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    )[0];

    return lastVolumeReport;
  })();

  const fractions: { code: string; name: string; icon_url: string; value: number }[] = (() => {
    if (!selectedPackagingService) return [];

    if (isDirectLicense) {
      return GERMANY_FRACTIONS.map((fraction) => ({
        code: fraction.code,
        name: fraction.name,
        icon_url: "/assets/svg/aluminium.svg",
        value:
          volumeReport?.volume_report_items.find((item) => String(item.setup_fraction_code) === fraction.code)?.value ||
          0,
      }));
    }

    return (selectedPackagingService.volume_reports?.[0]?.report_table?.fractions ?? []).map((fraction) => ({
      code: fraction.code,
      name: fraction.name,
      icon_url: fraction.fraction_icon.image_url,
      value:
        volumeReport?.volume_report_items
          .filter((item) => Number(item.setup_fraction_code) === Number(fraction.code))
          .reduce((acc, item) => acc + item.value, 0) || 0,
    }));
  })();

  return (
    <div id="declare-volumes" className="col-span-1 md:col-span-6 px-4 py-8 md:p-8 bg-background rounded-3xl">
      <div className="flex items-center gap-3 mb-5">
        <h2 className="text-title-3 text-primary font-bold">Declare Volumes</h2>
        <Link href={editVolumesHref}>
          <EditCircle className="size-7 fill-support-blue" />
        </Link>
      </div>
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-x-small-paragraph-regular font-medium text-tonal-dark-cream-30">
            Your last entry: {volumeReport?.updated_at ? formatDateToDDMMYYYY(volumeReport?.updated_at) : "-------"}
          </span>
          <SelectDropdown
            options={licenses.map((license) => ({
              label: String(license.year),
              value: String(license.year),
            }))}
            value={String(selectedYear)}
            onChangeValue={(year) => setSelectedYear(Number(year))}
          />
        </div>

        {isLoadingPackagingServices && (
          <div className="flex justify-center items-center gap-1 md:gap-2 px-2 py-3 border-[1px] border-tonal-dark-cream-80 rounded-md">
            <BiLoader className="fill-primary animate-spin" />
            <p className="text-center text-primary">Loading packaging services...</p>
          </div>
        )}

        {/* Tabs */}
        {!uniquePackaging && (
          <ul className="flex items-center overflow-x-auto pb-4">
            {packagingServices?.map((packagingService) => (
              <li
                key={packagingService.id}
                data-selected={packagingService.id === selectedPackagingService?.id}
                onClick={() => setSelectedPackagingService(packagingService)}
                className="group p-2 border-b-2 border-white data-[selected=true]:border-b-primary flex-1 text-nowrap flex justify-center items-center hover:bg-tonal-cream-96 cursor-pointer"
              >
                <p className="text-grey-blue/60 group-data-[selected=true]:text-primary font-bold">
                  {packagingService.name}
                </p>
              </li>
            ))}
          </ul>
        )}

        {!!packagingServices && (
          <ul className="flex flex-col">
            {fractions.map((fraction) => (
              <li
                key={fraction.code}
                className="flex items-center justify-between p-4 border-b-[1px] border-tonal-dark-cream-80 gap-2"
              >
                <div className="flex items-center gap-2 flex-1">
                  <div className="flex-none overflow-hidden flex items-center justify-center size-8">
                    <NextImage
                      draggable={false}
                      src={fraction.icon_url}
                      alt="Fraction icon"
                      width={100}
                      height={100}
                      className="object-cover aspect-square w-full h-full"
                    />
                  </div>
                  <QuestionTooltip>
                    <QuestionTooltipDescription>{fraction.name}</QuestionTooltipDescription>
                  </QuestionTooltip>
                  <p className="text-tonal-dark-cream-20 font-bold text-sm ">{fraction.name}</p>
                </div>
                <p className="text-tonal-dark-cream-20 flex-none text-sm">{formatWeight(fraction.value)}</p>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
}
