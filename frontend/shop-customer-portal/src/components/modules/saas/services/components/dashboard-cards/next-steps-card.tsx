"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { getServiceNextSteps, updateServiceNextStep } from "@/lib/api/service-next-steps";
import { ServiceNextStep } from "@/lib/api/service-next-steps/types";
import { useCustomer } from "@/hooks/use-customer";
import { CheckCircle } from "@arthursenno/lizenzero-ui-react/Icon";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { useRef } from "react";

interface NextStepsCardProps {
  licenseId?: number;
  actionGuideId?: number;
}

export function NextStepsCard({ licenseId, actionGuideId }: NextStepsCardProps) {
  const queryClient = useQueryClient();
  const { customer } = useCustomer();

  const toggleStepTimeout = useRef<NodeJS.Timeout | null>(null);

  const { data: nextSteps, isLoading: isLoadingNextSteps } = useQuery({
    queryKey: ["next-steps", licenseId, actionGuideId],
    queryFn: async () => {
      const response = await getServiceNextSteps({ licenseId, actionGuideId });
      if (!response.success) throw new Error(response.error);
      return response.data;
    },
    enabled: !!licenseId || !!actionGuideId,
  });

  async function toggleStep(nextStepId: number) {
    if (!customer) return;

    const prevNextSteps = queryClient.getQueryData<ServiceNextStep[]>(["nextSteps", licenseId, actionGuideId]);

    if (toggleStepTimeout.current) clearTimeout(toggleStepTimeout.current);

    queryClient.setQueryData(["next-steps", licenseId, actionGuideId], (prev: ServiceNextStep[]) => {
      return prev.map((nextStep) => {
        if (nextStep.id !== nextStepId) return nextStep;

        const isDone = !!nextStep.done_at;

        return {
          ...nextStep,
          done_at: isDone ? null : new Date().toISOString(),
        };
      });
    });

    toggleStepTimeout.current = setTimeout(() => {
      updateServiceNextStep(nextStepId, {
        done_at: nextSteps?.find((nextStep) => nextStep.id === nextStepId)?.done_at ? null : new Date().toISOString(),
      });
    }, 500);
  }

  return (
    <div id="next-steps" className="col-span-1 md:col-span-5 px-4 py-6 md:p-8 bg-background rounded-3xl">
      <h2 className="text-title-3 text-primary font-bold mb-10">Next Steps</h2>
      <div className="space-y-3">
        {!nextSteps &&
          isLoadingNextSteps &&
          Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="group py-5 space-y-3 cursor-pointer">
              <div className="flex items-center gap-3">
                <Skeleton className="flex-none size-7 rounded-full" />
                <Skeleton className="h-4 w-full" />
              </div>
              <div className="space-y-2">
                <Skeleton className="h-3 w-24" />
                <Skeleton className="h-3 w-24" />
              </div>
            </div>
          ))}
        {nextSteps?.map((nextStep) => (
          <div
            key={nextStep.id}
            className="group py-5 space-y-3 cursor-pointer"
            data-done={!!nextStep.done_at}
            onClick={() => toggleStep(nextStep.id)}
          >
            <div className="flex items-center">
              <div className="flex items-center gap-3">
                <div className="flex-none w-7 h-7 border-2 rounded-full border-tonal-dark-cream-60 data-[done=true]:border-success relative">
                  <CheckCircle className="hidden group-data-[done=true]:block group-hover:block absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-8 h-8 fill-tonal-dark-cream-60 group-data-[done=true]:fill-success" />
                </div>
                <p className="text-tonal-dark-cream-60 group-hover:text-primary group-data-[done=true]:text-primary">
                  {nextStep.title}
                </p>
              </div>
              {nextStep.done_at && (
                <p className="hidden group-data-[done=true]:block text-xs text-tonal-dark-cream-50 flex-1 text-right text-nowrap">
                  Done: {new Date(nextStep.done_at).toLocaleDateString()}
                </p>
              )}
            </div>
            {!nextStep.done_at && (
              <div className="space-y-2">
                <p className="block group-data-[done=true]:hidden text-sm text-tonal-dark-cream-50 group-hover:text-primary">
                  Available:{" "}
                  {new Date(nextStep.available_date).toLocaleString("en-US", { month: "long", year: "numeric" })}
                </p>
                <p className="block group-data-[done=true]:hidden text-sm text-tonal-dark-cream-50 group-hover:text-primary">
                  Deadline:{" "}
                  {new Date(nextStep.deadline_date).toLocaleString("en-US", { month: "long", year: "numeric" })}
                </p>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
