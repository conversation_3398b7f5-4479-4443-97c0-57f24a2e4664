"use client";

import Image from "next/image";

import { Error } from "@arthursenno/lizenzero-ui-react/Icon";
import { useLicenseTabs } from "./use-license-tabs";
import { Skeleton } from "@/components/ui/skeleton";
import { useTranslations } from "next-intl";

export function LicenseTabs() {
  const t = useTranslations("modules.saas.global.components.selectCountryTabs");

  const { licenses, selectedLicense, selectCountry } = useLicenseTabs();

  return (
    <div className="w-full flex flex-row justify-center">
      <div className="w-full">
        <div className="w-full rounded-[32px] items-start flex flex-col">
          <div className="justify-between w-full flex-col hidden md:flex">
            <p className="text-primary font-medium text-lg mb-2 lg:mb-4">{t("title")}</p>
          </div>

          {!!licenses.length && (
            <div className="hidden md:flex items-center gap-5 flex-wrap">
              <button
                data-selected={!selectedLicense}
                className="flex items-center font-bold py-3 px-5 gap-2 rounded-2xl cursor-pointer bg-tonal-dark-blue-96 text-primary data-[selected=true]:bg-primary data-[selected=true]:text-white"
                onClick={() => selectCountry(null)}
              >
                <div className="w-6 h-6 rounded-full flex-none overflow-hidden">
                  <Image
                    src={"/assets/images/europe_union.png"}
                    alt={`Europe union flag`}
                    width={24}
                    height={24}
                    className="w-full h-full object-cover"
                  />
                </div>
                {t("allCountries")}
              </button>

              {licenses.map((license) => (
                <div
                  key={license.id}
                  data-selected={license.id === selectedLicense?.id}
                  data-error={license._status !== "DONE"}
                  className="group flex items-center font-bold py-3 px-5 gap-2 rounded-2xl cursor-pointer bg-tonal-dark-blue-96 text-primary data-[selected=true]:bg-primary data-[selected=true]:text-white data-[error=true]:bg-error/20 data-[error=true]:text-primary"
                  onClick={() => selectCountry(license.country_code)}
                >
                  <div className="w-6 h-6 rounded-full flex-none overflow-hidden">
                    <Image
                      src={license.country_flag}
                      alt={`${license.country_name} flag`}
                      width={24}
                      height={24}
                      className="w-full h-full object-cover"
                    />
                  </div>

                  <span>{license.country_name}</span>

                  {license._status !== "DONE" && (
                    <Error
                      width={20}
                      height={20}
                      className="fill-tonal-red-40 group-data-[selected=true]:fill-on-primary ml-2"
                    />
                  )}
                </div>
              ))}
            </div>
          )}
          {!licenses.length && (
            <div className="hidden md:flex items-center gap-5 flex-wrap">
              <Skeleton className="w-32 h-12" />
              <Skeleton className="w-32 h-12" />
              <Skeleton className="w-32 h-12" />
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
