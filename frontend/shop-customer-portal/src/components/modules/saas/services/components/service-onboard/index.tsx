import { useEffect, useState } from "react";
import Joyride, { CallBackProps, STATUS, Step } from "react-joyride";

import { usePathname } from "@/i18n/navigation";
import { getCustomerTutorial, postCustomerTutorial } from "@/lib/api/tutorial";
import { useCustomer } from "@/hooks/use-customer";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";

import { ServiceOnboardTooltip } from "./service-onboard-tooltip";

interface ServiceOnboardProps {
  steps: Step[];
}

export function ServiceOnboard({ steps }: ServiceOnboardProps) {
  const { customer } = useCustomer();
  const pathname = usePathname();

  const service_type = (() => {
    if (pathname.includes(`direct-license`)) return `DIRECT_LICENSE`;
    if (pathname.includes(`action-guide`)) return `ACTION_GUIDE`;

    return `EU_LICENSE`;
  })();

  const [tutorialId, setTutorialId] = useState<number>();
  const [control, setControl] = useState<{ run: boolean; steps: Step[]; stepIndex: number }>({
    run: false,
    steps,
    stepIndex: 0,
  });

  function handleJoyrideCallback(data: CallBackProps) {
    const { status, type, action } = data;

    if (type === "step:after") {
      setControl((current) => ({
        ...current,
        stepIndex: action === "prev" ? current.stepIndex - 1 : current.stepIndex + 1,
      }));
    }

    if ((["finished", "skipped"].includes(status) && type === "tour:end") || type === "beacon") {
      setControl((current) => ({ ...current, run: false, stepIndex: 0 }));
      handleFinishTutorial();
    }
  }

  async function handleFinishTutorial() {
    if (!customer?.id) return;

    const customerTutorial = await postCustomerTutorial({
      customer_id: customer.id,
      is_finished: true,
      service_type: service_type,
      tutorial_id: tutorialId,
    });

    if (!customerTutorial) return;

    setTutorialId(customerTutorial.id);
  }

  function handleSeeTutorial() {
    setControl((current) => ({ ...current, run: true, stepIndex: 0 }));
  }

  useEffect(() => {
    (async () => {
      if (!customer?.id) return;

      const customerTutorials = await getCustomerTutorial(customer.id, service_type);

      if (!customerTutorials) return;

      if (!customerTutorials?.length) {
        return setControl((current) => ({ ...current, run: true, stepIndex: 0 }));
      }

      const customerJourneyTutorial = customerTutorials?.find(
        (customerTutorial) => customerTutorial.service_type === service_type
      );

      if (!customerJourneyTutorial || !customerJourneyTutorial?.is_finished) {
        return setControl((current) => ({ ...current, run: true, stepIndex: 0 }));
      }

      setTutorialId(customerJourneyTutorial.id);
    })();
  }, [customer]);

  return (
    <div className="hidden md:block">
      {!!tutorialId && (
        <Button color="light-blue" size="medium" variant="text" onClick={handleSeeTutorial} className="text-nowrap">
          See tutorial
        </Button>
      )}
      <Joyride
        callback={handleJoyrideCallback}
        continuous
        hideCloseButton
        // disableOverlayClose
        tooltipComponent={ServiceOnboardTooltip}
        beaconComponent={() => null}
        run={control.run}
        stepIndex={control.stepIndex}
        scrollToFirstStep
        showProgress
        showSkipButton
        steps={steps}
        styles={{
          options: {
            zIndex: 9999999,
          },
        }}
        spotlightPadding={0}
      />
    </div>
  );
}
