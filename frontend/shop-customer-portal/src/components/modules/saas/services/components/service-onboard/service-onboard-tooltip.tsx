import { Clear } from "@arthursenno/lizenzero-ui-react/Icon";
import Image from "next/image";
import { TooltipRenderProps } from "react-joyride";

export const ServiceOnboardTooltip = ({
  backProps,
  continuous,
  index,
  isLastStep,
  primaryProps,
  skipProps,
  step,
  closeProps,
  tooltipProps,
  size,
}: TooltipRenderProps) => {
  const boardImageUrl = isLastStep ? "/assets/images/nature.png" : "/assets/images/paradise.png";

  if (index === 0 || isLastStep) {
    return (
      <div className="rounded-[52px] overflow-hidden max-w-[720px]">
        <Image src={boardImageUrl} alt="paradise" className="w-full h-2/4" width={40} height={40} />
        <div className="w-full h-2/4 py-10 px-9 bg-on-primary">
          <p className="text-base text-tonal-dark-cream-30">
            <b>{("0" + (index + 1)).slice(-2)}</b> / {(`0` + size).slice(-2)}
          </p>
          <p className="text-primary text-[32px] font-bold mt-4">
            {isLastStep ? "Let’s start our journey!" : "Welcome to Lizenzero!"}
          </p>
          <p className="text-xl text-tonal-dark-cream-20 mt-4">
            {isLastStep
              ? "You're all set to start using Lizenzero!"
              : `Lorem ipsum dolor sit amet consectetur. Ut semper dictum sit amet. Non eget tortor maecenas dit ridiculus
            pharetra facilisi. Aliquam porttitor iaculis.`}
          </p>
          <div className="flex items-center justify-between mt-12">
            {isLastStep ? (
              <button className="text-base font-bold text-primary" {...backProps}>
                Back
              </button>
            ) : (
              <button className="text-base font-bold text-primary" {...skipProps}>
                Skip Tutorial →
              </button>
            )}
            <button
              className="rounded-full py-[18px] px-8 bg-tertiary text-on-tertiary text-base font-bold w-2/4 h-14"
              {...primaryProps}
            >
              Next Step
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-surface-02 rounded-3xl p-7 min-w-80 max-w-96">
      <div className="flex items-center justify-between">
        <p className="text-base text-tonal-dark-cream-30">
          <b>{("0" + (index + 1)).slice(-2)}</b> / {(`0` + size).slice(-2)}
        </p>
        <button {...closeProps} className="p-1 rounded-full bg-white">
          <Clear className="w-6 h-6 fill-primary" />
        </button>
      </div>

      <div className="mt-6 max-w-96">{step.content}</div>

      <div className="flex items-center justify-between mt-8">
        <button className="text-base font-bold text-primary" {...backProps}>
          Back
        </button>
        <button
          className="rounded-full py-[18px] px-8 bg-tertiary text-on-tertiary text-base font-bold h-14"
          {...primaryProps}
        >
          Next Step
        </button>
      </div>
    </div>
  );
};
