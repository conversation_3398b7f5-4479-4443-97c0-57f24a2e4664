"use client";
import { SelectDropdown } from "@/components/_common/select-dropdown";
import { SaasBreadcrumb } from "@/components/modules/saas/components/saas-breadcrumb";
import { SaasContainer } from "@/components/modules/saas/components/saas-container";
import { Link } from "@/i18n/navigation";
import { useCustomer } from "@/hooks/use-customer";
import { Error } from "@arthursenno/lizenzero-ui-react/Icon";
import { useEffect, useState } from "react";

import { DirectLicenseReportingTable } from "../components/direct-license-reporting-table";
import { LucidNumberInput } from "../components/lucid-number-input";

export interface License {
  year: number;
  orderNumber: string;
  dateTime: string;
  amount: string;
  order: string;
  materials: {
    aluminum: number;
    glass: number;
    beverageCartons: number;
    plastics: number;
    other: number;
    paperDerivates: number;
    otherComposite: number;
    ferrousMetals: number;
  };
}

const path = [
  { label: "Dashboard DE", href: "../" },
  { label: "LUCID", href: "#" },
];

export function DirectLicenseLUCID() {
  const { customer } = useCustomer();

  const directLicenseContract = customer?.contracts.find((contract) => contract.type === "DIRECT_LICENSE");

  const directLicenses = (directLicenseContract?.licenses || []).sort((a, b) => b.year - a.year);

  const [selectedLicense, setSelectedLicense] = useState(directLicenses[0]);

  useEffect(() => {
    setSelectedLicense(directLicenses[0]);
  }, [directLicenses]);

  return (
    <>
      <SaasBreadcrumb paths={path} />
      <SaasContainer className="pb-24">
        <h1 className="text-title-1 font-centra font-bold md:text-h1 text-grey-blue pb-4">LUCID</h1>
        <p className="text-grey-blue pb-10 md:pb-8">Have access to all your information regarding your LUCID number.</p>

        <div className=" w-full rounded-[32px] items-start bg-surface-02 flex flex-col p-10">
          <div className="flex flex-col md:flex-row w-full justify-between">
            <p className="text-primary font-medium text-xl mb-2">Enter a valid LUCID number</p>
          </div>
          <LucidNumberInput
            label="LUCID Number"
            errorMessage={
              <p className="text-error mt-4">
                Invalid LUCID number. Check out our{" "}
                <Link
                  className="underline hover:no-underline"
                  href="https://www.lizenzero.de/verpackungsregister-lucid/"
                >
                  LUCID Number article
                </Link>
                for help.
              </p>
            }
          />
        </div>

        <hr className="border-tonal-dark-cream-80 mt-8 pb-8" />
        <h2 className="text-2xl text-primary font-bold pb-4 md:pb-6">
          Volume reporting central agency packaging register XML
        </h2>
        <p className="font-paragraph-regular text-paragraph-regular text-tonal-dark-cream-40 pb-10 md:pb-11">
          To comply with the packaging law it is necessary to transfer your currently licensed packaging volumes to the
          database “LUCID” of the Cental Packaging Register (ZSVR).
          <br />
          <br />
          According to legislation, we are unfortunately not allowed to do this for you. But if you can simply download
          the volume report by clicking the button bellow and then upload the file at the{" "}
          <Link className="underline hover:no-underline text-support-blue" href="">
            Producert data report / XML
          </Link>{" "}
          upload on LUCID plataform.
        </p>
        <div className="hidden md:block mb-10">
          <p className="font-bold text-grey-blue">Select a year</p>
          <div className="flex gap-5 flex-wrap pt-4">
            {directLicenses.map((directLicense) => (
              <button
                key={directLicense.year}
                onClick={() => setSelectedLicense(directLicense)}
                data-selected={directLicense.year === selectedLicense.year}
                className="bg-tonal-dark-blue-96 text-primary px-5 py-3 rounded-2xl font-bold data-[selected=true]:bg-primary data-[selected=true]:text-on-primary transition-colors"
              >
                {directLicense.year}
              </button>
            ))}
          </div>
        </div>
        <div className="flex items-center gap-2 md:hidden mb-4">
          <p className="text-primary">Select a year:</p>
          <SelectDropdown
            options={directLicenses.map((directLicense) => ({
              label: String(directLicense.year),
              value: String(directLicense.year),
              disabled: directLicense.year === selectedLicense.year,
            }))}
            value={String(selectedLicense.year)}
            onChangeValue={(value) =>
              setSelectedLicense(
                directLicenses.find((directLicense) => String(directLicense.year) === value) || directLicenses[0]
              )
            }
          />
        </div>
        <DirectLicenseReportingTable licenseYear={selectedLicense.year} />

        <div className="my-10 rounded-[20px] bg-tonal-dark-blue-96 py-4 px-7 flex gap-2">
          <Error className="size-6 fill-primary flex-shrink-0" />
          <p className="text-primary">
            Having questions? We have an article that can help you!{" "}
            <Link href="#" className="font-bold underline hover:no-underline">
              Click here to read.
            </Link>
          </p>
        </div>
      </SaasContainer>
    </>
  );
}
