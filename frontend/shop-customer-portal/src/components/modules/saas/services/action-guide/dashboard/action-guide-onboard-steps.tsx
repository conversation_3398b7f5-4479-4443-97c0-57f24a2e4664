import { Step } from "react-joyride";
import { ServiceOnboardStep } from "@/components/modules/saas/services/components/service-onboard/service-onboard-step";

export const actionGuideOnboardSteps: Step[] = [
  {
    content: "first",
    placement: "center",
    target: "body",
  },
  {
    content: (
      <ServiceOnboardStep
        title="Browser through your countries"
        description="See all the countries that you purchase"
      />
    ),
    placement: "right-start",
    target: "#sb-action-countries",
    isFixed: true,
  },
  {
    content: (
      <ServiceOnboardStep
        title="Check your contract information"
        description="Easy way to access all of your contract information per country"
      />
    ),
    placement: "right-start",
    placementBeacon: "top",
    target: "#sb-eu-contract-management",
    isFixed: true,
  },
  {
    content: (
      <ServiceOnboardStep
        title="Access your invoices and track payment status"
        description="Here you can check all payment related content"
      />
    ),
    placement: "right-start",
    placementBeacon: "top",
    target: "#sb-eu-invoices-payment",
    isFixed: true,
  },
  {
    content: (
      <ServiceOnboardStep title="Visualize your task in the map!" description="Keep track of your countries" videoUrl />
    ),
    placement: "top-start",
    target: "#dashboard-Map",
    isFixed: true,
  },
  {
    content: <ServiceOnboardStep title="Or visualize all your task!" description="Keep track of your countries" />,
    placement: "right-start",
    placementBeacon: "top",
    target: "#dashboard-licenses",
    isFixed: true,
  },
  {
    content: (
      <ServiceOnboardStep
        title="Make sure to always stay up to date!"
        description="Here you can track the changes for the countries you had purchased."
        videoUrl
      />
    ),
    placement: "left-start",
    target: "#dashboard-law-changes",
    title: "Our Mission",
  },
  {
    content: "last",
    placement: "center",
    target: "body",
  },
];
