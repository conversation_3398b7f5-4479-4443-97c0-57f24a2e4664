"use client";

import { CountryIcon } from "@/components/_common/country-icon";
import { Divider } from "@/components/_common/divider";
import { TooltipIcon } from "@/components/_common/tooltipIcon";
import { SaasBreadcrumb } from "@/components/modules/saas/components/saas-breadcrumb";
import { SaasContainer } from "@/components/modules/saas/components/saas-container";
import { ServiceOnboard } from "@/components/modules/saas/services/components/service-onboard";
import { Link } from "@/i18n/navigation";
import { ActionGuide } from "@/lib/api/contracts/types";
import { useCustomer } from "@/hooks/use-customer";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East, Elipse } from "@arthursenno/lizenzero-ui-react/Icon";
import { useMemo, useState } from "react";

import { DashboardLawChanges } from "../../components/dashboard-law-changes";
import { actionGuideOnboardSteps } from "./action-guide-onboard-steps";

import { SelectDropdown } from "@/components/_common/select-dropdown";
import { SaasTitle } from "../../../components/saas-title";
import { DashboardBanner } from "../../components/dashboard-banner";
import { DashboardMap, ORDER_FILTERS, STATUS_FILTERS } from "../../components/dashboard-map";
import { CountryStatus, MapCountryStatus } from "../../components/dashboard-map/country-status";
import { useTranslations } from "next-intl";

const paths = [{ label: "Dashboard Action Guide", href: "#" }];

const sortActionsGuides = (actionGuides: ActionGuide[], order: (typeof ORDER_FILTERS)[number]) => {
  return [...actionGuides].sort((a, b) => {
    switch (order.value) {
      case "ASC":
        return a.country_name.localeCompare(b.country_name);
      case "DESC":
        return b.country_name.localeCompare(a.country_name);
      case "FIRST_MODIFIED":
        return new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime();
      case "LAST_MODIFIED":
        return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
      default:
        return 0;
    }
  });
};

export function ActionGuideDashboard() {
  const globalT = useTranslations("modules.saas.global");
  const t = useTranslations("modules.saas.pages.actionGuide.dashboard");

  const { customer } = useCustomer();
  const actionGuideContract = customer?.contracts.find((contract) => contract.type === "ACTION_GUIDE");
  const euLicenseContract = customer?.contracts.find((contract) => contract.type === "EU_LICENSE");

  const missingPowerOfAttorney = !!euLicenseContract?.general_informations.find(
    (information) => information.name === "Power of Attorney" && ["OPEN", "DECLINED"].includes(information.status)
  );

  const [order, setOrder] = useState<(typeof ORDER_FILTERS)[number]>(ORDER_FILTERS[0]);

  const actionGuides = useMemo(() => {
    return sortActionsGuides(actionGuideContract?.action_guides || [], order);
  }, [actionGuideContract?.action_guides, order]);

  if (!actionGuideContract) return null;

  if (!customer) return null;

  function handleChangeOrder(value: string) {
    const order = ORDER_FILTERS.find((order) => order.value === value);

    if (!order) return;

    setOrder(order);
  }

  const mapCountries = actionGuides.map((actionGuide) => {
    return {
      id: actionGuide.id,
      code: actionGuide.country_code,
      name: actionGuide.country_name,
      flag_url: actionGuide.country_flag,
      status: "DONE" as MapCountryStatus,
    };
  });

  return (
    <>
      {missingPowerOfAttorney && (
        <SaasContainer containerClassName="max-md:pb-0">
          <DashboardBanner />
        </SaasContainer>
      )}
      <SaasBreadcrumb paths={paths} />
      <SaasContainer containerClassName="max-md:pb-0">
        <h2 className="text-primary text-3xl font-bold mb-10">
          {globalT("words.greetings", { name: `${customer.first_name} ${customer.last_name}` })}
        </h2>
      </SaasContainer>
      <SaasContainer className="py-16" containerClassName="bg-tonal-cream-96">
        <SaasTitle className="mb-12 justify-between">
          <div className="flex items-center gap-4">Action Guides</div>
          <ServiceOnboard steps={actionGuideOnboardSteps} />
        </SaasTitle>
        <div className="flex flex-col gap-6 w-full">
          <DashboardMap type="ACTION_GUIDE" countries={mapCountries} />
          <div className="grid grid-cols-1 md:grid-cols-12 gap-6 w-full">
            <div
              className="md:col-span-5 bg-background rounded-[40px] py-6 md:py-10 px-4 md:px-6 space-y-6"
              id="dashboard-licenses"
            >
              <div className="flex items-center gap-2">
                <p className="text-grey-blue text-2xl font-bold mt-1">My Licenses</p>
                <TooltipIcon info={"My Licenses"} />
              </div>
              <div className="flex md:flex-row flex-col md:items-center items-start gap-4">
                <SelectDropdown
                  options={ORDER_FILTERS.map((orderFilter) => ({
                    label: orderFilter.label,
                    value: orderFilter.value,
                  }))}
                  value={order.value}
                  onChangeValue={(value) => handleChangeOrder(value)}
                />
              </div>
              <ul>
                {actionGuides.map((actionGuide) => (
                  <li key={actionGuide.id} className="group" data-status={STATUS_FILTERS[3].value}>
                    <div className="flex py-3 md:items-center items-start gap-2 justify-between">
                      <div className="flex flex-1 items-start md:items-center gap-2 w-full">
                        <div className="flex items-center h-full flex-none">
                          <Link href={`./action-guide/countries/${actionGuide.country_code}`}>
                            <CountryIcon
                              country={{ name: actionGuide.country_name, flag_url: actionGuide.country_flag }}
                              className="size-10"
                            />
                          </Link>
                        </div>
                        <div className="space-y-1">
                          <p className="text-primary font-bold text-base">{actionGuide.country_name}</p>
                          <div className="flex items-center gap-1 font-bold text-xs md:text-sm">
                            <Elipse className="w-2 h-2 flex-none text-error fill-error group-data-[status=IN_REVIEW]:fill-tertiary group-data-[status=DONE]:fill-success" />
                            <strong className="text-sm font-bold text-error group-data-[status=IN_REVIEW]:text-tertiary group-data-[status=DONE]:text-success">
                              All tasks are done
                            </strong>
                          </div>
                        </div>
                      </div>
                      <CountryStatus statusValue="DONE" />
                    </div>
                    <Divider style={{ margin: 0 }} />
                  </li>
                ))}
              </ul>

              <Link href="/eu/quick-journey/action-guide/shopping-cart" className="block">
                <Button
                  color="yellow"
                  variant="filled"
                  size="medium"
                  trailingIcon={<East />}
                  onClick={() => {}}
                  className="w-full"
                >
                  Purchase other country
                </Button>
              </Link>
            </div>
            <div className="md:col-span-7">
              <DashboardLawChanges />
            </div>
          </div>
        </div>
      </SaasContainer>
    </>
  );
}
