"use client";

import { Download, EditCircle, RequestQuote, Task } from "@arthursenno/lizenzero-ui-react/Icon";
import Link from "next/link";

import { Skeleton } from "@/components/ui/skeleton";
import { downloadCustomerFile } from "@/lib/api/file";
import { UploadedFile } from "@/lib/api/file/types";
import { getThirdPartyInvoices } from "@/lib/api/third-party-invoice";
import { ThirdPartyInvoice } from "@/lib/api/third-party-invoice/types";
import { cn } from "@/lib/utils";
import { useCustomer } from "@/hooks/use-customer";
import { downloadFile } from "@/utils/download-file";
import { formatCurrency } from "@/utils/formatCurrency";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { useMutation, useQuery } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { CgSpinnerAlt } from "react-icons/cg";

interface ThirdPartyInvoicesCardProps {
  countryCode: string;
}

export function ThirdPartyInvoicesCard({ countryCode }: ThirdPartyInvoicesCardProps) {
  const { customer } = useCustomer();
  const [loadingId, setLoadingId] = useState<number | null>(null);

  const euLicenseContract = customer?.contracts.find((c) => c.type === "EU_LICENSE");

  const license = euLicenseContract?.licenses.find((l) => l.country_code === countryCode);

  const { data: thirdPartyInvoices, isLoading: isLoadingThirdPartyInvoices } = useQuery<ThirdPartyInvoice[], Error>({
    queryKey: ["third-party-invoices", license?.id],
    queryFn: async () => {
      return license ? await getThirdPartyInvoices({ license_id: license.id }) : [];
    },
    enabled: !!license,
  });

  const downloadFileMutation = useMutation({
    mutationFn: async (file: UploadedFile) => {
      const downloadedFile = await downloadCustomerFile(file.id);

      downloadFile({ buffer: downloadedFile, fileName: file.original_name });
    },
  });

  function downloadContractFile(invoiceId: number) {
    try {
      setLoadingId(invoiceId);

      const invoiceSelect = thirdPartyInvoices?.find((item) => item.id === invoiceId);

      if (!invoiceSelect) return;

      const file = invoiceSelect?.license?.files.find((i) => i.original_name === invoiceSelect.title);
      const fileId = file?.id;
      if (!fileId) throw new Error("Invoice not found");

      downloadFileMutation.mutate(file, {
        onSuccess: () => {
          setLoadingId(null);
          enqueueSnackbar("File downloaded successfully", { variant: "success" });
        },
        onError: () => {
          setLoadingId(null);
          enqueueSnackbar("Failed to download file", { variant: "error" });
        },
      });
    } catch (error) {
      setLoadingId(null);
      console.error(error);
      enqueueSnackbar("Error downloading invoice", { variant: "error" });
    }
  }

  if (!license) return null;

  const allDone = !thirdPartyInvoices?.length || thirdPartyInvoices?.every((invoice) => invoice.status === "PAID");

  return (
    <div
      id="third-party-invoices"
      className="col-span-1 md:col-span-11 md:min-h-96 px-4 py-6 md:p-8 bg-background rounded-3xl"
    >
      <div className="flex items-center gap-3 mb-5">
        <h2 className={cn("text-2xl text-primary font-bold", { "text-error": !allDone })}>
          Authorize rep. & Third-party invoices
        </h2>
        <Link href={`/saas/eu-license/third-party-invoices?country-code=${license.country_code}`}>
          <EditCircle className="size-7 fill-support-blue" />
        </Link>
      </div>
      {isLoadingThirdPartyInvoices && (
        <div className="py-6 w-full space-y-4">
          <Skeleton className="w-full h-14" />
          <Skeleton className="w-full h-14" />
          <Skeleton className="w-full h-14" />
          <Skeleton className="w-full h-14" />
        </div>
      )}
      {thirdPartyInvoices && !thirdPartyInvoices.length && (
        <p className="text-primary text-center">No third party invoices found</p>
      )}
      {!!thirdPartyInvoices &&
        thirdPartyInvoices.length &&
        thirdPartyInvoices.map((thirdPartyInvoice) => (
          <div key={thirdPartyInvoice.id} className="w-full flex items-start gap-3 border-b border-tonal-dark-cream-80">
            {thirdPartyInvoice.status === "PAID" ? (
              <Task className="flex-none w-7 h-7 fill-success" />
            ) : (
              <RequestQuote className="flex-none w-7 h-7 fill-alert" />
            )}
            <div className="flex-1 flex items-center gap-4">
              <div className="flex-1 text-left">
                <p className="text-primary">{thirdPartyInvoice.title}</p>
                <p className="mt-1 text-xs font-medium text-on-tertiary">
                  Uploaded date{" "}
                  <span className="text-tonal-dark-cream-40">
                    {new Date(thirdPartyInvoice.updated_at).toLocaleDateString()}
                  </span>
                </p>
              </div>
              <div className="py-3 px-4 w-40">
                <p className="text-sm text-right text-tonal-dark-cream-30">{formatCurrency(thirdPartyInvoice.price)}</p>
              </div>
              <div className="flex items-center">
                <Button
                  type="button"
                  variant="text"
                  color="light-blue"
                  size="iconMedium"
                  className="p-2"
                  onClick={() => downloadContractFile(thirdPartyInvoice.id)}
                  disabled={!!loadingId}
                  leadingIcon={
                    loadingId === thirdPartyInvoice.id ? <CgSpinnerAlt className="animate-spin" /> : <Download />
                  }
                />
              </div>
            </div>
          </div>
        ))}
    </div>
  );
}
