import { Spinner } from "@/components/ui/loader";
import { CreateVolumeReportItemsParam } from "@/lib/api/volume-report-item";
import { VolumeReport } from "@/lib/api/volume-report/types";
import { generateVolumesXml } from "@/utils/generate-volumes-xml";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Download } from "@arthursenno/lizenzero-ui-react/Icon";
import { useTranslations } from "next-intl";
import { useState } from "react";

interface DownloadVolumesXmlProps {
  selectedVolumeReport: VolumeReport;
  licenseYear: number;
  selectedInterval: string;
  volumes: Record<string, CreateVolumeReportItemsParam>;
}

export function DownloadVolumesXml({
  selectedVolumeReport,
  licenseYear,
  selectedInterval,
  volumes,
}: DownloadVolumesXmlProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  const globalT = useTranslations("modules.saas.global");

  async function handleDownloadVolumeXml() {
    if (!selectedVolumeReport) return;

    const xmlContent = generateVolumesXml({ selectedVolumeReport, volumes });

    const blob = new Blob([xmlContent], { type: "application/xml" });
    const url = URL.createObjectURL(blob);

    const a = document.createElement("a");
    a.href = url;
    a.download = `quantity-report-sales-packaging-${licenseYear}-${selectedInterval}.xml`;
    document.body.appendChild(a);
    a.click();

    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }

  return (
    <Button
      type="button"
      color="gray"
      size="large"
      variant="text"
      className="text-success fill-success !hover:bg-success/10"
      leadingIcon={isDownloading ? <Spinner size="sm" /> : <Download />}
      onClick={handleDownloadVolumeXml}
      disabled={isDownloading}
    >
      {globalT("buttons.xml.label")}
    </Button>
  );
}
