"use client";

import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { SaasBreadcrumb } from "@/components/modules/saas/components/saas-breadcrumb";
import { SaasContainer } from "@/components/modules/saas/components/saas-container";
import { LicenseTabs } from "../../components/license-tabs";
import { LicenseTabsProvider } from "../../components/license-tabs/use-license-tabs";
import { LicenseThirdPartyInvoices } from "./license-third-party-invoices";
import { ThirdPartyInvoicesList } from "./third-party-invoices-list";
import { useTranslations } from "next-intl";

const path = [
  { label: "Dashboard EU", href: "/saas/eu-license" },
  { label: "Third Party Invoices", href: "#" },
];

export function EuLicenseThirdPartyInvoices() {
  const t = useTranslations("modules.saas.pages.euLicense.thirdPartyInvoices");

  return (
    <>
      <SaasBreadcrumb paths={path} />
      <SaasContainer>
        <TitleAndSubTitle showIcon title={t("title")} subText={t("description")} />
        <div className="w-full h-[1px] bg-tonal-dark-cream-80 rounded-full mb-8"></div>
        <LicenseTabsProvider pendencyType="INVOICES">
          <LicenseTabs />
          <div className="flex flex-col gap-4 md:gap-6 w-full mt-4 lg:mt-16">
            <ThirdPartyInvoicesList />
            <LicenseThirdPartyInvoices />
          </div>
        </LicenseTabsProvider>
      </SaasContainer>
    </>
  );
}
