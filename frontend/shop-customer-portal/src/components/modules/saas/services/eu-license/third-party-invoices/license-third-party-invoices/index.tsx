import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { useLicenseTabs } from "../../../components/license-tabs/use-license-tabs";
import { ThirdPartyInvoiceList } from "./third-party-invoice-list";
import { useTranslations } from "next-intl";

export function LicenseThirdPartyInvoices() {
  const { selectedLicense } = useLicenseTabs();
  const t = useTranslations("modules.saas.pages.euLicense.thirdPartyInvoices");

  if (!selectedLicense) return null;

  return (
    <div className="text-primary w-full rounded-[32px] bg-surface-02 items-start flex flex-col gap-5 py-5 px-4 lg:py-7 lg:px-8">
      <div className="flex flex-row gap-4 items-center">
        <h2 className="text-primary font-semibold text-2xl">{t("subtitle")}</h2>
        <QuestionTooltip>
          <QuestionTooltipDescription>{t("subtitle")}</QuestionTooltipDescription>
        </QuestionTooltip>
      </div>
      <ThirdPartyInvoiceList licenseId={selectedLicense.id} />
    </div>
  );
}
