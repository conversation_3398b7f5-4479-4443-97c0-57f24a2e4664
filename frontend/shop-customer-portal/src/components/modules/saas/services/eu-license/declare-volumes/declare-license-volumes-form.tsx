import { Divider } from "@/components/_common/divider";
import { FractionInput } from "@/components/_common/forms/fraction-input";
import { QuestionTooltip, QuestionTooltipDescription } from "@/components/_common/question-tooltip";
import { ReportTable } from "@/components/_common/report-table";
import { SelectDropdown } from "@/components/_common/select-dropdown";
import { Skeleton } from "@/components/ui/skeleton";
import { api } from "@/lib/api";
import { ReportSet } from "@/lib/api/commitment/types";
import { getPackagingService } from "@/lib/api/packaging-services";
import { PackagingService } from "@/lib/api/packaging-services/types";
import { createVolumeReportItems, CreateVolumeReportItemsParam } from "@/lib/api/volume-report-item";
import { queryClient } from "@/lib/react-query";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { East, Error, FilterAlt, KeyboardArrowDown, Plant } from "@arthursenno/lizenzero-ui-react/Icon";
import { Modal } from "@arthursenno/lizenzero-ui-react/Modal";
import { useMutation, useQuery } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { FormEvent, useRef, useState } from "react";
import { CgSpinnerAlt } from "react-icons/cg";
import { DownloadVolumesPdf } from "./download-volumes-pdf";
import { DownloadVolumesXml } from "./download-volumes-xml";
import { useTranslations } from "next-intl";

export const FREQUENCY_LABELS = {
  en: {
    ANNUALLY: "Annualy",
    MONTHLY: "Monthly",
    QUARTERLY: "Quarterly",
  },
} as Record<string, Record<string, string>>;

export const INTERVAL_LABELS = {
  en: {
    ANNUALLY: "Annualy",
    Q1: "Q1",
    Q2: "Q2",
    Q3: "Q3",
    Q4: "Q4",
    JANUARY: "January",
    FEBRUARY: "February",
    MARCH: "March",
    APRIL: "April",
    MAY: "May",
    JUNE: "June",
    JULY: "July",
    AUGUST: "August",
    SEPTEMBER: "September",
    OCTOBER: "October",
    NOVEMBER: "November",
    DECEMBER: "December",
  },
} as Record<string, Record<string, string>>;

interface DeclareLicenseVolumesFormProps {
  contractId: number;
  countryCode: string;
  licenseYear: number;
  packagingService: PackagingService;
}

export function DeclareLicenseVolumesForm({
  contractId,
  countryCode,
  licenseYear,
  packagingService,
}: DeclareLicenseVolumesFormProps) {
  const printRef = useRef<HTMLDivElement>(null);
  const globalT = useTranslations("modules.saas.global");
  const t = useTranslations("modules.saas.pages.euLicense.declareVolumes");
  const [selectedInterval, setSelectedInterval] = useState<string | null>(null);
  const [volumes, setVolumes] = useState<Record<string, CreateVolumeReportItemsParam>>({});
  const [openSuccessModal, setOpenSuccessModal] = useState(false);

  const { data: setupReportSet, isLoading: isLoadingSetupReportSet } = useQuery({
    queryKey: ["setup-report-set", packagingService.report_set.setup_report_set_id],
    queryFn: async () => {
      try {
        const response = await api.get(
          `/admin/service-setups/${countryCode}/report-sets/${packagingService.report_set.setup_report_set_id}`
        );
        if (!response.data || response.status !== 200) throw "Failed to fetch setup report set";

        return response.data as ReportSet;
      } catch (error) {
        console.error(error);
        throw error;
      }
    },
  });

  const selectedVolumeReport =
    packagingService.volume_reports.find((report) => report.interval === selectedInterval) || null;

  const { isLoading: isLoadingPackagingService } = useQuery({
    queryKey: ["packaging-service", packagingService.id],
    queryFn: async () => {
      const packagingServiceDetails = await getPackagingService(packagingService.id);

      const volumeReport = selectedInterval
        ? packagingServiceDetails.volume_reports.find((vol) => vol.interval === selectedInterval)
        : packagingServiceDetails.volume_reports[0];

      if (!volumeReport) return null;

      if (!selectedInterval) {
        setSelectedInterval(volumeReport.interval);
      }

      const formattedVolumes = volumeReport.volume_report_items.reduce(
        (acc, item) => {
          acc[`${item.setup_fraction_id}_${item.setup_column_id}`] = {
            setup_fraction_id: item.setup_fraction_id,
            setup_column_id: item.setup_column_id,
            setup_fraction_code: item.setup_fraction_code,
            setup_column_code: item.setup_column_code,
            value: item.value,
          };
          return acc;
        },
        {} as Record<string, CreateVolumeReportItemsParam>
      );
      setVolumes(formattedVolumes);

      return packagingServiceDetails;
    },
  });

  const submitReportMutation = useMutation({
    mutationFn: async (data: CreateVolumeReportItemsParam[]) => createVolumeReportItems(data),
  });

  async function handleFormSubmit(e: FormEvent) {
    e.preventDefault();

    if (!selectedVolumeReport) return;

    const volumeReportItems = Object.entries(volumes).map(([key, item]) => ({
      license_volume_report_id: selectedVolumeReport.id,
      setup_fraction_id: item.setup_fraction_id,
      setup_fraction_code: item.setup_fraction_code,
      setup_column_id: item.setup_column_id,
      setup_column_code: item.setup_column_code,
      value: item.value,
    }));

    submitReportMutation.mutate(volumeReportItems, {
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: ["packaging-services", { license_id: packagingService?.license_id }],
        });
        queryClient.invalidateQueries({
          queryKey: ["licenses", { contract_id: contractId }],
        });
        setOpenSuccessModal(true);
      },
      onError: () => {
        enqueueSnackbar("Failed to submit report. Please try again.", {
          variant: "error",
        });
      },
    });
  }

  function handleChangeVolumeReportItemValue({
    fractionId,
    fractionCode,
    columnId,
    columnCode,
    value,
  }: {
    fractionId: number;
    fractionCode: string;
    columnId: number;
    columnCode: string;
    value?: number;
  }) {
    const key = `${fractionId}_${columnId}`;

    setVolumes((prevVolumes) => ({
      ...prevVolumes,
      [key]: {
        setup_fraction_id: fractionId,
        setup_fraction_code: fractionCode,
        setup_column_id: columnId,
        setup_column_code: columnCode,
        value: value || 0,
      },
    }));
  }

  const isLoading = isLoadingPackagingService || isLoadingSetupReportSet;

  if (isLoading)
    return (
      <div className="mt-8 space-y-6">
        <Divider initialMarginDisabled />
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Skeleton className="w-40 h-10" />
            <Skeleton className="w-24 h-10" />
          </div>
          <Skeleton className="w-1/2 h-4" />
        </div>
        <div className="space-y-4">
          <Skeleton className="w-full h-6" />
          <Skeleton className="w-full h-6" />
          <Skeleton className="w-full h-6" />
          <Skeleton className="w-full h-6" />
          <Skeleton className="w-full h-6" />
          <Skeleton className="w-full h-6" />
          <Skeleton className="w-full h-6" />
          <Skeleton className="w-full h-6" />
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Skeleton className="w-32 h-10" />
            <Skeleton className="w-32 h-10" />
          </div>
          <Skeleton className="w-40 h-10" />
        </div>
      </div>
    );

  return (
    <div key={packagingService.id}>
      <SubmittedReportModal isOpen={openSuccessModal} setOpenModal={setOpenSuccessModal} />
      <Divider initialMarginDisabled />
      <div className="space-y-6 mt-8">
        <div className="flex items-start justify-between">
          <div className="space-y-6">
            <p className="text-title-3 text-primary font-bold">{packagingService.name}</p>
            <div className="flex items-center gap-2">
              <p>
                {t("reportsShouldBeDoneBetween")}{" "}
                <b>{FREQUENCY_LABELS["en"][packagingService?.report_set_frequency?.rhythm]}</b>
              </p>
              <QuestionTooltip>
                <QuestionTooltipDescription>{t("reportsShouldBeDoneBetween")}</QuestionTooltipDescription>
              </QuestionTooltip>
            </div>
          </div>
          {packagingService.report_set_frequency.rhythm === "ANNUALLY" && (
            <div className="text-support-blue text-lg font-bold">{packagingService.volume_reports[0].interval}</div>
          )}
          {packagingService.report_set_frequency.rhythm !== "ANNUALLY" && (
            <SelectDropdown
              options={packagingService.volume_reports.map((volumeReport) => ({
                label:
                  packagingService.report_set_frequency.rhythm === "ANNUALLY"
                    ? volumeReport.interval
                    : INTERVAL_LABELS["en"][volumeReport.interval],
                value: volumeReport.interval,
                icon:
                  volumeReport.status === "OPEN" ? (
                    <Error width={20} height={20} className="fill-tonal-red-40 " />
                  ) : undefined,
              }))}
              value={INTERVAL_LABELS["en"][packagingService.volume_reports[0].interval]}
              onChangeValue={setSelectedInterval}
            />
          )}
        </div>

        {isLoading && (
          <div className="w-full flex justify-center items-center py-16 gap-4">
            <CgSpinnerAlt size={32} className="animate-spin text-primary" />
            <p>{t("loadingReportTable")}</p>
          </div>
        )}

        {!!selectedVolumeReport && !isLoading && (
          <form onSubmit={handleFormSubmit}>
            <div ref={printRef}>
              <ReportTable
                columns={setupReportSet?.columns || []}
                fractions={setupReportSet?.fractions || []}
                field={(field, fraction) => (
                  <FractionInput
                    type="weight"
                    className="h-10 rounded-2xl"
                    disabled={field.disabled}
                    onChange={(value) => {
                      handleChangeVolumeReportItemValue({
                        columnId: fraction.columnId,
                        columnCode: fraction.columnCode,
                        fractionId: fraction.fractionId,
                        fractionCode: fraction.fractionCode,
                        value: Number(value) || undefined,
                      });
                    }}
                    value={volumes[`${fraction.fractionId}_${fraction.columnId}`]?.value}
                    // data-invalid={!!fractionErrors && !!fractionErrors[fraction.code]}
                  />
                )}
              />
            </div>
            <div className="flex flex-col md:flex-row w-full items-center justify-between mt-10 gap-4 md:gap-0">
              <div className="flex items-center space-x-4 font-medium text-tonal-dark-cream-40 col-span-5 order-2 md:order-1">
                <DownloadVolumesPdf
                  printRef={printRef}
                  licenseYear={licenseYear}
                  selectedInterval={selectedInterval!}
                />
                <DownloadVolumesXml
                  selectedVolumeReport={selectedVolumeReport}
                  licenseYear={licenseYear}
                  selectedInterval={selectedInterval!}
                  volumes={volumes}
                />
              </div>
              <div className="col-span-1 order-1 md:order-2 w-full md:w-auto">
                <Button
                  type="submit"
                  color="yellow"
                  size="medium"
                  variant="filled"
                  trailingIcon={
                    submitReportMutation.isPending ? <CgSpinnerAlt className="animate-spin size-5" /> : <East />
                  }
                  className="text-nowrap w-full md:w-auto"
                  disabled={submitReportMutation.isPending || !Object.values(volumes).length}
                >
                  {submitReportMutation.isPending
                    ? globalT("buttons.submitData.loading")
                    : globalT("buttons.submitData.label")}
                </Button>
              </div>
            </div>
          </form>
        )}
      </div>
    </div>
  );
}

function SubmittedReportModal({ isOpen, setOpenModal }: { isOpen: boolean; setOpenModal: (value: boolean) => void }) {
  const globalT = useTranslations("modules.saas.global");
  const t = useTranslations("modules.saas.pages.euLicense.declareVolumes");

  return (
    <Modal
      open={isOpen}
      style={{ borderRadius: "52px", maxWidth: "500px" }}
      className="bg-surface-01 z-[9999] min-w-min w-full"
      onOpenChange={(open) => setOpenModal(open)}
    >
      <div className="justify-center overflow-auto p-1 text-primary">
        <div className="flex items-center space-x-4 mb-5">
          <Plant width={40} height={40} className="fill-success" />
          <h3 className="font-medium text-primary text-[28px]">{t("reportSubmitted.title")}</h3>
        </div>
        <p>{t("reportSubmitted.description")}</p>

        <div className="w-full flex justify-end mt-5 items-center space-x-4">
          <Button
            color="dark-blue"
            variant="filled"
            size="medium"
            style={{ minWidth: "200px" }}
            onClick={() => setOpenModal(false)}
          >
            {globalT("buttons.close.label")}
          </Button>
        </div>
      </div>
    </Modal>
  );
}
