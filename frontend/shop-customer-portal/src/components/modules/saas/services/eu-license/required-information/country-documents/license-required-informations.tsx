import { CountryIcon } from "@/components/_common/country-icon";
import { Divider } from "@/components/_common/divider";
import { Skeleton } from "@/components/ui/skeleton";
import { StatusBadge } from "@/components/ui/status-badge";
import { getRequiredInformations } from "@/lib/api/required-informations";
import { useQuery } from "@tanstack/react-query";
import { useLicenseTabs } from "../../../components/license-tabs/use-license-tabs";
import { RequiredInformationItem } from "../required-information-item";

export function LicenseRequiredInformations() {
  const { selectedLicense } = useLicenseTabs();

  const { data: requiredInformations, isLoading } = useQuery({
    queryKey: ["required-informations", { license_id: selectedLicense?.id }],
    queryFn: async () => {
      if (!selectedLicense) return [];

      return getRequiredInformations({ license_id: selectedLicense.id });
    },
    enabled: !!selectedLicense,
  });

  if (!selectedLicense) return null;

  return (
    <div className="text-primary w-full rounded-[32px] bg-white mt-6">
      <div className="flex justify-start md:justify-between w-full py-4 px-7">
        <div className="flex items-center gap-3">
          <CountryIcon
            country={{ flag_url: selectedLicense.country_flag, name: selectedLicense.country_name }}
            className="size-7"
          />
          <span className="text-primary font-bold text-2xl">{selectedLicense.country_name}</span>
        </div>

        <div className="flex flex-row items-center justify-between md:justify-end flex-1">
          {selectedLicense._status === "DONE" && <StatusBadge variant="success" label="Done" />}
          {selectedLicense._status === "OPEN_TO_DOS" && <StatusBadge variant="error" label="Information Required" />}
        </div>
      </div>

      <ul className="space-y-8 w-full px-4 md:px-7 py-3 md:pb-8">
        {!!requiredInformations?.length &&
          requiredInformations?.map((requiredInformation) => (
            <li key={requiredInformation.id}>
              <RequiredInformationItem
                contractId={selectedLicense.contract_id}
                requiredInformation={requiredInformation}
              />
            </li>
          ))}
        {isLoading &&
          Array.from({ length: 3 }).map((_, index) => (
            <li key={index}>
              <div className="flex items-center gap-4 py-3">
                <Skeleton className="size-9 flex-none" />
                <Skeleton className="h-7 w-full" />
              </div>
              <Divider style={{ margin: 0 }} />
            </li>
          ))}
      </ul>
    </div>
  );
}
