"use client";

import { useCustomer } from "@/hooks/use-customer";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { Divider } from "@/components/_common/divider";
import { getRequiredInformations } from "@/lib/api/required-informations";
import { RequiredInformationItem } from "../required-information-item";
import { StatusBadge } from "@/components/ui/status-badge";
import { useTranslations } from "next-intl";

export function GeneralInformations() {
  const { customer } = useCustomer();
  const t = useTranslations("modules.saas.pages.euLicense.requiredInformation");

  const euLicenseContract = customer?.contracts.find((contract) => contract.type === "EU_LICENSE");

  const { data: generalInformations, isLoading } = useQuery({
    queryKey: ["general-informations", { contract_id: euLicenseContract?.id }],
    queryFn: async () => {
      if (!euLicenseContract) return [];

      return getRequiredInformations({ contract_id: euLicenseContract?.id });
    },
    enabled: !!euLicenseContract,
  });

  const allDone =
    !!generalInformations &&
    generalInformations?.every((generalInformation) => ["DONE", "APPROVED"].includes(generalInformation.status));

  return (
    <div className="w-full flex flex-row justify-center">
      <div className="w-full">
        <div className="w-full rounded-[32px] items-start bg-tonal-cream-96 flex flex-col px-4 py-6 md:py-7 md:px-8">
          <div className="flex items-center justify-between w-full">
            <p className="text-primary text-lg lg:text-3xl font-medium lg:font-bold text-nowrap">
              {t("generalDocuments.title")}
            </p>
            {!isLoading && (
              <>
                {allDone ? (
                  <StatusBadge variant="success" label="Done" />
                ) : (
                  <StatusBadge variant="error" label="Information Required" />
                )}
              </>
            )}
          </div>
          <div className="text-primary w-full rounded-[32px] bg-white mt-6">
            <ul className="space-y-8 w-full px-4 md:px-7 py-3 md:pb-8">
              {euLicenseContract &&
                !!generalInformations?.length &&
                generalInformations?.map((generalInformation) => (
                  <li key={generalInformation.id}>
                    <RequiredInformationItem
                      contractId={euLicenseContract.id}
                      requiredInformation={generalInformation}
                    />
                  </li>
                ))}
              {!euLicenseContract ||
                (isLoading &&
                  Array.from({ length: 3 }).map((_, index) => (
                    <li key={index}>
                      <div className="flex items-center gap-4 py-3">
                        <Skeleton className="size-9 flex-none" />
                        <Skeleton className="h-7 w-full" />
                      </div>
                      <Divider style={{ margin: 0 }} />
                    </li>
                  )))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
