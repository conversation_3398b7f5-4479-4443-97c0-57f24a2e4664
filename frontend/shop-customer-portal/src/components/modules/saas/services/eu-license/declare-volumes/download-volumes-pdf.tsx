import { Spinner } from "@/components/ui/loader";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Download } from "@arthursenno/lizenzero-ui-react/Icon";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import { useState } from "react";
import { toPng } from "html-to-image";
import { useTranslations } from "next-intl";

interface DownloadVolumesPdfProps {
  printRef: React.RefObject<HTMLDivElement>;
  licenseYear: number;
  selectedInterval: string;
}

export function DownloadVolumesPdf({ printRef, licenseYear, selectedInterval }: DownloadVolumesPdfProps) {
  const [isDownloading, setIsDownloading] = useState(false);
  const globalT = useTranslations("modules.saas.global");

  async function handleDownloadVolumePdf() {
    const element = printRef.current;
    if (!element) return;

    setIsDownloading(true);

    try {
      const data = await toPng(element);

      const pdf = new jsPDF();
      const pageWidth = pdf.internal.pageSize.getWidth();
      const pageHeight = pdf.internal.pageSize.getHeight();
      const imgProperties = pdf.getImageProperties(data);
      const imgWidth = pageWidth;
      const imgHeight = (imgProperties.height * imgWidth) / imgProperties.width;

      if (imgHeight <= pageHeight) {
        pdf.addImage(data, "PNG", 0, 0, imgWidth, imgHeight);
      } else {
        let positionY = 0;

        while (positionY < imgHeight) {
          pdf.addImage(data, "PNG", 0, -positionY, imgWidth, imgHeight);
          positionY += pageHeight;

          if (positionY < imgHeight) {
            pdf.addPage();
          }
        }
      }

      pdf.save(`quantity-report-sales-packaging-${licenseYear}-${selectedInterval}.pdf`);
    } catch (error) {
      console.error("Erro ao gerar o PDF:", error);
    } finally {
      setIsDownloading(false);
    }
  }

  return (
    <Button
      type="button"
      color="light-blue"
      size="large"
      variant="text"
      leadingIcon={isDownloading ? <Spinner size="sm" /> : <Download />}
      onClick={handleDownloadVolumePdf}
      disabled={isDownloading}
    >
      {globalT("buttons.pdf.label")}
    </Button>
  );
}
