"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { Controller, useForm, useWatch } from "react-hook-form";

import { z } from "zod";

import {
  QuestionTooltip,
  QuestionTooltipDescription,
  QuestionTooltipTitle,
} from "@/components/_common/question-tooltip";
import { deleteFile, downloadAdminFile } from "@/lib/api/file";
import { queryClient } from "@/lib/react-query";
import { formatFileSize } from "@/utils/filesize";
import { formatDateToDDMMYYYY } from "@/utils/formatDateToDDMMYYYY";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { CheckCircle, Delete, Download, Error } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";

import { StatusBadge } from "@/components/ui/status-badge";
import { submitRequiredInformation, updateRequiredInformation } from "@/lib/api/required-informations";
import { RequiredInformation } from "@/lib/api/required-informations/types";
import { useCustomer } from "@/hooks/use-customer";
import { downloadFile } from "@/utils/download-file";
import { useEffect } from "react";
import { CgSpinnerAlt } from "react-icons/cg";
import { FileUploadButton } from "../../components/file-upload-button";
import { RequiredInformationIcon } from "./required-information-icon";
import { useTranslations } from "next-intl";

interface RequiredInformationItemProps {
  contractId: number;
  requiredInformation: RequiredInformation;
}

function getDetailsByRequiredInformationType(requiredInformation: RequiredInformation) {
  const basePath = "/assets/images";

  switch (requiredInformation.type) {
    case "FILE":
    case "DOCUMENT":
      return {
        title: requiredInformation.name,
        imageUrl: `${basePath}/logo_pdf.png`,
      };
    case "IMAGE":
      return {
        title: requiredInformation.name,
        imageUrl: `${basePath}/logo_png.png`,
      };
    case "NUMBER":
      return {
        title: requiredInformation.name,
        imageUrl: `${basePath}/hashtag.png`,
      };
    case "TEXT":
      return {
        title: requiredInformation.name,
        imageUrl: `${basePath}/text_input.png`,
      };
    default:
      return { title: "", imageUrl: "" };
  }
}

const requiredInformationAnswerFormSchema = z.object({
  answer: z.string().trim().min(1, "Required field"),
});

type RequiredInformationAnswerFormValues = z.infer<typeof requiredInformationAnswerFormSchema>;

export function RequiredInformationItem({ contractId, requiredInformation }: RequiredInformationItemProps) {
  const globalT = useTranslations("modules.saas.global");
  const t = useTranslations("modules.saas.pages.euLicense.requiredInformation");
  const queryKey =
    requiredInformation.kind === "GENERAL_INFORMATION"
      ? ["general-informations", { contract_id: contractId }]
      : ["required-informations", { license_id: requiredInformation.license_id }];

  const requiredInformationDetails = getDetailsByRequiredInformationType(requiredInformation);

  const { customer } = useCustomer();

  const form = useForm<RequiredInformationAnswerFormValues>({
    resolver: zodResolver(requiredInformationAnswerFormSchema),
    defaultValues: {
      answer: requiredInformation.answer || "",
    },
  });

  useEffect(() => {
    if (requiredInformation.status === "DECLINED") {
      if (requiredInformation.type === "TEXT" || requiredInformation.type === "NUMBER") {
        form.setError("answer", { message: requiredInformation.decline?.title || "Declined information" });
      }
    }
  }, [requiredInformation.status]);

  const submitRequiredInformationMutation = useMutation({
    mutationFn: (data: { id: number; user_id: number; file?: File; answer?: string }) =>
      submitRequiredInformation(data.id, data),
  });

  const deleteFileMutation = useMutation({
    mutationFn: async (fileId: string) => {
      await deleteFile(fileId);

      if (!requiredInformation.answer_files?.filter((file) => file.id !== fileId).length) {
        await updateRequiredInformation(requiredInformation.id, { status: "OPEN" });
      }
    },
  });

  const downloadFileMutation = useMutation({
    mutationFn: async (fileId: string) => {
      if (!customer) return;
      return downloadAdminFile(fileId);
    },
  });

  async function handleSubmitRequiredInformation({ file, answer }: { file?: File; answer?: string }) {
    if (!customer) return;

    submitRequiredInformationMutation.mutate(
      {
        id: requiredInformation.id,
        user_id: customer.user_id,
        file,
        answer,
      },
      {
        onSuccess: () => {
          enqueueSnackbar("Information updated successfully", { variant: "success" });
          queryClient.invalidateQueries({ queryKey: ["licenses", { contract_id: contractId }] });

          queryClient.invalidateQueries({ queryKey });
        },
        onError: (error) => {
          console.error(error.message);
          enqueueSnackbar(error.message || "Unable to update information", { variant: "error" });
        },
      }
    );
  }

  async function handleDownloadFile(fileId: string) {
    downloadFileMutation.mutate(fileId, {
      onSuccess: (file) => {
        downloadFile({ buffer: file, fileName: "document.pdf" });

        enqueueSnackbar("File downloaded successfully", { variant: "default" });
      },
      onError: () => {
        enqueueSnackbar("Failed to download. Please try again", { variant: "default" });
      },
    });
  }

  async function handleDeleteFile(fileId: string) {
    if (!customer) return;

    deleteFileMutation.mutate(fileId, {
      onSuccess: () => {
        enqueueSnackbar("File deleted successfully", { variant: "success" });
        queryClient.invalidateQueries({ queryKey: ["licenses", { contract_id: contractId }] });
        queryClient.invalidateQueries({ queryKey });
      },
      onError: (error) => {
        enqueueSnackbar("Unable to delete file. Please try again", { variant: "error" });
      },
    });
  }

  const isTypeDocument = requiredInformation.type === "DOCUMENT";
  const isTypeImage = requiredInformation.type === "IMAGE";
  const isTypeFile = requiredInformation.type === "FILE";
  const isTypeText = requiredInformation.type === "TEXT";
  const isTypeNumber = requiredInformation.type === "NUMBER";

  const isSubmitting = form.formState.isSubmitting || submitRequiredInformationMutation.isPending;

  const inputAnswer = useWatch({ control: form.control, name: "answer" });

  return (
    <div className="group flex flex-col w-full gap-2">
      <div className="flex items-center gap-2 py-3 border-b-[1px] border-tonal-dark-cream-80">
        <RequiredInformationIcon requiredInformationType={requiredInformation.type} />

        <div className="flex items-center gap-2 flex-1">
          <p className="text-primary font-bold">{requiredInformationDetails.title}</p>

          <QuestionTooltip>
            {/* <QuestionTooltipTitle></QuestionTooltipTitle> */}
            <QuestionTooltipDescription>
              Lorem ipsum dolor sit amet consectetur adipisicing elit.
            </QuestionTooltipDescription>
          </QuestionTooltip>

          {requiredInformation.status === "DECLINED" && <StatusBadge variant="error" label="Declined" />}
          {requiredInformation.status === "OPEN" && <StatusBadge variant="info" label="New" />}
        </div>

        {requiredInformation.type === "DOCUMENT" && !!requiredInformation.file_id && (
          <Button
            variant="text"
            color="dark-blue"
            size="medium"
            disabled={downloadFileMutation.isPending}
            leadingIcon={downloadFileMutation.isPending ? <CgSpinnerAlt className="animate-spin" /> : <Download />}
            onClick={() => !!requiredInformation.file_id && handleDownloadFile(requiredInformation.file_id!)}
          >
            {downloadFileMutation.isPending ? globalT("buttons.download.loading") : globalT("buttons.download.label")}
          </Button>
        )}
      </div>

      {/* Answer Files */}
      {Array(requiredInformation.answer_files || []).length > 0 && (
        <ul className="flex flex-col">
          {requiredInformation.answer_files?.map((answer_file) => (
            <li key={answer_file.id} className="group flex items-center justify-between gap-4 py-1">
              <div className="flex flex-col gap-1">
                <p
                  data-declined={String(requiredInformation.status === "DECLINED")}
                  className="flex-1 flex items-center gap-2 text-xs md:text-sm text-tonal-dark-cream-20 data-[declined='true']:text-error"
                >
                  {!!Object.keys(requiredInformation.decline || {}).length && (
                    <QuestionTooltip icon={<Error className="size-5 fill-error" />}>
                      <QuestionTooltipTitle className="text-primary text-base font-bold">Declined</QuestionTooltipTitle>
                      <QuestionTooltipDescription>
                        {t("notAcceptedReasons")}
                        <ul className="mt-2">
                          {requiredInformation?.decline?.decline_reasons?.map((reason) => (
                            <li key={reason.id}>- {reason.title}</li>
                          ))}
                        </ul>
                      </QuestionTooltipDescription>
                    </QuestionTooltip>
                  )}
                  {answer_file.original_name || answer_file.name}
                </p>
                {!!requiredInformation.decline && (
                  <div className="flex items-center gap-2">
                    <span className="text-xs md:text-sm font-medium text-error">
                      {requiredInformation.decline?.title}
                    </span>
                  </div>
                )}
              </div>
              <div className="flex items-center justify-end text-right gap-4 text-tonal-dark-cream-40">
                <span className="md:pr-12 text-xs font-medium text-nowrap">
                  {formatFileSize(Number(answer_file.size ?? 0))}
                </span>
                <span className="text-xs font-medium">{formatDateToDDMMYYYY(answer_file.created_at)}</span>
                <Button
                  aria-label="Delete file"
                  type="button"
                  variant="text"
                  color="dark-blue"
                  size="iconSmall"
                  disabled={deleteFileMutation.isPending}
                  leadingIcon={
                    deleteFileMutation.isPending ? (
                      <CgSpinnerAlt className="animate-spin" />
                    ) : (
                      <Delete className="fill-primary" />
                    )
                  }
                  onClick={() => handleDeleteFile(answer_file.id)}
                />
              </div>
            </li>
          ))}
        </ul>
      )}

      {/* Answer Form */}
      {(isTypeText || isTypeNumber) && (
        <form
          onSubmit={form.handleSubmit(handleSubmitRequiredInformation)}
          data-declined={requiredInformation.status === "DECLINED"}
          className="group flex items-start gap-8 py-3"
        >
          {isTypeText && (
            <Controller
              control={form.control}
              name="answer"
              render={({ field, fieldState: { error } }) => (
                <Input
                  placeholder="Text Input"
                  variant={!!error && "error"}
                  errorMessage={error?.message}
                  {...field}
                  rightIcon={
                    (error?.message && <Error className="size-5 fill-error" />) ||
                    (!error && requiredInformation.answer !== null && <CheckCircle className="size-5 fill-success" />)
                  }
                />
              )}
            />
          )}
          {isTypeNumber && (
            <Controller
              control={form.control}
              name="answer"
              render={({ field, fieldState: { error } }) => (
                <Input
                  type="number"
                  placeholder="Number Input"
                  variant={!!error && "error"}
                  errorMessage={error?.message}
                  value={Number(field.value ?? 0)}
                  onChange={(event: React.ChangeEvent<HTMLInputElement>) => field.onChange(String(event.target.value))}
                />
              )}
            />
          )}

          <Button
            type="submit"
            variant="filled"
            color="yellow"
            size="medium"
            className="mt-2"
            disabled={requiredInformation.answer === inputAnswer || isSubmitting}
          >
            {isSubmitting ? globalT("buttons.submit.loading") : globalT("buttons.submit.label")}
          </Button>
        </form>
      )}

      {/* Upload (DOCUMENT | FILE | IMAGE) */}
      <div>
        {isTypeDocument && (
          <FileUploadButton
            isLoading={submitRequiredInformationMutation.isPending}
            onUpload={(file) => handleSubmitRequiredInformation({ file })}
            accept={{ "application/pdf": [".pdf"] }}
          >
            {submitRequiredInformationMutation.isPending
              ? globalT("buttons.uploadSignedDocument.label")
              : globalT("buttons.uploadSignedDocument.loading")}
          </FileUploadButton>
        )}
        {isTypeFile && (
          <FileUploadButton
            isLoading={submitRequiredInformationMutation.isPending}
            onUpload={(file) => handleSubmitRequiredInformation({ file })}
          >
            {submitRequiredInformationMutation.isPending
              ? globalT("buttons.uploadFile.label")
              : globalT("buttons.uploadFile.loading")}
          </FileUploadButton>
        )}
        {isTypeImage && (
          <FileUploadButton
            isLoading={submitRequiredInformationMutation.isPending}
            onUpload={(file) => handleSubmitRequiredInformation({ file })}
            accept={{ "image/*": [".png", ".jpg", ".jpeg"] }}
          >
            {submitRequiredInformationMutation.isPending
              ? globalT("buttons.uploadImage.label")
              : globalT("buttons.uploadImage.loading")}
          </FileUploadButton>
        )}
      </div>
    </div>
  );
}
