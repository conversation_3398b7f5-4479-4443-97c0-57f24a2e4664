"use client";

import { LicenseTabsProvider } from "../../../components/license-tabs/use-license-tabs";
import { RequiredInformationList } from "./required-information-list";
import { LicenseRequiredInformations } from "./license-required-informations";
import { LicenseTabs } from "../../../components/license-tabs";
import { useTranslations } from "next-intl";

export function CountryDocuments() {
  const t = useTranslations("modules.saas.pages.euLicense.requiredInformation");

  return (
    <div className="w-full flex flex-row justify-center">
      <div className="w-full">
        <div className=" w-full rounded-[32px] items-start bg-tonal-cream-96 flex flex-col px-4 py-6 md:py-7 md:px-8">
          <div className="flex justify-between w-full flex-col">
            <p className="text-primary text-lg lg:text-3xl font-medium lg:font-bold mb-2 lg:mb-6">
              {t("countryDocuments.title")}
            </p>
          </div>
          <LicenseTabsProvider pendencyType="REQUIRED_INFORMATIONS">
            <LicenseTabs />
            <RequiredInformationList />
            <LicenseRequiredInformations />
          </LicenseTabsProvider>
        </div>
      </div>
    </div>
  );
}
