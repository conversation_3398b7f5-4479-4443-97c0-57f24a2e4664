"use client";

import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>rror, GridView, Layers, Payments, StarRate } from "@arthursenno/lizenzero-ui-react/Icon";
import { Link } from "@/i18n/navigation";

import { SaasBreadcrumb } from "@/components/modules/saas/components/saas-breadcrumb";
import { SaasContainer } from "@/components/modules/saas/components/saas-container";
import { SaasTitle } from "@/components/modules/saas/components/saas-title";

import { ContractManagementCard } from "../../components/dashboard-cards/contract-management-card";
import { DeadlinesCard } from "./cards/deadlines-card";
import { LicenseInformationCard } from "./cards/license-information-card";
import { RequiredInformationCard } from "./cards/required-information-card";
import { ThirdPartyInvoicesCard } from "./cards/third-party-invoices-card";
import { NextStepsCard } from "../../components/dashboard-cards/next-steps-card";
import { DeclareVolumesCard } from "../../components/dashboard-cards/declare-volumes-card";
import { CountryIcon } from "@/components/_common/country-icon";
import { useCustomer } from "@/hooks/use-customer";
import { LicenseClerkControlStatus } from "@/lib/api/license/types";
import { DashboardBanner } from "../../components/dashboard-banner";
import { ComponentType, useState } from "react";
import { useTranslations } from "next-intl";

const path = [
  { label: "Dashboard EU", href: "#" },
  { label: "My countries", href: "" },
];

const LICENSE_CLERK_CONTROL_STATUS_LABEL: Record<LicenseClerkControlStatus, React.ReactNode> = {
  PENDING: (
    <>
      <Error className="size-6 fill-error" /> <strong className="text-error">Open to-dos</strong>
    </>
  ),
  DONE: (
    <>
      <CheckCircle className="size-6 fill-success" /> <strong className="text-success">License Complete</strong>
    </>
  ),
};

interface EuLicenseMyCountriesProps {
  countryCode: string;
}

enum FurtherInformationSelected {
  ESTIMATED_ANNUAL_LICENSING_FEE = "estimated_annual_licensing_fee",
  THE_PROCESS_IN_DETAIL = "the_process_in_detail",
  ADDITIONAL_INFORMATION = "additional_information",
  PLEASE_NOTE = "please_note",
}

export function EuLicenseMyCountries({ countryCode }: EuLicenseMyCountriesProps) {
  const t = useTranslations("modules.saas.pages.euLicense.myCountries");
  const { customer } = useCustomer();
  const [furtherInformationSelected, setFurtherInformationSelected] = useState<FurtherInformationSelected>(
    FurtherInformationSelected.ESTIMATED_ANNUAL_LICENSING_FEE
  );

  if (!customer) return null;

  const euLicenseContract = customer.contracts.find((c) => c.type === "EU_LICENSE");

  if (!euLicenseContract) return null;

  const missingPowerOfAttorney = !!euLicenseContract?.general_informations.find(
    (information) => information.name === "Power of Attorney" && ["OPEN", "DECLINED"].includes(information.status)
  );

  const contractedLicenses = euLicenseContract.licenses;

  const license = contractedLicenses.find((l) => l.country_code === countryCode);

  if (!license) return null;

  return (
    <>
      {missingPowerOfAttorney && (
        <SaasContainer containerClassName="max-md:pb-0">
          <DashboardBanner />
        </SaasContainer>
      )}
      <SaasBreadcrumb paths={path} />
      <SaasContainer className="pt-8 md:pt-16" containerClassName="bg-tonal-cream-96">
        <div className="flex justify-between items-center mb-12 w-full">
          <SaasTitle>
            <CountryIcon country={{ flag_url: license.country_flag, name: license.country_name }} className="size-12" />
            {license.country_name}
          </SaasTitle>
          <div className="flex items-center gap-3">
            {LICENSE_CLERK_CONTROL_STATUS_LABEL[license.clerk_control_status]}
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-11 gap-6">
          <LicenseInformationCard countryCode={license.country_code} />
          <ContractManagementCard />
          <RequiredInformationCard countryCode={license.country_code} />
          <NextStepsCard licenseId={license.id} />
          <ThirdPartyInvoicesCard countryCode={license.country_code} />
          <DeclareVolumesCard countryCode={license.country_code} />
          <DeadlinesCard countryCode={license.country_code} />
        </div>
      </SaasContainer>
      <SaasContainer className="py-16 space-y-10">
        <SaasTitle>Further informations</SaasTitle>
        <div className="flex flex-wrap gap-5">
          <ButtonFurtherInformation
            selected={furtherInformationSelected === FurtherInformationSelected.ESTIMATED_ANNUAL_LICENSING_FEE}
            onClick={() => setFurtherInformationSelected(FurtherInformationSelected.ESTIMATED_ANNUAL_LICENSING_FEE)}
            Icon={Payments}
            label={t("furtherInformations.estimatedAnnualLicensingFee.title")}
          />
          <ButtonFurtherInformation
            selected={furtherInformationSelected === FurtherInformationSelected.THE_PROCESS_IN_DETAIL}
            onClick={() => setFurtherInformationSelected(FurtherInformationSelected.THE_PROCESS_IN_DETAIL)}
            Icon={Layers}
            label={t("furtherInformations.theProcessInDetail.title")}
          />
          <ButtonFurtherInformation
            selected={furtherInformationSelected === FurtherInformationSelected.ADDITIONAL_INFORMATION}
            onClick={() => setFurtherInformationSelected(FurtherInformationSelected.ADDITIONAL_INFORMATION)}
            Icon={GridView}
            label={t("furtherInformations.additionalInformation.title")}
          />
          <ButtonFurtherInformation
            selected={furtherInformationSelected === FurtherInformationSelected.PLEASE_NOTE}
            onClick={() => setFurtherInformationSelected(FurtherInformationSelected.PLEASE_NOTE)}
            Icon={StarRate}
            label={t("furtherInformations.pleaseNote.title")}
          />
        </div>
        <div className="space-y-10">
          {furtherInformationSelected === FurtherInformationSelected.ESTIMATED_ANNUAL_LICENSING_FEE && (
            <p className="text-tonal-dark-cream-40">
              {t("furtherInformations.estimatedAnnualLicensingFee.licenseFee", { value: "4.991,33 €" })}
            </p>
          )}
          {furtherInformationSelected === FurtherInformationSelected.THE_PROCESS_IN_DETAIL && (
            <p className="text-tonal-dark-cream-40">
              {t("furtherInformations.estimatedAnnualLicensingFee.registrationFee", { value: "5.16" })}
            </p>
          )}
          {furtherInformationSelected === FurtherInformationSelected.ADDITIONAL_INFORMATION && (
            <p className="text-tonal-dark-cream-40">
              {t("furtherInformations.estimatedAnnualLicensingFee.priceDetails")}{" "}
              <Link href="https://www.conai.org/" className="text-support-blue underline underline-offset-2">
                {t("furtherInformations.estimatedAnnualLicensingFee.conaiPriceList")}
              </Link>
            </p>
          )}
          {furtherInformationSelected === FurtherInformationSelected.PLEASE_NOTE && (
            <p className="text-tonal-dark-cream-40">
              {t("furtherInformations.estimatedAnnualLicensingFee.guidePrice")}
            </p>
          )}
        </div>
      </SaasContainer>
    </>
  );
}

const ButtonFurtherInformation = ({
  selected,
  onClick,
  Icon,
  label,
}: {
  selected: boolean;
  onClick: () => void;
  Icon: ComponentType<React.SVGProps<SVGSVGElement>>;
  label: string;
}) => {
  return (
    <button
      onClick={onClick}
      data-selected={selected}
      className="flex items-center gap-2 px-5 pt-[10px] pb-3 rounded-2xl text-primary data-[selected=true]:text-white bg-tonal-dark-blue-90 data-[selected=true]:bg-primary font-bold"
    >
      <Icon data-selected={selected} className="size-6 fill-primary data-[selected=true]:fill-white" />
      {label}
    </button>
  );
};
