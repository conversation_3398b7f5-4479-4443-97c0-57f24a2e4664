"use client";

import { SelectDropdown } from "@/components/_common/select-dropdown";
import { Skeleton } from "@/components/ui/skeleton";
import { useCustomer } from "@/hooks/use-customer";
import { useQueryFilter } from "@/hooks/use-query-filter";
import { downloadCustomerFile } from "@/lib/api/file";
import { UploadedFile } from "@/lib/api/file/types";
import { getThirdPartyInvoices } from "@/lib/api/third-party-invoice";
import { ThirdPartyInvoice } from "@/lib/api/third-party-invoice/types";
import { downloadFile } from "@/utils/download-file";
import { formatCurrency } from "@/utils/formatCurrency";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Download, RequestQuote, SdCardAlert, Task } from "@arthursenno/lizenzero-ui-react/Icon";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import { enqueueSnackbar } from "notistack";
import { useState } from "react";
import { CgSpinnerAlt } from "react-icons/cg";

function formatQueryDate(date: string) {
  return new Date(date).toISOString().split("T")[0];
}

interface ThirdPartyInvoiceListProps {
  licenseId: number;
}

export function ThirdPartyInvoiceList({ licenseId }: ThirdPartyInvoiceListProps) {
  const currentYear = new Date().getFullYear();
  const globalT = useTranslations("modules.saas.global");
  const t = useTranslations("modules.saas.pages.euLicense.thirdPartyInvoices");
  const { customer } = useCustomer();

  const [idFileLoading, setIdFileLoading] = useState<string | null>(null);

  const licenseYears = (() => {
    if (!customer) return [];

    const contracts = customer.contracts;

    const years: number[] = [];

    for (const contract of contracts) {
      if (!!contract.licenses.length) {
        for (const license of contract.licenses) {
          if (!years.includes(license.year)) years.push(license.year);
        }
      }

      if (contract.type === "ACTION_GUIDE") {
        if (!years.includes(currentYear)) years.push(currentYear);
      }
    }

    return years.sort((a, b) => b - a);
  })();

  const { paramValues, changeParam } = useQueryFilter(["license-year", "start-date", "end-date"]);

  const licenseYear = paramValues["license-year"] || licenseYears[0];
  const startDate =
    paramValues["start-date"] ||
    formatQueryDate(new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString());
  const endDate =
    paramValues["end-date"] ||
    formatQueryDate(new Date(new Date().getFullYear(), new Date().getMonth() + 1, 0).toISOString());

  const thirdPartyInvoicesQuery = useQuery<ThirdPartyInvoice[]>({
    queryKey: [
      "third-party-invoices",
      {
        license_id: licenseId,
        from_date: startDate || null,
        to_date: endDate || null,
        license_year: licenseYear || null,
      },
    ],
    queryFn: () =>
      getThirdPartyInvoices({
        license_id: licenseId,
        from_date: endDate ? new Date(startDate).toISOString() : undefined,
        to_date: endDate ? new Date(endDate).toISOString() : undefined,
        license_year: String(licenseYear) || undefined,
      }),
    enabled: !!licenseId,
  });

  const downloadThirdPartyInvoiceMutation = useMutation({
    mutationFn: async (file: UploadedFile) => {
      const result = await downloadCustomerFile(file.id);

      if (!result) {
        throw new Error("Error downloading invoice");
      }

      downloadFile({ buffer: result, fileName: file.original_name });
    },
  });

  function handleDownloadThirdPartyInvoice(file: UploadedFile) {
    setIdFileLoading(file.id);

    downloadThirdPartyInvoiceMutation.mutate(file, {
      onSuccess: () => {
        setIdFileLoading(null);
        enqueueSnackbar("Third party invoice downloaded successfully!", { variant: "success" });
      },
      onError: () => {
        setIdFileLoading(null);
        enqueueSnackbar("An error occurred while downloading the third party invoice.", { variant: "error" });
      },
    });
  }

  const thirdPartyInvoices = thirdPartyInvoicesQuery?.data || [];

  return (
    <div className="w-full bg-surface-02 rounded-4xl md:rounded-[40px] space-y-10">
      <div className="flex flex-col md:flex-row md:items-center gap-4">
        <div className="flex flex-col md:flex-row gap-2">
          <p className="text-tonal-dark-cream-50 text-nowrap">{globalT("filters.invoicePeriod.label")}:</p>
          <div className="flex items-center gap-2">
            <label className="flex">
              <input
                id="start-date-input"
                onBlur={(e) => changeParam("start-date", e.target.value)}
                className="bg-surface-01 text-support-blue font-medium "
                type="date"
                max={endDate}
                defaultValue={startDate}
              />
            </label>
            <span className="text-tonal-dark-cream-50">to</span>
            <div className="flex">
              <input
                id="end-date-input"
                onBlur={(e) => changeParam("end-date", e.target.value)}
                className="bg-surface-01 text-support-blue font-medium "
                type="date"
                min={startDate}
                defaultValue={endDate}
              />
            </div>
          </div>
        </div>
        <span className="hidden md:block text-[#808FA9]">|</span>
        <div className="flex flex-col md:flex-row gap-2">
          <span className="text-tonal-dark-cream-50">License year:</span>
          <SelectDropdown
            options={licenseYears.map((year) => ({
              label: String(year),
              value: String(year),
            }))}
            value={String(licenseYear)}
            onChangeValue={(value) => changeParam("license-year", value)}
          />
        </div>
      </div>
      <div className="bg-white rounded-4xl px-3 py-4 p-6">
        {!thirdPartyInvoicesQuery.isLoading && !thirdPartyInvoices.length && (
          <p className="text-tonal-dark-cream-50 text-center py-6">No invoices found</p>
        )}
        {!thirdPartyInvoicesQuery.isLoading &&
          !!thirdPartyInvoices.length &&
          thirdPartyInvoices.map((thirdPartyInvoice) => {
            const file = thirdPartyInvoice?.license?.files?.find(
              (file) => file.original_name === thirdPartyInvoice.title
            );

            if (!file) return;

            return (
              <div
                key={thirdPartyInvoice.id}
                className="flex items-center gap-2 max-sm:gap-y-5 md:gap-4 px-0 pt-4 md:p-5 flex-wrap"
              >
                {thirdPartyInvoice.status === "PAID" && <Task className="size-7 fill-success" />}
                {thirdPartyInvoice.status === "OPEN" && <RequestQuote className="size-7 fill-error" />}
                {thirdPartyInvoice.status === "CANCELLED" && (
                  <SdCardAlert className="size-7 fill-tonal-dark-cream-60" />
                )}
                <div className="space-y-1">
                  <p className="text-primary text-sm md:text-base">{thirdPartyInvoice.title}</p>
                  <p className="text-xs font-semibold text-tonal-dark-cream-10">
                    {t("issueDate")}
                    <span className="text-tonal-dark-cream-40 ml-1 font-normal">
                      {new Date(thirdPartyInvoice.created_at).toLocaleDateString()}
                    </span>
                  </p>
                </div>
                <div className="px-2md:px-4 py-3 text-tonal-dark-cream-30 w-20 md:w-32 max-sm:ml-auto md:mx-auto text-right">
                  {formatCurrency(thirdPartyInvoice.price)}
                </div>
                <div className="flex sm:ml-auto max-sm:flex-1 items-center justify-between gap-4">
                  <div>
                    <Button
                      variant="text"
                      color="light-blue"
                      size="iconSmall"
                      disabled={downloadThirdPartyInvoiceMutation.isPending}
                      leadingIcon={
                        downloadThirdPartyInvoiceMutation.isPending && file.id === idFileLoading ? (
                          <CgSpinnerAlt className="animate-spin" />
                        ) : (
                          <Download />
                        )
                      }
                      onClick={() => handleDownloadThirdPartyInvoice(file)}
                    />
                  </div>
                </div>
              </div>
            );
          })}
        {thirdPartyInvoicesQuery.isLoading && (
          <div className="space-y-3">
            {Array.from({ length: thirdPartyInvoices.length || 6 }).map((_, index) => (
              <Skeleton key={index} className="h-12 rounded-2xl w-full" />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
