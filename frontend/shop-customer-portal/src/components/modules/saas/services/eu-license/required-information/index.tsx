"use client";

import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";

import { SaasBreadcrumb } from "@/components/modules/saas/components/saas-breadcrumb";
import { SaasContainer } from "@/components/modules/saas/components/saas-container";
import { CountryDocuments } from "./country-documents";
import { GeneralInformations } from "./general-informations";
import { useTranslations } from "next-intl";

const path = [
  { label: "Dashboard EU", href: "/saas/eu-license" },
  { label: "Required Information", href: "#" },
];

export function EuLicenseRequiredInformation() {
  const t = useTranslations("modules.saas.pages.euLicense.requiredInformation");

  return (
    <>
      <SaasBreadcrumb paths={path} />
      <SaasContainer>
        <TitleAndSubTitle showIcon title={t("title")} subText={t("description")} />
        <div className="space-y-10">
          <GeneralInformations />
          <CountryDocuments />
        </div>
      </SaasContainer>
    </>
  );
}
