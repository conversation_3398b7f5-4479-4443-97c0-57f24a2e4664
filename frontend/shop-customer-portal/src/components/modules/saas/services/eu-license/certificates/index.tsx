"use client";

import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";

import { SaasBreadcrumb } from "@/components/modules/saas/components/saas-breadcrumb";
import { SaasContainer } from "@/components/modules/saas/components/saas-container";
import { LicenseTabsProvider } from "../../components/license-tabs/use-license-tabs";
import { LicenseTabs } from "@/components/modules/saas/services/components/license-tabs";
import { CertificateList } from "./certificate-list";
import { LicenseCertificates } from "./license-certificates";
import { useTranslations } from "next-intl";

const path = [
  { label: "Dashboard EU", href: "/saas/eu-license" },
  { label: "Certificates", href: "#" },
];

export function EuLicenseCertificates() {
  const t = useTranslations("modules.saas.pages.euLicense.certificates");

  return (
    <>
      <SaasBreadcrumb paths={path} />
      <SaasContainer>
        <TitleAndSubTitle showIcon title={t("title")} subText={t("description")} />
        <div className="w-full h-[1px] bg-tonal-dark-cream-80 rounded-full mb-8"></div>
        <LicenseTabsProvider>
          <LicenseTabs />
          <div className="flex flex-col gap-4 md:gap-6 w-full mt-4 lg:mt-16">
            <CertificateList />
            <LicenseCertificates />
          </div>
        </LicenseTabsProvider>
      </SaasContainer>
    </>
  );
}
