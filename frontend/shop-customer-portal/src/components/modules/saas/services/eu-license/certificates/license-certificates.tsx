import { CountryIcon } from "@/components/_common/country-icon";
import { Divider } from "@/components/_common/divider";
import {
  QuestionTooltip,
  QuestionTooltipDescription,
  QuestionTooltipTitle,
} from "@/components/_common/question-tooltip";
import { Skeleton } from "@/components/ui/skeleton";
import { downloadCustomerFile } from "@/lib/api/file";
import { UploadedFile } from "@/lib/api/file/types";
import { useCustomer } from "@/hooks/use-customer";
import { downloadFile } from "@/utils/download-file";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Download, Pdf } from "@arthursenno/lizenzero-ui-react/Icon";
import { useMutation } from "@tanstack/react-query";
import { enqueueSnackbar } from "notistack";
import { CgSpinnerAlt } from "react-icons/cg";

import { useLicenseTabs } from "../../components/license-tabs/use-license-tabs";
import { useTranslations } from "next-intl";

export function LicenseCertificates() {
  const { selectedLicense } = useLicenseTabs();
  const { customer } = useCustomer();
  const globalT = useTranslations("modules.saas.global");
  const t = useTranslations("modules.saas.pages.euLicense.certificates");

  const certificates =
    selectedLicense?.files.filter((file) => ["LICENSE_PROOF_OF_REGISTRATION"].includes(file.type)) || [];

  const downloadCertificateMutation = useMutation({
    mutationFn: async (file: UploadedFile) => {
      if (!file || !customer) throw new Error();

      const downloadedFile = await downloadCustomerFile(file.id);

      downloadFile({ buffer: downloadedFile, fileName: file.original_name });
    },
  });

  if (!selectedLicense) return null;

  async function handleDownloadCertificate(file: UploadedFile) {
    downloadCertificateMutation.mutate(file, {
      onSuccess: () => {
        enqueueSnackbar("File downloaded successfully", { variant: "success" });
      },
      onError: () => {
        enqueueSnackbar("Error downloading file. Please try again.", { variant: "error" });
      },
    });
  }

  return (
    <div className="flex flex-col gap-4 md:gap-6 w-full">
      <div className="text-primary w-full rounded-4xl items-start bg-tonal-cream-96 flex flex-col py-5 md:py-7 px-4 md:px-8 ">
        <div className="flex justify-between w-full ">
          <div className="text-primary font-medium text-2xl flex flex-row justify-center items-center gap-2 md:gap-4">
            <CountryIcon
              country={{ flag_url: selectedLicense?.country_flag, name: selectedLicense?.country_name }}
              className="size-6 md:size-8"
            />
            <span className="text-base/none md:text-2xl/none font-bold">{selectedLicense.country_name}</span>
          </div>
        </div>
        <Divider />
        <div className="space-y-4 w-full">
          {!selectedLicense &&
            Array.from({ length: 3 }).map((_, index) => (
              <div
                key={index}
                className="flex justify-between items-center w-full border-b border-tonal-dark-cream-80 pb-3"
              >
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-3">
                    <Skeleton className="size-8 md:size-10 rounded-lg" />
                    <Skeleton className="h-6 w-44" />
                  </div>
                </div>
              </div>
            ))}
          {selectedLicense &&
            (certificates || []).map((certificateFile) => (
              <div
                key={certificateFile.id}
                className="flex justify-between items-center w-full border-b border-tonal-dark-cream-80 pb-3"
              >
                <div className="flex items-center gap-2">
                  <div className="flex items-center gap-2">
                    <Pdf className="size-12 fill-tonal-red-70" />
                    <p className="text-primary text-paragraph-regular font-bold line-clamp-1">
                      {certificateFile.original_name}
                    </p>
                  </div>
                  <QuestionTooltip>
                    <QuestionTooltipTitle>Participation Certificate</QuestionTooltipTitle>
                    <QuestionTooltipDescription>Lorem ipsum dolor sit amet consectetur.</QuestionTooltipDescription>
                  </QuestionTooltip>
                </div>
                <div className="flex items-center">
                  <Button
                    size="small"
                    variant="text"
                    color="dark-blue"
                    disabled={
                      downloadCertificateMutation.variables?.id === certificateFile.id &&
                      downloadCertificateMutation.isPending
                    }
                    leadingIcon={
                      downloadCertificateMutation.variables?.id === certificateFile.id &&
                      downloadCertificateMutation.isPending ? (
                        <CgSpinnerAlt className="animate-spin" />
                      ) : (
                        <Download />
                      )
                    }
                    onClick={() => handleDownloadCertificate(certificateFile)}
                  >
                    {downloadCertificateMutation.variables?.id === certificateFile.id &&
                    downloadCertificateMutation.isPending
                      ? globalT("buttons.download.loading")
                      : globalT("buttons.download.label")}
                  </Button>
                </div>
              </div>
            ))}
        </div>
      </div>
    </div>
  );
}
