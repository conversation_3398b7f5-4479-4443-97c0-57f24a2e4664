import { CountryIcon } from "@/components/_common/country-icon";
import { Skeleton } from "@/components/ui/skeleton";
import { StatusBadge } from "@/components/ui/status-badge";
import { KeyboardArrowRight } from "@arthursenno/lizenzero-ui-react/Icon";
import { useLicenseTabs } from "../../components/license-tabs/use-license-tabs";

export function ThirdPartyInvoicesList() {
  const { licenses, selectCountry, selectedLicense, isLoading } = useLicenseTabs();

  if (selectedLicense) return null;

  if (isLoading)
    return (
      <div className="flex flex-col gap-4 md:gap-6 w-full">
        {Array.from({ length: 3 }).map((_, index) => (
          <div
            key={index}
            className="w-full rounded-4xl items-start bg-white border border-surface-03 flex flex-col py-5 md:py-7 px-4 md:px-8"
          >
            <div className="flex justify-between w-full">
              <div className="text-primary font-medium text-2xl flex flex-row justify-center items-center gap-2 md:gap-4">
                <Skeleton className="size-6 md:size-8 rounded-full" />
                <Skeleton className="w-32 h-4" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );

  return (
    <div className="flex flex-col gap-4 md:gap-6 w-full">
      {licenses?.map((license) => {
        return (
          <div
            key={license.id}
            className="text-primary w-full rounded-4xl items-start bg-white border border-surface-03 flex flex-col py-5 md:py-7 px-4 md:px-8 cursor-pointer"
            onClick={() => selectCountry(license.country_code)}
          >
            <div className="flex justify-between w-full">
              <div className="text-primary font-medium text-2xl flex flex-row justify-center items-center gap-2 md:gap-4">
                <CountryIcon
                  country={{ flag_url: license.country_flag, name: license.country_name }}
                  className="size-6 md:size-8"
                />
                <span className="text-base md:text-2xl font-bold">{license.country_name}</span>
              </div>
              <div className="flex flex-row items-center flex-1 justify-end gap-2 md:gap-4">
                {!!license.pendencies.find((pendency) => pendency.type === "INVOICES") && (
                  <StatusBadge variant="error" label="Open invoice" />
                )}
                <KeyboardArrowRight className="fill-primary size-6 md:size-8" />
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
}
