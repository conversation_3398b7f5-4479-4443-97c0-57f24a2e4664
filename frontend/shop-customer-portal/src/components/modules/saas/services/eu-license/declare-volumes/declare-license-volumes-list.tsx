import { CountryIcon } from "@/components/_common/country-icon";
import { StatusBadge } from "@/components/ui/status-badge";
import { KeyboardArrowRight } from "@arthursenno/lizenzero-ui-react/Icon";
import { useLicenseTabs } from "../../components/license-tabs/use-license-tabs";
import { FREQUENCY_LABELS } from "./declare-license-volumes-form";

export function DeclareLicenseVolumesList() {
  const { licenses, selectCountry, selectedLicense } = useLicenseTabs();

  if (!!selectedLicense) return null;

  return (
    <div className="flex flex-col gap-4 md:gap-6 w-full mt-4 lg:mt-16">
      {licenses.map((license) => {
        const packagingService = license.packaging_services[0];

        if (!packagingService) return null;

        const rhythm = packagingService?.report_set_frequency?.rhythm || "Annually";

        return (
          <div
            key={license.country_code}
            className="text-primary w-full rounded-4xl items-start bg-white border border-surface-03 flex flex-col py-5 md:py-7 px-4 md:px-8 cursor-pointer"
            onClick={() => selectCountry(license.country_code)}
          >
            <div className="flex justify-between w-full ">
              <div className="text-primary font-medium text-2xl flex justify-center items-center gap-2 md:gap-4">
                <CountryIcon
                  country={{
                    name: license.country_name,
                    flag_url: license.country_flag,
                  }}
                  className="size-6 md:size-8"
                />
                <span className="text-base/none md:text-2xl/none font-bold">{license.country_name}</span>
                <span className="font-light text-tonal-dark-cream-50 text-sm hidden md:block mt-1">
                  Report rhythm: <b>{FREQUENCY_LABELS["en"][rhythm]}</b>
                </span>
              </div>
              <div className="flex gap-2 md:gap-4 items-center md:flex-initial">
                <div className="flex flex-1 md:flex-initial">
                  {license._status !== "DONE" ? (
                    <StatusBadge variant="info" label="Volume Report Open" />
                  ) : (
                    <StatusBadge variant="success" label="Volume Report Complete" />
                  )}
                </div>
                <KeyboardArrowRight className="fill-primary size-6 md:size-8" />
              </div>
            </div>
            {/* <div className="hidden md:block mt-4">
              <p className="text-xs font-medium text-on-surface-01">Report rhythm: {FREQUENCY_LABELS["en"][rhythm]}</p>
              <div className="mt-1 flex flex-row gap-3">
                <p className="text-xs font-medium text-on-surface-01">
                  Next Step: <span className="underline">Declare Volumes</span>
                </p>
              </div>
            </div> */}
          </div>
        );
      })}
    </div>
  );
}
