"use client";

import { Divider } from "@/components/_common/divider";
import { TitleAndSubTitle } from "@/components/_common/titleAndSubTitle";
import { SaasBreadcrumb } from "@/components/modules/saas/components/saas-breadcrumb";
import { SaasContainer } from "@/components/modules/saas/components/saas-container";
import { LicenseTabs } from "../../components/license-tabs";
import { LicenseTabsProvider } from "../../components/license-tabs/use-license-tabs";
import { DeclareLicenseVolumes } from "./declare-license-volumes";
import { DeclareLicenseVolumesList } from "./declare-license-volumes-list";
import { useTranslations } from "next-intl";

const path = [
  { label: "Dashboard EU", href: "/" },
  { label: "Declare Volumes", href: "saas/eu-license/declare-volumes" },
];

export function EuLicenseDeclareVolumes() {
  const t = useTranslations("modules.saas.pages.euLicense.declareVolumes");

  return (
    <>
      <SaasBreadcrumb paths={path} />
      <SaasContainer className="pb-24">
        <TitleAndSubTitle showIcon title={t("title")} subText={t("description")} />
        <Divider initialMarginDisabled className="mb-8" />
        <LicenseTabsProvider pendencyType="VOLUME_REPORTS">
          <LicenseTabs />
          <DeclareLicenseVolumes />
          <DeclareLicenseVolumesList />
        </LicenseTabsProvider>
      </SaasContainer>
    </>
  );
}
