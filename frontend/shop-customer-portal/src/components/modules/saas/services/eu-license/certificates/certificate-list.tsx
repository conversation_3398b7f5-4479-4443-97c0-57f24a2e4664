import { CountryIcon } from "@/components/_common/country-icon";
import { useLicenseTabs } from "../../components/license-tabs/use-license-tabs";
import { Download, KeyboardArrowRight } from "@arthursenno/lizenzero-ui-react/Icon";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Skeleton } from "@/components/ui/skeleton";
import { useTranslations } from "next-intl";

export function CertificateList() {
  const { licenses, selectCountry, selectedLicense, isLoading } = useLicenseTabs();
  const globalT = useTranslations("modules.saas.global");
  const t = useTranslations("modules.saas.pages.euLicense.certificates");

  if (selectedLicense) return null;

  return (
    <div className="flex flex-col gap-4 md:gap-6 w-full">
      <p className="text-primary font-sm text-darkcream-30 flex items-center gap-4">
        {t("downloadAllCertificates")}
        <Button size="small" variant="text" color="dark-blue" leadingIcon={<Download />}>
          {globalT("buttons.downloadAll.label")}
        </Button>
      </p>
      {isLoading &&
        Array.from({ length: 3 }).map((_, index) => (
          <div
            key={index}
            className="flex items-center w-full rounded-4xl bg-white border border-surface-03 py-5 md:py-7 px-4 md:px-8 gap-2 md:gap-4"
          >
            <Skeleton className="rounded-full size-6 md:size-8 flex-none" />
            <Skeleton className="w-24 h-6 flex-none" />
          </div>
        ))}
      {!isLoading &&
        licenses?.map((license) => (
          <div
            key={license.id}
            className="text-primary w-full rounded-4xl items-start bg-white border border-surface-03 flex flex-col py-5 md:py-7 px-4 md:px-8 cursor-pointer"
            onClick={() => selectCountry(license.country_code)}
          >
            <div className="flex justify-between w-full ">
              <div className="text-primary font-medium text-2xl flex flex-row justify-center items-center gap-2 md:gap-4">
                <CountryIcon
                  country={{ flag_url: license.country_flag, name: license.country_name }}
                  className="size-6 md:size-8"
                />
                <span className="text-base/none md:text-2xl/none font-bold">{license.country_name}</span>
                <span className="font-light text-tonal-dark-cream-50 text-sm hidden md:block mt-1">
                  {t("downloadCertificatePdf")}
                </span>
              </div>
              <div className="flex flex-row items-center md:flex-initial gap-2 md:gap-4">
                <KeyboardArrowRight className="fill-primary size-6 md:size-8" />
              </div>
            </div>
          </div>
        ))}
    </div>
  );
}
