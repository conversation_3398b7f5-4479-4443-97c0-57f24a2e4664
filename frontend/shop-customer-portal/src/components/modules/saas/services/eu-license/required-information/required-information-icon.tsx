import Image, { type ImageProps } from "next/image";

import type { RequiredInformationType } from "@/lib/api/required-informations/types";

function getImageSrcByType(type: RequiredInformationType) {
  const basePath = "/assets/images";

  switch (type) {
    case "FILE":
    case "DOCUMENT":
      return `${basePath}/logo_pdf.png`;
    case "IMAGE":
      return `${basePath}/logo_png.png`;
    case "NUMBER":
      return `${basePath}/hashtag.png`;
    case "TEXT":
      return `${basePath}/text_input.png`;
    default:
      return "";
  }
}

interface RequiredInformationIconProps extends Omit<ImageProps, "src" | "alt"> {
  requiredInformationType: RequiredInformationType;
}

export function RequiredInformationIcon(props: RequiredInformationIconProps) {
  const { requiredInformationType: type, ...restProps } = props;

  const imageSrc = getImageSrcByType(type);

  return <Image alt="Information icon" src={imageSrc} className="w-10 h-10" width={40} height={40} {...restProps} />;
}
