import { CountryIcon } from "@/components/_common/country-icon";
import { Skeleton } from "@/components/ui/skeleton";
import { StatusBadge } from "@/components/ui/status-badge";
import { KeyboardArrowRight } from "@arthursenno/lizenzero-ui-react/Icon";
import { useLicenseTabs } from "../../../components/license-tabs/use-license-tabs";

export function RequiredInformationList() {
  const { licenses, selectedLicense, selectCountry } = useLicenseTabs();

  if (selectedLicense) return null;

  return (
    <ul className="flex flex-col md:mt-6 w-full">
      {!!licenses.length &&
        licenses.map((license) => (
          <li
            key={license.id}
            className="text-primary w-full rounded-[32px] items-start bg-white flex flex-col py-5 md:py-7 px-4 md:px-8 mt-4 cursor-pointer"
            aria-label="Select license"
            onClick={() => selectCountry(license.country_code)}
          >
            <div className="flex items-center justify-start md:justify-between w-full gap-4">
              <div>
                <div className="flex items-center gap-4">
                  <div className="text-primary font-medium text-2xl flex flex-row items-center gap-2 md:gap-4">
                    <CountryIcon
                      country={{ flag_url: license.country_flag, name: license.country_name }}
                      className="size-6 md:size-8"
                    />
                    <span className="text-base font-bold text-primary">{license.country_name}</span>
                  </div>
                  {/* TODO: Validar isso depois */}
                  {/* <p className={`text-sm text-on-surface-01 block md:hidden`}>{!license.done && "File name"}</p> */}
                  {/* <p className={`text-sm text-on-surface-01 hidden md:block`}>{!license.done ? "-" : "File name"}</p> */}
                </div>
              </div>
              <div className="flex flex-row items-center justify-end flex-1">
                {license._status === "DONE" && <StatusBadge variant="success" label="Done" />}
                {license._status === "OPEN_TO_DOS" && <StatusBadge variant="error" label="Information Required" />}
                <KeyboardArrowRight className="size-6 md:size-8 fill-primary transform ml-3" />
              </div>
            </div>
          </li>
        ))}
      {!licenses.length &&
        Array.from({ length: 3 }).map((_, index) => (
          <li
            key={index}
            className="text-primary w-full rounded-[32px] items-start bg-white flex flex-col py-5 md:py-7 px-4 md:px-8 mt-4"
          >
            <div className="flex items-center gap-4 w-full">
              <Skeleton className="size-9 flex-none" />
              <Skeleton className="h-7 w-full" />
            </div>
          </li>
        ))}
    </ul>
  );
}
