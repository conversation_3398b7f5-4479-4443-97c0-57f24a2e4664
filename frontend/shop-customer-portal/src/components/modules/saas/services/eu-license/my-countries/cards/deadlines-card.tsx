"use client";

import Image from "next/image";
import { useTranslations } from "next-intl";

interface DeadlinesCardProps {
  countryCode: string;
}

export function DeadlinesCard({}: DeadlinesCardProps) {
  const t = useTranslations("modules.saas.pages.euLicense.myCountries");

  return (
    <div id="deadlines" className="col-span-1 md:col-span-5 px-5 py-6 md:p-8 bg-background rounded-3xl">
      <h2 className="text-title-3 text-primary font-bold mb-10">{t("deadlines.title")}</h2>
      <div className="space-y-3">
        <div className="flex gap-2 py-2">
          <Image
            src="/assets/icons/icons_pig.svg"
            width={20}
            height={20}
            className="w-5 h-5 mt-1 flex-none"
            alt="Deadline icon"
          />
          <div className="flex-1 space-y-2">
            <p className="text-primary font-bold">Up to EUR 100 in the normal procedure:</p>
            <p className="text-tonal-dark-cream-30">
              <b>Single declaration</b> required
            </p>
          </div>
        </div>
        <div className="flex gap-2 py-2">
          <Image
            src="/assets/icons/icons_pig.svg"
            width={20}
            height={20}
            className="w-5 h-5 mt-1 flex-none"
            alt="Deadline icon"
          />
          <div className="flex-1 space-y-2">
            <p className="text-primary font-bold">Up to EUR 200 in the simplified procedure:</p>
            <p className="text-tonal-dark-cream-30">
              <b>Single declaration</b> required
            </p>
          </div>
        </div>
        <div className="flex gap-2 py-2">
          <Image
            src="/assets/icons/icons_pig.svg"
            width={20}
            height={20}
            className="w-5 h-5 mt-1 flex-none"
            alt="Deadline icon"
          />
          <div className="flex-1 space-y-2">
            <p className="text-primary font-bold">Up to EUR 2,000: </p>
            <p className="text-tonal-dark-cream-30">
              <b>Annual quantity declaration</b> by 20 January in the following year
            </p>
          </div>
        </div>
        <div className="flex gap-2 py-2">
          <Image
            src="/assets/icons/icons_pig.svg"
            width={20}
            height={20}
            className="w-5 h-5 mt-1 flex-none"
            alt="Deadline icon"
          />
          <div className="flex-1 space-y-2">
            <p className="text-primary font-bold">Up to EUR 31,000:</p>
            <p className="text-tonal-dark-cream-30">
              <b>Quarterly quantity declaration</b> by the 20th of the following month (e.g. 20 April)
            </p>
          </div>
        </div>
        <div className="flex gap-2 py-2">
          <Image
            src="/assets/icons/icons_pig.svg"
            width={20}
            height={20}
            className="w-5 h-5 mt-1 flex-none"
            alt="Deadline icon"
          />
          <div className="flex-1 space-y-2">
            <p className="text-primary font-bold">From EUR 31,000:</p>
            <p className="text-tonal-dark-cream-30">
              <b>Monthly quantity declaration</b> by the 20th of the following month
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
