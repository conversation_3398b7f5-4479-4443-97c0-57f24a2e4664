"use client";

import { useQueryFilter } from "@/hooks/use-query-filter";
import { Link, usePathname } from "@/i18n/navigation";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { useTranslations } from "next-intl";
import { AuthTitle } from "../components/auth-title";

export function SuccessRecoverPassword() {
  const pathname = usePathname();

  const { paramValues } = useQueryFilter(["email"]);
  const email = paramValues.email;

  const loginUrl = pathname.includes("partner-hub") ? "/partner-hub/auth/login" : "/auth/login";

  const t = useTranslations("modules.auth.pages.recoverPassword.confirmation");

  return (
    <>
      <AuthTitle title={t("title")} />
      <div className="text-primary flex items-center gap-2 mt-4">
        <p>{t("subtitle")}</p>
        <p className="font-bold">{email}</p>
      </div>
      <div className="flex justify-center mt-10 w-full">
        <Link href={loginUrl} className="w-full">
          <Button className="w-full" size="medium" variant="filled" color="dark-blue">
            {t("button")}
          </Button>
        </Link>
      </div>
    </>
  );
}
