"use client";

import { UserTypes } from "@/utils/user";
import Image from "next/image";
import { usePathname } from "@/i18n/navigation";

export function AuthLayoutImage() {
  const pathname = usePathname();

  const role = pathname.includes("partner-hub") ? UserTypes.PARTNER : UserTypes.CUSTOMER;

  const backgroundImage =
    role === UserTypes.PARTNER ? "/assets/images/partner_hub_login_image.png" : "/assets/images/Garbage.png";

  return (
    <Image
      width={4000}
      height={1000}
      src={backgroundImage}
      alt="Auth background image"
      className="w-full h-full object-cover"
    />
  );
}
