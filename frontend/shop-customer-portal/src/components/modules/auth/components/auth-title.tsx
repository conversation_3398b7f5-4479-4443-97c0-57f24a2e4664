import { cn } from "@/lib/utils";

interface AuthTitleProps {
  title: string;
  subtitle?: string;
  className?: string;
}

export function AuthTitle({ title, subtitle, className }: AuthTitleProps) {
  return (
    <div className={cn("flex flex-col gap-4 text-primary mb-10 text-left", className)}>
      <h1 className="text-balance text-[40px] font-bold">{title}</h1>
      {subtitle && <p>{subtitle}</p>}
    </div>
  );
}
