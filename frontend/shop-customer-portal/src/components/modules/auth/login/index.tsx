"use client";

import { LoginSchema } from "@/components/_common/forms/schemas";
import { PasswordInput } from "@/components/ui/password-input";
import { Link, usePathname, useRouter } from "@/i18n/navigation";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { Check, Error } from "@arthursenno/lizenzero-ui-react/Icon";
import { Input } from "@arthursenno/lizenzero-ui-react/Input";
import { zodResolver } from "@hookform/resolvers/zod";
import { signIn } from "next-auth/react";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { UserTypes } from "@/utils/user";
import { AuthTitle } from "../components/auth-title";
import { useQueryFilter } from "@/hooks/use-query-filter";

interface LoginFormData {
  email: string;
  password: string;
}

export function LoginPage() {
  const [loading, setLoading] = useState(false);
  const pathname = usePathname();
  const router = useRouter();
  const role = pathname.includes("partner-hub") ? UserTypes.PARTNER : UserTypes.CUSTOMER;

  const { paramValues } = useQueryFilter(["redirect", "email"]);

  const redirect = paramValues.redirect;

  const {
    register,
    handleSubmit,
    setError,
    getValues,
    formState: { errors, isValid },
  } = useForm<LoginFormData>({
    resolver: zodResolver(LoginSchema),
    mode: "onBlur",
    defaultValues: {
      email: paramValues.email || undefined,
    },
  });

  const t = useTranslations("modules.auth.pages.login");

  async function submit(data: LoginFormData) {
    setLoading(true);

    try {
      const response = await signIn("credentials", {
        email: data.email,
        password: data.password,
        redirect: !!redirect,
        intent: role,
        callbackUrl: redirect || undefined,
      });

      if (response?.error === "User not found") {
        setLoading(false);
        setError("email", { message: "Email not found" });
        return;
      }

      if (response?.error) {
        setLoading(false);
        setError("email", { message: t("form.email.error") });
        setError("password", { message: t("form.password.error") });
        return;
      }

      if (role === UserTypes.PARTNER) router.push("/partner-hub");

      if (role === UserTypes.CUSTOMER) router.push("/");
    } catch (error) {
      setLoading(false);
    }
  }

  const forgotPasswordUrl = role === UserTypes.CUSTOMER ? "/auth/forgot-password" : "/partner-hub/auth/forgot-password";

  return (
    <>
      <AuthTitle title={t("title")} subtitle={t("subtitle")} />
      <form className="w-full" onSubmit={handleSubmit(submit)}>
        <div className="flex -mx-3">
          <div className="w-full px-3 mb-5">
            <div className="flex">
              <Input
                label={t("form.email.label")}
                placeholder={t("form.email.placeholder")}
                type="email"
                variant={errors.email ? "error" : "enabled"}
                {...register("email", { required: true })}
                rightIcon={
                  errors.email ? (
                    <Error className="fill-error size-5" />
                  ) : LoginSchema.shape.email.safeParse(getValues("email")).success ? (
                    getValues("email") && <Check className="fill-tonal-green-40 size-5" />
                  ) : null
                }
                errorMessage={errors.email && errors.email.message}
              />
            </div>
          </div>
        </div>
        <div className="flex -mx-3">
          <div className="w-full px-3 mb-12">
            <div className="flex">
              <PasswordInput
                label={t("form.password.label")}
                placeholder={t("form.password.placeholder")}
                {...register("password", { required: true })}
                variant={errors.password ? "error" : "enabled"}
                errorMessage={errors.password && errors.password.message}
                errorIcon={errors.password && <Error className="fill-error size-5" />}
              />
            </div>
            <div className="mt-2">
              <Link href={forgotPasswordUrl} className="inline-block">
                <Button type="button" variant="text" size="small" color="light-blue">
                  {t("forgotPassword")}
                </Button>
              </Link>
            </div>
          </div>
        </div>
        <div className="flex -mx-3">
          <div className="w-full px-3 mb-3">
            <Button variant="filled" size="medium" color="yellow" disabled={!isValid || loading} className="w-full">
              {loading ? t("form.submitButton.loading") : t("form.submitButton.label")}
            </Button>
            <div className="mt-4 text-center w-full flex items-center justify-center">
              {role === UserTypes.CUSTOMER && (
                <div className="text-sm md:text-base text-primary flex items-center gap-1">
                  {t("customer.dontHaveAnAccount")}
                  <Link
                    href="/eu/quick-journey/license/calculator"
                    className="font-bold hover:underline underline-offset-2"
                  >
                    {t("customer.purchaseAccess")}
                  </Link>
                </div>
              )}
              {role === UserTypes.PARTNER && (
                <div className="text-sm md:text-base text-primary flex items-center gap-1">
                  {t("partner.notAPartnerYet")}
                  <a href="mailto:<EMAIL>" className="font-bold hover:underline underline-offset-2">
                    {t("partner.contactUs")}
                  </a>
                </div>
              )}
            </div>
          </div>
        </div>
      </form>
    </>
  );
}
