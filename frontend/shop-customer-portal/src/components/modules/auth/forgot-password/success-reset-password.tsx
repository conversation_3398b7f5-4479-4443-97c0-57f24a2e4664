"use client";
import { <PERSON> } from "@/i18n/navigation";
import { Button } from "@arthursenno/lizenzero-ui-react/Button";
import { useTranslations } from "next-intl";
import { usePathname } from "@/i18n/navigation";
import { AuthTitle } from "../components/auth-title";

export function SuccessResetPassword() {
  const pathname = usePathname();

  const t = useTranslations("modules.auth.pages.forgotPassword.confirmation");
  const loginUrl = pathname.includes("partner-hub") ? "/partner-hub/auth/login" : "/auth/login";

  return (
    <>
      <AuthTitle title={t("title")} subtitle={t("subtitle")} />
      <div className="flex justify-center mt-10 w-full">
        <Link href={loginUrl} className="w-full">
          <Button className="w-full" size="medium" variant="filled" color="dark-blue">
            {t("button")}
          </Button>
        </Link>
      </div>
    </>
  );
}
