export class API {
  baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  async get<T = any>(path: string, params?: Record<string, any>): Promise<T> {
    const url = new URL(`${this.baseURL}${path}`);

    if (params) Object.keys(params).forEach((key) => url.searchParams.append(key, params[key]));

    const response = await fetch(url.toString(), { cache: "no-cache" });

    const data = await response.json();

    return data;
  }

  async post<T = any>(path: string, data: any): Promise<T> {
    const url = `${this.baseURL}${path}`;

    const response = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    return await response.json();
  }

  async put<T = any>(path: string, data: any): Promise<T> {
    const url = `${this.baseURL}${path}`;

    const response = await fetch(url, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    return await response.json();
  }

  async delete<T = any>(path: string): Promise<T> {
    const url = `${this.baseURL}${path}`;

    const response = await fetch(url, {
      method: "DELETE",
    });

    return await response.json();
  }
}
