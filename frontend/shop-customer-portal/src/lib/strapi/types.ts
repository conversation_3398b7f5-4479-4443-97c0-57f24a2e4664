import { type BlocksContent } from "@strapi/blocks-react-renderer";

type StrapiActionGuideAttributes<T = Record<string, unknown>> = {
  countryCode: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string;
  modules: any[];
  generalInformation: StrapiGeneralInformation[];
} & T;

export type StrapiActionGuide<T = Record<string, unknown>> = {
  id: number;
  attributes: StrapiActionGuideAttributes;
} & T;

export type StrapiGeneralInformation<T = Record<string, unknown>> = {
  id: number;
  label: string;
  icon: string;
  content: BlocksContent;
} & T;
