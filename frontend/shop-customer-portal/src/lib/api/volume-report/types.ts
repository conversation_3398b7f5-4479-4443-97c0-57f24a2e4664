import { ReportSetColumn, ReportSetFraction } from "../commitment/types";
import { VolumeReportItem } from "../volume-report-item/types";

export interface VolumeReport {
  id: number;
  license_packaging_service_id: number;
  status: LicenseVolumeReportStatus;
  year: number;
  interval: string;
  report_table: {
    fractions: ReportSetFraction[];
    columns: ReportSetColumn[];
  };
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  volume_report_items: VolumeReportItem[];
}

export type LicenseVolumeReportStatus = "OPEN" | "DONE" | "DECLINED" | "APPROVED";
