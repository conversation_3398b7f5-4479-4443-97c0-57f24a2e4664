import { api } from "..";
import { ApiEndpoints } from "../endpoints";
import { ThirdPartyInvoice } from "./types";

export interface GetThirdPartyInvoicesParams {
  license_id: number;
  from_date?: string;
  to_date?: string;
  license_year?: string;
}

export async function getThirdPartyInvoices(params: GetThirdPartyInvoicesParams) {
  try {
    const res = await api.get<ThirdPartyInvoice[]>(ApiEndpoints.thirdPartyInvoice.getAll, {
      params,
    });

    return res.data;
  } catch (error: any) {
    return error;
  }
}

export async function getThirdPartyInvoice(thirdPartyInvoiceId: number) {
  try {
    const res = await api.get<ThirdPartyInvoice>(ApiEndpoints.thirdPartyInvoice.findById(thirdPartyInvoiceId));

    return res.data;
  } catch (error: any) {
    return error;
  }
}
