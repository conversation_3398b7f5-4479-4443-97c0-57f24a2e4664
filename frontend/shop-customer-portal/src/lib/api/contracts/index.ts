import { api } from "@/lib/api";
import { ApiEndpoints } from "../endpoints";
import { Contract, ContractStatus } from "./types";

interface GetContractsParams {
  customer_id: number;
  status?: ContractStatus;
}

export async function getContracts(params: GetContractsParams) {
  const response = await api.get<Contract[]>(ApiEndpoints.contracts.findAll, {
    params,
  });

  return response.data;
}

export async function getContract(id: number) {
  const response = await api.get<Contract>(ApiEndpoints.contracts.findById(id));

  return response.data;
}
