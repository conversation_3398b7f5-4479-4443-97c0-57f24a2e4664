import { api } from "@/lib/api";
import { ApiEndpoints } from "../endpoints";
import { LoginResponse, TypeResendToken } from "./types";

interface CreateAccountParams {
  email: string;
  first_name: string;
  last_name: string;
  password?: string | null;
  company_name?: string | null;
}

export async function createAccount(data: CreateAccountParams) {
  try {
    const res = await api.post(ApiEndpoints.customer.create, {
      ...data,
      token_magic_link: window.location.href,
    });
    return res;
  } catch (error) {
    return error;
  }
}

export const getStatusByEmail = async (email: string) => {
  try {
    const res = await api.get(ApiEndpoints.user.getStatus(email));
    return res;
  } catch (error) {
    console.error("🚀 ~ getStatusByEmail ~ error:", error);
    return error;
  }
};

export const sendVerificationCode = async (email: string) => {
  try {
    const res = await api.post(ApiEndpoints.user.postVerificationCode, {
      email,
      callback_url: window.location.href,
    });
    return res;
  } catch (error) {
    console.error("🚀 ~ sendVerificationCode ~ error:", error);
  }
};

export const sendMagicToken = async (magicLink: string) => {
  try {
    const res = await api.post(ApiEndpoints.user.postVerificationMagicToken, {
      magicLink,
    });

    return res.data;
  } catch (error) {
    console.error("🚀 ~ sendVerificationCode ~ error:", error);
    throw new Error("Error sending verification code");
  }
};

export const confirmVerificationToken = async (email: string, token: string) => {
  try {
    const res = await api.post(ApiEndpoints.user.postConfirmVerificationCode, {
      email,
      token,
    });
    return res.data;
  } catch (error) {
    return error;
  }
};

export const createPassword = async (userId: number, password: string) => {
  try {
    const response = await api.post<LoginResponse>(ApiEndpoints.user.createPassword, {
      userId,
      password,
    });
    return response?.data;
  } catch (error) {
    return null;
  }
};

export const resendTokenEmail = async (email: string, type_resend_token: TypeResendToken) => {
  try {
    const res = await api.post(ApiEndpoints.user.postResendToken, {
      email,
      type_resend_token,
      token_magic_link: window.location.href,
    });
    return res;
  } catch (error) {
    return null;
  }
};

export const verifyAccount = async (email: string) => {
  try {
    const res = await api.get(ApiEndpoints.user.postVerify, {
      params: {
        email,
      },
    });
    return res;
  } catch (error) {
    console.error(error);
    throw new Error("Error verifying account");
  }
};

export const login = async (email: string, password: string) => {
  try {
    const res = await api.post(ApiEndpoints.user.postLogin, {
      data: {
        email,
        password,
      },
    });
    return res;
  } catch (error) {
    return null;
  }
};
