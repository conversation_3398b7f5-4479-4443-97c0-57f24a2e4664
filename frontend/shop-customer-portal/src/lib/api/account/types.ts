export interface CreateAccountResult {
  userId: string;
  email: string;
  createdAt: string;
  updatedAt: string;
}

export interface IAccountCreate {
  email: string;
  password: string;
  // newsletter: boolean;
  name: string;
  role_id: number;
  type: string;
  is_active: boolean;
}

export enum TypeResendToken {
  LOGIN,
  CREATE_ACCOUNT,
}

export interface LoginResponse {
  access_token: string;
  expires_in: number;
  refresh_token: string;
  user: {
    id: number;
    email: string;
    name: string;
    is_active: boolean;
    status: string;
    role_id: number;
    role: string;
    has_password: boolean;
  };
}
