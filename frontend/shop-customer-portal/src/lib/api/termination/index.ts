import { api } from "@/lib/api";
import { ApiEndpoints } from "../endpoints";
import { CreateTerminationParams } from "./types";

export async function createTermination(data: CreateTerminationParams) {
  try {
    const response = await api.post(ApiEndpoints.termination.create, data);

    return { success: true, data: response.data };
  } catch (error: any) {
    const errorMessage = error.response?.data?.message || error.message;
    return { success: false, error: errorMessage };
  }
}

export const revokeTermination = async (terminationId: number) => {
  try {
    const res = await api.post(ApiEndpoints.termination.revoke(terminationId));
    return res;
  } catch (error) {
    return error;
  }
};
