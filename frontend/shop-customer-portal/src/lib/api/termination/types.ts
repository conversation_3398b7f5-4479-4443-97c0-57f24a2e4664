import { UploadedFile } from "../file/types";

export interface Termination {
  id: number;
  created_at: string;
  completed_at?: string;
  requested_at: string;
  files?: UploadedFile[];
  reason: string[];
  status: TerminationStatus;
}
export interface CreateTerminationParams {
  contract_id: number;
  country_codes?: string[];
  reason_ids: number[];
  termination_file_id?: string;
}

export type TerminationStatus = "REQUESTED" | "COMPLETED" | "PENDING";
