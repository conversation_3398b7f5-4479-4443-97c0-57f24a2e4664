import { api } from "..";
import { ApiEndpoints } from "../endpoints";

export interface Certificate {
  id: number;
  license_id: number;
  name: string;
  status: CertificateStatus;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export type CertificateStatus = "PENDING" | "DONE";

export interface Files {
  id: string;
  type: string;
  size: string;
  name: string;
  original_name: string;
  license_id: number;
  extension: string;
  created_At: string;
}

export interface GetCertificatesParams {
  license_id: number;
}

export async function getCertificates({ license_id }: GetCertificatesParams) {
  try {
    const res = await api.get<Certificate[]>(ApiEndpoints.certificate.getAll, {
      params: { license_id },
    });

    return res.data;
  } catch (error: any) {
    return error;
  }
}
