import { api } from "..";
import { ApiEndpoints } from "../endpoints";
import { CreatePurchaseDto } from "./types";

export async function createPurchase(data: CreatePurchaseDto) {
  try {
    const res = await api.post(ApiEndpoints.purchase.create, data);
    return { success: true, data: res.data } as const;
  } catch (error: any) {
    const errorMessage = error.response.data.message || error.message;
    return {
      success: false,
      error: errorMessage,
    } as const;
  }
}
