import { ApiEndpoints } from "../endpoints";
import { api } from "@/lib/api";

export const getAllCountries = async () => {
  try {
    const res = await api.get(ApiEndpoints.country.countries);
    return res;
  } catch (error) {
    return null;
  }
};

export const getCountry = async (search?: string) => {
  try {
    const res = await api.get(ApiEndpoints.country.getSearchCountry(search ?? ""));
    return res;
  } catch (error) {
    return null;
  }
};

export const getAllDocuments = async () => {
  try {
    const res = await api.get(ApiEndpoints.country.getAllCountryDocuments);
    return res;
  } catch (error) {
    return null;
  }
};

export const getCountries = async () => {
  try {
    const res = await api.get(ApiEndpoints.country.countries);
    return res;
  } catch (error) {
    return null;
  }
};

export const recommendCountry = async (country: string, customerId?: string) => {
  const dto = {
    recommended_country_name: country,
    ...(customerId !== undefined && { customer_id: parseInt(customerId) }),
  };

  try {
    const res = await api.post(ApiEndpoints.country.recommend, dto);
    return res;
  } catch (error) {
    return null;
  }
};
