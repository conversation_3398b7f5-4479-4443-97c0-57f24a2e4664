import { api } from "..";
import { ApiEndpoints } from "../endpoints";
import type { UploadedFile, UploadFileParams } from "./types";

export async function uploadFile(data: UploadFileParams) {
  try {
    const formData = new FormData();

    formData.append("file", data.file);
    formData.append("type", data.type);

    if (data.required_information_id) formData.append("required_information_id", String(data.required_information_id));
    if (data.contract_id) formData.append("contract_id", String(data.contract_id));
    if (data.certificate_id) formData.append("certificate_id", String(data.certificate_id));
    if (data.license_id) formData.append("license_id", String(data.license_id));
    if (data.termination_id) formData.append("termination_id", String(data.termination_id));
    if (data.general_information_id) formData.append("general_information_id", String(data.general_information_id));

    const response = await api.post<UploadedFile>(ApiEndpoints.uploadFiles.post, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });

    return response.data;
  } catch (error) {
    console.error(error);
    return null;
  }
}

export async function deleteFile(fileId: string) {
  await api.delete(ApiEndpoints.uploadFiles.delete(fileId));
}

export async function downloadCustomerFile(fileId: string) {
  const response = await api.get(ApiEndpoints.uploadFiles.downloadCustomerFile(fileId), {
    responseType: "blob",
  });

  return response.data;
}

export async function downloadCustomerFileByRelation(relation: string, relationId: string) {
  const response = await api.get(ApiEndpoints.uploadFiles.downloadCustomerFileByRelation, {
    params: {
      relation,
      id: relationId,
    },
    responseType: "blob",
  });

  return response.data;
}

export async function downloadAdminFile(fileId: string) {
  const response = await api.get(ApiEndpoints.uploadFiles.downloadAdminFile(fileId), {
    responseType: "blob",
  });

  return response.data;
}
