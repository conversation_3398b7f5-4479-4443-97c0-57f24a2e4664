export interface InviteData {
  codes: InviteCode[];
  comissions: InviteComission[];
}

export interface InviteCode {
  id: number;
  label: string;
  value: number;
  isActive: boolean;
  useDate?: string;
}
export interface InviteComission {
  id: number;
  comission_date: string;
  product: string;
  commission: number;
  order_id: number;
  lead_source: string;
  start_date: string;
}

export interface CustomerInviteToken {
  id: number;
  token: string;
  share_link: string;
  customer_id: number;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date;
}
