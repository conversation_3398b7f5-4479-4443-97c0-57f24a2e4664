export enum CouponType {
  SYSTEM = "SYSTEM",
  CUSTOMER = "CUSTOMER",
}

export enum CouponMode {
  GENERAL = "GENERAL",
  INDIVIDUAL = "INDIVIDUAL",
  GROUP_SEGMENT = "GROUP_SEGMENT",
}

export enum CouponDiscountType {
  PERCENTAGE = "PERCENTAGE",
  ABSOLUTE = "ABSOLUTE",
  BUY_X_PRODUCTS_GET_Y_PRODUCTS = "BUY_X_PRODUCTS_GET_Y_PRODUCTS",
  BUY_X_PRODUCTS_GET_Y_DISCOUNT = "BUY_X_PRODUCTS_GET_Y_DISCOUNT",
}

export interface Coupon {
  id: number;
  buyXGetY?: any;
  code: string;
  commissionPercentage?: number;
  description?: string;
  discountType: CouponDiscountType;
  elegibleProducts?: any;
  endDate: Date;
  isActive: boolean;
  link?: string;
  maxAmount?: number;
  maxUses?: number;
  maxUsesPerCustomer?: number;
  minAmount?: number;
  minProducts?: number;
  mode: CouponMode;
  note?: string;
  startDate: Date;
  type: CouponType;
  value: number;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;
  redeemableByNewCustomers: boolean;
  forCommission?: boolean;
  is_active: boolean;
  used_at: Date;
}
