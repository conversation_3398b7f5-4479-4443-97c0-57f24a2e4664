import { api } from "..";
import { ApiEndpoints } from "../endpoints";
import { uploadFile } from "../file";
import type { RequiredInformation } from "./types";

export type GetRequiredInformationsParams = {
  license_id?: number;
  contract_id?: number;
};

export const getRequiredInformations = async (params: GetRequiredInformationsParams) => {
  try {
    const res = await api.get<RequiredInformation[]>(ApiEndpoints.requiredInformations.getAll, {
      params,
    });

    return res.data;
  } catch (error) {
    return [];
  }
};

export async function updateRequiredInformation(id: number, data: Partial<RequiredInformation>) {
  try {
    const response = await api.put(ApiEndpoints.requiredInformations.update(id), data);
    return response.data;
  } catch (error) {
    console.error(error);
  }
}

export const updateAnswer = async (payload: { requiredInfoId: number; answer: string }) => {
  const { requiredInfoId, answer } = payload;

  try {
    const response = await api.put(ApiEndpoints.requiredInformations.updateAnswer(requiredInfoId), { answer });
    return response.data;
  } catch (error) {
    console.error(error);
  }
};

export async function submitRequiredInformation(id: number, data: { user_id: number; file?: File; answer?: string }) {
  try {
    if (!data.file && !data.answer) return;

    if (data.file) {
      await uploadFile({
        file: data.file,
        type: "REQUIRED_INFORMATION",
        required_information_id: id,
      });
    }

    const response = await api.put(ApiEndpoints.requiredInformations.update(id), {
      answer: data.answer || null,
      status: "DONE",
    });
    return response.data;
  } catch (error) {
    console.error(error);
  }
}
