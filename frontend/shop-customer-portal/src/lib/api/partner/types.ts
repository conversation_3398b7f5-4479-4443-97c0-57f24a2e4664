import { Company } from "../company/types";
import { UploadedFile } from "../file/types";
import { MarketingMaterialPartner } from "../marketing-materials/types";

export interface IPartner {
  partner_firstname: string;
  partner_lastname: string;
  partner_email: string;
  user_id: number | string;
  banking: IBanking;
}

export interface IBanking {
  international_account_number?: string;
  business_identifier_code?: string;
}

export type Partner = {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  user_id: number;
  created_at: Date;
  updated_at: Date;
  deleted_at?: Date;
  companies: Company[];
  partner_banking?: any;
  partner_signatures: any;
  partner_contract: {
    id: number;
    partner_id: number;
    status: PartnerContractStatus;
    agreed_on: string;
    start_date: string;
    end_date: string;
    created_at: string;
    files: UploadedFile[];
    changes: {
      change_type: PartnerContractChangeType;
      change_description: string;
      created_at: string;
      updated_at: string;
    }[];
  } | null;
  marketing_material_partners: MarketingMaterialPartner[];
};

type PartnerContractStatus = "DRAFT" | "ACTIVE" | "EXPIRED" | "TERMINATED" | "TO_BE_SIGNED" | "DENIED";

type PartnerContractChangeType = "EMAIL" | "NOTE" | "FILE";
