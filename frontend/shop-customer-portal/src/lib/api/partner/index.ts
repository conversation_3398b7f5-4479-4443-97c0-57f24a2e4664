import { api } from "..";
import { ApiEndpoints } from "../endpoints";
import { <PERSON><PERSON><PERSON>, Partner } from "./types";

export const createPartner = async (data: Partial<IPartner>) => {
  try {
    const response = await api.post<Partner>(ApiEndpoints.partner.postCreate, data);
    return response.data;
  } catch {
    return null;
  }
};

export const updatePartner = async (partnerId: string | number, data: Partial<IPartner>) => {
  try {
    const res = await api.patch(ApiEndpoints.partner.patchPartner(partnerId), data);
    return res;
  } catch (error) {
    return error;
  }
};

export const signContractPartner = async (partnerId: string | number) => {
  try {
    const res = await api.post(ApiEndpoints.partner.signContractPartner(partnerId));
    return res;
  } catch (error) {
    return error;
  }
};

export async function getPartnerByUserId(id: string | number) {
  try {
    const response = await api.get<Partner>(ApiEndpoints.partner.getPartnerByUserId(id));

    if (response.status !== 200) return null;

    return response.data;
  } catch {
    return null;
  }
}

export async function checkCredentials(id: number | string, body: { email?: string; password?: string }): Promise<any> {
  try {
    const res = await api.patch(ApiEndpoints.partner.checkCredentials(id), body);
    return res;
  } catch (error) {
    return error;
  }
}
