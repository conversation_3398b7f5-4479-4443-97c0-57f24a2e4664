export interface VolumeReportItem {
  id: number;
  license_volume_report_id: number;
  setup_fraction_id: number;
  setup_fraction_code: string;
  setup_column_id: number;
  setup_column_code: string;
  value: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export type CreateVolumeReportItem = Omit<VolumeReportItem, "id" | "created_at" | "updated_at" | "deleted_at">;
