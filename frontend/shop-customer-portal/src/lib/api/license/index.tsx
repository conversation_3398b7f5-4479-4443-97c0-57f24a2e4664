import { api } from "@/lib/api";
import { ApiEndpoints } from "../endpoints";
import { FullLicense } from "./types";

export interface GetLicensesParams {
  contract_id?: number;
}

export async function getLicenses({ contract_id }: GetLicensesParams) {
  try {
    const response = await api.get<FullLicense[]>(ApiEndpoints.license.getAll, {
      params: {
        contract_id,
      },
    });

    return response.data;
  } catch (error) {
    console.error(error);
    return [];
  }
}

export async function getLicense(licenseId: number) {
  try {
    const response = await api.get<FullLicense>(ApiEndpoints.license.getOne(licenseId));

    return response.data;
  } catch (error) {
    console.error(error);
    return null;
  }
}
