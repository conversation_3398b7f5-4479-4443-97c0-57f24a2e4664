import { ContractType } from "../contracts/types";

export type OrderStatus = "OPEN" | "PARTIALLY_PAID" | "PAID" | "CANCELLED";

export interface Order {
  id: number;
  payment_customer_id: string;
  amount: number;
  status: OrderStatus;
  items: OrderItem[];
  payments: OrderPayment[];
  coupon?: string;
  order_data?: any;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface OrderItem {
  id: string;
  order_id: number;
  order: Order;
  license_year?: number;
  price: number;
  country_id: number;
  country_code: string;
  country_name: string;
  country_flag: string;
  service_type: ContractType;
  specification_type: OrderItemSpecificationType;
  description?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface OrderPayment {
  id: string;
  amount: number;
  currency: string;
  status: string;
  order_id: number;
  payment_method_id?: string;
  platform?: string;
  platform_payment_id?: string;
  payment_data?: any;
  invoice_id?: number;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export type OrderItemSpecificationType =
  | "HANDLING_FEE"
  | "REGISTRATION_FEE"
  | "VARIABLE_HANDLING_FEE"
  | "DIRECT_LICENSE_PURCHASE"
  | "DIRECT_LICENSE_REFUND"
  | "DIRECT_LICENSE_VOLUME_CHANGE";
