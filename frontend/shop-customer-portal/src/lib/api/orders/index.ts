import { api } from "..";
import { ApiEndpoints } from "../endpoints";
import { Order, OrderStatus } from "./types";

interface GetOrdersParams {
  customer_id?: number;
  start_date?: string;
  end_date?: string;
  status?: OrderStatus;
  license_year?: number;
}

interface GetOrdersResponse {
  count: number;
  orders: Order[];
  current_page: number;
  pages: number;
}

export async function getOrders(params: GetOrdersParams) {
  const response = await api.get<GetOrdersResponse>(ApiEndpoints.order.getAll, { params });

  return response.data;
}

export async function getOrderById(orderId: string) {
  const response = await api.get<Order>(ApiEndpoints.order.getOne(orderId));

  return response.data;
}
