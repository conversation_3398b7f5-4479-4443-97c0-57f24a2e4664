import { ContractType } from "../contracts/types";
import { CustomerCommitment } from "../commitment";
import { PaymentMethodType } from "@/lib/api/payment/types";

export type ShoppingCartJourney = "LONG" | "DIRECT_LICENSE" | "QUICK_LICENSE" | "QUICK_ACTION_GUIDE";

export interface ShoppingCart {
  id: string;
  email: string | null;
  journey: ShoppingCartJourney;
  journey_step: ShopStep;
  items: ShoppingCartItem[];
  total: number;
  vat_percentage: number;
  vat_value: number;
  payment: {
    payment_method_id?: string;
    payment_method_type?: PaymentMethodType;
    platform_payment_id?: string;
    order_id?: number;
  } | null;
  coupon?: ShoppingCartCoupon | null;
  customer_commitments: CustomerCommitment[];
  updated_at: string;
}

export interface ShoppingCartItem {
  id: number;
  service_type: ContractType;
  specification_type: "PURCHASE" | "VOLUME_CHANGE";
  country_id: number;
  country_code: string;
  // TODO: i18n
  country_name: string;
  country_flag: string;
  year: number;
  price_list: PriceList;
  packaging_services?: {
    id: number;
    name: string;
    fractions: Record<
      string,
      {
        code: string;
        name: string;
        weight: number;
      }
    >;
  }[];
  price: number;
  calculator?: ShoppingCartItemCalculator;
}

export interface ShoppingCartItemCalculator {
  license_costs: number;
}

export interface ShoppingCartCoupon {
  value: number;
  discount_type: "PERCENTAGE" | "ABSOLUTE";
  code: string;
}

export interface PriceList {
  id: number;
  type: ContractType;
  name: string;
  description: string;
  condition_type: "LICENSE_YEAR";
  condition_type_value: string;
  start_date: string;
  end_date: string;
  // EU License
  registration_fee: number | null;
  handling_fee: number | null;
  variable_handling_fee: number | null;
  // Direct License
  basic_price: number | null;
  minimum_price: number | null;
  thresholds:
    | {
        title: string;
        value: number;
        helper_text: string | null;
        fractions: Record<
          string,
          {
            code: string;
            name: string;
            value: number;
          }
        >;
      }[]
    | null;
  // Action Guide
  price: number | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export const SHOP_STEPS = {
  SELECT_COUNTRIES: "/select-countries",
  CREATE_ACCOUNT: "/create-account",
  SET_PASSWORD: "/set-password",
  OBLIGATIONS: "/obligations",
  SELECT_SERVICES: "/select-services",
  CALCULATOR: "/calculator",
  SHOPPING_CART: "/shopping-cart",
  COMPANY_INFORMATIONS: "/informations",
  BILLING: "/billing",
  PURCHASE: "/purchase",
  CONCLUSION: "/conclusion",
};

export type ShopStep = keyof typeof SHOP_STEPS;
