import { api } from "@/lib/api";
import { ApiEndpoints } from "../endpoints";

export const patchPassword = async (
  id: number | string,
  body: {
    oldPassword: string;
    newPassword: string;
  }
) => {
  try {
    const res = await api.patch(ApiEndpoints.user.patchPassword(id), body);
    return res;
  } catch (error) {
    return error;
  }
};

export const patchEmail = async (
  id: number | string,
  body: {
    newEmail: string;
    oldEmail: string;
    password: string;
  }
) => {
  try {
    const res = await api.patch(ApiEndpoints.user.patchEmail(id), body);
    return res;
  } catch (error) {
    return error;
  }
};

export const patchVerifyEmail = async (
  id: number | string,
  body: {
    token: string;
  }
) => {
  try {
    const res = await api.patch(ApiEndpoints.user.patchVerifyEmail(id), body);
    return res;
  } catch (error) {
    return error;
  }
};

interface PostRecoverPasswordBody {
  token: string | null;
  password: string;
  type: string;
}

export const postRecoverPassword = async (body: PostRecoverPasswordBody) => {
  try {
    const res = await api.post(ApiEndpoints.user.postRecoverPassword, body);
    if (res.status >= 400) {
      throw new Error("An error occurred while posting the data");
    }

    return {
      data: res.data,
      status: res.status,
    };
  } catch (e: any) {
    if (e?.response?.status >= 500 || e?.code === "ECONNABORTED" || e?.code === "ERR_NETWORK") {
      return {
        data: {
          message: "Failed to connect. Please wait and try again.",
          statusCode: 500,
        },
        status: 500,
      };
    }

    return { data: e?.response?.data, status: e?.response?.status };
  }
};

interface PostForgotPasswordBody {
  email: string;
  callbackUrl: string;
}

export const postForgotPassword = async (body: PostForgotPasswordBody) => {
  try {
    const res = await api.post(ApiEndpoints.user.postForgotPassword, body);
    if (res.status >= 400) {
      throw new Error("An error occurred while posting the data");
    }

    return {
      data: res.data,
      status: res.status,
    };
  } catch (e: any) {
    if (e?.response?.status >= 500 || e?.code === "ECONNABORTED" || e?.code === "ERR_NETWORK") {
      return {
        data: {
          message: "Failed to connect. Please wait and try again.",
          statusCode: 500,
        },
        status: 500,
      };
    }

    return { data: e?.response?.data, status: e?.response?.status };
  }
};
