export type ConsentType = "ACCOUNT" | "PURCHASE";

export interface Consent {
  id: number;
  name: string;
  type: ConsentType;
  description: string;
  version: number;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date | null;
  consents: CustomerConsent[];
}

export interface CustomerConsent {
  id?: number;
  customer_id: number;
  consent_id: number;
  given: boolean;
  givenAt?: Date | null;
  revokedAt?: Date | null;
  created_at?: Date;
  updated_at?: Date;
  customer?: any;
  consent?: Consent;
}

export interface ConsentListItem extends Consent {
  given: boolean;
}
