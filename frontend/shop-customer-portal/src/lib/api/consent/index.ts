import { api } from "..";
import { ApiEndpoints } from "../endpoints";
import { Consent, ConsentType, CustomerConsent } from "./types";

export const getConsents = async () => {
  try {
    const res = await api.get(ApiEndpoints.consent.getAll);
    return res.data;
  } catch {
    return null;
  }
};

export const getConsentsByType = async (type: ConsentType): Promise<Consent[] | null> => {
  try {
    const res = await api.get(ApiEndpoints.consent.getByType(type));
    return res.data;
  } catch {
    return null;
  }
};

// Custoemer Consents
export const getCustomerConsentsByCustomerId = async (id: number) => {
  try {
    const res = await api.get(ApiEndpoints.customerConsent.getByCustomerId(id));
    return res.data;
  } catch {
    null;
  }
};

export const createManyCustomerConsents = async (data: CustomerConsent[]) => {
  try {
    const res = await api.post(ApiEndpoints.customerConsent.createMany, data);
    return res.data;
  } catch {
    return null;
  }
};

export const updateCustomerConsent = async (id: number, data: Partial<CustomerConsent>) => {
  try {
    const res = await api.patch(ApiEndpoints.customerConsent.update(id), data);
    return res.data;
  } catch {
    return null;
  }
};

export const updateManyCustomerConsents = async (data: Partial<CustomerConsent>[]) => {
  try {
    const res = await api.patch(ApiEndpoints.customerConsent.updateMany, data);
    return res.data;
  } catch {
    return null;
  }
};
