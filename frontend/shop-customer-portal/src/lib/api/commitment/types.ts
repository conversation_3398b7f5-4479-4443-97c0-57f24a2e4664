import { PriceList } from "../shoppingCart/types";

export type ServiceSetup = {
  id: number;
  name: string;
  code: string;
  flag_url: string;
  authorize_representative_obligated: boolean;
  other_costs_obligated: boolean;
  created_at: string;
  updated_at: string;
  packaging_services: {
    id: number;
    name: string;
    description: string;
    country_id: number;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
    report_set_frequencies: {
      id: number;
      rhythm: "ANNUALLY";
      frequency: {
        deadline: unknown;
        open: unknown;
      };
      packaging_service_id: number;
      created_at: string;
      updated_at: string;
      deleted_at: string | null;
    }[];
    report_sets: {
      id: number;
      name: string;
      mode: "ON_PLATAFORM";
      type: "FRACTIONS";
      packaging_service_id: number;
      created_at: string;
      updated_at: string;
      deleted_at: string | null;
      fractions: {
        id: number;
        name: string;
        description: string;
        icon: string;
        is_active: boolean;
        report_set_id: number;
        parent_id: number | null;
        children: {
          id: number;
          name: string;
          description: string;
          icon: string;
          is_active: boolean;
          report_set_id: number;
          parent_id: number | null;
          children: {
            id: number;
            name: string;
            description: string;
            icon: string;
            is_active: boolean;
            report_set_id: number;
            parent_id: number | null;
            children: {
              id: number;
              name: string;
              description: string;
              icon: string;
              is_active: boolean;
              report_set_id: number;
              parent_id: number | null;
            };
          }[];
        }[];
      }[];
      columns: {
        id: number;
        name: string;
        description: string;
        unit_type: string;
        report_set_id: number;
        parent_id: number | null;
        created_at: string;
        updated_at: string;
        deleted_at: string | null;
        fractions: {
          id: number;
          column_id: number;
          fraction_id: number;
          created_at: Date;
          updated_at: Date;
          deleted_at: Date | null;
        }[];
      }[];
    }[];
  }[];
  representative_tiers: {
    id: number;
    name: string;
    price: number;
    country_id: number;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
  }[];
  required_informations: {
    id: number;
    country_id: number;
    type: string;
    name: string;
    description: string;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
    question: string | null;
    file_id: string | null;
    file: {
      id: string;
      name: string;
      original_name: string;
      extension: string;
      size: string;
      creator_type: string;
      document_type: string;
      country_id: number | null;
      user_id: string;
      created_at: string;
      updated_at: string;
    } | null;
  }[];
  other_costs: {
    id: number;
    name: string;
    price: number;
    country_id: number;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
  }[];
  country_price_lists: {
    id: number;
    country_id: number;
    price_list_id: number;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
    price_list: {
      id: number;
      type: string;
      name: string;
      description: string;
      condition_type: string;
      condition_type_value: string;
      start_date: string;
      end_date: string;
      basic_price: number;
      minimum_price: number;
      registration_fee: number;
      handling_fee: number;
      variable_handling_fee: number;
      price: number | null;
      created_at: string;
      updated_at: string;
      deleted_at: string | null;
    };
  };
};

export type Criteria = {
  id: number;
  mode: "COMMITMENT" | "CALCULATOR";
  type: CriteriaType;
  title: string;
  help_text: string | null;
  input_type: "YES_NO" | "SELECT";
  calculator_type: string | null;
  country_id: number;
  packaging_service_id: number | null;
  required_information_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  options: {
    id: number;
    option_value: string;
    option_to_value: string | null;
    value: string;
  }[];
};

type CriteriaType =
  | "PACKAGING_SERVICE"
  | "REPORT_SET"
  | "REPORT_FREQUENCY"
  | "AUTHORIZE_REPRESENTATIVE"
  | "REPRESENTATIVE_TIER"
  | "OTHER_COST"
  | "PRICE_LIST"
  | "REQUIRED_INFORMATION";

export type Commitment = (Omit<Criteria, "mode"> & { mode: "COMMITMENT"; answer?: string })[];

export type RepresentativeTier = {
  id: number;
  name: string;
  price: number;
};

export type OtherCost = {
  id: number;
  name: string;
  price: number;
};

export type PackagingService = {
  id: number;
  name: string;
  description: string;
  country_id: number;
  obliged: boolean;
};

export interface ReportSet {
  id: number;
  name: string;
  mode: string;
  type: string;
  fractions: ReportSetFraction[];
  columns: ReportSetColumn[];
}

export interface ReportSetFraction {
  id: number;
  parent_id: number | null;
  code: string;
  parent_code: string | null;
  name: string;
  description: string;
  icon: string;
  fraction_icon_id: number;
  fraction_icon: {
    id: number;
    image_url: string;
  };
  is_active: boolean;
  report_set_id: number;
  level: number;
  order: number;
  has_second_level: boolean;
  has_third_level: boolean;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
  children: ReportSetFraction[];
}

export interface ReportSetColumn {
  id: number;
  parent_id: number | null;
  code: string;
  parent_code: string | null;
  name: string;
  description: string;
  unit_type: ReportSetColumnUnitType;
  report_set_id: number;
  level: number;
  order: number;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
  children: ReportSetColumn[];
  fractions: ReportSetColumnFraction[];
}

export interface ReportSetColumnFraction {
  id: number;
  column_code: string;
  fraction_code: string;
  created_at: Date;
  updated_at: Date;
  deleted_at: Date | null;
}

export type ReportSetColumnUnitType = "KG" | "UNITS" | "EACH";

export type ReportSetFrequency = {
  id: number;
  rhythm: string;
  frequency: {
    deadline: unknown;
    open: unknown;
  };
  packaging_service_id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
};

export type RequiredInformation = {
  id: number;
  type: string;
  name: string;
  description: string;
  question: string | null;
  file_id: string | null;
};

type BasePriceList = {
  id: string;
  name: string;
  service_type: "EU_LICENSE" | "DIRECT_LICENSE" | "ACTION_GUIDE";
  description: string;
  condition_type: "LICENSE_YEAR";
  condition_type_value: string;
  start_date: string;
  end_date: string;
};

export type LicensePriceList = BasePriceList & {
  service_type: "EU_LICENSE";
  basic_price: number;
  minimum_price: number;
  registration_fee: number;
  handling_fee: number;
  variable_handling_fee: number;
};

export type ActionGuidePriceList = BasePriceList & {
  service_type: "ACTION_GUIDE";
  price: number;
};

export type ServiceSetupResult = {
  country: {
    id: number;
    name: string;
    code: string;
    flag_url: string;
    authorize_representative_obligated: boolean;
    other_costs_obligated: boolean;
  };
  year: string;
  packaging_services: (PackagingService & {
    report_set: ReportSet;
    report_set_frequency: ReportSetFrequency;
  })[];
  authorize_representative_obligated: boolean;
  representative_tier: RepresentativeTier;
  other_costs_obligated: boolean;
  other_costs: OtherCost[];
  required_informations: RequiredInformation[];
  price_list: PriceList;
};
