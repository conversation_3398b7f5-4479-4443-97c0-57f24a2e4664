import { api } from "@/lib/api";
import { PaymentMethodType } from "@/lib/api/payment/types";
import { ApiEndpoints } from "../endpoints";
import { CreatePaymentMethodDTO, ExpandedCheckoutSession, PaymentMethod } from "./types";
import { JourneyType } from "@/utils/journeys";

const BASE_URL = process.env.NEXT_PUBLIC_DOMAIN;
const CHECKOUT_SUCCESS_URL = (journey: JourneyType | "SAAS") => {
  const getPath = () => {
    switch (journey) {
      case "QUICK_LICENSE":
        return "/en/eu/quick-journey/license/purchase";
      case "QUICK_ACTION_GUIDE":
        return "/en/eu/quick-journey/action-guide/purchase";
      case "LONG":
        return "/en/eu/long-journey/purchase";
      case "DIRECT_LICENSE":
        return "/en/direct-license/purchase";
      case "SAAS":
        return "/en/saas/invoices-and-payment";
      default:
        return "/en/eu/long-journey/purchase";
    }
  };

  return `${BASE_URL}${getPath()}?session_id={CHECKOUT_SESSION_ID}`;
};
const CHECKOUT_CANCEL_URL = (journey: JourneyType | "SAAS") => {
  const getPath = () => {
    switch (journey.toLowerCase()) {
      case "QUICK_LICENSE":
        return "/en/eu/quick-journey/license/purchase";
      case "QUICK_ACTION_GUIDE":
        return "/en/eu/quick-journey/action-guide/purchase";
      case "LONG":
        return "/en/eu/long-journey/purchase";
      case "DIRECT_LICENSE":
        return "/en/direct-license/purchase";
      case "SAAS":
        return "/en/saas/invoices-and-payment";
      default:
        return "/en/eu/long-journey/purchase";
    }
  };

  return `${BASE_URL}${getPath()}`;
};

interface CreateCheckoutSessionParams {
  customer_id: number;
  total: number;
  payment_method_type: string;
  currency: string;
  journey: JourneyType | "SAAS";
}

export async function createCheckoutSession(params: CreateCheckoutSessionParams): Promise<string | null> {
  try {
    const res = await api.post(ApiEndpoints.payment.createCheckoutSession, {
      customer_id: params.customer_id,
      total: params.total,
      payment_method_type: params.payment_method_type,
      currency: params.currency,
      journey: params.journey,
      success_url: CHECKOUT_SUCCESS_URL(params.journey),
      cancel_url: CHECKOUT_CANCEL_URL(params.journey),
    });

    return res.data.sessionId;
  } catch {
    return null;
  }
}

export async function getCheckoutSession(sessionId: string): Promise<ExpandedCheckoutSession | null> {
  try {
    const res = await api.get(ApiEndpoints.payment.getCheckoutSession(sessionId));
    return res.data.session as ExpandedCheckoutSession;
  } catch {
    return null;
  }
}

// Payment Customer
export async function createPaymentCustomer(customerId: number) {
  try {
    const res = await api.post(ApiEndpoints.paymentCustomer.create, { customer_id: customerId });
    return res.data;
  } catch {
    return null;
  }
}

export async function getPaymentCustomer(id: number) {
  try {
    const res = await api.get(ApiEndpoints.paymentCustomer.getOneByCustomerId(id));
    return res.data;
  } catch {
    return null;
  }
}

export async function updateDefaultPaymentMethod(
  customerId: number,
  paymentMethod: Omit<PaymentMethodType, "CREDIT_CARD"> | string | null
) {
  try {
    const res = await api.patch(ApiEndpoints.paymentCustomer.setDefaultPaymentMethod(customerId), {
      payment_method: paymentMethod,
    });
    return res.data;
  } catch {
    return null;
  }
}

// Payment Methods
export async function createPaymentMethod(data: CreatePaymentMethodDTO) {
  try {
    const response = await api.post(ApiEndpoints.paymentMethod.create, data);

    if (response.status !== 200 && response.status !== 201) throw response;

    return { success: true, data: response.data } as const;
  } catch (err: any) {
    const errorMessage = err.response?.data?.message || err.message;
    return { success: false, error: errorMessage };
  }
}

export async function getAllPaymentMethods() {
  try {
    const res = await api.get(ApiEndpoints.paymentMethod.getMany);
    return res.data;
  } catch {
    return null;
  }
}

export async function getPaymentMethodById(paymentMethodId: string) {
  try {
    const res = await api.get(ApiEndpoints.paymentMethod.getOne(paymentMethodId));
    return res.data;
  } catch {
    return null;
  }
}

export async function getCustomerActivePaymentMethods(id: number): Promise<PaymentMethod[] | null> {
  try {
    const res = await api.get(ApiEndpoints.paymentMethod.getActiveByShopCustomerId(id));
    return res.data;
  } catch {
    return null;
  }
}

export async function deletePaymentMethod(paymentMethodId: string) {
  await api.delete(ApiEndpoints.paymentMethod.delete(paymentMethodId));
}

export async function payInvoice(invoiceId: number, paymentMethodId: string) {
  try {
    const res = await api.post(ApiEndpoints.payment.payInvoice(invoiceId), { payment_method_id: paymentMethodId });

    if (res.status !== 200 && res.status !== 201) throw res;

    return { success: true, data: res.data } as const;
  } catch (err: any) {
    const errorMessage = err.response?.data?.message || err.message;
    return { success: false, error: errorMessage } as const;
  }
}

export async function createPurchaseForExistingOrder(
  orderId: number,
  paymentMethodId: string,
  platformPaymentId?: string
) {
  try {
    const res = await api.post(ApiEndpoints.payment.payOrder(orderId), {
      payment_method_id: paymentMethodId,
      platform_payment_id: platformPaymentId,
    });
    return { success: true, data: res.data } as const;
  } catch (error: any) {
    const errorMessage = error.response.data.message || error.message;
    return {
      success: false,
      error: errorMessage,
    } as const;
  }
}
