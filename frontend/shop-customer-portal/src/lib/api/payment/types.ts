import Stripe from "stripe";

export interface CreatePaymentMethodDTO {
  customer_id: number;
  type: PaymentMethodType;
  platform: string;
  platform_payment_method_id: string;
  saved_for_future_purchase?: boolean;
  card_last_4?: string;
  card_brand?: string;
  card_country?: string;
  payment_method_data?: any;
  create_platform_payment?: boolean;
}

export type UpdatePaymentMethodDTO = Partial<CreatePaymentMethodDTO>;

export interface PaymentMethod {
  id: string;
  payment_customer_id: string;
  type: string;
  saved_for_future_purchase: boolean;
  platform?: string;
  platform_payment_method_id?: string;
  card_last_4?: string;
  card_brand?: string;
  card_country?: string;
  payment_method_data?: Record<string, any>;
  created_at: Date;
  updated_at: Date;
  deleted_at?: Date;
  Payment: any[];
  payment_customer: any;
  default_payment_method?: string;
}

export type PaymentMethodType = "CREDIT_CARD" | "PAYPAL" | "INVOICE" | "ALIPAY" | "KLARNA" | "EPS" | "IDEAL";

export interface CreateOrderDTO {
  payment_customer_id: string;
  status?: string;
  coupon?: string;
  order_data?: any;
}

export type UpdateOrderDTO = Partial<CreateOrderDTO>;

export type ExpandedCheckoutSession = Stripe.Checkout.Session & {
  payment_intent: Stripe.PaymentIntent;
};

export enum Currency {
  USD = "USD",
  EUR = "EUR",
}

export type GetPaymentCustomerByShopCustomerIdParams = {
  startDate?: string;
  endDate?: string;
  year?: number;
};
