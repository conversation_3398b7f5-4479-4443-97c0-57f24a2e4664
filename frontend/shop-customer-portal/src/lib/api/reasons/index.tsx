import { api } from "@/lib/api";
import { ApiEndpoints } from "../endpoints";
import { Reason } from "./types";

export interface GetReasonsParams {
  type: "LICENSE_INFORMATION" | "LICENSE_VOLUME_REPORT" | "TERMINATION";
}

export async function getReasons({ type }: GetReasonsParams) {
  try {
    const response = await api.get<Reason[]>(ApiEndpoints.reasons.getAll, {
      params: {
        type,
      },
    });

    return response.data;
  } catch (error) {
    console.error(error);
    return [];
  }
}
