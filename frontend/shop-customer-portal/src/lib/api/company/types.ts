export interface Company {
  id: number;
  name: string;
  description?: string;
  vat: string | null;
  tin: string | null;
  lucid: string | null;
  customer_id: number | null;
  partner_id: number | null;
  managing_director?: string;
  starting?: string;
  website?: string;
  emails: string[];
  address: {
    country_code: string;
    address_line: string;
    city: string;
    zip_code: string;
    street_and_number: string;
    additional_address: string;
  };
  billing?: {
    full_name: string;
    country_code: string;
    country_name: string;
    company_name: string;
    street_and_number: string;
    city: string;
    zip_code: string;
  };
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export type CreateCompany = Partial<Omit<Company, "id" | "created_at" | "updated_at" | "deleted_at">> & {
  vat?: string;
  tin?: string;
  lucid?: string;
  customer_id?: number;
  partner_id?: number;
  contact?: {
    name: string;
    email: string;
    phone_mobile: string;
  };
};

export type UpdateCompany = Partial<CreateCompany>;
