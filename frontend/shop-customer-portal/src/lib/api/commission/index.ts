import { api } from "..";
import { Coupon } from "../coupon/types";
import { ApiEndpoints } from "../endpoints";
import { GetCommissionParams, CommissionResponse } from "./types";

export async function getCommission(params?: GetCommissionParams) {
  try {
    const response = await api.get<CommissionResponse[]>(ApiEndpoints.commission.get, {
      params: {
        user_id: params?.user_id,
        service_type: params?.service_type,
        type: params?.type,
        client_name: params?.client_name,
      },
    });

    return response.data;
  } catch (error: any) {
    return null;
  }
}

export async function patchUseCommission(userId: number) {
  try {
    const response = await api.patch<CommissionResponse[]>(ApiEndpoints.commission.patchUse(userId));

    return response;
  } catch (error: any) {
    return null;
  }
}

export async function createCommissionCoupon(userId: number) {
  try {
    const response = await api.post<Coupon>(ApiEndpoints.commission.postCreateCoupon(userId));

    return response;
  } catch (error: any) {
    return null;
  }
}
