export interface GetCommissionParams {
  user_id?: number;
  service_type?: CommissionServiceType;
  type?: CommissionType;
  client_name?: string;
}

export enum CommissionUserType {
  CUSTOMER = "CUSTOMER",
  PARTNER = "PARTNER",
}

export enum CommissionType {
  AFFILIATE_LINK = "AFFILIATE_LINK",
  COUPON = "COUPON",
}

export enum CommissionServiceType {
  EU_LICENSE = "EU_LICENSE",
  DIRECT_LICENSE = "DIRECT_LICENSE",
  ACTION_GUIDE = "ACTION_GUIDE",
}

export interface CommissionResponse {
  id: number;
  user_id: number;
  user_type: CommissionUserType;
  commission_percentage: number;
  commission_value: number;
  type: CommissionType;
  coupon_id?: number | null;
  coupon_code?: string | null;
  affiliate_link?: string | null;
  service_type: CommissionServiceType;
  order_id: string;
  order_customer_id?: number | null;
  created_at: string;
  used: boolean;
}
