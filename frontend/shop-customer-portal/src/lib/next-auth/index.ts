import { type NextAuthOptions } from "next-auth";
import Credentials from "next-auth/providers/credentials";
import "server-only";

import { decodeToken } from "@/utils/decode-token";
import { UserTypes } from "@/utils/user";
import { confirmVerificationToken, sendMagicToken } from "../api/account";
import { authenticate, AuthenticateResponse } from "./credentials-auth";

const SECRET = process.env.NEXTAUTH_SECRET;
const ALLOWED_ROLES = [UserTypes.CUSTOMER, UserTypes.PARTNER];

function isExpired(expires_at: number) {
  const currentTime = Math.floor(Date.now() / 1000);

  return currentTime > expires_at;
}

function validateAuthenticationResponse(response: AuthenticateResponse) {
  if (!response || !response.access_token) {
    throw new Error("Invalid Credentials");
  }

  const user = decodeToken(response.access_token);

  if (!user || !user.id || !user.role) {
    throw new Error("Invalid Credentials");
  }

  if (!ALLOWED_ROLES.includes(user.role)) {
    throw new Error("Invalid Credentials");
  }

  return {
    id: user.id,
    role: user.role,
    has_password: user.has_password,
    email: user.email,
    name: `${user.first_name} ${user.last_name}`,
    first_name: user.first_name,
    last_name: user.last_name,
    access_token: response.access_token,
    refresh_token: response.refresh_token,
    expires_at: user.exp,
  };
}

async function validateApiToken(token: string) {
  try {
    const response = await fetch(`${process.env.API}/auth/status`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });
    const isValid = response.status === 200;

    return isValid;
  } catch (error) {
    return false;
  }
}

export const authOptions: NextAuthOptions = {
  providers: [
    Credentials({
      id: "credentials",
      name: "credentials",
      credentials: {
        email: { label: "Username", type: "text", placeholder: "email" },
        password: { label: "Password", type: "password" },
        intent: { label: "Intent", type: "text" },
      },
      async authorize(credentials) {
        if (!credentials || !credentials.email || !credentials.password) {
          throw new Error("Invalid Credentials");
        }

        const response = await authenticate(credentials.email, credentials.password);

        const user = validateAuthenticationResponse(response);

        if (credentials.intent !== user.role) throw new Error("Invalid Credentials");

        return validateAuthenticationResponse(response);
      },
    }),
    Credentials({
      id: "by-email",
      name: "by-email",
      credentials: {
        email: { label: "Username", type: "text", placeholder: "email" },
        token: { label: "token", type: "token" },
      },
      async authorize(credentials) {
        if (!credentials || !credentials.email || !credentials.token) {
          throw new Error("Invalid Credentials");
        }

        const response = await confirmVerificationToken(credentials.email, credentials.token);

        return validateAuthenticationResponse(response);
      },
    }),
    Credentials({
      id: "by-magic",
      name: "by-magic",
      credentials: {
        magic: { label: "magic", type: "magic" },
      },
      async authorize(credentials) {
        if (!credentials || !credentials.magic) {
          throw new Error("Invalid Credentials");
        }

        const response = await sendMagicToken(credentials.magic);

        return validateAuthenticationResponse(response);
      },
    }),
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async signIn(data) {
      const { user } = data;

      if (!user) return false;

      if (isExpired(user.expires_at)) return false;

      if (!ALLOWED_ROLES.includes(user.role)) return false;

      return true;
    },
    async session(data) {
      const isApiTokenValid = await validateApiToken(data.token.access_token);

      if (!isApiTokenValid) throw new Error("Session expired");

      if (isExpired(data.token.expires_at)) throw new Error("Session expired");

      data.session.user = data.token;

      return data.session;
    },
    async jwt(data) {
      // Session.update
      if (data.trigger === "update" && data.session.access_token) {
        const isApiTokenValid = await validateApiToken(data.session.access_token);

        if (!isApiTokenValid) throw new Error("Session expired");

        const user = decodeToken(data.session.access_token);

        if (!user) throw new Error("Session expired");

        data.token = {
          id: user.id,
          role: user.role,
          has_password: user.has_password,
          email: user.email,
          name: `${user.first_name} ${user.last_name}`,
          first_name: user.first_name,
          last_name: user.last_name,
          sub: user.id,
          expires_at: user.exp,
          access_token: data.session.access_token,
          refresh_token: data.session.refresh_token,
        };

        return data.token;
      }

      // Login
      if (data.user) {
        data.token = {
          id: data.user.id,
          role: data.user.role,
          has_password: data.user.has_password,
          email: data.user.email,
          name: data.user.name,
          first_name: data.user.first_name,
          last_name: data.user.last_name,
          sub: data.user.id,
          expires_at: data.user.expires_at,
          access_token: data.user.access_token,
          refresh_token: data.user.refresh_token,
        };
      }

      return data.token;
    },
  },
  secret: SECRET,
  pages: {
    signIn: "/en/auth/login",
    signOut: "/",
  },
};
