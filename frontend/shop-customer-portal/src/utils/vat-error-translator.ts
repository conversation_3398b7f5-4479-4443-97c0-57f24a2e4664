import { useTranslations } from "next-intl";

/**
 * Hook to get VAT API error message translator
 * @returns Function that translates VAT API error keys to localized messages
 */
export function useVatErrorTranslator() {
  const globalT = useTranslations("global");

  return (errorKey: string): string => {
    const errorMap: Record<string, string> = {
      INVALID: globalT("inputs.vatId.errors.api.INVALID"),
      NOT_REGISTERED: globalT("inputs.vatId.errors.api.NOT_REGISTERED"),
      VALID_FROM_FUTURE: globalT("inputs.vatId.errors.api.VALID_FROM_FUTURE"),
      VALID_PAST_ONLY: globalT("inputs.vatId.errors.api.VALID_PAST_ONLY"),
      TEMPORARY_UNAVAILABLE: globalT("inputs.vatId.errors.api.TEMPORARY_UNAVAILABLE"),
      OWN_VAT_INVALID: globalT("inputs.vatId.errors.api.OWN_VAT_INVALID"),
      PROCESSING_BY_OTHERS: globalT("inputs.vatId.errors.api.PROCESSING_BY_OTHERS"),
      FORMAT_INVALID_FOR_COUNTRY: globalT("inputs.vatId.errors.api.FORMAT_INVALID_FOR_COUNTRY"),
      CHECKSUM_INVALID: globalT("inputs.vatId.errors.api.CHECKSUM_INVALID"),
      ILLEGAL_CHARACTERS: globalT("inputs.vatId.errors.api.ILLEGAL_CHARACTERS"),
      INVALID_COUNTRY_PREFIX: globalT("inputs.vatId.errors.api.INVALID_COUNTRY_PREFIX"),
      NOT_AUTHORIZED_FOR_GERMAN: globalT("inputs.vatId.errors.api.NOT_AUTHORIZED_FOR_GERMAN"),
      OWN_VAT_FORMAT_INVALID: globalT("inputs.vatId.errors.api.OWN_VAT_FORMAT_INVALID"),
      SIMPLE_MISSING_PARAMS: globalT("inputs.vatId.errors.api.SIMPLE_MISSING_PARAMS"),
      QUALIFIED_MISSING_PARAMS: globalT("inputs.vatId.errors.api.QUALIFIED_MISSING_PARAMS"),
      PROCESSING_ERROR_MEMBER_STATE: globalT("inputs.vatId.errors.api.PROCESSING_ERROR_MEMBER_STATE"),
      QUALIFIED_NOT_POSSIBLE: globalT("inputs.vatId.errors.api.QUALIFIED_NOT_POSSIBLE"),
      QUALIFIED_FAILED_SIMPLE_DONE: globalT("inputs.vatId.errors.api.QUALIFIED_FAILED_SIMPLE_DONE"),
      REQUEST_PARAMS_INVALID: globalT("inputs.vatId.errors.api.REQUEST_PARAMS_INVALID"),
      PRINT_NOT_AVAILABLE: globalT("inputs.vatId.errors.api.PRINT_NOT_AVAILABLE"),
      PROCESSING_NOT_POSSIBLE: globalT("inputs.vatId.errors.api.PROCESSING_NOT_POSSIBLE"),
    };

    return errorMap[errorKey] || globalT("inputs.vatId.errors.invalid");
  };
}
