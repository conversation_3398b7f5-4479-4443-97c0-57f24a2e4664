import { ClientAcquisition } from "@/types/partner-hub/client-acquisition";
import { Invite, InviteResult, MonthlyInviteResult, YearlyInviteResult } from "@/types/partner-hub/invite";
import { PartnerContract } from "@/types/partner-hub/partner-contract";

export const partnerContract: PartnerContract = {
  id: 1,
  status: "agreed",
  changes: [
    {
      description:
        "Lorem ipsum dolor sit amet consectetur. Semper integer consequat sit adipiscing duis ac phasellus eget.",
      timestamp: new Date(),
    },
    {
      description:
        "Lorem ipsum dolor sit amet consectetur. Semper integer consequat sit adipiscing duis ac phasellus eget.",
      timestamp: new Date(),
    },
    {
      description:
        "Lorem ipsum dolor sit amet consectetur. Semper integer consequat sit adipiscing duis ac phasellus eget.",
      timestamp: new Date(),
    },
  ],
};

export const materials = [
  { id: 1, name: "Spring break campaign 1", url: "./", date: new Date(), type: "campaign" },
  { id: 2, name: "Spring break campaign 2", url: "./", date: new Date(), type: "standard" },
  { id: 3, name: "Spring break campaign 3", url: "./", date: new Date(), type: "campaign" },
  { id: 4, name: "Spring break campaign 4", url: "./", date: new Date(), type: "standard" },
  { id: 5, name: "Spring break campaign 5", url: "./", date: new Date(), type: "campaign" },
  { id: 6, name: "Spring break campaign 6", url: "./", date: new Date(), type: "standard" },
] as const;

export const clientAcquisition: ClientAcquisition = {
  id: 1,
  email: "<EMAIL>",
  acquisitions: [
    {
      year: "2023",
      clients: [
        { method: "direct-license", amount: 300 },
        { method: "eu-license", amount: 170 },
        { method: "action-guide", amount: 60 },
      ],
    },
    {
      year: "2024",
      clients: [
        { method: "direct-license", amount: 0 },
        { method: "eu-license", amount: 0 },
        { method: "action-guide", amount: 0 },
      ],
    },
  ],
};

export const invites: Invite[] = [
  { id: 1, value: "Licenseero25", type: "code", product: "direct-license", expiration: new Date() },
  { id: 2, value: "Licenseero24", type: "code", product: "eu-license", expiration: new Date() },
  { id: 3, value: "Licenseero23", type: "code", product: "action-guide", expiration: new Date() },
  { id: 4, value: "/taylor-launtner", type: "link", product: "all", expiration: new Date() },
];

const inviteMonthlyResults: MonthlyInviteResult["results"] = [
  { month: "January", value: 266 },
  { month: "February", value: 305 },
  { month: "March", value: 237 },
  { month: "April", value: 73 },
  { month: "May", value: 209 },
  { month: "June", value: 214 },
  { month: "July", value: 109 },
  { month: "August", value: 75 },
  { month: "September", value: 91 },
  { month: "October", value: 32 },
  { month: "November", value: 330 },
  { month: "December", value: 140 },
];

const inviteYearlyResults: YearlyInviteResult["results"] = [
  { year: "2021", value: 506 },
  { year: "2022", value: 627 },
  { year: "2023", value: 237 },
  { year: "2024", value: 73 },
];

export const inviteMonthlyAllResults = (inviteId: number): InviteResult => {
  return {
    invite: invites.find((i) => i.id === inviteId) || invites[0],
    period: "m",
    product: "all",
    results: inviteMonthlyResults,
  };
};

export const inviteMonthlyActionGuideResults = (inviteId: number): InviteResult => {
  return {
    invite: invites.find((i) => i.id === inviteId) || invites[0],
    period: "m",
    product: "action-guide",
    results: inviteMonthlyResults,
  };
};

export const inviteMonthlyDirectLicenseResults = (inviteId: number): InviteResult => {
  return {
    invite: invites.find((i) => i.id === inviteId) || invites[0],
    period: "m",
    product: "direct-license",
    results: inviteMonthlyResults,
  };
};

export const inviteMonthlyEULicenseResults = (inviteId: number): InviteResult => {
  return {
    invite: invites.find((i) => i.id === inviteId) || invites[0],
    period: "m",
    product: "eu-license",
    results: inviteMonthlyResults,
  };
};

export const inviteYearlyAllResults = (inviteId: number): InviteResult => {
  return {
    invite: invites.find((i) => i.id === inviteId) || invites[0],
    period: "y",
    product: "all",
    results: inviteYearlyResults,
  };
};

export const inviteYearlyActionGuideResults = (inviteId: number): InviteResult => {
  return {
    invite: invites.find((i) => i.id === inviteId) || invites[0],
    period: "y",
    product: "action-guide",
    results: inviteYearlyResults,
  };
};

export const inviteYearlyDirectLicenseResults = (inviteId: number): InviteResult => {
  return {
    invite: invites.find((i) => i.id === inviteId) || invites[0],
    period: "y",
    product: "direct-license",
    results: inviteYearlyResults,
  };
};

export const inviteYearlyEULicenseResults = (inviteId: number): InviteResult => {
  return {
    invite: invites.find((i) => i.id === inviteId) || invites[0],
    period: "y",
    product: "eu-license",
    results: inviteYearlyResults,
  };
};

export const commissionResult = {
  id: 1,
  count: 25,
  results: Array.from(Array(12).keys()).map(() => ({
    commissionDate: new Date(),
    amount: 515,
    commissionPercentage: 10,
    commissionValue: 30,
    orderId: "6565656",
    orderNumber: "6565656",
    leadSource: invites[0],
    firstTimePurchase: true,
  })),
};
