export const COUNTRIES = [
  {
    name: "Afghanistan",
    phone: { code: "+93", mask: "99-999-9999" },
    code: "AF",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/af.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Aland Islands",
    phone: { code: "+358", mask: "(999)999-99-99" },
    code: "AX",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ax.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Albania",
    phone: { code: "+355", mask: "(999)999-999" },
    code: "AL",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/al.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Algeria",
    phone: { code: "+213", mask: "99-999-9999" },
    code: "DZ",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/dz.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "American Samoa",
    phone: { code: "+1", mask: "(684)999-9999" },
    code: "AS",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/as.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Andorra",
    phone: { code: "+376", mask: "999-999" },
    code: "AD",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ad.svg",
    address: { zipCodeMask: "AD999" },
  },
  {
    name: "Angola",
    phone: { code: "+244", mask: "(999)999-999" },
    code: "AO",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ao.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Anguilla",
    phone: { code: "+1", mask: "(264)999-9999" },
    code: "AI",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ai.svg",
    address: { zipCodeMask: "AI-2640" },
  },
  {
    name: "Antarctica",
    phone: { code: "+672", mask: "199-999" },
    code: "AQ",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/aq.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Antigua and Barbuda",
    phone: { code: "+1", mask: "(268)999-9999" },
    code: "AG",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ag.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Argentina",
    phone: { code: "+54", mask: "(999)999-9999" },
    code: "AR",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ar.svg",
    address: { zipCodeMask: "A9999AAA" },
  },
  {
    name: "Armenia",
    phone: { code: "+374", mask: "99-999-999" },
    code: "AM",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/am.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Aruba",
    phone: { code: "+297", mask: "999-9999" },
    code: "AW",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/aw.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Ascension Island",
    phone: { code: "+247", mask: "9999" },
    code: "AC",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/sh.svg",
    address: { zipCodeMask: "ASCN 1ZZ" },
  },
  {
    name: "Australia",
    phone: { code: "+61", mask: "9-9999-9999" },
    code: "AU",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/au.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Austria",
    phone: { code: "+43", mask: "(999)999-9999" },
    code: "AT",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/at.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Azerbaijan",
    phone: { code: "+994", mask: "99-999-99-99" },
    code: "AZ",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/az.svg",
    address: { zipCodeMask: "AZ 9999" },
  },
  {
    name: "Bahamas",
    phone: { code: "+1", mask: "(242)999-9999" },
    code: "BS",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/bs.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Bahrain",
    phone: { code: "+973", mask: "9999-9999" },
    code: "BH",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/bh.svg",
    address: { zipCodeMask: "999" },
  },
  {
    name: "Bangladesh",
    phone: { code: "+880", mask: "1999-999999" },
    code: "BD",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/bd.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Barbados",
    phone: { code: "+1", mask: "(246)999-9999" },
    code: "BB",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/bb.svg",
    address: { zipCodeMask: "BB99999" },
  },
  {
    name: "Belarus",
    phone: { code: "+375", mask: "(99)999-99-99" },
    code: "BY",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/by.svg",
    address: { zipCodeMask: "999999" },
  },
  {
    name: "Belgium",
    phone: { code: "+32", mask: "(999)999-999" },
    code: "BE",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/be.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Belize",
    phone: { code: "+501", mask: "999-9999" },
    code: "BZ",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/bz.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Benin",
    phone: { code: "+229", mask: "99-99-9999" },
    code: "BJ",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/bj.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Bermuda",
    phone: { code: "+1", mask: "(441)999-9999" },
    code: "BM",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/bm.svg",
    address: { zipCodeMask: "AA 99" },
  },
  {
    name: "Bhutan",
    phone: { code: "+975", mask: ["17-999-999", "77-999-999", "9-999-999"] },
    code: "BT",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/bt.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Bolivia",
    phone: { code: "+591", mask: "9-999-9999" },
    code: "BO",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/bo.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Bosnia and Herzegovina",
    phone: { code: "+387", mask: ["99-9999", "99-99999"] },
    code: "BA",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ba.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Botswana",
    phone: { code: "+267", mask: "99-999-999" },
    code: "BW",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/bw.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Brazil",
    phone: { code: "+55", mask: ["(99)9999-9999", "(99)99999-9999"] },
    code: "BR",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/br.svg",
    address: { zipCodeMask: "99999-999" },
  },
  {
    name: "British Indian Ocean Territory",
    phone: { code: "+246", mask: "999-9999" },
    code: "IO",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/io.svg",
    address: { zipCodeMask: "BBND 1ZZ" },
  },
  {
    name: "Brunei Darussalam",
    phone: { code: "+673", mask: "999-9999" },
    code: "BN",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/bn.svg",
    address: { zipCodeMask: "AA9999" },
  },
  {
    name: "Bulgaria",
    phone: { code: "+359", mask: "(999)999-999" },
    code: "BG",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/bg.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Burkina Faso",
    phone: { code: "+226", mask: "99-99-9999" },
    code: "BF",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/bf.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Burundi",
    phone: { code: "+257", mask: "99-99-9999" },
    code: "BI",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/bi.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Cambodia",
    phone: { code: "+855", mask: "99-999-999" },
    code: "KH",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/kh.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Cameroon",
    phone: { code: "+237", mask: "9999-9999" },
    code: "CM",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/cm.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Canada",
    phone: { code: "+1", mask: "(999)999-9999" },
    code: "CA",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ca.svg",
    address: { zipCodeMask: "A9A 9A9" },
  },
  {
    name: "Cape Verde",
    phone: { code: "+238", mask: "(999)99-99" },
    code: "CV",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/cv.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Cayman Islands",
    phone: { code: "+1", mask: "(345)999-9999" },
    code: "KY",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ky.svg",
    address: { zipCodeMask: "KY9-9999" },
  },
  {
    name: "Central African Republic",
    phone: { code: "+236", mask: "99-99-9999" },
    code: "CF",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/cf.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Chad",
    phone: { code: "+235", mask: "99-99-99-99" },
    code: "TD",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/td.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Chile",
    phone: { code: "+56", mask: "9-9999-9999" },
    code: "CL",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/cl.svg",
    address: { zipCodeMask: "9999999" },
  },
  {
    name: "China",
    phone: { code: "+86", mask: ["(999)9999-999", "(999)9999-9999", "99-99999-99999"] },
    code: "CN",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/cn.svg",
    address: { zipCodeMask: "999999" },
  },
  {
    name: "Christmas Island",
    phone: { code: "+61", mask: "9-9999-9999" },
    code: "CX",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/cx.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Cocos (Keeling) Islands",
    phone: { code: "+61", mask: "9-9999-9999" },
    code: "CC",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/cc.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Colombia",
    phone: { code: "+57", mask: "(999)999-9999" },
    code: "CO",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/co.svg",
    address: { zipCodeMask: "999999" },
  },
  {
    name: "Comoros",
    phone: { code: "+269", mask: "99-99999" },
    code: "KM",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/km.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Congo",
    phone: { code: "+242", mask: "99-99999" },
    code: "CG",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/cg.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Cook Islands",
    phone: { code: "+682", mask: "99-999" },
    code: "CK",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ck.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Costa Rica",
    phone: { code: "+506", mask: "9999-9999" },
    code: "CR",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/cr.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Croatia",
    phone: { code: "+385", mask: "99-999-999" },
    code: "HR",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/hr.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Cuba",
    phone: { code: "+53", mask: "9-999-9999" },
    code: "CU",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/cu.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Cyprus",
    phone: { code: "+357", mask: "99-999-999" },
    code: "CY",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/cy.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Czech Republic",
    phone: { code: "+420", mask: "(999)999-999" },
    code: "CZ",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/cz.svg",
    address: { zipCodeMask: "999 99" },
  },
  {
    name: "Democratic Republic of the Congo",
    phone: { code: "+243", mask: "(999)999-999" },
    code: "CD",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/cd.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Denmark",
    phone: { code: "+45", mask: "99-99-99-99" },
    code: "DK",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/dk.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Djibouti",
    phone: { code: "+253", mask: "99-99-99-99" },
    code: "DJ",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/dj.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Dominica",
    phone: { code: "+1", mask: "(767)999-9999" },
    code: "DM",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/dm.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Dominican Republic",
    phone: { code: "+1", mask: ["(809)999-9999", "(829)999-9999", "(849)999-9999"] },
    code: "DO",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/do.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Ecuador",
    phone: { code: "+593", mask: ["9-999-9999", "99-999-9999"] },
    code: "EC",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ec.svg",
    address: { zipCodeMask: "999999" },
  },
  {
    name: "Egypt",
    phone: { code: "+20", mask: "(999)999-9999" },
    code: "EG",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/eg.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "El Salvador",
    phone: { code: "+503", mask: "99-99-9999" },
    code: "SV",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/sv.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Equatorial Guinea",
    phone: { code: "+240", mask: "99-999-9999" },
    code: "GQ",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/gq.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Eritrea",
    phone: { code: "+291", mask: "9-999-999" },
    code: "ER",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/er.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Estonia",
    phone: { code: "+372", mask: ["999-9999", "9999-9999"] },
    code: "EE",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ee.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Eswatini",
    phone: { code: "+268", mask: "99-99-9999" },
    code: "SZ",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/sz.svg",
    address: { zipCodeMask: "A999" },
  },
  {
    name: "Ethiopia",
    phone: { code: "+251", mask: "99-999-9999" },
    code: "ET",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/et.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Falkland Islands (Malvinas)",
    phone: { code: "+500", mask: "99999" },
    code: "FK",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/fk.svg",
    address: { zipCodeMask: "FIQQ 1ZZ" },
  },
  {
    name: "Faroe Islands",
    phone: { code: "+298", mask: "999-999" },
    code: "FO",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/fo.svg",
    address: { zipCodeMask: "999" },
  },
  {
    name: "Fiji",
    phone: { code: "+679", mask: "99-99999" },
    code: "FJ",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/fj.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Finland",
    phone: { code: "+358", mask: "(999)999-99-99" },
    code: "FI",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/fi.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "France",
    phone: { code: "+33", mask: "(999)999-999" },
    code: "FR",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/fr.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "French Guiana",
    phone: { code: "+594", mask: "99999-9999" },
    code: "GF",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/gf.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "French Polynesia",
    phone: { code: "+689", mask: "99-99-99" },
    code: "PF",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/pf.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Gabon",
    phone: { code: "+241", mask: "9-99-99-99" },
    code: "GA",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ga.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Gambia",
    phone: { code: "+220", mask: "(999)99-99" },
    code: "GM",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/gm.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Georgia",
    phone: { code: "+995", mask: "(999)999-999" },
    code: "GE",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ge.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Germany",
    phone: {
      code: "+49",
      mask: ["999-999", "999 99-99", "999 99-999", "999 99-9999", "************", "999 9999-9999"],
    },
    code: "DE",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/de.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Ghana",
    phone: { code: "+233", mask: "(999)999-999" },
    code: "GH",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/gh.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Gibraltar",
    phone: { code: "+350", mask: "999-99999" },
    code: "GI",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/gi.svg",
    address: { zipCodeMask: "GX11 1AA" },
  },
  {
    name: "Greece",
    phone: { code: "+30", mask: "(999)999-9999" },
    code: "GR",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/gr.svg",
    address: { zipCodeMask: "999 99" },
  },
  {
    name: "Greenland",
    phone: { code: "+299", mask: "99-99-99" },
    code: "GL",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/gl.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Grenada",
    phone: { code: "+1", mask: "(473)999-9999" },
    code: "GD",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/gd.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Guadeloupe",
    phone: { code: "+590", mask: "(999)999-999" },
    code: "GP",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/gp.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Guam",
    phone: { code: "+1", mask: "(671)999-9999" },
    code: "GU",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/gu.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Guatemala",
    phone: { code: "+502", mask: "9-999-9999" },
    code: "GT",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/gt.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Guernsey",
    phone: { code: "+44", mask: "(9999)999999" },
    code: "GG",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/gg.svg",
    address: { zipCodeMask: "GY9 9AA" },
  },
  {
    name: "Guinea",
    phone: { code: "+224", mask: "99-999-999" },
    code: "GN",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/gn.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Guinea-Bissau",
    phone: { code: "+245", mask: "9-999999" },
    code: "GW",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/gw.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Guyana",
    phone: { code: "+592", mask: "999-9999" },
    code: "GY",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/gy.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Haiti",
    phone: { code: "+509", mask: "99-99-9999" },
    code: "HT",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ht.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Holy See (Vatican City State)",
    phone: { code: "+39", mask: "06 69899999" },
    code: "VA",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/va.svg",
    address: { zipCodeMask: "00120" },
  },
  {
    name: "Honduras",
    phone: { code: "+504", mask: "9999-9999" },
    code: "HN",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/hn.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Hong Kong",
    phone: { code: "+852", mask: "9999-9999" },
    code: "HK",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/hk.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Hungary",
    phone: { code: "+36", mask: "(999)999-999" },
    code: "HU",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/hu.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Iceland",
    phone: { code: "+354", mask: "999-9999" },
    code: "IS",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/is.svg",
    address: { zipCodeMask: "999" },
  },
  {
    name: "India",
    phone: { code: "+91", mask: "(9999)999-999" },
    code: "IN",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/in.svg",
    address: { zipCodeMask: "999999" },
  },
  {
    name: "Indonesia",
    phone: { code: "+62", mask: ["99-999-99", "99-999-999", "99-999-9999", "(899)999-999", "(899)999-99-999"] },
    code: "ID",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/id.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Iran",
    phone: { code: "+98", mask: "(999)999-9999" },
    code: "IR",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ir.svg",
    address: { zipCodeMask: "99999-99999" },
  },
  {
    name: "Iraq",
    phone: { code: "+964", mask: "(999)999-9999" },
    code: "IQ",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/iq.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Ireland",
    phone: { code: "+353", mask: "(999)999-999" },
    code: "IE",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ie.svg",
    address: { zipCodeMask: "A99 A999" },
  },
  {
    name: "Isle of Man",
    phone: { code: "+44", mask: "(9999)999999" },
    code: "IM",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/im.svg",
    address: { zipCodeMask: "IM9 9AA" },
  },
  {
    name: "Israel",
    phone: { code: "+972", mask: ["9-999-9999", "59-999-9999"] },
    code: "IL",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/il.svg",
    address: { zipCodeMask: "9999999" },
  },
  {
    name: "Italy",
    phone: { code: "+39", mask: "(999)9999-999" },
    code: "IT",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/it.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Ivory Coast / Cote d'Ivoire",
    phone: { code: "+225", mask: "99-999-999" },
    code: "CI",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ci.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Jamaica",
    phone: { code: "+1", mask: "(876)999-9999" },
    code: "JM",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/jm.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Japan",
    phone: { code: "+81", mask: ["(999)999-999", "99-9999-9999"] },
    code: "JP",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/jp.svg",
    address: { zipCodeMask: "999-9999" },
  },
  {
    name: "Jersey",
    phone: { code: "+44", mask: "(9999)9999-999999" },
    code: "JE",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/je.svg",
    address: { zipCodeMask: "JE9 9AA" },
  },
  {
    name: "Jordan",
    phone: { code: "+962", mask: "9-9999-9999" },
    code: "JO",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/jo.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Kazakhstan",
    phone: { code: "+77", mask: ["(699)999-99-99", "(799)999-99-99"] },
    code: "KZ",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/kz.svg",
    address: { zipCodeMask: "999999" },
  },
  {
    name: "Kenya",
    phone: { code: "+254", mask: "999-999999" },
    code: "KE",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ke.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Kiribati",
    phone: { code: "+686", mask: "99-999" },
    code: "KI",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ki.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Korea, Democratic People's Republic of Korea",
    phone: {
      code: "+850",
      mask: ["999-999", "9999-9999", "99-999-999", "999-9999-999", "************", "9999-9999999999999"],
    },
    code: "KP",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/kp.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Korea, Republic of South Korea",
    phone: { code: "+82", mask: "99-999-9999" },
    code: "KR",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/kr.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Kosovo",
    phone: { code: "+383", mask: ["99-999-999", "999-999-999"] },
    code: "XK",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/xk.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Kuwait",
    phone: { code: "+965", mask: "9999-9999" },
    code: "KW",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/kw.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Kyrgyzstan",
    phone: { code: "+996", mask: "(999)999-999" },
    code: "KG",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/kg.svg",
    address: { zipCodeMask: "999999" },
  },
  {
    name: "Laos",
    phone: { code: "+856", mask: ["99-999-999", "(2099)999-999"] },
    code: "LA",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/la.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Latvia",
    phone: { code: "+371", mask: "99-999-999" },
    code: "LV",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/lv.svg",
    address: { zipCodeMask: "LV-9999" },
  },
  {
    name: "Lebanon",
    phone: { code: "+961", mask: ["9-999-999", "99-999-999"] },
    code: "LB",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/lb.svg",
    address: { zipCodeMask: "9999 9999" },
  },
  {
    name: "Lesotho",
    phone: { code: "+266", mask: "9-999-9999" },
    code: "LS",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ls.svg",
    address: { zipCodeMask: "999" },
  },
  {
    name: "Liberia",
    phone: { code: "+231", mask: "99-999-999" },
    code: "LR",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/lr.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Libya",
    phone: { code: "+218", mask: ["99-999-999", "21-999-9999"] },
    code: "LY",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ly.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Liechtenstein",
    phone: { code: "+423", mask: "(999)999-9999" },
    code: "LI",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/li.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Lithuania",
    phone: { code: "+370", mask: "(999)99-999" },
    code: "LT",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/lt.svg",
    address: { zipCodeMask: "LT-99999" },
  },
  {
    name: "Luxembourg",
    phone: { code: "+352", mask: "(999)999-999" },
    code: "LU",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/lu.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Macau",
    phone: { code: "+853", mask: "9999-9999" },
    code: "MO",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/mo.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Madagascar",
    phone: { code: "+261", mask: "99-99-99999" },
    code: "MG",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/mg.svg",
    address: { zipCodeMask: "999" },
  },
  {
    name: "Malawi",
    phone: { code: "+265", mask: ["1-999-999", "9-9999-9999"] },
    code: "MW",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/mw.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Malaysia",
    phone: { code: "+60", mask: ["9-999-999", "99-999-999", "(999)999-999", "99-999-9999"] },
    code: "MY",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/my.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Maldives",
    phone: { code: "+960", mask: "999-9999" },
    code: "MV",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/mv.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Mali",
    phone: { code: "+223", mask: "99-99-9999" },
    code: "ML",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ml.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Malta",
    phone: { code: "+356", mask: "9999-9999" },
    code: "MT",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/mt.svg",
    address: { zipCodeMask: "AAA 9999" },
  },
  {
    name: "Marshall Islands",
    phone: { code: "+692", mask: "999-9999" },
    code: "MH",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/mh.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Martinique",
    phone: { code: "+596", mask: "(999)99-99-99" },
    code: "MQ",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/mq.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Mauritania",
    phone: { code: "+222", mask: "99-99-9999" },
    code: "MR",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/mr.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Mauritius",
    phone: { code: "+230", mask: "999-9999" },
    code: "MU",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/mu.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Mayotte",
    phone: { code: "+262", mask: "99999-9999" },
    code: "YT",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/yt.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Mexico",
    phone: { code: "+52", mask: ["99-99-9999", "(999)999-9999"] },
    code: "MX",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/mx.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Micronesia, Federated States of Micronesia",
    phone: { code: "+691", mask: "999-9999" },
    code: "FM",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/fm.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Moldova",
    phone: { code: "+373", mask: "9999-9999" },
    code: "MD",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/md.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Monaco",
    phone: { code: "+377", mask: ["99-999-999", "(999)999-999"] },
    code: "MC",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/mc.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Mongolia",
    phone: { code: "+976", mask: "99-99-9999" },
    code: "MN",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/mn.svg",
    address: { zipCodeMask: "999999" },
  },
  {
    name: "Montenegro",
    phone: { code: "+382", mask: "99-999-999" },
    code: "ME",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/me.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Montserrat",
    phone: { code: "+1", mask: "(664)999-9999" },
    code: "MS",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ms.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Morocco",
    phone: { code: "+212", mask: "99-9999-999" },
    code: "MA",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ma.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Mozambique",
    phone: { code: "+258", mask: "99-999-999" },
    code: "MZ",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/mz.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Myanmar",
    phone: { code: "+95", mask: ["999-999", "9-999-999", "99-999-999"] },
    code: "MM",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/mm.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Namibia",
    phone: { code: "+264", mask: "99-999-9999" },
    code: "NA",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/na.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Nauru",
    phone: { code: "+674", mask: "999-9999" },
    code: "NR",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/nr.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Nepal",
    phone: { code: "+977", mask: "99-999-999" },
    code: "NP",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/np.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Netherlands",
    phone: { code: "+31", mask: "99-999-9999" },
    code: "NL",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/nl.svg",
    address: { zipCodeMask: "9999 AA" },
  },
  {
    name: "New Caledonia",
    phone: { code: "+687", mask: "99-9999" },
    code: "NC",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/nc.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "New Zealand",
    phone: { code: "+64", mask: ["9-999-999", "(999)999-999", "(999)999-9999"] },
    code: "NZ",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/nz.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Nicaragua",
    phone: { code: "+505", mask: "9999-9999" },
    code: "NI",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ni.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Niger",
    phone: { code: "+227", mask: "99-99-9999" },
    code: "NE",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ne.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Nigeria",
    phone: { code: "+234", mask: ["99-999-99", "99-999-999", "(999)999-9999"] },
    code: "NG",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ng.svg",
    address: { zipCodeMask: "999999" },
  },
  {
    name: "Niue",
    phone: { code: "+683", mask: "9999" },
    code: "NU",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/nu.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Norfolk Island",
    phone: { code: "+672", mask: "399-999" },
    code: "NF",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/nf.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "North Macedonia",
    phone: { code: "+389", mask: "99-999-999" },
    code: "MK",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/mk.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Northern Mariana Islands",
    phone: { code: "+1", mask: "(670)999-9999" },
    code: "MP",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/mp.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Norway",
    phone: { code: "+47", mask: "(999)99-999" },
    code: "NO",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/no.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Oman",
    phone: { code: "+968", mask: "99-999-999" },
    code: "OM",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/om.svg",
    address: { zipCodeMask: "999" },
  },
  {
    name: "Pakistan",
    phone: { code: "+92", mask: "(999)999-9999" },
    code: "PK",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/pk.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Palau",
    phone: { code: "+680", mask: "999-9999" },
    code: "PW",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/pw.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Palestine",
    phone: { code: "+970", mask: "99-999-9999" },
    code: "PS",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ps.svg",
    address: { zipCodeMask: "999" },
  },
  {
    name: "Panama",
    phone: { code: "+507", mask: "999-9999" },
    code: "PA",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/pa.svg",
    address: { zipCodeMask: "999999" },
  },
  {
    name: "Papua New Guinea",
    phone: { code: "+675", mask: "(999)99-999" },
    code: "PG",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/pg.svg",
    address: { zipCodeMask: "999" },
  },
  {
    name: "Paraguay",
    phone: { code: "+595", mask: "(999)999-999" },
    code: "PY",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/py.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Peru",
    phone: { code: "+51", mask: "(999)999-999" },
    code: "PE",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/pe.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Philippines",
    phone: { code: "+63", mask: "(999)999-9999" },
    code: "PH",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ph.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Pitcairn",
    phone: { code: "+870", mask: "999-999-999" },
    code: "PN",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/pn.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Poland",
    phone: { code: "+48", mask: "(999)999-999" },
    code: "PL",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/pl.svg",
    address: { zipCodeMask: "99-999" },
  },
  {
    name: "Portugal",
    phone: { code: "+351", mask: "99-999-9999" },
    code: "PT",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/pt.svg",
    address: { zipCodeMask: "9999-999" },
  },
  {
    name: "Puerto Rico",
    phone: { code: "+1", mask: ["(*************", "(*************"] },
    code: "PR",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/pr.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Qatar",
    phone: { code: "+974", mask: "9999-9999" },
    code: "QA",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/qa.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Reunion",
    phone: { code: "+262", mask: "99999-9999" },
    code: "RE",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/re.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Romania",
    phone: { code: "+40", mask: "99-999-9999" },
    code: "RO",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ro.svg",
    address: { zipCodeMask: "999999" },
  },
  {
    name: "Russia",
    phone: { code: "+7", mask: "(999)999-99-99" },
    code: "RU",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ru.svg",
    address: { zipCodeMask: "999999" },
  },
  {
    name: "Rwanda",
    phone: { code: "+250", mask: "(999)999-999" },
    code: "RW",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/rw.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Saint Barthelemy",
    phone: { code: "+590", mask: "999-99-99-99" },
    code: "BL",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/bl.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Saint Helena, Ascension and Tristan Da Cunha",
    phone: { code: "+290", mask: "9999" },
    code: "SH",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/sh.svg",
    address: { zipCodeMask: "STHL 1ZZ" },
  },
  {
    name: "Saint Kitts and Nevis",
    phone: { code: "+1", mask: "(869)999-9999" },
    code: "KN",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/kn.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Saint Lucia",
    phone: { code: "+1", mask: "(758)999-9999" },
    code: "LC",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/lc.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Saint Martin",
    phone: { code: "+590", mask: "(999)999-999" },
    code: "MF",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/mf.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Saint Pierre and Miquelon",
    phone: { code: "+508", mask: "99-9999" },
    code: "PM",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/pm.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Saint Vincent and the Grenadines",
    phone: { code: "+1", mask: "(784)999-9999" },
    code: "VC",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/vc.svg",
    address: { zipCodeMask: "VC9999" },
  },
  {
    name: "Samoa",
    phone: { code: "+685", mask: "99-9999" },
    code: "WS",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ws.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "San Marino",
    phone: { code: "+378", mask: "9999-999999" },
    code: "SM",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/sm.svg",
    address: { zipCodeMask: "47890" },
  },
  {
    name: "Sao Tome and Principe",
    phone: { code: "+239", mask: "99-99999" },
    code: "ST",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/st.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Saudi Arabia",
    phone: { code: "+966", mask: ["9-999-9999", "59-9999-9999"] },
    code: "SA",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/sa.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Senegal",
    phone: { code: "+221", mask: "99-999-9999" },
    code: "SN",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/sn.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Serbia",
    phone: { code: "+381", mask: "99-999-9999" },
    code: "RS",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/rs.svg",
    address: { zipCodeMask: "999999" },
  },
  {
    name: "Seychelles",
    phone: { code: "+248", mask: "9-999-999" },
    code: "SC",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/sc.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Sierra Leone",
    phone: { code: "+232", mask: "99-999999" },
    code: "SL",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/sl.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Singapore",
    phone: { code: "+65", mask: "9999-9999" },
    code: "SG",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/sg.svg",
    address: { zipCodeMask: "999999" },
  },
  {
    name: "Sint Maarten",
    phone: { code: "+1", mask: "(721)999-9999" },
    code: "SX",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/sx.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Slovakia",
    phone: { code: "+421", mask: "(999)999-999" },
    code: "SK",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/sk.svg",
    address: { zipCodeMask: "999 99" },
  },
  {
    name: "Slovenia",
    phone: { code: "+386", mask: "99-999-999" },
    code: "SI",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/si.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Solomon Islands",
    phone: { code: "+677", mask: ["99999", "999-9999"] },
    code: "SB",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/sb.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Somalia",
    phone: { code: "+252", mask: ["9-999-999", "99-999-999"] },
    code: "SO",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/so.svg",
    address: { zipCodeMask: "AA 99999" },
  },
  {
    name: "South Africa",
    phone: { code: "+27", mask: "99-999-9999" },
    code: "ZA",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/za.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "South Georgia and the South Sandwich Islands",
    phone: { code: "+500", mask: "99999" },
    code: "GS",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/gs.svg",
    address: { zipCodeMask: "SIQQ 1ZZ" },
  },
  {
    name: "South Sudan",
    phone: { code: "+211", mask: "99-999-9999" },
    code: "SS",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ss.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Spain",
    phone: { code: "+34", mask: "(999)999-999" },
    code: "ES",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/es.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Sri Lanka",
    phone: { code: "+94", mask: "99-999-9999" },
    code: "LK",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/lk.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Sudan",
    phone: { code: "+249", mask: "99-999-9999" },
    code: "SD",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/sd.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Suriname",
    phone: { code: "+597", mask: ["999-999", "999-9999"] },
    code: "SR",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/sr.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Svalbard and Jan Mayen",
    phone: { code: "+47", mask: "(999)99-999" },
    code: "SJ",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/sj.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Sweden",
    phone: { code: "+46", mask: "99-999-9999" },
    code: "SE",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/se.svg",
    address: { zipCodeMask: "999 99" },
  },
  {
    name: "Switzerland",
    phone: { code: "+41", mask: "99-999-9999" },
    code: "CH",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ch.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Syrian Arab Republic",
    phone: { code: "+963", mask: "99-9999-999" },
    code: "SY",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/sy.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Taiwan",
    phone: { code: "+886", mask: ["9999-9999", "9-9999-9999"] },
    code: "TW",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/tw.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Tajikistan",
    phone: { code: "+992", mask: "99-999-9999" },
    code: "TJ",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/tj.svg",
    address: { zipCodeMask: "999999" },
  },
  {
    name: "Tanzania, United Republic of Tanzania",
    phone: { code: "+255", mask: "99-999-9999" },
    code: "TZ",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/tz.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Thailand",
    phone: { code: "+66", mask: ["99-999-999", "99-999-9999"] },
    code: "TH",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/th.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Timor-Leste",
    phone: { code: "+670", mask: ["999-9999", "779-99999", "789-99999"] },
    code: "TL",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/tl.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Togo",
    phone: { code: "+228", mask: "99-999-999" },
    code: "TG",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/tg.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Tokelau",
    phone: { code: "+690", mask: "9999" },
    code: "TK",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/tk.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Tonga",
    phone: { code: "+676", mask: "99999" },
    code: "TO",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/to.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Trinidad and Tobago",
    phone: { code: "+1", mask: "(868)999-9999" },
    code: "TT",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/tt.svg",
    address: { zipCodeMask: "999999" },
  },
  {
    name: "Tunisia",
    phone: { code: "+216", mask: "99-999-999" },
    code: "TN",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/tn.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Turkey",
    phone: { code: "+90", mask: "(999)999-9999" },
    code: "TR",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/tr.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Turkmenistan",
    phone: { code: "+993", mask: "9-999-9999" },
    code: "TM",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/tm.svg",
    address: { zipCodeMask: "999999" },
  },
  {
    name: "Turks and Caicos Islands",
    phone: { code: "+1", mask: "(249)999-999" },
    code: "TC",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/tc.svg",
    address: { zipCodeMask: "TKCA 1ZZ" },
  },
  {
    name: "Tuvalu",
    phone: { code: "+688", mask: ["29999", "909999"] },
    code: "TV",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/tv.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Uganda",
    phone: { code: "+256", mask: "(999)999-999" },
    code: "UG",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ug.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Ukraine",
    phone: { code: "+380", mask: "(99)999-99-99" },
    code: "UA",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ua.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "United Arab Emirates",
    phone: { code: "+971", mask: ["9-999-9999", "59-999-9999"] },
    code: "AE",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ae.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "United Kingdom",
    phone: { code: "+44", mask: "99-9999-9999" },
    code: "GB",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/gb.svg",
    address: { zipCodeMask: "AA9A 9AA" },
  },
  {
    name: "United States",
    phone: { code: "+1", mask: "(999)999-9999" },
    code: "US",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/us.svg",
    address: { zipCodeMask: "99999-9999" },
  },
  {
    name: "Uruguay",
    phone: { code: "+598", mask: "9-999-99-99" },
    code: "UY",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/uy.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Uzbekistan",
    phone: { code: "+998", mask: "99-999-9999" },
    code: "UZ",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/uz.svg",
    address: { zipCodeMask: "999999" },
  },
  {
    name: "Vanuatu",
    phone: { code: "+678", mask: ["99999", "99-99999"] },
    code: "VU",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/vu.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Venezuela, Bolivarian Republic of Venezuela",
    phone: { code: "+58", mask: "(999)999-9999" },
    code: "VE",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ve.svg",
    address: { zipCodeMask: "9999" },
  },
  {
    name: "Vietnam",
    phone: { code: "+84", mask: ["99-9999-999", "(999)9999-999"] },
    code: "VN",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/vn.svg",
    address: { zipCodeMask: "999999" },
  },
  {
    name: "Virgin Islands, British",
    phone: { code: "+1", mask: "(284)999-9999" },
    code: "VG",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/vg.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Virgin Islands, U.S.",
    phone: { code: "+1", mask: "(340)999-9999" },
    code: "VI",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/vi.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Wallis and Futuna",
    phone: { code: "+681", mask: "99-9999" },
    code: "WF",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/wf.svg",
    address: { zipCodeMask: "98600" },
  },
  {
    name: "Yemen",
    phone: { code: "+967", mask: ["9-999-999", "99-999-999", "999-999-999"] },
    code: "YE",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/ye.svg",
    address: { zipCodeMask: "" },
  },
  {
    name: "Zambia",
    phone: { code: "+260", mask: "99-999-9999" },
    code: "ZM",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/zm.svg",
    address: { zipCodeMask: "99999" },
  },
  {
    name: "Zimbabwe",
    phone: { code: "+263", mask: "9-999999" },
    code: "ZW",
    flag_url: "https://cdn.kcak11.com/CountryFlags/countries/zw.svg",
    address: { zipCodeMask: "" },
  },
];

export const EU_COUNTRY_CODES = [
  "AT",
  "BE",
  "BG",
  "HR",
  "CY",
  "CZ",
  "DK",
  "DE",
  "EE",
  "FI",
  "FR",
  "GR",
  "HU",
  "IE",
  "IT",
  "LV",
  "LT",
  "LU",
  "MT",
  "NL",
  "PL",
  "PT",
  "RO",
  "SK",
  "SI",
  "ES",
  "SE",
];
