export function extractShopPath(url: string) {
  const quickJourneyPattern = /(\/eu\/quick-journey\/[^\/]+)/;
  const longJourneyPattern = /(\/eu\/long-journey)/;
  const directLicensePattern = /(\/direct-license)/;
  const localeEuPattern = /(\/eu)/;

  const isFullUrl = /^https?:\/\//.test(url);

  if (isFullUrl) {
    const startIndex = url.indexOf("/", 8);
    if (startIndex !== -1) {
      url = url.substring(startIndex);
    } else {
      return "/";
    }
  }

  let match = url.match(quickJourneyPattern);
  if (match) {
    return match[1];
  }

  match = url.match(longJourneyPattern);
  if (match) {
    return match[1];
  }

  match = url.match(directLicensePattern);
  if (match) {
    return match[1];
  }

  match = url.match(localeEuPattern);
  if (match) {
    return match[1];
  }

  return "/";
}
