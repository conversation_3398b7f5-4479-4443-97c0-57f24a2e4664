export function formatDateToGMT(dateString: string): string {
  const date = new Date(dateString);

  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0"); // Janeiro é 0
  const year = date.getFullYear();

  const hours = String(date.getHours()).padStart(2, "0");
  const minutes = String(date.getMinutes()).padStart(2, "0");
  const seconds = String(date.getSeconds()).padStart(2, "0");

  const timezoneOffset = -3 * 60; // Offset para GMT-0300 em minutos
  const timezone = `GMT${(timezoneOffset >= 0 ? "+" : "") + timezoneOffset / 60}00`;

  return `${day}/${month}/${year} ${hours}:${minutes}:${seconds} ${timezone}`;
}
