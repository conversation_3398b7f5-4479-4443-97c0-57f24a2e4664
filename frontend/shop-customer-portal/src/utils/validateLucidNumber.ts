const quantifier = [3, 9, 7, 1, 3, 9, 7, 1, 3, 9, 7, 1, 1];

function crossSum(number: number): number {
  let sum = 0;
  while (number !== 0) {
    sum += number % 10;
    number = Math.floor(number / 10);
  }
  return sum;
}

/**
 * Validates a LUCID number.
 * @param lucidNumber - The LUCID number to validate (expects "DE" at the start of the string).
 * @returns True if the LUCID number is valid, false otherwise.
 */
export function validateLUCIDNumber(lucidNumber: string): boolean {
  if (!/^DE\d{13}(-V)?$/.test(lucidNumber)) {
    return false;
  }

  const onlyDigitsString = lucidNumber.slice(2, 15);

  const minRegNr = BigInt("1004507420210");
  const maxRegNr = BigInt("6004507420219");
  const regNrBigInt = BigInt(onlyDigitsString);

  if (regNrBigInt < minRegNr || regNrBigInt > maxRegNr) {
    return false;
  }

  let sum = 0;
  for (let i = 0; i < onlyDigitsString.length; i++) {
    let tempSum = parseInt(onlyDigitsString[i]) * quantifier[i];
    if (tempSum >= 10) {
      tempSum = crossSum(tempSum);
    }
    sum += tempSum;
  }

  return sum % 10 === 0;
}
