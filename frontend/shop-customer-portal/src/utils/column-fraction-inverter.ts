import { ReportSet } from "@/lib/api/commitment/types";

export function setupFractionToCartFraction(fraction: ReportSet["fractions"][number]): any {
  return {
    id: fraction.id,
    name: fraction.name,
    description: fraction.description,
    icon: fraction.icon,
    isActive: fraction.is_active,
    reportSetId: fraction.report_set_id,
    parentId: fraction.parent_id,
    value: undefined,
    price: 99,
    ...(fraction.children && { children: fraction.children.map((child) => setupFractionToCartFraction(child)) }),
  };
}

export function setupColumnToCartColumn(column: ReportSet["columns"][number]): any {
  return {
    id: column.id,
    name: column.name,
    description: column.description,
    reportSetId: column.report_set_id,
    parentId: column.parent_id,
    unitType: column.unit_type,
    ...(column.children && { children: column.children.map((child) => setupColumnToCartColumn(child)) }),
  };
}
