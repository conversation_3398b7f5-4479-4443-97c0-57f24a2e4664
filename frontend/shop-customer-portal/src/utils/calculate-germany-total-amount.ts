import { PriceList } from "@/lib/api/shoppingCart/types";

interface CalculateDirectLicenseNetValueParams {
  fractions: {
    name: string;
    code: string;
    weight: number;
  }[];
  currentFractions:
    | {
        name: string;
        code: string;
        weight: number;
      }[]
    | null;
  priceList: PriceList;
}

export function calculateDirectLicenseNetValue({
  fractions,
  currentFractions,
  priceList,
}: CalculateDirectLicenseNetValueParams) {
  if (!fractions || !priceList) return 0;

  const thresholds = priceList.thresholds!;

  function calculateTotalForThreshold(thresholdIndex: number): number {
    const currentThreshold = thresholds[thresholdIndex];

    const total = fractions.reduce((acc, curr) => {
      const fractionPriceInCents = (currentThreshold.fractions[curr.code].value || 0) / 10; // Convert to cents
      const fractionWeight = fractions?.find((fraction) => fraction.code === curr.code)?.weight || 0;
      const currentFractionWeight = currentFractions?.find((fraction) => fraction.code === curr.code)?.weight || 0;

      if (currentFractions) {
        if (fractionWeight === 0) return acc;

        const weightDifference = (curr.weight || 0) - (currentFractionWeight || 0);

        const fractionWeightInKilograms = weightDifference / 1000; // Transform weight from grams (g) to kilograms (kg)
        return acc + fractionPriceInCents * fractionWeightInKilograms;
      }

      return acc + fractionPriceInCents * ((curr.weight || 0) / 1000);
    }, 0);

    // If there's a next threshold and total exceeds its value, recalculate with next threshold
    if (thresholds[thresholdIndex + 1] && total > thresholds[thresholdIndex + 1].value) {
      return calculateTotalForThreshold(thresholdIndex + 1);
    }

    return total;
  }

  let total = calculateTotalForThreshold(0);

  if (!currentFractions) {
    total = total + (priceList.basic_price || 0);

    total = total > (priceList.minimum_price || 0) ? total : priceList.minimum_price || 0;
  }

  return total;
}
