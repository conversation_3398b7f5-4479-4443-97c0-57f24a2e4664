import { jwtDecode } from "jwt-decode";
import { UserTypes } from "./user";

interface DecodedToken {
  id: string;
  first_name: string;
  last_name: string;
  email: string;
  role: UserTypes;
  has_password: boolean;
  exp: number;
}

export function decodeToken(token?: string) {
  try {
    if (!token) return null;

    const decodedToken = jwtDecode<DecodedToken>(token);

    return decodedToken;
  } catch (error) {
    return null;
  }
}
