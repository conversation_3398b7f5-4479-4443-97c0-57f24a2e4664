import { ReportSetColumn } from "@/lib/api/commitment/types";
import { CreateVolumeReportItemsParam } from "@/lib/api/volume-report-item";
import { VolumeReport } from "@/lib/api/volume-report/types";

type Volumes = Record<string, CreateVolumeReportItemsParam>;

function getXmlIndent(level: number) {
  return " ".repeat(level * 2);
}

function escapeXML(str: string) {
  return str
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&apos;");
}

function createColumn({
  columns,
  volumes,
  level,
  id,
}: {
  columns: ReportSetColumn[];
  volumes: Volumes;
  level: number;
  id: number;
}) {
  let xmlContent = ``;

  xmlContent += `${getXmlIndent(level)}<columns>\n`;
  columns.map((column) => {
    column.children?.map((secondLevelColumn) => {
      xmlContent += `${getXmlIndent(level + 1)}<column>\n`;
      xmlContent += `${getXmlIndent(level + 2)}<column_name>${escapeXML(secondLevelColumn.name)}</column_name>\n`;
      xmlContent += `${getXmlIndent(level + 2)}<values>\n`;

      if (volumes[`${id}_${secondLevelColumn.id}`]?.value)
        xmlContent += `${getXmlIndent(level + 3)}<value>${
          volumes[`${id}_${secondLevelColumn.id}`]?.value / 1000
        }</value>\n`;

      xmlContent += `${getXmlIndent(level + 2)}</values>\n`;
      xmlContent += `${getXmlIndent(level + 1)}</column>\n`;
    });
  });
  xmlContent += `${getXmlIndent(level)}</columns>\n`;

  return xmlContent;
}

export function generateVolumesXml({
  selectedVolumeReport,
  volumes,
}: {
  selectedVolumeReport: VolumeReport;
  volumes: Volumes;
}) {
  let xmlContent = `<?xml version="1.0" encoding="UTF-8"?>\n`;
  xmlContent += `<fractions>\n`;

  selectedVolumeReport.report_table?.fractions.map((firstLevelFraction) => {
    xmlContent += `${getXmlIndent(1)}<first_fractions>\n`;
    xmlContent += `${getXmlIndent(2)}<first_name_fraction>${escapeXML(
      firstLevelFraction.name
    )}</first_name_fraction>\n`;

    xmlContent += createColumn({
      columns: selectedVolumeReport.report_table?.columns,
      id: firstLevelFraction.id,
      level: 2,
      volumes,
    });

    firstLevelFraction.children?.map((secondLevelFraction) => {
      xmlContent += `${getXmlIndent(2)}<second_fractions>\n`;
      xmlContent += `${getXmlIndent(3)}<second_name_fraction>${escapeXML(
        secondLevelFraction.name
      )}</second_name_fraction>\n`;

      xmlContent += createColumn({
        columns: selectedVolumeReport.report_table?.columns,
        id: secondLevelFraction.id,
        level: 3,
        volumes,
      });

      secondLevelFraction.children?.map((thirdLevelFraction) => {
        xmlContent += `${getXmlIndent(3)}<third_fractions>\n`;
        xmlContent += `${getXmlIndent(4)}<third_name_fraction>${escapeXML(
          thirdLevelFraction.name
        )}</third_name_fraction>\n`;

        xmlContent += createColumn({
          columns: selectedVolumeReport.report_table?.columns,
          id: thirdLevelFraction.id,
          level: 4,
          volumes,
        });

        xmlContent += `${getXmlIndent(3)}</third_fractions>\n`;
      });
      xmlContent += `${getXmlIndent(2)}</second_fractions>\n`;
    });

    xmlContent += `${getXmlIndent(1)}</first_fractions>\n`;
  });
  xmlContent += `</fractions>\n`;

  return xmlContent;
}
