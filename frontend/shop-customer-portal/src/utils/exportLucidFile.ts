import { VolumeReport } from "@/lib/api/volume-report/types";
import { GERMANY_FRACTIONS } from "@/utils/consts/direct-license";
import { enqueueSnackbar } from "notistack";
import { formatWeight } from "./format-weight";

export function exportLucidXML(lucidNumber: string, volumeReport: VolumeReport) {
  const date = new Date().toISOString().split("T")[0];
  const currentDate = new Date();
  const reportYear = volumeReport.year;

  let typeOfReportCode = "HMM1";

  const fractions: { code: string; name: string; value: number }[] = (() => {
    return GERMANY_FRACTIONS.map((fraction) => ({
      code: fraction.code,
      name: fraction.name,
      value:
        volumeReport?.volume_report_items.find((item) => String(item.setup_fraction_code) === fraction.code)?.value ||
        0,
    }));
  })();

  try {
    if (currentDate <= new Date(`${reportYear - 1}-12-31`)) {
      typeOfReportCode = "HPM1";
    } else if (
      currentDate >= new Date(`${reportYear + 1}-01-01`) &&
      currentDate <= new Date(`${reportYear + 1}-05-15`)
    ) {
      typeOfReportCode = "HJM1";
    }

    const xmlContent = `
      <?xml version="1.0" encoding="UTF-8"?>
        <VerpackungsregisterSchnittstelle xmlns="http://www.verpackungsregister.org/xml/schema">
          <ReportingParty>
            <ProducerNumber>${lucidNumber}</ProducerNumber>
          </ReportingParty>
          <Quantity>
            <TypeOfReportCode>${typeOfReportCode}</TypeOfReportCode>
            <ReportingPeriodFrom>${reportYear}-01-01</ReportingPeriodFrom>
            <ReportingPeriodTo>${reportYear}-12-31</ReportingPeriodTo>
            <SystemOperatorID>DE6161328237553</SystemOperatorID>
            <ParticipationQuantities>
              ${fractions
                .map(
                  (fraction) => `
                <Material>
                  <MaterialCode>${fraction.code}</MaterialCode>
                  <MaterialName>${fraction.name}</MaterialName>
                  <Mass>${formatWeight(fraction.value)}</Mass>
                </Material>
              `
                )
                .join("\n")}
            </ParticipationQuantities>
          </Quantity>
        </VerpackungsregisterSchnittstelle>
    `.trim();

    const blob = new Blob([xmlContent], { type: "application/xml" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `LUCID_report_${reportYear}_${typeOfReportCode}_${date}.xml`;

    document.body.appendChild(a);
    a.click();

    setTimeout(() => {
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    }, 100);
  } catch (error) {
    console.error("Error generating XML:", error);
    enqueueSnackbar("There was an error generating the XML file. Please try again.", { variant: "error" });
  }
}
