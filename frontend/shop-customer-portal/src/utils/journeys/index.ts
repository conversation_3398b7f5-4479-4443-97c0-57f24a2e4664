export type JourneyType = "LONG" | "DIRECT_LICENSE" | "QUICK_LICENSE" | "QUICK_ACTION_GUIDE";

export const JOURNEY_STEPS = {
  SELECT_COUNTRIES: "/select-countries",
  CREATE_ACCOUNT: "/create-account",
  SET_PASSWORD: "/set-password",
  OBLIGATIONS: "/obligations",
  SELECT_SERVICES: "/select-services",
  CALCULATOR: "/calculator",
  SHOPPING_CART: "/shopping-cart",
  COMPANY_INFORMATIONS: "/informations",
  BILLING: "/billing",
  PURCHASE: "/purchase",
  CONCLUSION: "/conclusion",
};

export type JourneyStep = keyof typeof JOURNEY_STEPS;

export const JOURNEY_RULES = {
  AUTHENTICATION: "AUTHENTICATION",
  PASSWORD: "PASSWORD",
  CART_ITEMS: "CART_ITEMS",
  COMMITMENT: "COMMITMENT",
  CUSTOMER_ID: "CUSTOMER_ID",
  COMPANY_ID: "COMPANY_ID",
  PAYMENT_METHOD: "PAYMENT_METHOD",
};

export type JourneyRule = keyof typeof JOURNEY_RULES;

export type Journey = {
  type: JourneyType;
  basePath: string;
  steps: {
    [key: string]: {
      rules?: JourneyRule[];
      skip_rules?: JourneyRule[];
    };
  };
};

export const JOURNEYS: Record<JourneyType, Journey> = {
  LONG: {
    type: "LONG",
    basePath: "/eu/long-journey",
    steps: {
      SELECT_COUNTRIES: {},
      CREATE_ACCOUNT: {
        skip_rules: ["CUSTOMER_ID"],
      },
      OBLIGATIONS: {
        rules: ["CART_ITEMS", "COMMITMENT"],
      },
      SELECT_SERVICES: {
        rules: ["AUTHENTICATION", "CART_ITEMS"],
      },
      CALCULATOR: {
        rules: ["AUTHENTICATION", "CART_ITEMS"],
      },
      SHOPPING_CART: {
        rules: ["AUTHENTICATION", "CART_ITEMS"],
      },
      SET_PASSWORD: {
        rules: ["AUTHENTICATION", "CUSTOMER_ID"],
        skip_rules: ["PASSWORD"],
      },
      COMPANY_INFORMATIONS: {
        rules: ["AUTHENTICATION", "PASSWORD", "CUSTOMER_ID", "CART_ITEMS"],
        skip_rules: ["COMPANY_ID"],
      },
      BILLING: {
        rules: ["AUTHENTICATION", "PASSWORD", "CUSTOMER_ID", "CART_ITEMS", "COMPANY_ID"],
        skip_rules: ["PAYMENT_METHOD"],
      },
      PURCHASE: {
        rules: ["AUTHENTICATION", "PASSWORD", "CUSTOMER_ID", "CART_ITEMS", "COMPANY_ID", "PAYMENT_METHOD"],
      },
      CONCLUSION: {
        rules: ["AUTHENTICATION", "PASSWORD", "CUSTOMER_ID", "COMPANY_ID", "PAYMENT_METHOD"],
      },
    },
  },
  QUICK_LICENSE: {
    type: "QUICK_LICENSE",
    basePath: "/eu/quick-journey/license",
    steps: {
      CALCULATOR: {},
      SHOPPING_CART: {
        rules: ["CART_ITEMS", "COMMITMENT"],
      },
      CREATE_ACCOUNT: {
        skip_rules: ["CUSTOMER_ID"],
      },
      SET_PASSWORD: {
        rules: ["AUTHENTICATION", "CUSTOMER_ID"],
        skip_rules: ["PASSWORD"],
      },
      COMPANY_INFORMATIONS: {
        rules: ["AUTHENTICATION", "PASSWORD", "CUSTOMER_ID", "CART_ITEMS", "COMMITMENT"],
        skip_rules: ["COMPANY_ID"],
      },
      BILLING: {
        rules: ["AUTHENTICATION", "PASSWORD", "CUSTOMER_ID", "CART_ITEMS", "COMPANY_ID", "COMMITMENT"],
        skip_rules: ["PAYMENT_METHOD"],
      },
      PURCHASE: {
        rules: [
          "AUTHENTICATION",
          "PASSWORD",
          "CUSTOMER_ID",
          "CART_ITEMS",
          "COMPANY_ID",
          "PAYMENT_METHOD",
          "COMMITMENT",
        ],
      },
      CONCLUSION: {
        rules: ["AUTHENTICATION", "PASSWORD", "CUSTOMER_ID", "COMPANY_ID", "PAYMENT_METHOD"],
      },
    },
  },
  QUICK_ACTION_GUIDE: {
    type: "QUICK_ACTION_GUIDE",
    basePath: "/eu/quick-journey/action-guide",
    steps: {
      SHOPPING_CART: {},
      CREATE_ACCOUNT: {
        skip_rules: ["CUSTOMER_ID"],
      },
      SET_PASSWORD: {
        rules: ["AUTHENTICATION", "CUSTOMER_ID"],
        skip_rules: ["PASSWORD"],
      },
      COMPANY_INFORMATIONS: {
        rules: ["AUTHENTICATION", "PASSWORD", "CUSTOMER_ID", "CART_ITEMS"],
        skip_rules: ["COMPANY_ID"],
      },
      BILLING: {
        rules: ["AUTHENTICATION", "PASSWORD", "CUSTOMER_ID", "CART_ITEMS", "COMPANY_ID"],
        skip_rules: ["PAYMENT_METHOD"],
      },
      PURCHASE: {
        rules: ["AUTHENTICATION", "PASSWORD", "CUSTOMER_ID", "CART_ITEMS", "COMPANY_ID", "PAYMENT_METHOD"],
      },
      CONCLUSION: {
        rules: ["AUTHENTICATION", "PASSWORD", "CUSTOMER_ID", "COMPANY_ID", "PAYMENT_METHOD"],
      },
    },
  },
  DIRECT_LICENSE: {
    type: "DIRECT_LICENSE",
    basePath: "/direct-license",
    steps: {
      CALCULATOR: {},
      CREATE_ACCOUNT: {
        skip_rules: ["CUSTOMER_ID"],
      },
      SET_PASSWORD: {
        rules: ["AUTHENTICATION", "CUSTOMER_ID"],
        skip_rules: ["PASSWORD"],
      },
      COMPANY_INFORMATIONS: {
        rules: ["AUTHENTICATION", "PASSWORD", "CUSTOMER_ID", "CART_ITEMS"],
        skip_rules: ["COMPANY_ID"],
      },
      BILLING: {
        rules: ["AUTHENTICATION", "PASSWORD", "CUSTOMER_ID", "CART_ITEMS", "COMPANY_ID"],
        skip_rules: ["PAYMENT_METHOD"],
      },
      PURCHASE: {
        rules: ["AUTHENTICATION", "PASSWORD", "CUSTOMER_ID", "CART_ITEMS", "COMPANY_ID", "PAYMENT_METHOD"],
      },
      CONCLUSION: {
        rules: ["AUTHENTICATION", "PASSWORD", "CUSTOMER_ID", "COMPANY_ID", "PAYMENT_METHOD"],
      },
    },
  },
} as const;

export function getJourneyPath(journeyType: JourneyType, journeyStep?: JourneyStep) {
  const journey = JOURNEYS[journeyType];

  if (!journey) return null;

  if (!journeyStep) return journey.basePath;

  const journeyStepPath = JOURNEY_STEPS[journeyStep];

  if (!journeyStepPath) return null;

  return `${journey.basePath}${journeyStepPath}`;
}
