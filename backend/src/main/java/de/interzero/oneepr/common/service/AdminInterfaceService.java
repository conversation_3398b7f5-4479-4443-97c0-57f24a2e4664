package de.interzero.oneepr.common.service;

import de.interzero.oneepr.admin.service_setup.ServiceSetupService;
import de.interzero.oneepr.admin.service_setup.dto.ServiceSetupCommitmentResponseDto;
import de.interzero.oneepr.admin.service_setup.dto.SubmitCommitmentDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AdminInterfaceService {

    private final ServiceSetupService serviceSetupService;

    public ServiceSetupCommitmentResponseDto submitServiceSetupCommitment(String countryCode,
                                                                          SubmitCommitmentDto data) {
        return serviceSetupService.submitServiceSetupCommitment(countryCode, data);
    }

}
