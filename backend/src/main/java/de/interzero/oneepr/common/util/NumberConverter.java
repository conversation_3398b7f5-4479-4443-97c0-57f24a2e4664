package de.interzero.oneepr.common.util;

/**
 * Utility class for converting numbers between different formats.
 * Some conversions and error checks in the original JS code happened as a result of library functions, since
 * java does not have a direct equivalent for some of these operations, they are implemented here.
 */
public class NumberConverter {


    private NumberConverter() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * Converts a Long value to an Integer, checking for potential loss of information.
     *
     * @param longValue the Long value to convert
     * @return the converted Integer value
     */
    public static Integer longToInteger(Long longValue) {
        // Check for potential loss of information
        if (longValue < Integer.MIN_VALUE || longValue > Integer.MAX_VALUE) {
            throw new IllegalArgumentException("Loss of information: value is out of Integer range");
        }

        return longValue.intValue();
    }
}
