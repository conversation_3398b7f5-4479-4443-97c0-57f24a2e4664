package de.interzero.oneepr.common;

import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.web.server.ResponseStatusException;

/**
 * Utility class for authentication related tasks
 */
@Slf4j
public class AuthUtil {

    private AuthUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * @return AuthenticatedUser instance for the current user.
     * @throws ResponseStatusException if the user is not authenticated or the ID is missing.
     */
    public static AuthenticatedUser getRelevantUserDetails() {
        Authentication authentication = getAuthenticationInternal();

        String id = extractUserId(authentication);
        Role determinedRole = extractUserRole(authentication, id);
        String email = extractUserEmail(authentication, id);

        return new AuthenticatedUser(id, determinedRole, email);
    }

    /**
     * Returns AuthenticatedUser if a valid authenticated principal exists; otherwise returns null.
     * Unlike getRelevantUserDetails(), this method does not throw when the request is anonymous.
     */
    public static AuthenticatedUser getUserDetailsOrNull() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null || authentication instanceof AnonymousAuthenticationToken || !authentication.isAuthenticated()) {
            return null;
        }

        String id = authentication.getName();
        if (id == null || id.trim().isEmpty()) {
            return null;
        }

        Role determinedRole = extractUserRole(authentication, id);
        String email = extractUserEmail(authentication, id);

        return new AuthenticatedUser(id, determinedRole, email);
    }

    /**
     * Extracts the user ID from the Authentication object.
     *
     * @param authentication The Authentication object.
     * @return The user ID.
     * @throws ResponseStatusException if the user ID is null or empty.
     */
    private static String extractUserId(Authentication authentication) {
        String id = authentication.getName();
        if (id == null || id.trim().isEmpty()) {
            log.error("Authenticated user ID (from authentication.getName()) is null or empty.");
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "Authenticated user ID is missing.");
        }
        return id;
    }

    /**
     * Determines the user's Role based on their authorities.
     *
     * @param authentication The Authentication object.
     * @param userId         The user ID, for logging purposes.
     * @return The determined Role, or null if no mappable role is found.
     */
    private static Role extractUserRole(Authentication authentication,
                                        String userId) {
        Role determinedRole = null;
        for (GrantedAuthority authority : authentication.getAuthorities()) {
            String authorityString = authority.getAuthority();
            String potentialRoleString = authorityString.startsWith("ROLE_") ? authorityString.substring(5) : authorityString;
            try {
                determinedRole = Role.valueOf(potentialRoleString.toUpperCase());
                break;
            } catch (IllegalArgumentException e) {
                log.trace("Authority '{}' for user '{}' not mapped to a Role enum value.", authorityString, userId);
            }
        }

        if (determinedRole == null) {
            log.warn(
                    "No mappable Role found for user '{}'. Role will be null. Authorities: {}",
                    userId,
                    authentication.getAuthorities());
        }
        return determinedRole;
    }

    /**
     * Extracts the user's email from the Authentication principal, if available.
     *
     * @param authentication The Authentication object.
     * @param userId         The user ID, for logging purposes.
     * @return The user's email, or null if not found or empty.
     */
    private static String extractUserEmail(Authentication authentication,
                                           String userId) {
        String email = null;
        Object principal = authentication.getPrincipal();

        if (principal instanceof Jwt jwtPrincipal) {
            email = jwtPrincipal.getClaimAsString("email");
        }

        if (email != null && email.trim().isEmpty()) {
            log.debug("Extracted email for user '{}' is present but empty/whitespace. Setting to null.", userId);
            email = null;
        }

        if (email == null) {
            log.debug("Email not found in principal or was empty for user '{}'. Email will be null.", userId);
        }
        return email;
    }

    private static Authentication getAuthenticationInternal() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        if (authentication == null) {
            log.warn("No Authentication object found in SecurityContext. User is not authenticated.");
            throw new ResponseStatusException(
                    HttpStatus.UNAUTHORIZED,
                                              "User not authenticated (no authentication token).");
        }

        if (authentication instanceof AnonymousAuthenticationToken) {
            log.warn("Authentication token is an AnonymousAuthenticationToken. User is considered anonymous.");
            throw new ResponseStatusException(HttpStatus.UNAUTHORIZED, "User is anonymous.");
        }

        if (!authentication.isAuthenticated()) {
            log.warn("Authentication object indicates user is not authenticated (isAuthenticated() is false).");
            throw new ResponseStatusException(
                    HttpStatus.UNAUTHORIZED,
                                              "User authentication is invalid (not authenticated).");
        }

        return authentication;
    }

}
