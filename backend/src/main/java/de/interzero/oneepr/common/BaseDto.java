package de.interzero.oneepr.common;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.HashSet;
import java.util.Set;

/**
 * This class exists to differentiate between fields that were sent in the request payload vs those that were not.
 * In JavaScript, this is normally done by checking if a field is `undefined`. However, in Java, we cannot
 * use `undefined` as a value. Instead, we use this class to track which fields were included in the request.
 */
public abstract class BaseDto {

    /**
     * Set of field names that were present in the JSON input during deserialization.
     */
    @JsonIgnore
    private Set<String> presentFields = new HashSet<>();

    /**
     * Returns a set of field names that were present in the JSON input.
     * This is useful for determining which fields were actually provided
     * during deserialization.
     *
     * @return a set of field names that were present in the JSON input
     */
    @JsonIgnore
    public Set<String> getPresentFields() {
        return new HashSet<>(presentFields);
    }

    /**
     * Package-private method to set the present fields. Used by the custom deserializer.
     *
     * @param presentFields the set of field names that were present in the JSON input
     */
    void setPresentFields(Set<String> presentFields) {
        this.presentFields = presentFields;
    }
}
