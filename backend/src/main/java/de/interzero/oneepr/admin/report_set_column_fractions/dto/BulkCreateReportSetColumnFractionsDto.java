package de.interzero.oneepr.admin.report_set_column_fractions.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * Data Transfer Object for creating multiple ReportSetColumnFraction associations in bulk.
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BulkCreateReportSetColumnFractionsDto extends BaseDto {

    @JsonProperty("column_id")
    @Schema(
            description = "ID of the associated report set column",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private Integer columnId;

    @JsonProperty("fraction_ids")
    @Schema(
            description = "Array of IDs of the associated report set fractions",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private List<Integer> fractionIds;
}