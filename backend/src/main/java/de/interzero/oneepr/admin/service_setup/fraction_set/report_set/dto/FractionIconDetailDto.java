package de.interzero.oneepr.admin.service_setup.fraction_set.report_set.dto;

import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.time.Instant;

/**
 * This DTO represents the detailed, serializable view of a FractionIcon entity.
 * It is a direct translation of the FractionIcon entity's fields intended for API responses.
 * It is designed to be returned as a nested object within other DTOs like ReportSetFractionDetailDto.
 */
@Data
@JsonFilter("fractionIconFilter")
public class FractionIconDetailDto {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("image_url")
    private String imageUrl;

    @JsonProperty("created_at")
    private Instant createdAt;

    @JsonProperty("updated_at")
    private Instant updatedAt;

    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @JsonProperty("file_id")
    private String fileId;
}