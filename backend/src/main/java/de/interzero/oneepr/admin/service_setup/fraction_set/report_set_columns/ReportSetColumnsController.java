package de.interzero.oneepr.admin.service_setup.fraction_set.report_set_columns;

import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_columns.dto.CreateReportSetColumnDto;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.Role;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

/**
 * REST controller for managing Report Set Column entities.
 * Provides endpoints for creating, reading, and deleting report set columns.
 * Access is restricted to specific administrative roles.
 */
@RestController
@RequestMapping(Api.REPORT_SET_COLUMNS)
@Tag(name = "ReportSetColumns")
@RequiredArgsConstructor
@Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
public class ReportSetColumnsController {

    private final ReportSetColumnsService reportSetColumnsService;

    /**
     * Creates a new report set column.
     *
     * @param data DTO containing the details for the new column.
     * @return The newly created ReportSetColumn entity.
     */
    @PostMapping
    @Operation(summary = "Create a new report set column")
    @ApiResponse(
            responseCode = "201",
            description = "Report set column created successfully",
            content = @Content(schema = @Schema(implementation = ReportSetColumn.class))
    )
    @ApiResponse(
            responseCode = "404",
            description = "Report set not found or Parent column not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid report set ID or parent ID"
    )
    public ReportSetColumn create(@RequestBody CreateReportSetColumnDto data) {
        return this.reportSetColumnsService.create(data);
    }

    /**
     * Retrieves all active report set columns.
     *
     * @return A list of ReportSetColumn entities.
     */
    @GetMapping
    @Operation(summary = "Get all report set columns")
    @ApiResponse(
            responseCode = "200",
            description = "Report set columns retrieved successfully"
    )
    public List<ReportSetColumn> findAll() {
        return this.reportSetColumnsService.findAll();
    }

    /**
     * Retrieves a single report set column by its ID.
     *
     * @param id The string representation of the column's ID.
     * @return The found ReportSetColumn entity.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get report set column by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Report set column retrieved successfully",
            content = @Content(schema = @Schema(implementation = ReportSetColumn.class))
    )
    @ApiResponse(
            responseCode = "404",
            description = "Report set column not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid report set column ID"
    )
    public ReportSetColumn findOne(@PathVariable String id) {
        try {
            return this.reportSetColumnsService.findOne(Integer.valueOf(id));
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid report set column ID format");
        }
    }

    /**
     * Soft-deletes a report set column and its children by its ID.
     *
     * @param id The string representation of the column's ID.
     * @return An HTTP 200 OK response on successful deletion.
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete report set column by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Report set column deleted successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Report set column not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid report set column ID"
    )
    public ResponseEntity<Void> remove(@PathVariable String id) {
        try {
            this.reportSetColumnsService.remove(Integer.valueOf(id));
            return ResponseEntity.ok().build();
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid report set column ID format");
        }
    }
}