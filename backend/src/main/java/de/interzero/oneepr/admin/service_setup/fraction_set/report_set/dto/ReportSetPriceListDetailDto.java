package de.interzero.oneepr.admin.service_setup.fraction_set.report_set.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list.ReportSetPriceList;
import lombok.Data;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

/**
 * Represents a ReportSetPriceList for response serialization.
 * It is a sub-component of the ReportSetDetailDto.
 */
@Data
public class ReportSetPriceListDetailDto {
    @JsonProperty("id")
    private Integer id;

    @JsonProperty("title")
    private String title;

    @JsonProperty("start_date")
    private Instant startDate;

    @JsonProperty("end_date")
    private Instant endDate;

    @JsonProperty("base_price")
    private Integer basePrice;

    @JsonProperty("fixed_price")
    private Integer fixedPrice;

    @JsonProperty("minimum_fee")
    private Integer minimumFee;

    @JsonProperty("type")
    private ReportSetPriceList.Type type;

    @JsonProperty("license_year")
    private Integer licenseYear;

    @JsonProperty("items")
    private List<ReportSetPriceListItemDetailDto> items = new ArrayList<>();
}