package de.interzero.oneepr.admin.report_set_columns;

import de.interzero.oneepr.admin.report_set.ReportSetRepository;
import de.interzero.oneepr.admin.report_set_columns.dto.CreateReportSetColumnDto;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.security.SecureRandom;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

/**
 * Service class for managing Report Set Column business logic.
 */
@Service
@RequiredArgsConstructor
public class ReportSetColumnsService {

    private final ReportSetColumnRepository reportSetColumnRepository;

    private final ReportSetRepository reportSetRepository;

    private final ModelMapper modelMapper;

    private final SecureRandom secureRandom = new SecureRandom();

    private static final String REPORT_SET_NOT_FOUND = "Report set not found";

    private static final String PARENT_COLUMN_NOT_FOUND = "Parent column not found";

    private static final String REPORT_SET_COLUMN_NOT_FOUND = "Report set column not found";

    /**
     * Creates a new ReportSetColumn after validating its parent entities.
     * Generates a unique random code for the new column.
     *
     * @param createDto The DTO containing data for the new column.
     * @return The newly created and persisted ReportSetColumn entity.
     */
    @Transactional
    public ReportSetColumn create(CreateReportSetColumnDto createDto) {
        // --- Validation Step ---
        var reportSet = reportSetRepository.findById(createDto.getReportSetId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, REPORT_SET_NOT_FOUND));

        ReportSetColumn parentColumn = null;
        if (createDto.getParentId() != null) {
            parentColumn = reportSetColumnRepository.findById(createDto.getParentId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, PARENT_COLUMN_NOT_FOUND));
        }

        // --- Creation Step ---
        ReportSetColumn newColumn = modelMapper.map(createDto, ReportSetColumn.class);

        // Set relationships and generated values
        newColumn.setReportSet(reportSet);
        if (parentColumn != null) {
            newColumn.setParent(parentColumn);
        }

        byte[] randomBytes = new byte[16];
        secureRandom.nextBytes(randomBytes);
        newColumn.setCode(bytesToHex(randomBytes));

        return reportSetColumnRepository.save(newColumn);
    }

    /**
     * Retrieves all non-deleted report set columns.
     *
     * @return A list of active ReportSetColumn entities.
     */
    @Transactional(readOnly = true)
    public List<ReportSetColumn> findAll() {
        return reportSetColumnRepository.findAllByDeletedAtIsNull();
    }

    /**
     * Retrieves a single, non-deleted report set column by its ID.
     *
     * @param id The ID of the report set column to retrieve.
     * @return The found ReportSetColumn entity.
     */
    @Transactional(readOnly = true)
    public ReportSetColumn findOne(Integer id) {
        return reportSetColumnRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, REPORT_SET_COLUMN_NOT_FOUND));
    }

    /**
     * Soft-deletes a report set column and all of its direct children.
     * This operation is performed within a single transaction.
     *
     * @param id The ID of the report set column to soft-delete.
     */
    @Transactional
    public void remove(Integer id) {
        ReportSetColumn columnToRemove = reportSetColumnRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, REPORT_SET_COLUMN_NOT_FOUND));

        List<ReportSetColumn> children = reportSetColumnRepository.findByParentId(id);

        Instant deletionTime = Instant.now();

        List<ReportSetColumn> allToDelete = new ArrayList<>(children);
        allToDelete.add(columnToRemove);

        allToDelete.forEach(column -> column.setDeletedAt(deletionTime));

        reportSetColumnRepository.saveAll(allToDelete);
    }

    /**
     * Utility method to convert a byte array into a zero-padded, lowercase hexadecimal string.
     *
     * @param bytes The byte array to convert; must not be {@code null}.
     * @return The resulting hexadecimal {@link String}.
     * @ts-legacy This method is a direct translation of the Node.js `crypto.randomBytes(size).toString("hex")`
     * pattern. It is required because the standard Java libraries do not provide a built-in function
     * for direct byte-array-to-hex-string conversion. This custom implementation ensures faithful
     * replication of the original system's unique code generation logic.
     */
    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder(2 * bytes.length);
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
}