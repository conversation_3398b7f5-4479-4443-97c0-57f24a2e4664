package de.interzero.oneepr.admin.country;

import de.interzero.oneepr.admin.country.dto.*;
import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.Criteria;
import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.CriteriaRepository;
import de.interzero.oneepr.common.config.ModelMapperConfig;
import de.interzero.oneepr.customer.customer.CustomerService;
import de.interzero.oneepr.customer.customer.dto.GroupByCountryDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ResponseStatusException;

import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Service class for managing Country entities and related business logic.
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CountryService {

    private final CustomerService customerService;

    private final CountryRepository countryRepository;

    private final ModelMapper modelMapper;

    private final CriteriaRepository criteriaRepository;

    private static final String COUNTRY_NOT_FOUND = "Country not found";

    private final CountryMapper countryMapper;

    /**
     * Creates and persists a new Country.
     *
     * @param createDto The DTO containing the data for the new country.
     * @return The newly created and persisted Country entity.
     */
    @Transactional
    public CountryResponseDto create(CreateCountryDto createDto) {
        Country country = modelMapper.map(createDto, Country.class);
        return countryMapper.toResponseDto(countryRepository.save(country));
    }

    /**
     * Retrieves all countries from the database, sorted alphabetically by name.
     *
     * @return A list of all Country entities.
     */
    @Transactional(readOnly = true)
    public List<CountryResponseDto> findAll(Boolean licenseRequired) {
        Sort sortByName = Sort.by("name");
        if (licenseRequired != null) {
            Specification<Country> spec = (root, query, cb) -> cb.equal(root.get("licenseRequired"), licenseRequired);
            return countryRepository.findAll(spec, sortByName).stream().map(countryMapper::toResponseDto).toList();
        }
        return countryRepository.findAll(sortByName).stream().map(countryMapper::toResponseDto).toList();
    }

    /**
     * Retrieves a single country by its unique identifier.
     *
     * @param id The ID of the country to retrieve.
     * @return The found Country entity.
     * @throws ResponseStatusException if no country with the given ID is found.
     */
    @Transactional(readOnly = true)
    public CountryResponseDto findOne(Integer id) {
        return countryRepository.findById(id).map(countryMapper::toResponseDto)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, COUNTRY_NOT_FOUND));
    }

    /**
     * Retrieves a list of countries, enriched with customer count data.
     * Supports optional searching by country name or code.
     *
     * @param search An optional string to filter countries by name or code (case-insensitive).
     * @return A list of {@link CountryOverviewDto} objects containing combined data.
     * @ts-legacy This method was refactored. Instead of making a placeholder external API call,
     * it now enriches country data by calling the internal {@code CustomerService#groupByCountry()}
     * method, which provides aggregated customer and license counts. This aligns with the
     * implemented business logic for customer data aggregation.
     */
    @Transactional(readOnly = true)
    public List<CountryOverviewDto> overview(String search) {
        // Step 1: Build the database query specification.
        Specification<Country> spec = Specification.where(null);
        if (StringUtils.hasText(search)) {
            spec = (root, query, cb) -> cb.or(
                    cb.like(cb.lower(root.get("name")), "%" + search.toLowerCase() + "%"),
                    cb.like(cb.lower(root.get("code")), "%" + search.toLowerCase() + "%"));
        }
        List<Country> countries = countryRepository.findAll(spec, Sort.by("name"));
        Map<String, GroupByCountryDto> customersByCountry = customerService.groupByCountry();
        return countries.stream().map(country -> {
            CountryOverviewDto dto = modelMapper.map(country, CountryOverviewDto.class);
            GroupByCountryDto customerInfo = customersByCountry.get(country.getCode());

            return getCountryOverviewDto(customerInfo, dto);
        }).toList();
    }

    /**
     * Retrieves all countries that are marked as published, sorted alphabetically by name.
     *
     * @return A list of published Country entities.
     */
    @Transactional(readOnly = true)
    public List<CountryResponseDto> published() {
        return countryRepository.findByIsPublishedTrueOrderByNameAsc().stream().map(countryMapper::toResponseDto)
                .toList();
    }


    /**
     * Retrieves a single country overview by its code, enriched with external customer data.
     *
     * @param countryCode The unique two-letter code of the country.
     * @return A {@link CountryOverviewDto} containing combined data.
     * @throws ResponseStatusException if no country with the given code is found.
     * @ts-legacy This method was refactored. Instead of making a placeholder external API call,
     * it now enriches country data by calling the internal {@code CustomerService#groupByCountry()}
     * method to ensure data consistency with the main overview endpoint.
     */
    @Transactional(readOnly = true)
    public CountryOverviewDto countryOverview(String countryCode) {
        Country country = countryRepository.findByCode(countryCode)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, COUNTRY_NOT_FOUND));

        Map<String, GroupByCountryDto> customersByCountry = customerService.groupByCountry();
        GroupByCountryDto customerInfo = customersByCountry.get(country.getCode());

        CountryOverviewDto dto = modelMapper.map(country, CountryOverviewDto.class);

        return getCountryOverviewDto(customerInfo, dto);
    }

    /**
     * Retrieves a single country by its unique code and enriches it with the status of key criteria.
     * <p>
     *
     * @param code The unique two-letter code of the country to retrieve.
     * @return A {@link CountryWithCriteriaStatusDto} containing country details and boolean flags
     * for the presence of specific criteria types.
     * @throws ResponseStatusException if no country with the given code is found.
     * @ts-legacy This method faithfully translates the business logic of the original NestJS
     * `findOneByCode` but uses a different data-fetching strategy. The original Prisma query
     * uses a filtered 'include' to fetch the country and a subset of its criteria in a single
     * operation. Standard JPA's primary eager-loading tool, {@code JOIN FETCH}, does not
     * support filtering a fetched collection. Therefore, to remain compliant, this implementation uses the standard, robust two-query
     * approach: one query fetches the {@code Country}, and a second fetches only the
     * relevant, active {@code Criteria}.
     */
    @Transactional(readOnly = true)
    public CountryWithCriteriaStatusDto findOneByCode(String code) {
        Country country = countryRepository.findByCode(code)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, COUNTRY_NOT_FOUND));

        var relevantTypes = EnumSet.of(
                Criteria.Type.AUTHORIZE_REPRESENTATIVE,
                Criteria.Type.OTHER_COST,
                Criteria.Type.REPRESENTATIVE_TIER);

        List<Criteria> relevantCriteriaList = criteriaRepository.findActiveByCountryAndTypeIn(
                country.getId(),
                relevantTypes);
        CountryResponseDto countryResponseDto = countryMapper.toResponseDto(country);
        CountryWithCriteriaStatusDto dto = modelMapper.map(countryResponseDto, CountryWithCriteriaStatusDto.class);
        var relevantCriteriaByType = relevantCriteriaList.stream().collect(Collectors.groupingBy(Criteria::getType));

        dto.setHasAuthorizeRepresentativeCriteria(relevantCriteriaByType.containsKey(Criteria.Type.AUTHORIZE_REPRESENTATIVE));
        dto.setHasOtherCostCriteria(relevantCriteriaByType.containsKey(Criteria.Type.OTHER_COST));
        dto.setHasRepresentativeTierCriteria(relevantCriteriaByType.containsKey(Criteria.Type.REPRESENTATIVE_TIER));

        return dto;
    }

    /**
     * Partially updates an existing country with the provided data.
     *
     * @param id        The ID of the country to update.
     * @param updateDto A DTO containing the fields to be updated. Null fields are ignored.
     * @return The updated Country entity.
     * @throws ResponseStatusException if no country with the given ID is found.
     */
    @Transactional
    public CountryResponseDto update(Integer id,
                                     UpdateCountryDto updateDto) {
        Country country = countryRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, COUNTRY_NOT_FOUND));

        ModelMapperConfig.mapPresentFields(updateDto, country);

        return countryMapper.toResponseDto(countryRepository.save(country));
    }

    /**
     * Permanently deletes a country from the database.
     *
     * @param id The ID of the country to delete.
     * @throws ResponseStatusException if no country with the given ID is found.
     */
    @Transactional
    public void remove(Integer id) {
        if (!countryRepository.existsById(id)) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, COUNTRY_NOT_FOUND);
        }
        countryRepository.deleteById(id);
    }

    private static CountryOverviewDto getCountryOverviewDto(GroupByCountryDto customerInfo,
                                                            CountryOverviewDto dto) {
        if (customerInfo != null) {
            dto.setLicensedCustomerCount(customerInfo.getLicensedCustomerCount());
            dto.setUnlicensedCustomerCount(customerInfo.getUnlicensedCustomerCount());
        } else {
            dto.setLicensedCustomerCount(0);
            dto.setUnlicensedCustomerCount(0);
        }
        //@ts-legacy `tasks: 0` from original NestJS code
        dto.setTasks(0);
        return dto;
    }
}
