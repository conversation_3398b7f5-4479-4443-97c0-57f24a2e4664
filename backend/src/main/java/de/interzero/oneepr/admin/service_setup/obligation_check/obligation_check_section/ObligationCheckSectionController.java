package de.interzero.oneepr.admin.service_setup.obligation_check.obligation_check_section;

import de.interzero.oneepr.admin.service_setup.obligation_check.obligation_check_section.dto.CreateObligationCheckSectionDto;
import de.interzero.oneepr.admin.service_setup.obligation_check.obligation_check_section.dto.UpdateObligationCheckSectionDto;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.Role;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Controller for handling CRUD operations for ObligationCheckSection entities.
 * Endpoints are nested under a specific country.
 */
@RestController
@RequestMapping(Api.OBLIGATION_CHECK_SECTIONS)
@RequiredArgsConstructor
@Tag(name = "Admin - Obligation Check Sections")
public class ObligationCheckSectionController {

    private final ObligationCheckSectionService obligationCheckSectionService;

    /**
     * Creates a new obligation check section for a country.
     *
     * @param createDto The DTO with section data.
     * @return A response entity with the created section and a location header.
     */
    @Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
    @PostMapping("")
    public ResponseEntity<ObligationCheckSection> create(@Valid @RequestBody CreateObligationCheckSectionDto createDto) {
        ObligationCheckSection createdSection = obligationCheckSectionService.create(createDto);
        return new ResponseEntity<>(createdSection, HttpStatus.CREATED);
    }

    /**
     * Retrieves all obligation check sections for a country.
     *
     * @param countryCode The string ID of the parent country.
     * @return A list of sections.
     */
    @GetMapping("/country/{countryCode}")
    public ResponseEntity<List<ObligationCheckSection>> findAllByCountry(@PathVariable String countryCode) {
        List<ObligationCheckSection> sections = obligationCheckSectionService.getOrSetupSectionsForCountry(countryCode);
        return ResponseEntity.ok(sections);
    }

    /**
     * Retrieves a single obligation check section by its ID.
     *
     * @param id The string ID of the section.
     * @return The found section.
     */
    @Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
    @GetMapping("/{id}")
    public ResponseEntity<ObligationCheckSection> findOne(@PathVariable Long id) {
        ObligationCheckSection section = obligationCheckSectionService.findOne(id);
        return ResponseEntity.ok(section);
    }

    /**
     * Partially updates an obligation check section.
     *
     * @param id The string ID of the section to update.
     * @param updateDto The DTO with fields to update.
     * @return The updated section.
     */
    @PutMapping("/{id}")
    @Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
    public ResponseEntity<ObligationCheckSection> update(@PathVariable long id,
                                                         @Valid @RequestBody UpdateObligationCheckSectionDto updateDto) {
        ObligationCheckSection updatedSection = obligationCheckSectionService.update(id, updateDto);
        return ResponseEntity.ok(updatedSection);
    }

    /**
     * Deletes an obligation check section.
     *
     * @param id The string ID of the section to delete.
     * @return A response entity with no content.
     */
    @DeleteMapping("/remove/{id}")
    @Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
    public ResponseEntity<Void> delete(@PathVariable long id) {
        obligationCheckSectionService.delete(id);
        return ResponseEntity.noContent().build();
    }
}