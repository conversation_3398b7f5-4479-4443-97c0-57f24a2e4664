package de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.price_list.ReportSetPriceListItem;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSet;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
@Table(
        name = "report_set_price_list",
        schema = "public"
)
public class ReportSetPriceList {

    public enum Type {
        FIXED_PRICE,
        PRICE_PER_CATEGORY,
        PRICE_PER_VOLUME_BASE_PRICE,
        PRICE_PER_VOLUME_MINIMUM_FEE
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "title",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("title")
    private String title;

    @NotNull
    @Column(
            name = "start_date",
            nullable = false
    )
    @JsonProperty("start_date")
    private Instant startDate;

    @NotNull
    @Column(
            name = "end_date",
            nullable = false
    )
    @JsonProperty("end_date")
    private Instant endDate;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false,
            updatable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @Column(name = "base_price")
    @JsonProperty("base_price")
    private Integer basePrice;

    @Column(name = "fixed_price")
    @JsonProperty("fixed_price")
    private Integer fixedPrice;

    @Column(name = "minimum_fee")
    @JsonProperty("minimum_fee")
    private Integer minimumFee;

    @NotNull
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "type",
            nullable = false
    )
    @JsonProperty("type")
    private Type type;

    @NotNull
    @Column(
            name = "license_year",
            nullable = false
    )
    @JsonProperty("license_year")
    private Integer licenseYear = 2025;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "report_set_id",
            nullable = false
    )
    @JsonIgnore
    @JsonProperty("report_set")
    private ReportSet reportSet;

    @OneToMany(mappedBy = "priceList")
    @JsonProperty("items")
    private List<ReportSetPriceListItem> items = new ArrayList<>();

    @PrePersist
    protected void onCreate() {
        this.createdAt = this.updatedAt = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = Instant.now();
    }

    // --- Transient Getter for Foreign Key ---

    @Transient
    @JsonProperty("report_set_id")
    public Integer getReportSetId() {
        return (this.reportSet != null) ? this.reportSet.getId() : null;
    }

    /**
     * Helper method to synchronize the bidirectional relationship with ReportSetPriceListItem.
     *
     * @param item The item to add to this price list.
     */
    public void addItem(ReportSetPriceListItem item) {
        this.items.add(item);
        item.setPriceList(this);
    }
}