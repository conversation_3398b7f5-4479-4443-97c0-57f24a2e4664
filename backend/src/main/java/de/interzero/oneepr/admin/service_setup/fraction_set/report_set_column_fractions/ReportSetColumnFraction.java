package de.interzero.oneepr.admin.service_setup.fraction_set.report_set_column_fractions;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_columns.ReportSetColumn;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions.ReportSetFraction;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;

@Getter
@Setter
@Entity
@Table(
        name = "report_set_column_fraction",
        schema = "public"
)
public class ReportSetColumnFraction {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false,
            updatable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "column_code",
            referencedColumnName = "code",
            nullable = false
    )
    @JsonIgnore
    @JsonProperty("report_set_column")
    private ReportSetColumn reportSetColumn;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "fraction_code",
            referencedColumnName = "code",
            nullable = false
    )
    @JsonIgnore
    @JsonProperty("report_set_fraction")
    private ReportSetFraction reportSetFraction;

    @PrePersist
    protected void onCreate() {
        this.createdAt = this.updatedAt = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = Instant.now();
    }

    // --- Transient Getters for Foreign Key Codes ---

    @Transient
    @JsonProperty("column_code")
    public String getColumnCode() {
        return (this.reportSetColumn != null) ? this.reportSetColumn.getCode() : null;
    }

    @Transient
    @JsonProperty("fraction_code")
    public String getFractionCode() {
        return (this.reportSetFraction != null) ? this.reportSetFraction.getCode() : null;
    }
}