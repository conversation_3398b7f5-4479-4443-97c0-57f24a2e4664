package de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list;

import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSetRepository;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list.dto.CreateReportSetPriceListDto;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list.dto.UpdateReportSetPriceListDto;
import de.interzero.oneepr.common.config.ModelMapperConfig;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.List;

/**
 * Service class for managing Report Set Price List business logic.
 */
@Service
@RequiredArgsConstructor
public class ReportSetPriceListService {

    private final ReportSetPriceListRepository reportSetPriceListRepository;

    private final ReportSetRepository reportSetRepository;

    private final ModelMapper modelMapper;

    private static final String PRICE_LIST_NOT_FOUND = "Report set price list not found";

    private static final String REPORT_SET_NOT_FOUND = "Report set not found";

    /**
     * Creates a new ReportSetPriceList.
     *
     * @param createDto The DTO containing data for the new price list.
     * @return The newly created and persisted ReportSetPriceList entity.
     */
    @Transactional
    public ReportSetPriceList create(CreateReportSetPriceListDto createDto) {
        var reportSet = reportSetRepository.findById(createDto.getReportSetId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, REPORT_SET_NOT_FOUND));

        ReportSetPriceList newPriceList = modelMapper.map(createDto, ReportSetPriceList.class);
        newPriceList.setReportSet(reportSet);

        return reportSetPriceListRepository.save(newPriceList);
    }

    /**
     * Retrieves all non-deleted report set price lists.
     *
     * @return A list of active ReportSetPriceList entities.
     */
    @Transactional(readOnly = true)
    public List<ReportSetPriceList> findAll() {
        return reportSetPriceListRepository.findAllByDeletedAtIsNull();
    }

    /**
     * Retrieves a single, non-deleted report set price list by its ID.
     *
     * @param id The ID of the price list to retrieve.
     * @return The found ReportSetPriceList entity.
     */
    @Transactional(readOnly = true)
    public ReportSetPriceList findOne(Integer id) {
        return reportSetPriceListRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, PRICE_LIST_NOT_FOUND));
    }

    /**
     * Partially updates an existing report set price list.
     *
     * @param id        The ID of the price list to update.
     * @param updateDto DTO containing the fields to update.
     * @return The updated ReportSetPriceList entity.
     */
    @Transactional
    public ReportSetPriceList update(Integer id,
                                     UpdateReportSetPriceListDto updateDto) {
        ReportSetPriceList priceList = reportSetPriceListRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, PRICE_LIST_NOT_FOUND));

        ModelMapperConfig.mapPresentFields(updateDto, priceList);

        return reportSetPriceListRepository.save(priceList);
    }

    /**
     * Soft-deletes a report set price list and all other price lists in the same report set.
     *
     * @param id The ID of the price list to soft-delete, which triggers the cascade.
     */
    @Transactional
    public void remove(Integer id) {
        ReportSetPriceList priceList = reportSetPriceListRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, PRICE_LIST_NOT_FOUND));

        // Fetch all price lists (including the one to be deleted) that belong to the same ReportSet.
        List<ReportSetPriceList> allToDelete = reportSetPriceListRepository.findAllByReportSet_Id(priceList.getReportSetId());

        Instant deletionTime = Instant.now();
        allToDelete.forEach(pl -> pl.setDeletedAt(deletionTime));

        reportSetPriceListRepository.saveAll(allToDelete);
    }
}