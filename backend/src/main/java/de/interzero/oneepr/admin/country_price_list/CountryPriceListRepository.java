package de.interzero.oneepr.admin.country_price_list;

import de.interzero.oneepr.admin.country.Country;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CountryPriceListRepository extends JpaRepository<CountryPriceList, Integer> {

    /**
     * Finds all active (not soft-deleted) CountryPriceList entities.
     *
     * @return A list of active CountryPriceList entities.
     */
    List<CountryPriceList> findAllByDeletedAtIsNull();

    /**
     * Finds an active (not soft-deleted) CountryPriceList by its ID.
     *
     * @param id The ID of the entity.
     * @return An Optional containing the active entity if found, otherwise empty.
     */
    Optional<CountryPriceList> findByIdAndDeletedAtIsNull(Integer id);

    List<CountryPriceList> findByCountryAndDeletedAtIsNull(Country country);

    /**
     * Finds all active (not deleted) CountryPriceList entities for a given country ID.
     * This query uses a JOIN FETCH to eagerly load the associated PriceList entity,
     * preventing N+1 query problems when accessing the price list from the results.
     *
     * @param countryId The ID of the country for which to retrieve the price lists.
     * @return A List of CountryPriceList entities, each with its associated PriceList eagerly fetched.
     */
    @EntityGraph(attributePaths = "priceList")
    List<CountryPriceList> findByCountry_IdAndDeletedAtIsNull(Integer countryId);

    /**
     * Finds all active (not deleted) CountryPriceList entities for a given country code.
     * An entry is considered active if both the CountryPriceList link and its associated PriceList
     * have not been soft-deleted. This query uses a JOIN FETCH to eagerly load the associated
     * PriceList entity, preventing N+1 query problems.
     *
     * @param countryCode The unique code of the country for which to retrieve the price lists.
     * @return A List of CountryPriceList entities, each with its associated PriceList eagerly fetched.
     */
    @Query(
            "SELECT cpl FROM CountryPriceList cpl JOIN FETCH cpl.priceList pl JOIN cpl.country c WHERE c.code = :countryCode AND cpl.deletedAt IS NULL AND pl.deletedAt IS NULL"
    )
    List<CountryPriceList> findActiveByCountryCodeWithPriceList(@Param("countryCode") String countryCode);
}