package de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions;

import de.interzero.oneepr.admin.fraction_icon.FractionIconRepository;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSetRepository;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions.dto.CreateReportSetFractionDto;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions.dto.UpdateReportSetFractionDto;
import de.interzero.oneepr.common.config.ModelMapperConfig;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.security.SecureRandom;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

/**
 * Service class for managing Report Set Fraction business logic.
 */
@Service
@RequiredArgsConstructor
public class ReportSetFractionService {

    private final ReportSetFractionRepository reportSetFractionRepository;

    private final ReportSetRepository reportSetRepository;

    private final FractionIconRepository fractionIconRepository;

    private final ModelMapper modelMapper;

    private final SecureRandom secureRandom = new SecureRandom();

    private static final String REPORT_SET_NOT_FOUND = "Report set not found";

    private static final String FRACTION_NOT_FOUND = "Report set fraction not found";

    private static final String PARENT_FRACTION_NOT_FOUND = "Parent fraction not found";

    private static final String FRACTION_ICON_NOT_FOUND = "Fraction icon not found";

    /**
     * Creates a new ReportSetFraction after validating its parent entities.
     *
     * @param createDto The DTO containing data for the new fraction.
     * @return The newly created and persisted ReportSetFraction entity.
     */
    @Transactional
    public ReportSetFraction create(CreateReportSetFractionDto createDto) {
        var reportSet = reportSetRepository.findById(createDto.getReportSetId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, REPORT_SET_NOT_FOUND));

        var fractionIcon = fractionIconRepository.findById(createDto.getFractionIconId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, FRACTION_ICON_NOT_FOUND));

        ReportSetFraction parentFraction = null;
        if (createDto.getParentId() != null) {
            parentFraction = reportSetFractionRepository.findById(createDto.getParentId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, PARENT_FRACTION_NOT_FOUND));
        }

        ReportSetFraction newFraction = modelMapper.map(createDto, ReportSetFraction.class);
        newFraction.setReportSet(reportSet);
        newFraction.setFractionIcon(fractionIcon);
        if (parentFraction != null) {
            newFraction.setParent(parentFraction);
        }

        byte[] randomBytes = new byte[16];
        secureRandom.nextBytes(randomBytes);
        newFraction.setCode(bytesToHex(randomBytes));

        return reportSetFractionRepository.save(newFraction);
    }

    /**
     * Retrieves all non-deleted report set fractions.
     *
     * @return A list of active ReportSetFraction entities.
     */
    @Transactional(readOnly = true)
    public List<ReportSetFraction> findAll() {
        return reportSetFractionRepository.findAllByDeletedAtIsNull();
    }

    /**
     * Retrieves a single, non-deleted report set fraction by its ID.
     *
     * @param id The ID of the report set fraction to retrieve.
     * @return The found ReportSetFraction entity.
     */
    @Transactional(readOnly = true)
    public ReportSetFraction findOne(Integer id) {
        return reportSetFractionRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, FRACTION_NOT_FOUND));
    }

    /**
     * Partially updates an existing report set fraction.
     *
     * @param id        The ID of the fraction to update.
     * @param updateDto DTO containing the fields to update.
     * @return The updated ReportSetFraction entity.
     */
    @Transactional
    public ReportSetFraction update(Integer id,
                                    UpdateReportSetFractionDto updateDto) {
        ReportSetFraction fraction = reportSetFractionRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, FRACTION_NOT_FOUND));

        ModelMapperConfig.mapPresentFields(updateDto, fraction);

        if (updateDto.getParentId() != null) {
            var parent = reportSetFractionRepository.findById(updateDto.getParentId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, PARENT_FRACTION_NOT_FOUND));
            fraction.setParent(parent);
        }
        if (updateDto.getFractionIconId() != null) {
            var icon = fractionIconRepository.findById(updateDto.getFractionIconId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, FRACTION_ICON_NOT_FOUND));
            fraction.setFractionIcon(icon);
        }

        return reportSetFractionRepository.save(fraction);
    }

    /**
     * Soft-deletes a report set fraction and all of its direct children.
     *
     * @param id The ID of the report set fraction to soft-delete.
     */
    @Transactional
    public void remove(Integer id) {
        ReportSetFraction fractionToRemove = reportSetFractionRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, FRACTION_NOT_FOUND));

        List<ReportSetFraction> children = reportSetFractionRepository.findByParentId(id);

        Instant deletionTime = Instant.now();
        List<ReportSetFraction> allToDelete = new ArrayList<>(children);
        allToDelete.add(fractionToRemove);

        allToDelete.forEach(fraction -> fraction.setDeletedAt(deletionTime));

        reportSetFractionRepository.saveAll(allToDelete);
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder(2 * bytes.length);
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }
}