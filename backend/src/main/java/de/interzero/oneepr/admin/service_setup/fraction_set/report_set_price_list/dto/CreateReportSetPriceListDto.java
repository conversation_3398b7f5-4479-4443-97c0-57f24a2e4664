package de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list.ReportSetPriceList;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

/**
 * Data Transfer Object for creating a new report set price list.
 */
@Getter
@Setter
public class CreateReportSetPriceListDto extends BaseDto {

    @Schema(
            description = "ID of the associated report set",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("report_set_id")
    private Integer reportSetId;

    @Schema(
            description = "ID of the associated report set fraction",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("fraction_id")
    private Integer fractionId;

    @Schema(
            description = "Code of the associated report set fraction",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("fraction_code")
    private String fractionCode;

    @Schema(
            description = "Title of the report set price list",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("title")
    private String title;

    @Schema(
            description = "License year of the report set price list",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("license_year")
    private Integer licenseYear;

    @Schema(
            description = "Start date of the report set price list",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("start_date")
    private Instant startDate;

    @Schema(
            description = "End date of the report set price list",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("end_date")
    private Instant endDate;

    @Schema(
            description = "Type of the report set price list",
            implementation = ReportSetPriceList.Type.class
    )
    @JsonProperty("type")
    private ReportSetPriceList.Type type;

    @Schema(description = "Fixed price of the report set price list")
    @JsonProperty("fixed_price")
    private Integer fixedPrice;

    @Schema(description = "Base price of the report set price list")
    @JsonProperty("base_price")
    private Integer basePrice;

    @Schema(description = "Minimum fee of the report set price list")
    @JsonProperty("minimum_fee")
    private Integer minimumFee;
}