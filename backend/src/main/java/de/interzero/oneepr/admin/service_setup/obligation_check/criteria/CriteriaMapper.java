package de.interzero.oneepr.admin.service_setup.obligation_check.criteria;

import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.dto.CriteriaResponseDto;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingService;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * Mapper for converting the Criteria entity and its related objects into response DTOs.
 * This mapper uses MapStruct to automate the conversion process.
 */
@Mapper(
        componentModel = "spring",
        unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface CriteriaMapper {

    /**
     * Converts a Criteria entity to its corresponding response DTO.
     * MapStruct will automatically map fields with the same names. This includes the
     * transient getters (e.g., getCountryId()) to their corresponding DTO fields (e.g., countryId).
     * It will also automatically use the other methods in this interface to map nested collections.
     *
     * @param criteria The source Criteria entity.
     * @return The populated CriteriaResponseDto.
     */
    CriteriaResponseDto toDto(Criteria criteria);

    /**
     * Converts a CriteriaOption entity to its DTO representation.
     * This method is automatically invoked by MapStruct when mapping the 'options' list
     * within the main 'toDto(Criteria criteria)' method.
     *
     * @param criteriaOption The source CriteriaOption entity.
     * @return The populated CriteriaOptionResponseDto.
     */
    CriteriaResponseDto.CriteriaOptionResponseDto toDto(CriteriaOption criteriaOption);

    /**
     * Converts a PackagingService entity to its minimal DTO representation for nesting.
     * This method is automatically invoked by MapStruct when mapping the 'packagingServices' set
     * within the 'toDto(CriteriaOption criteriaOption)' method.
     *
     * @param packagingService The source PackagingService entity.
     * @return The populated PackagingServiceResponseDto.
     */
    CriteriaResponseDto.PackagingServiceResponseDto toDto(PackagingService packagingService);
}