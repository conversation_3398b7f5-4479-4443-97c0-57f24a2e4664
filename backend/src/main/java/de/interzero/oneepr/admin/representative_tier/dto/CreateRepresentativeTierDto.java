package de.interzero.oneepr.admin.representative_tier.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Data Transfer Object for creating a new representative tier.
 */
@Getter
@Setter
public class CreateRepresentativeTierDto extends BaseDto {

    @Schema(
            description = "Name of the representative tier",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("name")
    private String name;

    @Schema(
            description = "Price of the representative tier",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("price")
    private Integer price;

    @Schema(
            description = "ID of the country",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("country_id")
    private Integer countryId;
}