package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Top-level Data Transfer Object for the response of the service setup commitment submission.
 * <p>
 * This DTO is the final object returned by the {@code ServiceSetupService.submitServiceSetupCommitment}
 * method. It is required to act as a wrapper that organizes the complete response into a
 * single, well-defined JSON structure for the client.
 * <p>
 * It consolidates two distinct pieces of information:
 * <ul>
 *   <li><b>{@code setup}:</b> The primary result of the commitment logic, containing the fully
 *   calculated service configuration in a {@link CommitmentSubmitResultDto}.</li>
 *   <li><b>{@code commitment}:</b> An echo of the commitment criteria and the user's submitted
 *   answers, useful for front-end confirmation and state management, in a list of
 *   {@link FormattedCommitmentDto}.</li>
 * </ul>
 * This structure provides a clear and comprehensive API contract for the commitment submission endpoint.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ServiceSetupCommitmentResponseDto {

    @JsonProperty("year")
    private String year;

    @JsonProperty("setup")
    private CommitmentSubmitResultDto setup;

    @JsonProperty("commitment")
    private List<FormattedCommitmentDto> commitment;
}