package de.interzero.oneepr.admin.service_setup.obligation_check.obligation_check_section;

import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.service_setup.obligation_check.obligation_check_section.dto.CreateObligationCheckSectionDto;
import de.interzero.oneepr.admin.service_setup.obligation_check.obligation_check_section.dto.UpdateObligationCheckSectionDto;
import de.interzero.oneepr.common.config.ModelMapperConfig;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.Collections;
import java.util.List;

/**
 * Service class for managing ObligationCheckSection entities.
 * Handles the business logic for CRUD operations.
 */
@Service
@RequiredArgsConstructor
@Transactional
public class ObligationCheckSectionService {

    private final ObligationCheckSectionRepository obligationCheckSectionRepository;

    private final CountryRepository countryRepository;

    private final ModelMapper modelMapper;

    private static final String SECTION_NOT_FOUND = "Obligation check section not found.";

    private static final String COUNTRY_NOT_FOUND = "Country not found.";

    /**
     * Creates a new obligation check section for a given country.
     *
     * @param createDto The DTO containing the data for the new section.
     * @return The newly created ObligationCheckSection entity.
     * @throws ResponseStatusException if the country is not found.
     */
    @Transactional
    public ObligationCheckSection create(CreateObligationCheckSectionDto createDto) {
        Country country = countryRepository.findByCode(createDto.getCountryCode())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, COUNTRY_NOT_FOUND));

        ObligationCheckSection section = modelMapper.map(createDto, ObligationCheckSection.class);
        section.setCountry(country);

        return obligationCheckSectionRepository.save(section);
    }

    /**
     * Retrieves all obligation check sections for a country without any side effects.
     * This is a pure read operation.
     *
     * @param countryCode The two-letter code of the country.
     * @return A list of existing ObligationCheckSection entities, which may be empty.
     */
    public List<ObligationCheckSection> findAllByCountry(String countryCode) {
        return obligationCheckSectionRepository.findAllByCountry_Code(countryCode);
    }

    /**
     * Retrieves all sections for a country, creating and persisting a default "Section 1"
     * if no sections currently exist. This method is designed to be called by the UI when setting up
     * an obligation check for the first time to ensure a consistent starting state.
     * This method is transactional and performs a write operation if necessary.
     *
     * @param countryCode The two-letter code of the country.
     * @return A list containing the existing sections, or a singleton list with the newly created default section.
     * @throws ResponseStatusException if the country for the given code is not found.
     */

    public List<ObligationCheckSection> getOrSetupSectionsForCountry(String countryCode) {
        List<ObligationCheckSection> sections = this.findAllByCountry(countryCode);

        if (sections.isEmpty()) {
            Country country = countryRepository.findByCode(countryCode)
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Country not found"));

            ObligationCheckSection firstSection = new ObligationCheckSection();
            firstSection.setTitle("Section 1");
            firstSection.setDisplayOrder(1);
            firstSection.setCountry(country);

            ObligationCheckSection savedSection = obligationCheckSectionRepository.save(firstSection);
            return Collections.singletonList(savedSection);
        }

        return sections;
    }

    /**
     * Finds a single active obligation check section by its ID and country ID.
     *
     * @param sectionId The ID of the section.
     * @return The ObligationCheckSection entity.
     * @throws ResponseStatusException if the section is not found.
     */
    public ObligationCheckSection findOne(long sectionId) {
        return obligationCheckSectionRepository.findById(sectionId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, SECTION_NOT_FOUND));
    }

    /**
     * Updates an existing obligation check section.
     *
     * @param sectionId The ID of the section to update.
     * @param updateDto The DTO containing the fields to update.
     * @return The updated ObligationCheckSection entity.
     * @throws ResponseStatusException if the section is not found.
     */
    public ObligationCheckSection update(long sectionId,
                                         UpdateObligationCheckSectionDto updateDto) {
        ObligationCheckSection existingSection = findOne(sectionId);
        ModelMapperConfig.mapPresentFields(updateDto, existingSection);
        if (updateDto.getCountryCode() != null) {
            Country country = countryRepository.findByCode(updateDto.getCountryCode())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Country not found"));
            existingSection.setCountry(country);
        }
        return obligationCheckSectionRepository.save(existingSection);
    }

    /**
     * Hard-deletes an obligation check section by setting its deleted_at timestamp.
     *
     * @param sectionId The ID of the section to delete.
     * @throws ResponseStatusException if the section is not found.
     */
    @Transactional
    public void delete(long sectionId) {
        obligationCheckSectionRepository.deleteById(sectionId);
    }
}