package de.interzero.oneepr.admin.fraction_icon;

import com.fasterxml.jackson.annotation.JsonFilter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions.ReportSetFraction;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
@Table(
        name = "fraction_icon",
        schema = "public",
        uniqueConstraints = {@UniqueConstraint(columnNames = {"file_id"})}
)
@JsonFilter("fractionIconFilter")
public class FractionIcon {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "fraction_icon_id_seq"
    )
    @SequenceGenerator(
            name = "fraction_icon_id_seq",
            sequenceName = "fraction_icon_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "image_url",
            nullable = false
    )
    @JsonProperty("image_url")
    private String imageUrl = "";

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @NotNull
    @OneToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "file_id",
            nullable = false
    )
    @JsonIgnore
    @JsonProperty("file_id")
    private Files file;

    @OneToMany(mappedBy = "fractionIcon")
    @JsonIgnore
    @JsonProperty("report_set_fractions")
    private List<ReportSetFraction> reportSetFractions = new ArrayList<>();

    /**
     * Exposes the file_id for JSON serialization, fulfilling Rule #3.
     * The ID type is String, matching the Prisma schema.
     *
     * @return The ID of the associated File, or null if not set.
     */
    @Transient
    @JsonProperty("file_id")
    public String getFileId() {
        return (this.file != null) ? this.file.getId() : null;
    }

    /**
     * Sets the creation and update timestamps before the entity is first persisted.
     * This is a JPA lifecycle callback that translates Prisma's `@default(now())` and `@updatedAt`.
     */
    @PrePersist
    protected void onCreate() {
        createdAt = updatedAt = Instant.now();
    }

    /**
     * Update clusters auditing fields on update.
     */
    @PreUpdate
    protected void onUpdate() {
        updatedAt = Instant.now();
    }
}