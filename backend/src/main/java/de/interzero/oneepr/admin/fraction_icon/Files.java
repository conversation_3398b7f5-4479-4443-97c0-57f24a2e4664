package de.interzero.oneepr.admin.fraction_icon;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.report_set.ReportSet;
import de.interzero.oneepr.admin.required_information.RequiredInformation;
import de.interzero.oneepr.admin.settings.Settings;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.UUID;

@Getter
@Setter
@Entity
@Table(
        name = "files",
        schema = "public"
)
public class Files {

    @Id
    @JsonProperty("id")
    @Column(
            name = "id",
            nullable = false,
            updatable = false,
            columnDefinition = "text"
    )
    private String id;

    @NotNull
    @Column(
            name = "name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("name")
    private String name;

    @NotNull
    @Column(
            name = "extension",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("extension")
    private String extension;

    @NotNull
    @Column(
            name = "size",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("size")
    private String size;

    @NotNull
    @Column(
            name = "creator_type",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("creator_type")
    private String creatorType;

    @NotNull
    @Column(
            name = "document_type",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("document_type")
    private String documentType;

    @Column(
            name = "user_id",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("user_id")
    private String userId;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @Column(name = "updated_at")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @NotNull
    @Column(
            name = "original_name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("original_name")
    private String originalName;

    @Column(name = "country_id")
    @JsonProperty("country_id")
    private Integer countryId;

    // --- Inverse Side of Relationships ---

    @OneToOne(mappedBy = "file")
    @JsonIgnore
    @JsonProperty("fraction_icon")
    private FractionIcon fractionIcon;

    @OneToOne(mappedBy = "sheetFile")
    @JsonIgnore
    @JsonProperty("report_set")
    private ReportSet reportSet;

    @OneToOne(mappedBy = "files")
    @JsonIgnore
    @JsonProperty("required_information")
    private RequiredInformation requiredInformation;

    @OneToOne(mappedBy = "file")
    @JsonIgnore
    @JsonProperty("settings")
    private Settings settings;

    @PrePersist
    protected void onCreate() {
        if (this.id == null) {
            this.id = UUID.randomUUID().toString();
        }
        createdAt = Instant.now();
        updatedAt = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = Instant.now();
    }

}
