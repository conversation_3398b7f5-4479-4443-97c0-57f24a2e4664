package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSet;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * Data Transfer Object representing the input for a single report set within a license cost calculation request.
 * <p>
 * This DTO is a required component of the {@link CalculateLicenseCostsDto} payload. It serves to
 * logically group a list of user-submitted {@link FractionInputDto} objects under a specific
 * report set, identified by its {@code id}.
 * <p>
 * This structure is necessary to allow the client to provide all relevant weight data for a
 * given report set in a single, organized object, which the back-end service then uses for its
 * calculations. It decouples the API input from the more complex internal
 * {@link ReportSet} entity.
 */
@Data
public class ReportSetInputDto {

    @Schema(description = "The ID of the report set.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "A list of fractions and their weights for this report set.")
    @JsonProperty("fractions")
    private List<FractionInputDto> fractions;
}