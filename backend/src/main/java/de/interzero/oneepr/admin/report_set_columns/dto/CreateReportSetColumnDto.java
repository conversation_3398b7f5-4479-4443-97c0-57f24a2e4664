package de.interzero.oneepr.admin.report_set_columns.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.report_set_columns.ReportSetColumn;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * Data Transfer Object for creating a new report set column.
 */
@Getter
@Setter
public class CreateReportSetColumnDto extends BaseDto {

    @Schema(
            description = "Name of the report set fraction column",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("name")
    private String name;

    @Schema(
            description = "Description of the report set fraction column",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("description")
    private String description;

    @Schema(
            description = "Unit type of the report set fraction column",
            requiredMode = Schema.RequiredMode.REQUIRED,
            implementation = ReportSetColumn.UnitType.class
    )
    @JsonProperty("unit_type")
    private ReportSetColumn.UnitType unitType;

    @Schema(
            description = "ID of the associated report set",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("report_set_id")
    private Integer reportSetId;

    @Schema(description = "ID of the parent column (if any)")
    @JsonProperty("parent_id")
    private Integer parentId;
}