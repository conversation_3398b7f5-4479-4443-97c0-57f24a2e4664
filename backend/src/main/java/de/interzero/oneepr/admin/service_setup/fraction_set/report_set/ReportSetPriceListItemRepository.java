package de.interzero.oneepr.admin.service_setup.fraction_set.report_set;

import de.interzero.oneepr.admin.price_list.ReportSetPriceListItem;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ReportSetPriceListItemRepository extends JpaRepository<ReportSetPriceListItem, Integer> {

    @Modifying
    @Query("DELETE FROM ReportSetPriceListItem i WHERE i.reportSetFraction.code IN :codes")
    void deleteByFractionCodeIn(@Param("codes") List<String> codes);

}
