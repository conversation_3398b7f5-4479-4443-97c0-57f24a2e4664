package de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions;

import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions.dto.CreateReportSetFractionDto;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions.dto.UpdateReportSetFractionDto;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.Role;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.headers.Header;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

/**
 * REST controller for managing Report Set Fraction entities.
 * Provides endpoints for creating, reading, updating, and deleting report set fractions.
 * Access is restricted to specific administrative roles.
 */
@RestController
@RequestMapping(Api.REPORT_SET_FRACTIONS)
@Tag(name = "ReportSetFractions")
@RequiredArgsConstructor
@Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
public class ReportSetFractionController {

    private final ReportSetFractionService reportSetFractionsService;


    /**
     * Creates a new report set fraction.
     *
     * @param data DTO containing the details for the new fraction.
     * @return A response entity with status 201 and the created ReportSetFraction in the body.
     */
    @PostMapping
    @Operation(summary = "Create a new report set fraction")
    @ApiResponse(
            responseCode = "201",
            description = "Report set fraction created successfully",
            headers = @Header(
                    name = "Location",
                    description = "URI of the newly created resource"
            ),
            content = @Content(schema = @Schema(implementation = ReportSetFraction.class))
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid report set fraction data"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Report set not found"
    )
    @ResponseStatus(HttpStatus.CREATED)
    public ReportSetFraction create(@RequestBody CreateReportSetFractionDto data) {
        return reportSetFractionsService.create(data);
    }

    /**
     * Retrieves all active report set fractions.
     *
     * @return A list of ReportSetFraction entities.
     */
    @GetMapping
    @Operation(summary = "Get all report set fractions")
    @ApiResponse(
            responseCode = "200",
            description = "Report set fractions retrieved successfully"
    )
    public List<ReportSetFraction> findAll() {
        return this.reportSetFractionsService.findAll();
    }

    /**
     * Retrieves a single report set fraction by its ID.
     *
     * @param id The string representation of the fraction's ID.
     * @return The found ReportSetFraction entity.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get report set fraction by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Report set fraction retrieved successfully",
            content = @Content(schema = @Schema(implementation = ReportSetFraction.class))
    )
    @ApiResponse(
            responseCode = "404",
            description = "Report set fraction not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid report set fraction ID"
    )
    public ReportSetFraction findOne(@PathVariable String id) {
        try {
            return this.reportSetFractionsService.findOne(Integer.valueOf(id));
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid report set fraction ID format");
        }
    }

    /**
     * Updates an existing report set fraction by its ID.
     *
     * @param id   The string representation of the fraction's ID.
     * @param data DTO containing the fields to update.
     * @return The updated ReportSetFraction entity.
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update report set fraction by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Report set fraction updated successfully",
            content = @Content(schema = @Schema(implementation = ReportSetFraction.class))
    )
    @ApiResponse(
            responseCode = "404",
            description = "Report set fraction not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid report set fraction ID"
    )
    public ReportSetFraction update(@PathVariable String id,
                                    @RequestBody UpdateReportSetFractionDto data) {
        try {
            return this.reportSetFractionsService.update(Integer.valueOf(id), data);
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid report set fraction ID format");
        }
    }

    /**
     * Soft-deletes a report set fraction and its children by its ID.
     *
     * @param id The string representation of the fraction's ID.
     * @return An HTTP 200 OK response on successful deletion.
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete report set fraction by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Report set fraction deleted successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Report set fraction not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid report set fraction ID"
    )
    public ResponseEntity<Void> remove(@PathVariable String id) {
        try {
            this.reportSetFractionsService.remove(Integer.valueOf(id));
            return ResponseEntity.ok().build();
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid report set fraction ID format");
        }
    }
}