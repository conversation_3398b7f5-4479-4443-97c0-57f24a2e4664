package de.interzero.oneepr.admin.service_setup;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country_price_list.CountryPriceList;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingService;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSet;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_columns.ReportSetColumn;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list.ReportSetPriceList;
import de.interzero.oneepr.admin.service_setup.dto.ServiceSetupDto;
import de.interzero.oneepr.admin.service_setup.report_set_frequency.ReportSetFrequency;
import de.interzero.oneepr.admin.service_setup.report_set_frequency.dto.CreateReportSetFrequencyDto;
import de.interzero.oneepr.admin.service_setup.required_information.RequiredInformation;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

/**
 * Abstract MapStruct mapper responsible for converting various service setup-related domain entities
 * into the comprehensive {@link ServiceSetupDto}.
 * <p>
 * This class centralizes the transformation logic required to build the complex, nested DTO
 * that represents the entire state of a country's service configuration. As a Spring component,
 * it can be injected into services and can leverage other beans like the {@link ObjectMapper}.
 * <p>
 * Key responsibilities include:
 * <ul>
 *   <li>Mapping the root {@link Country} entity and its direct associations into the main DTO.</li>
 *   <li>Providing specific mapping methods for nested entities like {@link PackagingService},
 *   {@link ReportSet}, and {@link ReportSetColumn} to shape them correctly within the larger DTO structure.</li>
 *   <li>Handling special cases, such as JSON string deserialization, via custom lifecycle hooks.</li>
 * </ul>
 *
 * <b>Custom Logic with {@code @AfterMapping}:</b>
 * <p>
 * A critical feature of this mapper is the {@link #afterMappingReportSetFrequency} method.
 * Standard MapStruct mappings cannot process raw JSON strings. This method intercepts the mapping
 * from a {@link ReportSetFrequency} entity and uses an injected {@code ObjectMapper} to parse the
 * entity's {@code frequency} JSON string into a structured Java {@code Object}. This ensures the
 * final DTO contains a ready-to-use, deserialized representation of the frequency data, offloading
 * - * complex parsing logic from the service layer.
 */
@Mapper(componentModel = "spring")
public abstract class ServiceSetupMapper {

    @Mapping(
            target = "id",
            source = "id"
    )
    @Mapping(
            target = "name",
            source = "name"
    )
    @Mapping(
            target = "createdAt",
            source = "createdAt"
    )
    @Mapping(
            target = "updatedAt",
            source = "updatedAt"
    )
    @Mapping(
            target = "authorizeRepresentativeObligated",
            source = "authorizeRepresentativeObligated"
    )
    @Mapping(
            target = "code",
            source = "code"
    )
    @Mapping(
            target = "flagUrl",
            source = "flagUrl"
    )
    @Mapping(
            target = "otherCostsObligated",
            source = "otherCostsObligated"
    )
    @Mapping(
            target = "isPublished",
            source = "isPublished"
    )
    @Mapping(
            target = "packagingServices",
            source = "packagingServices"
    )
    @Mapping(
            target = "representativeTiers",
            source = "representativeTiers"
    )
    @Mapping(
            target = "requiredInformations",
            source = "requiredInformations"
    )
    @Mapping(
            target = "otherCosts",
            source = "otherCosts"
    )
    @Mapping(
            target = "countryPriceLists",
            source = "countryPriceLists"
    )
    public abstract ServiceSetupDto toServiceSetupDto(Country country);


    @Mapping(
            target = "packagingService",
            source = "."
    )
    public abstract ServiceSetupDto.PackagingServiceWithRelations toDto(PackagingService packagingService);

    @Mapping(
            target = "id",
            source = "id"
    )
    @Mapping(
            target = "name",
            source = "name"
    )
    @Mapping(
            target = "mode",
            source = "mode"
    )
    @Mapping(
            target = "type",
            source = "type"
    )
    @Mapping(
            target = "createdAt",
            source = "createdAt"
    )
    @Mapping(
            target = "updatedAt",
            source = "updatedAt"
    )
    @Mapping(
            target = "deletedAt",
            source = "deletedAt"
    )
    @Mapping(
            target = "packagingServiceId",
            source = "packagingServiceId"
    )
    @Mapping(
            target = "sheetFileId",
            source = "sheetFileId"
    )
    @Mapping(
            target = "sheetFileDescription",
            source = "sheetFileDescription"
    )
    @Mapping(
            target = "fractions",
            source = "fractions"
    )
    @Mapping(
            target = "columns",
            source = "columns"
    )
    @Mapping(
            target = "priceLists",
            source = "priceLists"
    )
    public abstract ServiceSetupDto.ReportSetWithRelations toDto(ReportSet reportSet);

    @Mapping(
            target = "reportSetColumn",
            source = "."
    )
    public abstract ServiceSetupDto.ReportSetColumnWithChildren toColumnWithChildrenDto(ReportSetColumn reportSetColumn);

    @Mapping(
            target = "reportSetColumn",
            source = "."
    )
    public abstract ServiceSetupDto.ReportSetColumnWithFractions toColumnWithFractionsDto(ReportSetColumn reportSetColumn);

    @Mapping(
            target = "countryPriceList",
            source = "."
    )
    @Mapping(
            target = "priceList",
            source = "priceList"
    )
    public abstract ServiceSetupDto.CountryPriceListWithPriceList toDto(CountryPriceList countryPriceList);

    @Mapping(
            target = "reportSetPriceList",
            source = "."
    )
    @Mapping(
            target = "items",
            source = "items"
    )
    public abstract ServiceSetupDto.ReportSetPriceListWithItems toDto(ReportSetPriceList reportSetPriceList);

    @Mapping(
            target = "requiredInformation",
            source = "."
    )
    @Mapping(
            target = "file",
            source = "files"
    )
    public abstract ServiceSetupDto.RequiredInformationWithFile toDto(RequiredInformation requiredInformation);


    @Mapping(
            target = "id",
            source = "id"
    )
    @Mapping(
            target = "rhythm",
            source = "rhythm"
    )
    @Mapping(
            target = "createdAt",
            source = "createdAt"
    )
    @Mapping(
            target = "updatedAt",
            source = "updatedAt"
    )
    @Mapping(
            target = "deletedAt",
            source = "deletedAt"
    )
    @Mapping(
            target = "packagingServiceId",
            source = "packagingServiceId"
    )
    @Mapping(
            target = "frequency",
            ignore = true
    )
    public abstract ServiceSetupDto.ReportSetFrequencyWithFrequency toDto(ReportSetFrequency reportSetFrequency);

    @SuppressWarnings("java:S112")
    @AfterMapping
    protected void afterMappingReportSetFrequency(ReportSetFrequency source,
                                                  @MappingTarget ServiceSetupDto.ReportSetFrequencyWithFrequency targetDto) {
        CreateReportSetFrequencyDto frequencyContainerDto = new CreateReportSetFrequencyDto();

        frequencyContainerDto.setPackagingServiceId(source.getPackagingServiceId());
        frequencyContainerDto.setRhythm(source.getRhythm());

        String frequencyJson = source.getFrequency();

        if (frequencyJson != null && !frequencyJson.isBlank()) {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.registerModule(new JavaTimeModule());
                objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
                Object parsedFrequencyObject = objectMapper.readValue(frequencyJson, Object.class);

                frequencyContainerDto.setFrequency(parsedFrequencyObject);

            } catch (JsonProcessingException e) {
                throw new RuntimeException("FATAL: Failed to parse frequency JSON: " + frequencyJson, e);
            }
        }

        targetDto.setFrequency(frequencyContainerDto);
    }
}