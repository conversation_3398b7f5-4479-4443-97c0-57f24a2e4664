package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.Instant;

/**
 * Data Transfer Object for the response of the findServiceSetupPriceLists method.
 * It represents a CountryPriceList join entity, which includes the fully-detailed,
 * nested PriceList object.
 * This structure is a direct translation of the Prisma query with 'include: { price_list: true }'.
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class CountryPriceListResponseDto {

    @Schema(
            description = "The unique identifier of the country-price list association.",
            example = "1"
    )
    @JsonProperty("id")
    private Integer id;

    @Schema(
            description = "ID of the associated country.",
            example = "10"
    )
    @JsonProperty("country_id")
    private Integer countryId;

    @Schema(
            description = "ID of the associated price list.",
            example = "5"
    )
    @JsonProperty("price_list_id")
    private Integer priceListId;

    @Schema(description = "The timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "The timestamp of the last update.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "The full associated price list object.")
    @JsonProperty("price_list")
    private PriceListDto priceList;
}