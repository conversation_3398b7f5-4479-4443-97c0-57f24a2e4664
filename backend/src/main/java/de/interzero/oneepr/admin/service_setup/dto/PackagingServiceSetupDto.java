package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.Criteria;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingService;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.Collections;
import java.util.List;

@Data
@NoArgsConstructor
public class PackagingServiceSetupDto {

    private Integer id;

    private String name;

    private String description;

    private Integer countryId;

    private Instant createdAt;

    private Instant updatedAt;

    @JsonProperty("criterias")
    private List<Criteria> criterias;

    @JsonProperty("has_criteria")
    private boolean hasCriteria;

    @JsonProperty("has_report_set_criteria")
    private boolean hasReportSetCriteria;

    @JsonProperty("has_report_frequency_criteria")
    private boolean hasReportFrequencyCriteria;

    /**
     * Data Transfer Object representing a Packaging Service within the context of the service setup commitment screen.
     * <p>
     * This DTO originates from the {@link PackagingService}
     * entity but is required to augment the base entity data with calculated, context-specific
     * metadata that simplifies front-end logic. It is constructed by the service layer to hold not only
     * the packaging service details but also a filtered list of its associated criteria and several
     * computed boolean flags.
     * <p>
     * These additional fields are necessary because:
     * <ul>
     *   <li>{@code criterias}: Holds a specific, filtered subset of criteria relevant only to the current
     *   operation (e.g., only COMMITMENT mode criteria).</li>
     *   <li>{@code has_criteria}, {@code has_report_set_criteria}, etc.: These are flags calculated
     *   by the service to inform the client whether specific types of commitment questions exist for
     *   this service. This offloads business logic from the front-end, allowing it to simply check a
     *   boolean to conditionally render UI components.</li>
     * </ul>
     * This approach encapsulates the business logic for determining criteria relevance within the back-end
     * service, providing the client with a clean, ready-to-use data structure.
     */
    public static PackagingServiceSetupDto fromEntity(PackagingService ps,
                                                      List<Criteria> filteredCriteria) {
        PackagingServiceSetupDto dto = new PackagingServiceSetupDto();
        dto.setId(ps.getId());
        dto.setName(ps.getName());
        dto.setDescription(ps.getDescription());
        dto.setCountryId(ps.getCountryId());
        dto.setCreatedAt(ps.getCreatedAt());
        dto.setUpdatedAt(ps.getUpdatedAt());

        dto.setCriterias(filteredCriteria != null ? filteredCriteria : Collections.emptyList());

        dto.setHasCriteria(filteredCriteria != null && filteredCriteria.stream()
                .anyMatch(c -> c.getType() == Criteria.Type.PACKAGING_SERVICE));

        dto.setHasReportSetCriteria(filteredCriteria != null && filteredCriteria.stream()
                .anyMatch(c -> c.getType() == Criteria.Type.REPORT_SET));

        dto.setHasReportFrequencyCriteria(filteredCriteria != null && filteredCriteria.stream()
                .anyMatch(c -> c.getType() == Criteria.Type.REPORT_FREQUENCY));

        return dto;
    }
}