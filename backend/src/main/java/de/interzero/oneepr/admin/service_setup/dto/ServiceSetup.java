package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSet;
import de.interzero.oneepr.admin.service_setup.obligation_check.obligation_check_section.ObligationCheckSection;
import de.interzero.oneepr.admin.service_setup.report_set_frequency.ReportSetFrequency;
import de.interzero.oneepr.admin.service_setup.required_information.RequiredInformation;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingService;
import de.interzero.oneepr.admin.service_setup.third_party_cost.ThirdPartyCost;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
@Table(
        name = "service_setup",
        schema = "public"
)
public class ServiceSetup {
    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "service_setup_seq"
    )
    @SequenceGenerator(
            name = "service_setup_seq",
            sequenceName = "service_setup_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Long id;

    @NotNull
    @OneToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "country_id",
            nullable = false
    )
    @JsonIgnore
    @JsonProperty("country")
    private Country country;

    @OneToMany(
            mappedBy = "service_setup",
            fetch = FetchType.EAGER
    )
    @JsonProperty("packaging_services")
    private List<PackagingService> packagingServices = new ArrayList<>();


    @OneToMany(
            mappedBy = "service_setup",
            fetch = FetchType.EAGER
    )
    @JsonProperty("obligation_checks")
    private List<ObligationCheckSection> obligationChecks = new ArrayList<>();

    @OneToMany(
            mappedBy = "service_setup",
            fetch = FetchType.EAGER
    )
    @JsonProperty("report_sets")
    private List<ReportSet> reportSets = new ArrayList<>();

    @OneToMany(
            mappedBy = "service_setup",
            fetch = FetchType.EAGER
    )
    @JsonProperty("third_party_costs")
    private List<ThirdPartyCost> thirdPartyCosts = new ArrayList<>();

    @OneToMany(
            mappedBy = "service_setup",
            fetch = FetchType.EAGER
    )
    @JsonProperty("report_set_frequencies")
    private List<ReportSetFrequency> reportSetFrequencies = new ArrayList<>();

    @OneToMany(
            mappedBy = "service_setup",
            fetch = FetchType.EAGER
    )
    @JsonProperty("required_informations")
    private List<RequiredInformation> requiredInformations = new ArrayList<>();



}
