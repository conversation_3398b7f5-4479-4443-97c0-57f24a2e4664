package de.interzero.oneepr.admin.representative_tier;

import de.interzero.oneepr.admin.country.Country;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RepresentativeTierRepository extends JpaRepository<RepresentativeTier, Integer> {

    /**
     * Finds all tiers that have not been soft-deleted.
     *
     * @return A list of active RepresentativeTier entities.
     */
    List<RepresentativeTier> findAllByDeletedAtIsNull();

    /**
     * Finds a single tier by its ID, but only if it has not been soft-deleted.
     *
     * @param id The ID of the tier.
     * @return An Optional containing the tier if found and active, otherwise empty.
     */
    Optional<RepresentativeTier> findByIdAndDeletedAtIsNull(Integer id);

    List<RepresentativeTier> findByCountryAndDeletedAtIsNull(Country country);

    /**
     * Finds all active (non-deleted) representative tiers for a given country code.
     * <p>
     * This query uses a {@code JOIN FETCH} to eagerly load the associated {@code Country}
     * entity in the same query. This is a key performance optimization that prevents
     * potential N+1 issues when the country information is accessed from the resulting
     * {@link RepresentativeTier} objects.
     *
     * @param countryCode The unique code of the country for which to retrieve representative tiers.
     * @return A list of {@link RepresentativeTier} entities with their associated {@code Country}
     * eagerly fetched.
     */
    @Query("SELECT rt FROM RepresentativeTier rt JOIN FETCH rt.country c WHERE c.code = :countryCode AND rt.deletedAt IS NULL")
    List<RepresentativeTier> findAllByCountryCodeAndDeletedAtIsNullWithCountry(@Param("countryCode") String countryCode);
}