package de.interzero.oneepr.admin.service_setup.fraction_set.report_set_column_fractions;

import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_column_fractions.dto.CreateReportSetColumnFractionDto;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.Role;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

/**
 * REST controller for managing the association between report set columns and fractions.
 */
@RestController
@RequestMapping(Api.REPORT_SET_COLUMN_FRACTIONS)
@Tag(name = "ReportSetColumnFractions")
@RequiredArgsConstructor
@Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
public class ReportSetColumnFractionsController {

    private final ReportSetColumnFractionsService reportSetColumnFractionsService;

    /**
     * Creates a new association between a column and a fraction.
     *
     * @param data The DTO containing the codes for the column and fraction.
     * @return The newly created {@link ReportSetColumnFraction} entity with a 201 Created status.
     */
    @PostMapping
    @Operation(summary = "Create a new report set column fraction")
    @ApiResponse(
            responseCode = "201",
            description = "Report set column fraction created successfully"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid report set column fraction data"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Report set column or fraction not found"
    )
    public ResponseEntity<ReportSetColumnFraction> create(@RequestBody CreateReportSetColumnFractionDto data) {
        ReportSetColumnFraction createdAssociation = reportSetColumnFractionsService.create(data);
        return new ResponseEntity<>(createdAssociation, HttpStatus.CREATED);
    }

    /**
     * Retrieves all non-deleted column-fraction associations.
     *
     * @return A list of all active associations.
     */
    @GetMapping
    @Operation(summary = "Get all report set column fractions")
    @ApiResponse(
            responseCode = "200",
            description = "Report set column fractions retrieved successfully"
    )
    public ResponseEntity<List<ReportSetColumnFraction>> findAll() {
        List<ReportSetColumnFraction> associations = reportSetColumnFractionsService.findAll();
        return ResponseEntity.ok(associations);
    }

    /**
     * Retrieves a specific column-fraction association by its ID.
     *
     * @param id The string representation of the association's ID.
     * @return The found {@link ReportSetColumnFraction} object.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get report set column fraction by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Report set column fraction retrieved successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Report set column fraction not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid ID format"
    )
    public ResponseEntity<ReportSetColumnFraction> findOne(@PathVariable String id) {
        try {
            ReportSetColumnFraction association = reportSetColumnFractionsService.findOne(Integer.valueOf(id));
            return ResponseEntity.ok(association);
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid ID format");
        }
    }

    /**
     * Soft-deletes a column-fraction association by its ID.
     *
     * @param id The string representation of the association's ID.
     * @return A 200 OK response upon successful deletion.
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete report set column fraction by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Report set column fraction deleted successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Report set column fraction not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid ID format"
    )
    public ResponseEntity<Void> remove(@PathVariable String id) {
        try {
            reportSetColumnFractionsService.remove(Integer.valueOf(id));
            return ResponseEntity.ok().build();
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid ID format");
        }
    }
}