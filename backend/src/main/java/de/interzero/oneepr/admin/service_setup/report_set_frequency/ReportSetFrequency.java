package de.interzero.oneepr.admin.service_setup.report_set_frequency;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingService;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;

import java.time.Instant;

@Getter
@Setter
@Entity
@Table(
        name = "report_set_frequency",
        schema = "public"
)
public class ReportSetFrequency {

    public enum Rhythm {
        ANNUALLY,
        MONTHLY,
        QUARTERLY
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "rhythm",
            nullable = false
    )
    @JsonProperty("rhythm")
    private Rhythm rhythm;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false,
            updatable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @NotNull
    @Column(
            name = "frequency",
            nullable = false
    )
    @JdbcTypeCode(SqlTypes.JSON)
    @JsonProperty("frequency")
    private String frequency;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "packaging_service_id",
            nullable = false
    )
    @JsonIgnore
    @JsonProperty("packaging_service")
    private PackagingService packagingService;

    @PrePersist
    protected void onCreate() {
        this.createdAt = this.updatedAt = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = Instant.now();
    }

    // --- Transient Getter for Foreign Key ---

    @Transient
    @JsonProperty("packaging_service_id")
    public Integer getPackagingServiceId() {
        return (this.packagingService != null) ? this.packagingService.getId() : null;
    }
}