package de.interzero.oneepr.admin.service_setup.service_detail.packaging_service;

import de.interzero.oneepr.admin.country.Country;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for managing {@link PackagingService} entities.
 */
@Repository
public interface PackagingServiceRepository extends JpaRepository<PackagingService, Integer> {

    /**
     * Finds all non-deleted packaging services.
     *
     * @return A list of packaging services.
     */
    List<PackagingService> findAllByDeletedAtIsNull();

    /**
     * Finds all non-deleted packaging services for a specific country.
     *
     * @param countryId The ID of the country to filter by.
     * @return A list of packaging services for the given country.
     */
    List<PackagingService> findAllByCountry_IdAndDeletedAtIsNull(Integer countryId);

    /**
     * Finds a single non-deleted packaging service by its ID.
     *
     * @param id The ID of the packaging service.
     * @return An optional containing the found service.
     */
    @NonNull
    Optional<PackagingService> findByIdAndDeletedAtIsNull(@NonNull Integer id);

    List<PackagingService> findByCountryAndDeletedAtIsNull(Country country);

    /**
     * Finds all active (non-deleted) packaging services for a given country ID.
     * <p>
     * This method filters services where the {@code deletedAt} timestamp is null,
     * effectively retrieving only the records that are currently active.
     *
     * @param countryId The ID of the country for which to retrieve packaging services.
     * @return A list of active {@link PackagingService} entities.
     */
    @Query("SELECT ps FROM PackagingService ps WHERE ps.country.id = :countryId AND ps.deletedAt IS NULL")
    List<PackagingService> findActiveByCountryId(@Param("countryId") Integer countryId);

    List<PackagingService> findByCountry_CodeAndDeletedAtIsNull(String countryCode);

}