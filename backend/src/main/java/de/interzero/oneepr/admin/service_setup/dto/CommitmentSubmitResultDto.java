package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.other_cost.OtherCost;
import de.interzero.oneepr.admin.representative_tier.RepresentativeTier;
import de.interzero.oneepr.admin.required_information.RequiredInformation;
import lombok.Data;

import java.util.List;

/**
 * Data Transfer Object representing the core 'setup' data generated from a user's commitment submission.
 * <p>
 * This DTO serves as the main payload within the {@code ServiceSetupCommitmentResponseDto} and is
 * returned by the {@code ServiceSetupService.submitServiceSetupCommitment} method.
 * <p>
 * It is needed because the result of a commitment submission is a complex aggregation of data
 * derived from multiple entities and business logic. This object consolidates the selected
 * country details, the applicable packaging services (with their calculated obligation status),
 * the chosen representative tier, a filtered list of other costs, and the relevant price list for
 * the specified year. By structuring the response this way, the API provides a clear,
 * - * self-contained result without exposing the entire underlying entity graph, ensuring the response
 * is tailored precisely to the operation's outcome.
 */
@Data
public class CommitmentSubmitResultDto {

    @JsonProperty("country")
    private CommitmentResultCountryDto country;

    @JsonProperty("year")
    private String year;

    @JsonProperty("packaging_services")
    private List<CommitmentPackagingServiceDto> packagingServices;

    @JsonProperty("authorize_representative_obligated")
    private boolean authorizeRepresentativeObligated;

    @JsonProperty("representative_tier")
    private RepresentativeTier representativeTier;

    @JsonProperty("other_costs_obligated")
    private boolean otherCostsObligated;

    @JsonProperty("other_costs")
    private List<OtherCost> otherCosts;

    @JsonProperty("required_informations")
    private List<RequiredInformation> requiredInformations;

    @JsonProperty("price_list")
    private PriceListDto priceList;
}