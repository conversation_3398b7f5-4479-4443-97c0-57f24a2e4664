package de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.fraction_icon.FractionIcon;
import de.interzero.oneepr.admin.price_list.ReportSetPriceListItem;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSet;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_column_fractions.ReportSetColumnFraction;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Getter
@Setter
@Entity
@Table(
        name = "report_set_fraction",
        schema = "public"
)
public class ReportSetFraction {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("name")
    private String name;

    @NotNull
    @Column(
            name = "description",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("description")
    private String description;

    @Column(name = "parent_id")
    @JsonProperty("parent_id")
    private Integer parentId;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false,
            updatable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @NotNull
    @Column(
            name = "is_active",
            nullable = false
    )
    @JsonProperty("is_active")
    private Boolean isActive = true;

    @NotNull
    @Column(
            name = "level",
            nullable = false
    )
    @JsonProperty("level")
    private Integer level = 1;

    @NotNull
    @Column(
            name = "\"order\"",
            nullable = false
    )
    @JsonProperty("order")
    private Integer order = 1;

    @NotNull
    @Column(
            name = "code",
            nullable = false,
            unique = true,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("code")
    private String code;

    @NotNull
    @Column(
            name = "icon",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("icon")
    private String icon = "aluminium";

    @NotNull
    @Column(
            name = "has_second_level",
            nullable = false
    )
    @JsonProperty("has_second_level")
    private Boolean hasSecondLevel = false;

    @NotNull
    @Column(
            name = "has_third_level",
            nullable = false
    )
    @JsonProperty("has_third_level")
    private Boolean hasThirdLevel = false;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "fraction_icon_id")
    @JsonIgnore
    @JsonProperty("fraction_icon")
    private FractionIcon fractionIcon;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(
            name = "parent_code",
            referencedColumnName = "code"
    )
    @JsonProperty("parent")
    @JsonBackReference
    private ReportSetFraction parent;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "report_set_id",
            nullable = false
    )
    @JsonIgnore
    @JsonProperty("report_set")
    private ReportSet reportSet;

    @OneToMany(mappedBy = "reportSetFraction")
    @JsonIgnore
    @JsonProperty("columns")
    private List<ReportSetColumnFraction> columns = new ArrayList<>();

    @OneToMany(mappedBy = "parent")
    @JsonManagedReference
    @JsonProperty("children")
    private Set<ReportSetFraction> children = new HashSet<>();

    @OneToMany(mappedBy = "reportSetFraction")
    @JsonIgnore
    @JsonProperty("price_list_items")
    private List<ReportSetPriceListItem> priceListItems = new ArrayList<>();

    @PrePersist
    protected void onCreate() {
        this.createdAt = this.updatedAt = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        this.updatedAt = Instant.now();
    }

    // --- Transient Getters for Foreign Key IDs ---

    @Transient
    @JsonProperty("report_set_id")
    public Integer getReportSetId() {
        return (this.reportSet != null) ? this.reportSet.getId() : null;
    }

    @Transient
    @JsonProperty("fraction_icon_id")
    public Integer getFractionIconId() {
        return (this.fractionIcon != null) ? this.fractionIcon.getId() : null;
    }

    @Transient
    @JsonProperty("parent_code")
    public String getParentCode() {
        return (this.parent != null) ? this.parent.getCode() : null;
    }

    public void addColumn(ReportSetColumnFraction column) {
        this.columns.add(column);
    }
}