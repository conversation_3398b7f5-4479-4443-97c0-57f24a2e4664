package de.interzero.oneepr.admin.service_setup.fraction_set.report_set;

import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingService;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * Repository for managing {@link ReportSet} entities.
 */
@Repository
public interface ReportSetRepository extends JpaRepository<ReportSet, Integer>, JpaSpecificationExecutor<ReportSet> {

    /**
     * Soft-deletes all ReportSet entities associated with a specific packaging service ID.
     *
     * @param packagingServiceId The ID of the parent packaging service.
     * @param now                The current timestamp to set as the deleted_at value.
     */
    @Modifying
    @Query("UPDATE ReportSet rs SET rs.deletedAt = :now WHERE rs.packagingService.id = :packagingServiceId")
    void softDeleteByPackagingServiceId(@Param("packagingServiceId") Integer packagingServiceId,
                                        @Param("now") Instant now);

    Optional<ReportSet> findReportSetByIdAndDeletedAtIsNull(Integer packagingServiceId);

    List<ReportSet> findByPackagingServiceInAndDeletedAtIsNull(List<PackagingService> packagingServices);

    List<ReportSet> findByPackagingService_IdAndDeletedAtIsNull(Integer packagingServiceId);

    long countByPackagingService_IdAndDeletedAtIsNull(Integer packagingServiceId);

    /**
     * Finds all active ReportSets for a given country code, eagerly fetching the parent PackagingService.
     * The DISTINCT keyword is critical to prevent Hibernate from returning duplicate root entities
     * when using JOIN FETCH, ensuring a correct and predictable result set size.
     */
    @Query(
            "SELECT DISTINCT rs FROM ReportSet rs JOIN FETCH rs.packagingService ps  WHERE ps.country.code = :countryCode  AND ps.deletedAt IS NULL  AND rs.deletedAt IS NULL"
    )
    List<ReportSet> findActiveByCountryCodeWithPackagingService(@Param("countryCode") String countryCode);

    /**
     * Finds a single active ReportSet by its ID and the associated country's code.
     * <p>
     * An entity is considered active if both the ReportSet and its parent PackagingService
     * have not been soft-deleted. This method uses eager fetching ({@code JOIN FETCH} and
     * {@code LEFT JOIN FETCH}) to load the {@code packagingService} and the optional
     * {@code sheetFile} relations in a single database query, preventing N+1 performance issues.
     *
     * @param reportSetId The ID of the ReportSet to find.
     * @param countryCode The unique code of the country to which the ReportSet must belong.
     * @return An {@link Optional} containing the found {@link ReportSet} with its
     * associations loaded, or an empty Optional if no matching active ReportSet is found.
     */
    @Query(
            "SELECT rs FROM ReportSet rs  JOIN FETCH rs.packagingService ps  LEFT JOIN FETCH rs.sheetFile sf  WHERE rs.id = :reportSetId  AND ps.country.code = :countryCode  AND rs.deletedAt IS NULL  AND ps.deletedAt IS NULL"
    )
    Optional<ReportSet> findActiveByIdAndCountryCodeWithAssociations(@Param("reportSetId") Integer reportSetId,
                                                                     @Param("countryCode") String countryCode);
}