package de.interzero.oneepr.admin.service_setup.obligation_check.obligation_check_section.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * This DTO is used as the request body for creating a new ObligationCheckSection.
 * Origin: This DTO is newly created based on the ObligationCheckSection entity.
 * Purpose: It captures the necessary data from an admin user to create a new section within an obligation check.
 * Function: Represents the data required to create a new section, including title and display order.
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class CreateObligationCheckSectionDto extends BaseDto {

    @Schema(
            description = "The title of the section, displayed as a tab name.",
            example = "Section 1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @NotBlank
    @JsonProperty("title")
    private String title;

    @Schema(
            description = "The display order of the section tab.",
            example = "1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @NotNull
    @JsonProperty("display_order")
    private Integer displayOrder;

    @NotNull
    @JsonProperty("country_code")
    @Schema(
            description = "The country code of the section tab",
            example = "AT",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    private String countryCode;
}