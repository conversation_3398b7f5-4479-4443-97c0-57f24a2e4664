package de.interzero.oneepr.admin.service_setup.fraction_set.report_set.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * This DTO is used as the request body for creating a new ReportSetFraction.
 * It contains all fields necessary to define a fraction and its position in a hierarchy,
 * omitting fields that are auto-generated by the database or system.
 */
@Getter
@Setter
public class ReportSetFractionCreateDto extends BaseDto {

    @Schema(
            description = "The unique identifier of the fraction. Omit for creation.",
            example = "101"
    )
    @JsonProperty("id")
    private Integer id;

    @Schema(
            description = "The display name of the fraction.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "Plastics"
    )
    @JsonProperty("name")
    @NotBlank
    private String name;

    @Schema(
            description = "A detailed description of the fraction.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "All plastic-based packaging materials."
    )
    @JsonProperty("description")
    @NotBlank
    private String description;

    @Schema(
            description = "The ID of the parent fraction. Omit or set to null for root fractions.",
            example = "100"
    )
    @JsonProperty("parent_id")
    private Integer parentId;

    @Schema(
            description = "Indicates if the fraction is currently active.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "true"
    )
    @JsonProperty("is_active")
    @NotNull
    private Boolean isActive;

    @Schema(
            description = "The nesting level of the fraction in the hierarchy (1 for root).",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "1"
    )
    @JsonProperty("level")
    @NotNull
    private Integer level;

    @Schema(
            description = "The display order of the fraction among its siblings.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "1"
    )
    @JsonProperty("order")
    @NotNull
    private Integer order;

    @Schema(
            description = "A unique code for the fraction, used for linking.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "PLASTICS"
    )
    @JsonProperty("code")
    @NotBlank
    private String code;

    @Schema(
            description = "The name of the icon associated with the fraction.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "plastics"
    )
    @JsonProperty("icon")
    @NotBlank
    private String icon;

    @Schema(
            description = "Indicates if this fraction has second-level children.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "true"
    )
    @JsonProperty("has_second_level")
    @NotNull
    private Boolean hasSecondLevel;

    @Schema(
            description = "Indicates if this fraction has third-level children.",
            requiredMode = Schema.RequiredMode.REQUIRED,
            example = "false"
    )
    @JsonProperty("has_third_level")
    @NotNull
    private Boolean hasThirdLevel;

    @Schema(
            description = "The ID of the associated fraction icon entity. Can be null.",
            example = "503"
    )
    @JsonProperty("fraction_icon_id")
    private Integer fractionIconId;

    @Schema(
            description = "The code of the parent fraction. Omit or set to null for root fractions.",
            example = "MATERIALS"
    )
    @JsonProperty("parent_code")
    private String parentCode;
}