package de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list.ReportSetPriceList;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;

/**
 * Data Transfer Object for partially updating a report set price list.
 */
@Getter
@Setter
public class UpdateReportSetPriceListDto extends BaseDto {

    @Schema(description = "Title of the report set price list")
    @JsonProperty("title")
    private String title;

    @Schema(description = "License year of the report set price list")
    @JsonProperty("license_year")
    private Integer licenseYear;

    @Schema(description = "Start date of the report set price list")
    @JsonProperty("start_date")
    private Instant startDate;

    @Schema(description = "End date of the report set price list")
    @JsonProperty("end_date")
    private Instant endDate;

    @Schema(
            description = "Type of the report set price list",
            implementation = ReportSetPriceList.Type.class
    )
    @JsonProperty("type")
    private ReportSetPriceList.Type type;

    @Schema(description = "Fixed price of the report set price list")
    @JsonProperty("fixed_price")
    private Integer fixedPrice;

    @Schema(description = "Base price of the report set price list")
    @JsonProperty("base_price")
    private Integer basePrice;

    @Schema(description = "Minimum fee of the report set price list")
    @JsonProperty("minimum_fee")
    private Integer minimumFee;
}