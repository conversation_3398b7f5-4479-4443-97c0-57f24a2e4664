package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class SubmitCommitmentDto extends BaseDto {

    @Schema(
            description = "The year",
            example = "2025"
    )
    @JsonProperty("year")
    private Integer year;

    @Schema(
            description = "The commitment answers",
            example = """
                    [
                      { "id": 1, "answer": "OBLIGED" },
                      { "id": 2, "answer": "REQUEST" }
                    ]
                    """
    )
    @JsonProperty("commitment")
    private List<CommitmentAnswerDto> commitment;

    /**
     * Inner static DTO for nested commitment answer structures.
     */
    @Getter
    @Setter
    public static class CommitmentAnswerDto extends BaseDto {

        @JsonProperty("id")
        private Integer id;

        @JsonProperty("answer")
        private String answer;

        public CommitmentAnswerDto(Integer id,
                                   String obliged) {
            super();
            this.id = id;
            this.answer = obliged;
        }
        @SuppressWarnings("unused")
        public CommitmentAnswerDto() {

        }
    }
}