package de.interzero.oneepr.admin.required_information;

import de.interzero.oneepr.admin.required_information.dto.CreateRequiredInformationDto;
import de.interzero.oneepr.admin.required_information.dto.UpdateRequiredInformationDto;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.Role;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

/**
 * REST controller for managing Required Information entries.
 * Access is restricted to specific administrative roles.
 */
@RestController
@RequestMapping(Api.ADMIN_REQUIRED_INFORMATIONS)
@Tag(name = "RequiredInformation")
@RequiredArgsConstructor
@Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
public class RequiredInformationController {

    private final RequiredInformationService requiredInformationService;

    /**
     * Creates a new required information entry.
     *
     * @param data DTO containing the details for the new entry.
     * @return The newly created RequiredInformation entity with a 200 OK status.
     */
    @PostMapping
    @Operation(summary = "Create a new required information")
    @ApiResponse(
            responseCode = "200",
            description = "Required information created successfully",
            content = @Content(schema = @Schema(implementation = RequiredInformation.class))
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid country ID"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Country not found"
    )
    public RequiredInformation create(@RequestBody CreateRequiredInformationDto data) {
        return this.requiredInformationService.create(data);
    }

    /**
     * Retrieves all active required information entries.
     *
     * @return A list of RequiredInformation entities.
     */
    @GetMapping
    @Operation(summary = "Get all required information")
    @ApiResponse(
            responseCode = "200",
            description = "Required information retrieved successfully"
    )
    public List<RequiredInformation> findAll() {
        return this.requiredInformationService.findAll();
    }

    /**
     * Retrieves a single required information entry by its ID.
     *
     * @param id The string representation of the entry's ID.
     * @return The found RequiredInformation entity.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get required information by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Required information retrieved successfully",
            content = @Content(schema = @Schema(implementation = RequiredInformation.class))
    )
    @ApiResponse(
            responseCode = "404",
            description = "Required information not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid required information ID"
    )
    public RequiredInformation findOne(@PathVariable String id) {
        try {
            return this.requiredInformationService.findOne(Integer.valueOf(id));
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid required information ID format");
        }
    }

    /**
     * Updates an existing required information entry by its ID.
     *
     * @param id   The string representation of the entry's ID.
     * @param data DTO containing the fields to update.
     * @return The updated RequiredInformation entity.
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update required information by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Required information updated successfully",
            content = @Content(schema = @Schema(implementation = RequiredInformation.class))
    )
    @ApiResponse(
            responseCode = "404",
            description = "Required information not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid required information ID"
    )
    public RequiredInformation update(@PathVariable String id,
                                      @RequestBody UpdateRequiredInformationDto data) {
        try {
            return this.requiredInformationService.update(Integer.valueOf(id), data);
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid required information ID format");
        }
    }

    /**
     * Soft-deletes a required information entry and its children by its ID.
     *
     * @param id The string representation of the entry's ID.
     * @return An HTTP 200 OK response on successful deletion.
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete required information by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Required information deleted successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Required information not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid required information ID"
    )
    public ResponseEntity<Void> remove(@PathVariable String id) {
        try {
            this.requiredInformationService.remove(Integer.valueOf(id));
            return ResponseEntity.ok().build();
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid required information ID format");
        }
    }
}