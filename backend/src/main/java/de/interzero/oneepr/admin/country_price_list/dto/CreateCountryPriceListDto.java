package de.interzero.oneepr.admin.country_price_list.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

/**
 * DTO for creating a new country price list association.
 */
@Getter
@Setter
public class CreateCountryPriceListDto extends BaseDto {

    @Schema(
            description = "ID of the price list",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @NotNull
    @JsonProperty("price_list_id")
    private Integer priceListId;

    @Schema(
            description = "ID of the country",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @NotNull
    @JsonProperty("country_id")
    private Integer countryId;
}