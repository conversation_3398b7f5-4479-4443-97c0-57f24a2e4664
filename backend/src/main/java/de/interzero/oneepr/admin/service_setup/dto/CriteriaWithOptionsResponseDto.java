package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.criteria.Criteria;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * Data Transfer Object for the response of the findServiceSetupCriterias method.
 * It represents a Criteria entity and includes its list of associated CriteriaOption entities,
 * which mirrors the 'include: { options: true }' from the original Prisma query.
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class CriteriaWithOptionsResponseDto {

    @Schema(
            description = "The unique identifier of the criteria.",
            example = "1"
    )
    @JsonProperty("id")
    private Integer id;

    @Schema(
            description = "Help text for the criteria.",
            example = "Choose one option."
    )
    @JsonProperty("help_text")
    private String helpText;

    @Schema(
            description = "The type of input control to render.",
            example = "RADIO"
    )
    @JsonProperty("input_type")
    private Criteria.InputType inputType;

    @Schema(
            description = "The mode of the criteria.",
            example = "COMMITMENT"
    )
    @JsonProperty("mode")
    private Criteria.Mode mode;

    @Schema(
            description = "The title of the criteria.",
            example = "Do you agree?"
    )
    @JsonProperty("title")
    private String title;

    @Schema(
            description = "The type of the criteria.",
            example = "REQUIRED_INFORMATION"
    )
    @JsonProperty("type")
    private Criteria.Type type;

    @Schema(
            description = "The type of calculator associated with the criteria.",
            example = "LICENSE_FEES"
    )
    @JsonProperty("calculator_type")
    private Criteria.CalculatorType calculatorType;

    @Schema(description = "A list of options for this criteria.")
    @JsonProperty("options")
    private List<CriteriaOptionDto> options;

    @JsonProperty("country_id")
    private Integer countryId;

    @JsonProperty("packaging_service_id")
    private Integer packagingServiceId;

    @JsonProperty("required_information_id")
    private Integer requiredInformationId;
}