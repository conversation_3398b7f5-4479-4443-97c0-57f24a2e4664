package de.interzero.oneepr.admin.country.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.dto.CriteriaResponseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;
import java.util.List;

/**
 * This DTO is a direct representation of the serialized Country entity.
 * It is used as the response body for endpoints that return country details.
 */
@Schema(description = "Detailed response object for a country")
@Data
public class CountryResponseDto {

    @Schema(
            description = "The unique identifier for the country.",
            example = "1"
    )
    @JsonProperty("id")
    private Integer id;

    @Schema(
            description = "The name of the country.",
            example = "Germany"
    )
    @JsonProperty("name")
    private String name;

    @Schema(description = "Timestamp of when the country was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "Timestamp of the last update to the country.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(
            description = "Flag indicating if an authorized representative is obligated for this country.",
            example = "true"
    )
    @JsonProperty("authorize_representative_obligated")
    private Boolean authorizeRepresentativeObligated;

    @Schema(
            description = "The two-letter ISO country code.",
            example = "DE"
    )
    @JsonProperty("code")
    private String code;

    @Schema(
            description = "URL of the country's flag image.",
            example = "https://example.com/flags/de.svg"
    )
    @JsonProperty("flag_url")
    private String flagUrl;

    @Schema(
            description = "Flag indicating if other costs are obligated for this country.",
            example = "false"
    )
    @JsonProperty("other_costs_obligated")
    private Boolean otherCostsObligated;

    @Schema(
            description = "Flag indicating if the country is published and visible to users.",
            example = "true"
    )
    @JsonProperty("is_published")
    private Boolean isPublished;

    @JsonProperty("criterias")
    private List<CriteriaResponseDto> criterias;

    @JsonProperty("license_required")
    private Boolean licenseRequired;
}