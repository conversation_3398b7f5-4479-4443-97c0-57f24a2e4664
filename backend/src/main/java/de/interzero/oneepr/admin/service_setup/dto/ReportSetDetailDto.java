package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.fraction_icon.Files;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingService;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSet;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_columns.ReportSetColumn;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions.ReportSetFraction;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list.ReportSetPriceList;
import lombok.AccessLevel;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * Represents a detailed, fully populated view of a single {@link ReportSet} for the service setup interface.
 *
 * <p><b>Why this DTO exists:</b>
 * This DTO is designed to be the comprehensive data structure for displaying or editing a single report set.
 * It encapsulates not only the ReportSet's own data but also its complete hierarchy of related collections
 * (fractions, columns, price lists) and a calculated boolean flag indicating if its parent packaging service
 * has relevant commitment criteria.
 *
 * <p><b>What this DTO contains:</b>
 * It is a direct mapping of a {@code ReportSet} entity after it has been fully populated with its
 * related collections by the service layer, plus the {@code has_criteria} boolean flag.
 *
 * <p><b>Where this DTO is used:</b>
 * It is the return type for the {@code ServiceSetupService.findServiceSetupReportSet} method and is serialized
 * as the JSON response for the corresponding controller endpoint.
 */
@Data
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ReportSetDetailDto {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("mode")
    private ReportSet.ReportSetMode mode;

    @JsonProperty("type")
    private ReportSet.ReportSetType type;

    @JsonProperty("created_at")
    private Instant createdAt;

    @JsonProperty("updated_at")
    private Instant updatedAt;

    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @JsonProperty("sheet_file_description")
    private String sheetFileDescription;

    // --- Fields that differ from the Entity's serialization ---

    @JsonProperty("packaging_service")
    private PackagingService packagingService;

    @JsonProperty("sheet_file")
    private Files sheetFile;

    @JsonProperty("has_criteria")
    private boolean hasCriteria;


    @JsonProperty("columns")
    private List<ReportSetColumn> columns;

    @JsonProperty("fractions")
    private List<ReportSetFraction> fractions;

    @JsonProperty("price_lists")
    private List<ReportSetPriceList> priceLists;

    /**
     * Factory method to create a {@code ReportSetDetailDto} from a source entity.
     * <p>
     * This method maps fields from the {@link ReportSet} entity to this DTO. It is the
     * responsibility of the caller to ensure that all lazy-loaded associations on the
     * source entity (e.g., packagingService, sheetFile, columns, fractions, priceLists)
     * have been initialized within a transactional context before this method is called.
     *
     * @param reportSet   The fully populated {@link ReportSet} entity.
     * @param hasCriteria A boolean indicating if the parent packaging service has the relevant criteria.
     * @return A new, populated {@code ReportSetDetailDto} instance.
     */
    public static ReportSetDetailDto fromEntity(ReportSet reportSet,
                                                boolean hasCriteria) {
        ReportSetDetailDto dto = new ReportSetDetailDto();

        // Map scalar fields
        dto.setId(reportSet.getId());
        dto.setName(reportSet.getName());
        dto.setMode(reportSet.getMode());
        dto.setType(reportSet.getType());
        dto.setCreatedAt(reportSet.getCreatedAt());
        dto.setUpdatedAt(reportSet.getUpdatedAt());
        dto.setDeletedAt(reportSet.getDeletedAt());
        dto.setSheetFileDescription(reportSet.getSheetFileDescription());

        // Map full nested objects
        dto.setPackagingService(reportSet.getPackagingService());
        dto.setSheetFile(reportSet.getSheetFile());

        // Map nested collections
        dto.setColumns(reportSet.getColumns());
        dto.setFractions(reportSet.getFractions());
        dto.setPriceLists(reportSet.getPriceLists());

        // Set the calculated flag
        dto.setHasCriteria(hasCriteria);

        return dto;
    }
}