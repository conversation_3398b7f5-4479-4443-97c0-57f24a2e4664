package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonUnwrapped;
import de.interzero.oneepr.admin.country_price_list.CountryPriceList;
import de.interzero.oneepr.admin.fraction_icon.Files;
import de.interzero.oneepr.admin.other_cost.OtherCost;
import de.interzero.oneepr.admin.packaging_service.PackagingService;
import de.interzero.oneepr.admin.price_list.PriceList;
import de.interzero.oneepr.admin.price_list.ReportSetPriceListItem;
import de.interzero.oneepr.admin.report_set.ReportSet;
import de.interzero.oneepr.admin.report_set_column_fractions.ReportSetColumnFraction;
import de.interzero.oneepr.admin.report_set_columns.ReportSetColumn;
import de.interzero.oneepr.admin.report_set_fractions.ReportSetFraction;
import de.interzero.oneepr.admin.report_set_frequency.ReportSetFrequency;
import de.interzero.oneepr.admin.report_set_frequency.dto.CreateReportSetFrequencyDto;
import de.interzero.oneepr.admin.report_set_price_list.ReportSetPriceList;
import de.interzero.oneepr.admin.representative_tier.RepresentativeTier;
import de.interzero.oneepr.admin.required_information.RequiredInformation;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.util.List;

/**
 * Represents the complete data setup for a country's services.
 * <p>
 * This DTO uses composition to aggregate data from multiple base entities,
 * creating a clean and well-structured response for the API endpoint.
 * The @JsonUnwrapped annotation is used to flatten the properties of the base
 * entity into the current level, mimicking the original intent of extension.
 */
@Getter
@Setter
public class ServiceSetupDto {

    private Integer id;

    private String name;

    private Instant createdAt;

    private Instant updatedAt;

    private Boolean authorizeRepresentativeObligated;

    private String code;

    private String flagUrl;

    private Boolean otherCostsObligated;

    private Boolean isPublished;

    @JsonProperty("other_costs")
    private List<OtherCost> otherCosts;

    @JsonProperty("country_price_lists")
    private List<CountryPriceListWithPriceList> countryPriceLists;

    @JsonProperty("packaging_services")
    private List<PackagingServiceWithRelations> packagingServices;

    @JsonProperty("representative_tiers")
    private List<RepresentativeTier> representativeTiers;

    @JsonProperty("required_informations")
    private List<RequiredInformationWithFile> requiredInformations;

    @Getter
    @Setter
    public static class PackagingServiceWithRelations {

        @JsonUnwrapped
        private PackagingService packagingService;

        @JsonProperty("report_set_frequencies")
        private List<ReportSetFrequencyWithFrequency> reportSetFrequencies;

        @JsonProperty("report_sets")
        private List<ReportSetWithRelations> reportSets;
    }

    @Getter
    @Setter
    public static class ReportSetFrequencyWithFrequency {

        private Integer id;

        private ReportSetFrequency.Rhythm rhythm;

        private Instant createdAt;

        private Instant updatedAt;

        private Instant deletedAt;

        private Integer packagingServiceId;

        private CreateReportSetFrequencyDto frequency;
    }

    @Getter
    @Setter
    public static class ReportSetWithRelations {

        private Integer id;

        private String name;

        private ReportSet.ReportSetMode mode;

        private ReportSet.ReportSetType type;

        private Instant createdAt;

        private Instant updatedAt;

        private Instant deletedAt;

        private Integer packagingServiceId;

        private String sheetFileId;

        private String sheetFileDescription;

        @JsonProperty("fractions")
        private List<ReportSetFraction> fractions;

        @JsonProperty("columns")
        private List<ReportSetColumnWithChildren> columns;

        @JsonProperty("price_lists")
        private List<ReportSetPriceListWithItems> priceLists;
    }

    @Getter
    @Setter
    public static class ReportSetColumnWithChildren {

        @JsonUnwrapped
        private ReportSetColumn reportSetColumn;

        @JsonProperty("children")
        private List<ReportSetColumnWithFractions> children;
    }

    @Getter
    @Setter
    public static class ReportSetColumnWithFractions {

        @JsonUnwrapped
        private ReportSetColumn reportSetColumn;

        @JsonProperty("fractions")
        private List<ReportSetColumnFraction> fractions;
    }

    @Getter
    @Setter
    public static class ReportSetPriceListWithItems {

        @JsonUnwrapped
        private ReportSetPriceList reportSetPriceList;

        @JsonProperty("items")
        private List<ReportSetPriceListItem> items;
    }

    @Getter
    @Setter
    public static class RequiredInformationWithFile {

        @JsonUnwrapped
        private RequiredInformation requiredInformation;

        @JsonProperty("file")
        private Files file;
    }

    @Getter
    @Setter
    public static class CountryPriceListWithPriceList {

        @JsonUnwrapped
        private CountryPriceList countryPriceList;

        @JsonProperty("price_list")
        private PriceList priceList;
    }
}