package de.interzero.oneepr.admin.service_setup.obligation_check.obligation_check_section.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class UpdateObligationCheckSectionDto {

    @Schema(
            description = "The title of the section, displayed as a tab name.",
            example = "Section 1",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @NotBlank
    @JsonProperty("title")
    private String title;

    @Schema(
            description = "The display order of the section tab.",
            example = "1"
    )
    @JsonProperty("display_order")
    private Integer displayOrder;

    @JsonProperty("country_code")
    @Schema(
            description = "The country code of the section tab",
            example = "AT"
    )
    private String countryCode;
}
