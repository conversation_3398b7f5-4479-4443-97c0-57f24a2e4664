package de.interzero.oneepr.admin.service_setup.fraction_set.report_set.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ReportSetFindAllDto extends BaseDto {

    @Schema(
            description = "ID of the packaging service",
            requiredMode = Schema.RequiredMode.REQUIRED
    )
    @JsonProperty("packaging_service_id")
    private Integer packagingServiceId;
}
