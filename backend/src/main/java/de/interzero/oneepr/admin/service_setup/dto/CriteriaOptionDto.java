package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.time.Instant;

/**
 * A helper Data Transfer Object representing a CriteriaOption entity.
 * <p>
 * This DTO is used within CriteriaWithOptionsResponseDto to represent the nested 'options'
 * that were included via the Prisma 'include' statement. Its purpose is to provide a
 * structured representation of a criteria option record.
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class CriteriaOptionDto {

    @Schema(
            description = "The unique identifier of the criteria option.",
            example = "1"
    )
    @JsonProperty("id")
    private Integer id;

    @Schema(
            description = "The display value of the option.",
            example = "Yes, I agree"
    )
    @JsonProperty("value")
    private String value;

    @Schema(
            description = "The underlying technical value of the option.",
            example = "AGREE"
    )
    @JsonProperty("option_value")
    private String optionValue;

    @Schema(
            description = "An optional secondary value for range-based options.",
            example = "100"
    )
    @JsonProperty("option_to_value")
    private String optionToValue;

    @Schema(description = "The timestamp of when the record was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "The timestamp of the last update.")
    @JsonProperty("updated_at")
    private Instant updatedAt;


    @Schema(
            description = "The ID of the associated criteria.",
            example = "49"
    )
    @JsonProperty("criteria_id")
    private Integer criteriaId;
}