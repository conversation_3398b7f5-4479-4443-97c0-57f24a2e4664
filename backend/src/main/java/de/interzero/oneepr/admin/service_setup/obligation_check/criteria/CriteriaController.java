package de.interzero.oneepr.admin.service_setup.obligation_check.criteria;

import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.dto.CreateCriteriaDto;
import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.dto.CriteriaResponseDto;
import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.dto.UpdateCriteriaDto;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.Role;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.ArraySchema;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping(Api.CRITERIAS)
@Tag(name = "Criteria")
@RequiredArgsConstructor
public class CriteriaController {

    private final CriteriaService criteriaService;

    @PostMapping
    @Operation(summary = "Create a new criteria")
    @ApiResponse(
            responseCode = "201",
            description = "Criteria created successfully",
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = Criteria.class)
            )
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid input",
            content = @Content
    )
    @Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
    public ResponseEntity<CriteriaResponseDto> create(@Valid @RequestBody CreateCriteriaDto createDto) {
        return new ResponseEntity<>(criteriaService.create(createDto), HttpStatus.CREATED);
    }

    @GetMapping
    @Operation(summary = "Get all criterias")
    @ApiResponse(
            responseCode = "200",
            description = "All criterias retrieved successfully",
            content = @Content(
                    mediaType = "application/json",
                    array = @ArraySchema(schema = @Schema(implementation = Criteria.class))
            )
    )
    @Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
    public List<CriteriaResponseDto> findAll() {
        return criteriaService.findAll();
    }


    @GetMapping("/section/{id}")
    @Operation(summary = "Get all criterias with section Id")
    @ApiResponse(
            responseCode = "200",
            description = "All criterias retrieved successfully",
            content = @Content(
                    mediaType = "application/json",
                    array = @ArraySchema(schema = @Schema(implementation = Criteria.class))
            )
    )
    public List<CriteriaResponseDto> findAllWithSectionId(@PathVariable Long id) {
        return criteriaService.findAllWithSectionId(id);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get criteria by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Criteria retrieved successfully",
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = Criteria.class)
            )
    )
    @ApiResponse(
            responseCode = "404",
            description = "Criteria not found",
            content = @Content
    )
    @Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
    public CriteriaResponseDto findOne(@PathVariable("id") Integer id) {
        return criteriaService.findOne(id);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update criteria by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Criteria updated successfully",
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = Criteria.class)
            )
    )
    @ApiResponse(
            responseCode = "404",
            description = "Criteria not found",
            content = @Content
    )
    @Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
    public CriteriaResponseDto update(@PathVariable("id") Integer id,
                                      @Valid @RequestBody UpdateCriteriaDto updateDto) {
        return criteriaService.update(id, updateDto);
    }


    @DeleteMapping("/{id}")
    @Operation(summary = "Delete criteria by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Criteria deleted successfully",
            content = @Content
    )
    @ApiResponse(
            responseCode = "404",
            description = "Criteria not found",
            content = @Content
    )
    @Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
    public ResponseEntity<Void> remove(@PathVariable("id") Integer id) {
        criteriaService.remove(id);
        return ResponseEntity.ok().build();
    }
}