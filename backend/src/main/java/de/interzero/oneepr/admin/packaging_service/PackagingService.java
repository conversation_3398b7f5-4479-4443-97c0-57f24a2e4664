package de.interzero.oneepr.admin.packaging_service;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.criteria.Criteria;
import de.interzero.oneepr.admin.criteria.CriteriaOption;
import de.interzero.oneepr.admin.report_set.ReportSet;
import de.interzero.oneepr.admin.report_set_frequency.ReportSetFrequency;
import de.interzero.oneepr.admin.required_information.RequiredInformation;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(
        name = "packaging_service",
        schema = "public"
)
public class PackagingService {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "packaging_service_id_seq"
    )
    @SequenceGenerator(
            name = "packaging_service_id_seq",
            sequenceName = "packaging_service_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("name")
    private String name;

    @NotNull
    @Column(
            name = "description",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("description")
    private String description;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "country_id",
            nullable = false
    )
    @JsonIgnore
    @JsonProperty("country")
    private Country country;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false,
            updatable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @OneToMany(
            mappedBy = "packagingService",
            fetch = FetchType.LAZY
    )
    @JsonIgnore
    @JsonProperty("criterias")
    private List<Criteria> criterias = new ArrayList<>();

    @OneToMany(
            mappedBy = "packagingService",
            fetch = FetchType.LAZY
    )
    @JsonIgnore
    @JsonProperty("report_sets")
    private List<ReportSet> reportSets = new ArrayList<>();

    @OneToMany(
            mappedBy = "packagingService",
            fetch = FetchType.LAZY
    )
    @JsonIgnore
    @JsonProperty("report_set_frequencies")
    private List<ReportSetFrequency> reportSetFrequencies = new ArrayList<>();

    @ManyToMany(
            mappedBy = "packagingServices",
            fetch = FetchType.LAZY,
            cascade = {CascadeType.PERSIST, CascadeType.MERGE}
    )
    private Set<CriteriaOption> criteriaOptions = new HashSet<>();

    /**
     * Exposes the country_id for JSON serialization.
     *
     * @return The ID of the associated Country.
     */
    @Transient
    @JsonProperty("country_id")
    public Integer getCountryId() {
        return (this.country != null) ? this.country.getId() : null;
    }

    /**
     * Exposes the required_information_ids for JSON serialization.
     *
     * @return List of IDs of the associated RequiredInformation entities.
     */
    @Transient
    @JsonProperty("required_information_ids")
    public List<Integer> getRequiredInformationIds() {
        return this.requiredInformations.stream().map(RequiredInformation::getId).toList();
    }

    /**
     * Sets creation and update timestamps before the entity is first persisted.
     */
    @PrePersist
    protected void onCreate() {
        createdAt = updatedAt = Instant.now();
    }

    @ManyToMany(
            mappedBy = "packagingServices",
            fetch = FetchType.LAZY,
            cascade = {CascadeType.PERSIST, CascadeType.MERGE}
    )
    @JsonIgnore
    @JsonProperty("required_informations")
    private List<RequiredInformation> requiredInformations = new ArrayList<>();

    /**
     * Updates the 'updatedAt' timestamp before an existing entity is updated.
     */
    @PreUpdate
    protected void onUpdate() {
        updatedAt = Instant.now();
    }

    public void addCriteria(Criteria criteria) {
        this.criterias.add(criteria);
        criteria.setPackagingService(this);
    }

    public void removeCriteria(Criteria criteria) {
        this.criterias.remove(criteria);
        criteria.setPackagingService(null);
    }

    /**
     * Helper method to add a required information to this packaging service.
     * Maintains bidirectional relationship consistency.
     *
     * @param requiredInformation The required information to add
     */
    public void addRequiredInformation(RequiredInformation requiredInformation) {
        if (!this.requiredInformations.contains(requiredInformation)) {
            this.requiredInformations.add(requiredInformation);
            requiredInformation.getPackagingServices().add(this);
        }
    }

    /**
     * Helper method to remove a required information from this packaging service.
     * Maintains bidirectional relationship consistency.
     *
     * @param requiredInformation The required information to remove
     */
    public void removeRequiredInformation(RequiredInformation requiredInformation) {
        if (this.requiredInformations.contains(requiredInformation)) {
            this.requiredInformations.remove(requiredInformation);
            requiredInformation.getPackagingServices().remove(this);
        }
    }

}
