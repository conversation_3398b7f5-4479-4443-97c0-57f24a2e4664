package de.interzero.oneepr.admin.service_setup.obligation_check.criteria;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CriteriaOptionRepository extends JpaRepository<CriteriaOption, Integer> {

    /**
     * @ts-legacy This custom query is created to efficiently implement the "delete-and-recreate" logic
     * from the original NestJS update method, which used `prisma.criteriaOption.deleteMany`.
     */
    @Modifying
    @Query("DELETE FROM CriteriaOption co WHERE co.criteria.id = :criteriaId")
    void deleteByCriteriaId(Integer criteriaId);
}