package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

/**
 * Data Transfer Object representing the validation status of a country's service setup.
 * <p>
 * This DTO is used as a standardized response for service endpoints that check whether a
 * country's configuration is complete and valid. It is not derived from a single entity but
 * serves as a generic status container.
 * <p>
 * It is needed to provide a clear, structured boolean response from validation logic.
 * The {@code @JsonInclude(JsonInclude.Include.NON_NULL)} annotation is a key feature,
 * ensuring the {@code message} field is only serialized to JSON when the setup is incomplete,
 * which results in a cleaner API contract for success cases (e.g., {@code {"completed": true}}).
 * The static factory methods {@link #completed()} and {@link #incomplete(String)} provide a
 * readable and consistent way to construct status objects within the service layer.
 */
@Getter
@Setter
@AllArgsConstructor()
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ServiceSetupStatusDto {

    private boolean completed;

    private String message;

    /**
     * Creates a success status response.
     */
    public static ServiceSetupStatusDto completed() {
        return new ServiceSetupStatusDto(true, null);
    }

    /**
     * Creates an incomplete/failure status response with a message.
     */
    public static ServiceSetupStatusDto incomplete(String message) {
        return new ServiceSetupStatusDto(false, message);
    }
}