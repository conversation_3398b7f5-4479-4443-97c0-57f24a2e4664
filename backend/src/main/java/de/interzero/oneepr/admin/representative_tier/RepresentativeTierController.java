package de.interzero.oneepr.admin.representative_tier;

import de.interzero.oneepr.admin.representative_tier.dto.CreateRepresentativeTierDto;
import de.interzero.oneepr.admin.representative_tier.dto.UpdateRepresentativeTierDto;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.Role;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

/**
 * REST controller for managing Representative Tier entities.
 * Access is restricted to specific administrative roles.
 */
@RestController
@RequestMapping(Api.ADMIN_REPRESENTATIVE_TIERS)
@Tag(name = "RepresentativeTier")
@RequiredArgsConstructor
@Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
public class RepresentativeTierController {

    private final RepresentativeTierService representativeTierService;

    /**
     * Creates a new representative tier.
     *
     * @param data DTO containing the details for the new tier.
     * @return The newly created RepresentativeTier entity with a 200 OK status.
     */
    @PostMapping
    @Operation(summary = "Create a new representative tier")
    @ApiResponse(
            responseCode = "200",
            description = "Representative tier created successfully",
            content = @Content(schema = @Schema(implementation = RepresentativeTier.class))
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid country ID"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Country not found"
    )
    public RepresentativeTier create(@RequestBody CreateRepresentativeTierDto data) {
        return this.representativeTierService.create(data);
    }

    /**
     * Retrieves all active representative tiers.
     *
     * @return A list of RepresentativeTier entities.
     */
    @GetMapping
    @Operation(summary = "Get all representative tiers")
    @ApiResponse(
            responseCode = "200",
            description = "Representative tiers retrieved successfully"
    )
    public List<RepresentativeTier> findAll() {
        return this.representativeTierService.findAll();
    }

    /**
     * Retrieves a single representative tier by its ID.
     *
     * @param id The string representation of the tier's ID.
     * @return The found RepresentativeTier entity.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get representative tier by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Representative tier retrieved successfully",
            content = @Content(schema = @Schema(implementation = RepresentativeTier.class))
    )
    @ApiResponse(
            responseCode = "404",
            description = "Representative tier not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid representative tier ID"
    )
    public RepresentativeTier findOne(@PathVariable String id) {
        try {
            return this.representativeTierService.findOne(Integer.valueOf(id));
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid representative tier ID format");
        }
    }

    /**
     * Updates an existing representative tier by its ID.
     *
     * @param id   The string representation of the tier's ID.
     * @param data DTO containing the fields to update.
     * @return The updated RepresentativeTier entity.
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update representative tier by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Representative tier updated successfully",
            content = @Content(schema = @Schema(implementation = RepresentativeTier.class))
    )
    @ApiResponse(
            responseCode = "404",
            description = "Representative tier not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid representative tier ID"
    )
    public RepresentativeTier update(@PathVariable String id,
                                     @RequestBody UpdateRepresentativeTierDto data) {
        try {
            return this.representativeTierService.update(Integer.valueOf(id), data);
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid representative tier ID format");
        }
    }

    /**
     * Soft-deletes a representative tier by its ID.
     *
     * @param id The string representation of the tier's ID.
     * @return An HTTP 200 OK response on successful deletion.
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete representative tier by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Representative tier deleted successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Representative tier not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid representative tier ID"
    )
    public ResponseEntity<Void> remove(@PathVariable String id) {
        try {
            this.representativeTierService.remove(Integer.valueOf(id));
            return ResponseEntity.ok().build();
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid representative tier ID format");
        }
    }
}