package de.interzero.oneepr.admin.report_set_frequency;

import de.interzero.oneepr.admin.packaging_service.PackagingService;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ReportSetFrequencyRepository extends JpaRepository<ReportSetFrequency, Integer> {

    /**
     * Finds all frequencies that have not been soft-deleted.
     *
     * @return A list of active ReportSetFrequency entities.
     */
    List<ReportSetFrequency> findAllByDeletedAtIsNull();

    /**
     * Finds a single frequency by its ID, but only if it has not been soft-deleted.
     *
     * @param id The ID of the frequency record.
     * @return An Optional containing the frequency if found and active, otherwise empty.
     */
    Optional<ReportSetFrequency> findByIdAndDeletedAtIsNull(Integer id);

    List<ReportSetFrequency> findByPackagingServiceInAndDeletedAtIsNull(List<PackagingService> packagingServices);

    List<ReportSetFrequency> findByPackagingService_IdAndDeletedAtIsNull(Integer packagingServiceId);

    /**
     * Finds all active (non-deleted) report set frequencies for a given country code.
     * <p>
     * An entity is considered active if both the {@code ReportSetFrequency} and its parent
     * {@code PackagingService} have not been soft-deleted. This method uses a {@code JOIN FETCH}
     * to eagerly load the associated {@code PackagingService} relation in a single database
     * query, preventing potential N+1 performance issues.
     *
     * @param countryCode The unique code of the country for which to retrieve frequencies.
     * @return A list of {@link ReportSetFrequency} entities with their associated
     * {@code PackagingService} eagerly fetched.
     */
    @EntityGraph(attributePaths = "packagingService")
    List<ReportSetFrequency> findByPackagingService_Country_CodeAndDeletedAtIsNullAndPackagingService_DeletedAtIsNull(String countryCode);
}