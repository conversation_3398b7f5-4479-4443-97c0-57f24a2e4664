package de.interzero.oneepr.admin.service_setup.fraction_set.report_set.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.time.Instant;

/**
 * Represents the join entity between a ReportSetColumn and a ReportSetFraction for response serialization.
 * It is a sub-component of the ReportSetColumnDetailDto.
 */
@Data
public class ReportSetColumnFractionDetailDto {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("created_at")
    private Instant createdAt;

    @JsonProperty("updated_at")
    private Instant updatedAt;

    @JsonProperty("fraction_code")
    private String fractionCode;

    @JsonProperty("column_code")
    private String columnCode;
}