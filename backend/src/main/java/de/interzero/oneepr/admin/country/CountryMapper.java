package de.interzero.oneepr.admin.country;

import de.interzero.oneepr.admin.country.dto.CountryResponseDto;
import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.CriteriaMapper;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

/**
 * Mapper for converting the Country entity and its related objects into response DTOs.
 * This mapper uses MapStruct to automate the conversion process.
 */
@Mapper(
        componentModel = "spring",
        // This is the key: it tells MapStruct it can use another mapper for nested objects.
        uses = {CriteriaMapper.class},
        unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface CountryMapper {

    /**
     * Converts a Country entity to its corresponding response DTO.
     * MapStruct will automatically map all fields with the same name.
     * When it encounters the 'criterias' collection, it will see that it needs to map
     * a List<Criteria> to a List<CriteriaResponseDto> and will automatically use the
     * toDto method from the CriteriaMapper specified in the 'uses' attribute.
     *
     * @param country The source Country entity.
     * @return The populated CountryResponseDto.
     */
    CountryResponseDto toResponseDto(Country country);
}