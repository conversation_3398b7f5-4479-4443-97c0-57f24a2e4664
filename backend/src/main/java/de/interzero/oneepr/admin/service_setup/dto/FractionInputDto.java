package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions.ReportSetFraction;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Data Transfer Object representing the input data for a single material fraction in a calculation request.
 * <p>
 * This DTO is a nested component within the request payload for the
 * {@code ServiceSetupService.calculateLicenseCosts} method.
 * It is required to create a simple and explicit structure for the client to submit the essential
 * details for each fraction: its unique {@code code} and its {@code weight}. This decouples the API input
 * from the more complex internal {@link ReportSetFraction} entity.
 */
@Data
public class FractionInputDto {

    @Schema(
            description = "The unique code of the fraction.",
            example = "F001"
    )
    @JsonProperty("code")
    private String code;

    @Schema(
            description = "The weight of the fraction in grams.",
            example = "5000"
    )
    @JsonProperty("weight")
    private double weight;
}