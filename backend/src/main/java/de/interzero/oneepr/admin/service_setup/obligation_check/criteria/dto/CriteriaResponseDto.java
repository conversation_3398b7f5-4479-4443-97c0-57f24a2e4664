package de.interzero.oneepr.admin.service_setup.obligation_check.criteria.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.Criteria;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.Instant;
import java.util.List;
import java.util.Set;

/**
 * DTO representing the response for a Criteria entity.
 * Origin: This DTO is a direct representation of the Criteria entity for API responses.
 * Purpose: It provides a structured and controlled view of the Criteria data sent to the client.
 * Function: Aggregates the core attributes of a Criteria and its child options.
 */
@Data
public class CriteriaResponseDto {

    @Schema(description = "The unique identifier for the criteria.")
    @JsonProperty("id")
    private Integer id;

    @Schema(description = "The timestamp when the criteria was created.")
    @JsonProperty("created_at")
    private Instant createdAt;

    @Schema(description = "The timestamp when the criteria was soft-deleted. Null if active.")
    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @Schema(description = "Optional help text to provide additional context for the question.")
    @JsonProperty("help_text")
    private String helpText;

    @Schema(description = "The type of input control to be rendered (e.g., RADIO, YES_NO).")
    @JsonProperty("input_type")
    private Criteria.InputType inputType;

    @Schema(description = "The mode of the criteria, indicating its purpose (e.g., COMMITMENT).")
    @JsonProperty("mode")
    private Criteria.Mode mode;

    @Schema(description = "The main text or question for the criterion.")
    @JsonProperty("title")
    private String title;

    @Schema(description = "The category or domain this criterion applies to (e.g., PACKAGING_SERVICE).")
    @JsonProperty("type")
    private Criteria.Type type;

    @Schema(description = "The timestamp of the last update.")
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Schema(description = "The type of calculation associated with this criterion, if any.")
    @JsonProperty("calculator_type")
    private Criteria.CalculatorType calculatorType;

    @Schema(description = "The ID of the country this criterion belongs to.")
    @JsonProperty("country_id")
    private Integer countryId;

    @Schema(description = "The ID of the packaging service this criterion is associated with, if any.")
    @JsonProperty("packaging_service_id")
    private Integer packagingServiceId;

    @Schema(description = "The ID of the required information this criterion is associated with, if any.")
    @JsonProperty("required_information_id")
    private Integer requiredInformationId;

    @Schema(description = "The ID of the obligation check section this criterion belongs to, if any.")
    @JsonProperty("obligation_check_section_id")
    private Long obligationCheckSectionId;

    @Schema(description = "A list of answer options available for this criterion.")
    @JsonProperty("options")
    private List<CriteriaOptionResponseDto> options;

    /**
     * Nested DTO representing a single option for a Criteria.
     */
    @Data
    public static class CriteriaOptionResponseDto {

        @Schema(description = "The unique identifier for the option.")
        @JsonProperty("id")
        private Integer id;

        @Schema(description = "The main value or label for the option (e.g., 'Yes', 'Plastic').")
        @JsonProperty("value")
        private String value;

        @Schema(description = "The start value of the option/range.")
        @JsonProperty("option_value")
        private String optionValue;

        @Schema(description = "The end value of the option/range (nullable for single values).")
        @JsonProperty("option_to_value")
        private String optionToValue;

        @Schema(description = "A set of packaging services linked to this specific option.")
        @JsonProperty("packaging_services")
        private Set<PackagingServiceResponseDto> packagingServices;

        @Schema(description = "The ID of the conditional follow-up question linked to this option, if any.")
        @JsonProperty("conditional_criteria_id")
        private Integer conditionalCriteriaId;
    }

    /**
     * A minimal DTO to represent a linked PackagingService within a nested response.
     */
    @Data
    public static class PackagingServiceResponseDto {

        @Schema(description = "The unique identifier for the packaging service.")
        @JsonProperty("id")
        private Integer id;

        @Schema(description = "The name of the packaging service.")
        @JsonProperty("name")
        private String name;
    }
}