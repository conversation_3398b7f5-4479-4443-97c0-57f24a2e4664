package de.interzero.oneepr.admin.report_set_price_list;

import de.interzero.oneepr.admin.report_set_price_list.dto.CreateReportSetPriceListDto;
import de.interzero.oneepr.admin.report_set_price_list.dto.UpdateReportSetPriceListDto;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.Role;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;

/**
 * REST controller for managing Report Set Price List entities.
 * Access is restricted to specific administrative roles.
 */
@RestController
@RequestMapping(Api.REPORT_SET_PRICE_LISTS)
@Tag(name = "ReportSetPriceLists")
@RequiredArgsConstructor
@Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
public class ReportSetPriceListController {

    private final ReportSetPriceListService reportSetPriceListService;

    /**
     * Creates a new report set price list.
     *
     * @param data DTO containing the details for the new price list.
     * @return The newly created ReportSetPriceList entity with a 200 OK status.
     */
    @PostMapping
    @Operation(summary = "Create a new report set price list")
    @ApiResponse(
            responseCode = "200",
            description = "Report set price list created successfully",
            content = @Content(schema = @Schema(implementation = ReportSetPriceList.class))
    )
    public ReportSetPriceList create(@RequestBody CreateReportSetPriceListDto data) {
        return this.reportSetPriceListService.create(data);
    }

    /**
     * Retrieves all active report set price lists.
     *
     * @return A list of ReportSetPriceList entities.
     */
    @GetMapping
    @Operation(summary = "Get all report set price lists")
    @ApiResponse(
            responseCode = "200",
            description = "Report set price lists retrieved successfully"
    )
    public List<ReportSetPriceList> findAll() {
        return this.reportSetPriceListService.findAll();
    }

    /**
     * Retrieves a single report set price list by its ID.
     *
     * @param id The string representation of the price list's ID.
     * @return The found ReportSetPriceList entity.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get report set price list by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Report set price list retrieved successfully",
            content = @Content(schema = @Schema(implementation = ReportSetPriceList.class))
    )
    @ApiResponse(
            responseCode = "404",
            description = "Report set price list not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid report set price list ID"
    )
    public ReportSetPriceList findOne(@PathVariable String id) {
        try {
            return this.reportSetPriceListService.findOne(Integer.valueOf(id));
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid report set price list ID format");
        }
    }

    /**
     * Updates an existing report set price list by its ID.
     *
     * @param id   The string representation of the price list's ID.
     * @param data DTO containing the fields to update.
     * @return The updated ReportSetPriceList entity.
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update report set price list by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Report set price list updated successfully",
            content = @Content(schema = @Schema(implementation = ReportSetPriceList.class))
    )
    @ApiResponse(
            responseCode = "404",
            description = "Report set price list not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid report set price list ID"
    )
    public ReportSetPriceList update(@PathVariable String id,
                                     @RequestBody UpdateReportSetPriceListDto data) {
        try {
            return this.reportSetPriceListService.update(Integer.valueOf(id), data);
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid report set price list ID format");
        }
    }

    /**
     * Soft-deletes a report set price list and its siblings by its ID.
     *
     * @param id The string representation of the price list's ID.
     * @return An HTTP 200 OK response on successful deletion.
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete report set price list by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Report set price list deleted successfully"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Report set price list not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid report set price list ID"
    )
    public ResponseEntity<Void> remove(@PathVariable String id) {
        try {
            this.reportSetPriceListService.remove(Integer.valueOf(id));
            return ResponseEntity.ok().build();
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid report set price list ID format");
        }
    }
}