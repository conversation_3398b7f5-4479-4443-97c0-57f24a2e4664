package de.interzero.oneepr.admin.service_setup.fraction_set.report_set;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.admin.fraction_icon.Files;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingService;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_columns.ReportSetColumn;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions.ReportSetFraction;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list.ReportSetPriceList;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(
        name = "report_set",
        schema = "public"
)
public class ReportSet {

    public enum ReportSetMode {
        ON_PLATAFORM,
        BY_EXCEL,
        FLAT_RATES
    }

    public enum ReportSetType {
        FRACTIONS,
        CATEGORIES
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("name")
    private String name;

    @NotNull
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "mode",
            nullable = false,
            columnDefinition = "report_set_mode"
    )
    @JsonProperty("mode")
    private ReportSetMode mode;

    @NotNull
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "type",
            nullable = false,
            columnDefinition = "report_set_type"
    )
    @JsonProperty("type")
    private ReportSetType type;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false,
            updatable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @Column(
            name = "sheet_file_description",
            length = Integer.MAX_VALUE
    )
    @JsonProperty("sheet_file_description")
    private String sheetFileDescription;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "packaging_service_id",
            nullable = false
    )
    @JsonIgnore
    private PackagingService packagingService;

    @OneToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(
            name = "sheet_file_id",
            unique = true
    )
    @JsonIgnore
    private Files sheetFile;

    @OneToMany(
            mappedBy = "reportSet",
            fetch = FetchType.LAZY,
            cascade = CascadeType.ALL,
            orphanRemoval = true
    )
    @JsonProperty("columns")
    private List<ReportSetColumn> columns = new ArrayList<>();

    @OneToMany(
            mappedBy = "reportSet",
            fetch = FetchType.LAZY,
            cascade = CascadeType.ALL,
            orphanRemoval = true
    )
    @JsonProperty("fractions")
    private List<ReportSetFraction> fractions = new ArrayList<>();

    @OneToMany(
            mappedBy = "reportSet",
            fetch = FetchType.LAZY,
            cascade = CascadeType.ALL,
            orphanRemoval = true
    )
    @JsonProperty("price_lists")
    private List<ReportSetPriceList> priceLists = new ArrayList<>();

    @Transient
    @JsonProperty("packaging_service_id")
    public Integer getPackagingServiceId() {
        return (this.packagingService != null) ? this.packagingService.getId() : null;
    }

    @Transient
    @JsonProperty("sheet_file_id")
    public String getSheetFileId() {
        return (this.sheetFile != null) ? this.sheetFile.getId() : null;
    }

    @PrePersist
    protected void onCreate() {
        createdAt = updatedAt = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = Instant.now();
    }

    // --- Bi-directional relationship helper methods ---

    public void addColumn(ReportSetColumn column) {
        this.columns.add(column);
        column.setReportSet(this);
    }

    public void removeColumn(ReportSetColumn column) {
        this.columns.remove(column);
        column.setReportSet(null);
    }

    public void addFraction(ReportSetFraction fraction) {
        this.fractions.add(fraction);
        fraction.setReportSet(this);
    }

    public void removeFraction(ReportSetFraction fraction) {
        this.fractions.remove(fraction);
        fraction.setReportSet(null);
    }

    public void addPriceList(ReportSetPriceList priceList) {
        this.priceLists.add(priceList);
        priceList.setReportSet(this);
    }

    public void removePriceList(ReportSetPriceList priceList) {
        this.priceLists.remove(priceList);
        priceList.setReportSet(null);
    }
}