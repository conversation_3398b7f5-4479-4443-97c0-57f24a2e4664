package de.interzero.oneepr.admin.service_setup.dto;

import com.fasterxml.jackson.annotation.JsonUnwrapped;
import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.Criteria;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Data Transfer Object used to echo back the commitment criteria along with the user's submitted answer.
 * <p>
 * This DTO is a key component of the {@code ServiceSetupCommitmentResponseDto} and is created
 * within the {@code ServiceSetupService.submitServiceSetupCommitment} method.
 * <p>
 * It is needed to create a specific JSON response format. The {@code @JsonUnwrapped} annotation
 * on the {@code criteria} field is crucial, as it flattens all properties of the
 * {@link Criteria} entity directly into this DTO's
 * JSON representation. This allows the API to return the full context of each criterion
 * alongside its corresponding answer in a clean, non-nested structure, which is often useful
 * for front-end state management.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FormattedCommitmentDto {

    @JsonUnwrapped
    private Criteria criteria;

    private String answer;
}