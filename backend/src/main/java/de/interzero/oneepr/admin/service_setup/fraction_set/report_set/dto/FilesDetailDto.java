package de.interzero.oneepr.admin.service_setup.fraction_set.report_set.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.time.Instant;

/**
 * This DTO represents the detailed, serializable view of a Files entity.
 * It is designed to be returned from API endpoints, often as a nested object
 * within other DTOs like ReportSetDetailDto.
 */
@Data
public class FilesDetailDto {

    @JsonProperty("id")
    private String id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("extension")
    private String extension;

    @JsonProperty("size")
    private String size;

    @JsonProperty("creator_type")
    private String creatorType;

    @JsonProperty("document_type")
    private String documentType;

    @JsonProperty("user_id")
    private String userId;

    @JsonProperty("created_at")
    private Instant createdAt;

    @JsonProperty("updated_at")
    private Instant updatedAt;

    @JsonProperty("original_name")
    private String originalName;

    @JsonProperty("country_id")
    private Integer countryId;
}