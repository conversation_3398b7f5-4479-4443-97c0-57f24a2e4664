package de.interzero.oneepr.admin.service_setup.fraction_set.report_set;

import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.dto.ReportSetCreateDto;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.dto.ReportSetDetailDto;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.dto.ReportSetFindAllDto;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.dto.UpdateReportSetDto;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.Role;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Map;


@RestController
@RequestMapping(Api.REPORT_SETS)
@Tag(name = "ReportSet")
@RequiredArgsConstructor
@Secured({Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK})
public class ReportSetController {

    private final ReportSetService reportSetService;

    private static final String ONE_EXAMPLE = """
                {
                    id: 1,
                    type: "FRACTIONS",
                    mode: "ON_PLATAFORM",
                    name: "Report set name",
                    packaging_service_id: 1,
                    sheet_file_id: null,
                    sheet_file_description: null,
                    created_at: "2024-03-20T10:00:00Z",
                    updated_at: "2024-03-20T10:00:00Z",
                    deleted_at: null,
                }
            """;

    private static final String FIND_ALL_EXAMPLE = """
                [
                    {
                        id: 1,
                        type: "FRACTIONS",
                        mode: "ON_PLATAFORM",
                        name: "Report set name",
                        packaging_service_id: 1,
                        sheet_file_id: null,
                        sheet_file_description: null,
                        created_at: "2024-03-20T10:00:00Z",
                        updated_at: "2024-03-20T10:00:00Z",
                        deleted_at: null,
                    }
                ]
            """;


    /**
     * Creates a new report set.
     *
     * @param data DTO containing the details for the new report set.
     * @return The newly created ReportSet entity.
     */
    @PostMapping
    @Operation(summary = "Create a new report set")
    @ApiResponse(
            responseCode = "201",
            description = "Report set created successfully",
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = ReportSet.class),
                    examples = @ExampleObject(value = ONE_EXAMPLE)
            )
    )
    public ReportSet create(@RequestBody ReportSetCreateDto data) {
        return this.reportSetService.create(data);
    }

    /**
     * find all by packaging_service_id
     *
     * @param data only contains one field packaging_service_id
     * @return a list of ReportSet
     */
    @GetMapping
    @Operation(summary = "Get all report sets")
    @Parameter(
            name = "packaging_service_id",
            description = "Packaging Service ID",
            required = false
    )
    @ApiResponse(
            responseCode = "200",
            description = "Report sets retrieved successfully",
            content = @Content(
                    mediaType = "application/json",
                    examples = @ExampleObject(value = FIND_ALL_EXAMPLE)
            )
    )
    public List<ReportSet> findAll(@RequestBody ReportSetFindAllDto data) {
        return this.reportSetService.findAll(data);
    }

    /**
     * @param id report set id
     * @return report set
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get report set by ID")

    @ApiResponse(
            responseCode = "201",
            description = "Report set retrieved successfully",
            content = @Content(
                    mediaType = "application/json",
                    examples = @ExampleObject(value = ONE_EXAMPLE)
            )
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid report set ID"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Report set not found"
    )
    public ReportSet findOne(@PathVariable String id) {
        try {
            return this.reportSetService.findOne(Integer.valueOf(id));
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid report set ID format");
        }
    }

    /**
     * Updates a report set by its ID and request body.
     *
     * @param id   report set id
     * @param data Report Set data
     * @return ReportSet which is findByReportSetId
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update report set by ID")
    @ApiResponse(
            responseCode = "201",
            description = "Report set updated successfully",
            content = @Content(
                    mediaType = "application/json",
                    examples = @ExampleObject(value = ONE_EXAMPLE)
            )
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid report set ID"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Report set not found"
    )
    public ReportSetDetailDto update(@PathVariable String id,
                                     @RequestBody UpdateReportSetDto data) {
        try {
            return this.reportSetService.update(Integer.valueOf(id), data);
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid report set ID format");
        }
    }


    /**
     * Duplicates a report set by its ID.
     *
     * @param id The string representation of the report set's ID.
     * @return { ok: true }
     * @ts-legacy The original NestJS add a "+" before id e.g. this.reportSetService.duplicate(+id);
     */
    @PostMapping("/{id}/duplicate")
    @Operation(summary = "Duplicate report set by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Report set duplicated successfully",
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = Map.class),
                    examples = @ExampleObject(value = "{'ok': 'true'}")
            )
    )
    @ApiResponse(
            responseCode = "404",
            description = "Report set not found"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid report set ID"
    )
    public Map<String, Boolean> duplicate(@PathVariable String id) {
        try {
            return this.reportSetService.duplicate(Integer.valueOf(id));
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid report set ID format");
        }
    }

    /**
     * remove a report set by its ID. set delete at to Instant.now()
     *
     * @param id The string representation of the report set's ID.
     * @ts-legacy The original NestJS add a "+" before id e.g. this.reportSetService.remove(+id);
     */
    @DeleteMapping("/{id}")
    @Operation(description = "Delete report set by ID")
    @ApiResponse(
            responseCode = "200",
            description = "Report set deleted successfully"
    )
    @ApiResponse(
            responseCode = "400",
            description = "Invalid report set ID"
    )
    @ApiResponse(
            responseCode = "404",
            description = "Report set not found"
    )
    public void remove(@PathVariable String id) {
        try {
            this.reportSetService.remove(Integer.valueOf(id));
        } catch (NumberFormatException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Invalid report set ID format");
        }
    }
}