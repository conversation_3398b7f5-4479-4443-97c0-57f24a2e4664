package de.interzero.oneepr.admin.required_information;

import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.criteria.Criteria;
import de.interzero.oneepr.admin.criteria.CriteriaRepository;
import de.interzero.oneepr.admin.fraction_icon.FilesRepository;
import de.interzero.oneepr.admin.packaging_service.PackagingService;
import de.interzero.oneepr.admin.packaging_service.PackagingServiceRepository;
import de.interzero.oneepr.admin.required_information.dto.CreateRequiredInformationDto;
import de.interzero.oneepr.admin.required_information.dto.UpdateRequiredInformationDto;
import de.interzero.oneepr.common.config.ModelMapperConfig;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.util.List;

/**
 * Service class for managing Required Information business logic.
 */
@Service
@RequiredArgsConstructor
public class RequiredInformationService {

    private final RequiredInformationRepository requiredInformationRepository;

    private final CountryRepository countryRepository;

    private final FilesRepository filesRepository;

    private final CriteriaRepository criteriaRepository;

    private final PackagingServiceRepository packagingServiceRepository;

    private static final String NOT_FOUND = "Required information not found";

    private static final String COUNTRY_NOT_FOUND = "Country not found";

    private static final String FILE_NOT_FOUND = "File not found";

    /**
     * Creates a new RequiredInformation entry.
     *
     * @param createDto DTO with data for the new entry.
     * @return The newly created and persisted RequiredInformation entity.
     */
    @Transactional
    public RequiredInformation create(CreateRequiredInformationDto createDto) {
        RequiredInformation newInfo = ModelMapperConfig.mapPresentFields(createDto, RequiredInformation.class);

        if (createDto.getCountryId() != null) {
            var country = countryRepository.findById(createDto.getCountryId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, COUNTRY_NOT_FOUND));
            newInfo.setCountry(country);
        }

        if (createDto.getFileId() != null) {
            var file = filesRepository.findById(createDto.getFileId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, FILE_NOT_FOUND));
            newInfo.setFiles(file);
        }

        // Handle packaging service relationships
        if (createDto.getPackagingServiceIds() != null && !createDto.getPackagingServiceIds().isEmpty()) {
            List<PackagingService> packagingServices = packagingServiceRepository.findAllById(createDto.getPackagingServiceIds());
            if (packagingServices.size() != createDto.getPackagingServiceIds().size()) {
                throw new ResponseStatusException(HttpStatus.NOT_FOUND, "One or more packaging services not found");
            }
            packagingServices.forEach(newInfo::addPackagingService);
        }

        return requiredInformationRepository.save(newInfo);
    }

    /**
     * Retrieves all non-deleted required information entries.
     *
     * @return A list of active RequiredInformation entities.
     */
    @Transactional(readOnly = true)
    public List<RequiredInformation> findAll() {
        return requiredInformationRepository.findAllByDeletedAtIsNull();
    }

    /**
     * Retrieves a single, non-deleted required information entry by its ID.
     *
     * @param id The ID of the entry to retrieve.
     * @return The found RequiredInformation entity.
     */
    @Transactional(readOnly = true)
    public RequiredInformation findOne(Integer id) {
        return requiredInformationRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, NOT_FOUND));
    }

    /**
     * Partially updates an existing required information entry.
     *
     * @param id        The ID of the entry to update.
     * @param updateDto DTO containing the fields to update.
     * @return The updated RequiredInformation entity.
     */
    @Transactional
    public RequiredInformation update(Integer id,
                                      UpdateRequiredInformationDto updateDto) {
        RequiredInformation info = requiredInformationRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, NOT_FOUND));

        ModelMapperConfig.mapPresentFields(updateDto, info);

        if (updateDto.getCountryId() != null) {
            var country = countryRepository.findById(updateDto.getCountryId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, COUNTRY_NOT_FOUND));
            info.setCountry(country);
        }

        if (updateDto.getFileId() != null) {
            var file = filesRepository.findById(updateDto.getFileId())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, FILE_NOT_FOUND));
            info.setFiles(file);
        }

        // Handle packaging service relationships update
        if (updateDto.getPackagingServiceIds() != null) {
            // Clear existing relationships
            info.getPackagingServices().clear();

            // Add new relationships if any
            if (!updateDto.getPackagingServiceIds().isEmpty()) {
                List<PackagingService> packagingServices = packagingServiceRepository.findAllById(updateDto.getPackagingServiceIds());
                if (packagingServices.size() != updateDto.getPackagingServiceIds().size()) {
                    throw new ResponseStatusException(HttpStatus.NOT_FOUND, "One or more packaging services not found");
                }
                packagingServices.forEach(info::addPackagingService);
            }
        }

        return requiredInformationRepository.save(info);
    }

    /**
     * Soft-deletes a required information entry and all of its direct child criteria.
     *
     * @param id The ID of the entry to soft-delete.
     */
    @Transactional
    public void remove(Integer id) {
        RequiredInformation infoToRemove = requiredInformationRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, NOT_FOUND));

        List<Criteria> relatedCriteria = criteriaRepository.findByRequiredInformation_Id(id);

        Instant deletionTime = Instant.now();

        infoToRemove.setDeletedAt(deletionTime);
        relatedCriteria.forEach(criteria -> criteria.setDeletedAt(deletionTime));

        requiredInformationRepository.save(infoToRemove);
        criteriaRepository.saveAll(relatedCriteria);
    }

    /**
     * Associates packaging services with a required information entry.
     *
     * @param requiredInformationId The ID of the required information entry.
     * @param packagingServiceIds   List of packaging service IDs to associate.
     * @return The updated RequiredInformation entity.
     */
    @Transactional
    public RequiredInformation associatePackagingServices(Integer requiredInformationId,
                                                          List<Integer> packagingServiceIds) {
        RequiredInformation info = requiredInformationRepository.findByIdAndDeletedAtIsNull(requiredInformationId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, NOT_FOUND));

        List<PackagingService> packagingServices = packagingServiceRepository.findAllById(packagingServiceIds);
        if (packagingServices.size() != packagingServiceIds.size()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "One or more packaging services not found");
        }

        packagingServices.forEach(info::addPackagingService);
        return requiredInformationRepository.save(info);
    }

    /**
     * Removes packaging service associations from a required information entry.
     *
     * @param requiredInformationId The ID of the required information entry.
     * @param packagingServiceIds   List of packaging service IDs to disassociate.
     * @return The updated RequiredInformation entity.
     */
    @Transactional
    public RequiredInformation disassociatePackagingServices(Integer requiredInformationId,
                                                             List<Integer> packagingServiceIds) {
        RequiredInformation info = requiredInformationRepository.findByIdAndDeletedAtIsNull(requiredInformationId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, NOT_FOUND));

        List<PackagingService> packagingServices = packagingServiceRepository.findAllById(packagingServiceIds);
        if (packagingServices.size() != packagingServiceIds.size()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "One or more packaging services not found");
        }

        packagingServices.forEach(info::removePackagingService);
        return requiredInformationRepository.save(info);
    }
}