package de.interzero.oneepr.admin.service_setup.fraction_set.report_set.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import java.time.Instant;
import java.util.Set;

/**
 * This DTO represents the detailed, serializable view of a ReportSetFraction entity.
 * It is designed to be returned from API endpoints, supporting a nested structure for children
 * and containing its associated fraction icon. It is a sub-component of ReportSetDetailDto.
 */
@Data
public class ReportSetFractionDetailDto {

    @JsonProperty("id")
    private Integer id;

    @JsonProperty("name")
    private String name;

    @JsonProperty("description")
    private String description;

    @JsonProperty("parent_id")
    private Integer parentId;

    @JsonProperty("created_at")
    private Instant createdAt;

    @JsonProperty("updated_at")
    private Instant updatedAt;

    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @JsonProperty("is_active")
    private Boolean isActive;

    @JsonProperty("level")
    private Integer level;

    @JsonProperty("order")
    private Integer order;

    @JsonProperty("code")
    private String code;

    @JsonProperty("icon")
    private String icon;

    @JsonProperty("has_second_level")
    private Boolean hasSecondLevel;

    @JsonProperty("has_third_level")
    private Boolean hasThirdLevel;

    @JsonProperty("report_set_id")
    private Integer reportSetId;

    @JsonProperty("parent_code")
    private String parentCode;

    @JsonProperty("fraction_icon_id")
    private Integer fractionIconId;

    @JsonProperty("fraction_icon")
    private FractionIconDetailDto fractionIcon;

    @JsonProperty("children")
    private Set<ReportSetFractionDetailDto> children;
}