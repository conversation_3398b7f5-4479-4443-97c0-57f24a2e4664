package de.interzero.oneepr.customer.http;

import de.interzero.oneepr.customer.customer.dto.CreateCustomerDto;
import de.interzero.oneepr.customer.http.dto.UpdateCrmCompanyDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;
import java.util.Map;

/**
 * This is an interface for the CRM service. It is a placeholder for the actual implementation, which will either be
 * implemented in Spring Boot or replaced with a proper service call.
 * <p>
 * Since this should return values based on the CRM service, it is currently a non-working placeholder, but functions
 * TODO should be built here as if they were implemented in the CRM service. The purchaseService is deleted and will start this service again from scratch
 * for a good example of how to call a service defined in this class.
 */
@Slf4j
@Service
public class CrmInterface {

    private final RestTemplate restTemplate;

    private final String crmBaseUrl;

    public CrmInterface(@Value("${services.crm.base-url}") String crmBaseUrl) {
        this.restTemplate = new RestTemplate();
        this.crmBaseUrl = crmBaseUrl;
    }

    /**
     * This method is a placeholder for creating a volume report in the CRM service.
     *
     * @return the ID of the created volume report
     */
    public Long volumeReports(String country,
                              String customerId,
                              String activeYear) {
        log.warn("method not implemented yet");
        // original method can be found in the CRM API in src/volume-reports/volume-reports.service.ts#create(dto: CreateVolumeReportDto)
        return 0L; // Placeholder return value
    }

    /**
     * This method is a placeholder for creating a registration and termination in the CRM service.
     *
     * @return the ID of the created registration and termination
     */
    public Long registrationsAndTerminations(String country,
                                             String customerId,
                                             String activeYear) {
        log.warn("method not implemented yet");
        // original method can be found in the CRM API in src/registration-and-terminations/registration-and-terminations.service.ts#create(dto: CreateRegistrationAndTerminationDto)
        return 0L; // Placeholder return value
    }

    /**
     * This method is a placeholder for creating a third-party invoice in the CRM service.
     * Corresponds to the original axios.post call to a CRM endpoint like "/third-party-invoices".
     *
     * @param country    The country associated with the license of the invoice.
     * @param customerId The customer ID (as a String) associated with the contract of the invoice.
     * @param timeline   The due date of the invoice.
     * @return The ID (e.g., Monday.com item ID as an Integer) of the created third-party invoice in the CRM,
     * or null if creation failed or no ID is returned.
     * @ts-legacy Simulates the external API call previously made with axios to create a CRM record
     * for the third-party invoice. The original call expected some ID back (mondayItemId).
     */
    public Long thirdPartyInvoices(String country,
                                   String customerId,
                                   Instant timeline) {
        log.warn(
                "CrmInterface#thirdPartyInvoices method not implemented yet. " + "Payload would be: country='{}', customerId='{}', timeline='{}'",
                country,
                customerId,
                timeline);
        return 0L;
    }

    /**
     * Updates the customer's email in the CRM system (Monday.com).
     * <p>
     * This mirrors the original Node.js implementation by using {@code oldEmail} to identify the customer,
     * and replacing it with the current email retrieved from the database.     *
     *
     * @param oldEmail The current email address stored in CRM.
     * @param newEmail The new email address to update to.
     */
    public void updateCustomerEmail(String oldEmail,
                                    String newEmail) {
        String url = crmBaseUrl + "/integrations/customer-io/update-email/" + oldEmail;

        Map<String, Object> body = Map.of("newEmail", newEmail);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        try {
            ResponseEntity<Void> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, Void.class);
            if (response.getStatusCode() != HttpStatus.OK) {
                log.warn("CRM returned non-OK status when updating email: {}", response.getStatusCode());
            }
        } catch (Exception e) {
            log.error("Failed to update email in CRM for {} -> {}: {}", oldEmail, newEmail, e.getMessage());
        }
    }

    /**
     * Updates the customer's email in the external CRM system (e.g., Monday).
     * <p>
     * This mirrors the Node.js method MondayService#updateCustomerEmailMonday.
     *
     * @param customerId ID of the customer whose email is being updated
     * @param email      New email address to update in CRM
     */
    public void updateCustomerEmailMonday(String email,
                                          Integer customerId) {
        log.warn(
                "CrmInterface#updateCustomerEmail not implemented yet. Would send PATCH to /accounts with customerId={} and email={}",
                customerId,
                email);

        // TODO: Implement real HTTP request once CRM integration is ready
    }

    /**
     * Updates the board accounts data for the customer in the CRM system.
     * <p>
     * This mirrors the original CRM integration that updates customer company data (used in Monday.com).
     * Currently acts as a placeholder until CRM service is implemented.
     *
     * @param dto        The data object containing fields like VAT, TIN, address, company name, etc.
     * @param customerId The ID of the customer.
     */
    public void updateBoardAccountsMonday(UpdateCrmCompanyDto dto,
                                          Integer customerId) {
        log.warn(
                "CrmInterface#updateBoardAccounts not implemented yet. Would send PUT to /accounts with payload: " + "customerId={}, vatId={}, taxId={}, city={}, postCode={}, country={}, street={}, companyName={}",
                customerId,
                dto.getVat(),
                dto.getTin(),
                dto.getAddress() != null ? dto.getAddress().getCity() : null,
                dto.getAddress() != null ? dto.getAddress().getZipCode() : null,
                dto.getAddress() != null ? dto.getAddress().getCountryCode() : null,
                dto.getAddress() != null ? dto.getAddress().getStreetAndNumber() : null,
                dto.getName());

        // TODO: Implement real HTTP request once CRM integration is ready
    }

    /**
     * Sends customer data to Monday CRM board.
     * <p>
     * This mimics the original integration with Monday.com.
     * Actual implementation via HTTP should be added when CRM is ready.
     *
     * @param dto        Customer creation data
     * @param customerId ID of the customer
     */
    public void sendDataBoardCustomer(CreateCustomerDto dto,
                                      Integer customerId) {
        try {
            log.warn(
                    "CrmInterface#sendDataBoardCustomer not implemented yet. Would send POST to /crm/monday with customerId={} and data: firstName={}, lastName={}, email={}, companyName={}",
                    customerId,
                    dto.getFirstName(),
                    dto.getLastName(),
                    dto.getEmail(),
                    dto.getCompanyName());

            // TODO: Implement real HTTP POST to Monday board when CRM is ready

        } catch (Exception e) {
            log.info("MONDAY ERROR", e);
        }
    }

}