package de.interzero.oneepr.customer.entity;

import de.interzero.oneepr.customer.coupon.Coupon;
import de.interzero.oneepr.customer.partner.Partner;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

@Getter
@Setter
@Entity
@Table(
        name = "coupon_partners",
        schema = "public"
)
public class CouponPartner {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "coupon_partners_id_gen"
    )
    @SequenceGenerator(
            name = "coupon_partners_id_gen",
            sequenceName = "coupon_partners_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "coupon_id",
            nullable = false
    )
    private Coupon coupon;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "partner_id",
            nullable = false
    )
    private Partner partner;

}