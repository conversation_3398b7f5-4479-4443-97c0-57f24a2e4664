package de.interzero.oneepr.customer.shared.auth;

import lombok.Data;

/**
 * This Java class is a direct adaptation of an {@code AuthenticatedUser}
 * concept and structure originating from the Node.js codebase.
 * </p>
 * Represents an authenticated user's identity within the application.
 * <p>
 * This immutable class encapsulates essential attributes of a user who has successfully
 * passed authentication, including their unique identifier ({@code id}),
 * assigned {@link Role}, and {@code email} address.
 * <p>
 * Instances are typically created either for regular users with all identifying attributes
 * or for a special {@code SYSTEM} user. The {@code SYSTEM} user, creatable via the
 * static factory method {@link #forSystemRole()}, is unique in that it may have a {@code null}
 * ID and email, distinguishing it from standard user representations. This special handling
 * is evident in its dedicated private constructor and factory method.
 * </p>
 * <p>
 * The structure and concept of this class are analogous to mechanisms for handling
 * authenticated user data derived from request contexts (e.g., headers) in web applications,
 * ensuring consistent access to user identity information throughout the system.
 * </p>
 *
 * @see Role
 */
@Data
public class AuthenticatedUser {

    private final String id;

    private final Role role;

    private final String email;

    /**
     * Constructor for regular users
     *
     * @param id    the id credential
     * @param role  the role credential
     * @param email the email credential
     */
    public AuthenticatedUser(String id,
                             Role role,
                             String email) {
        this.id = id;
        this.role = role;
        this.email = email;
    }

    /**
     * Private constructor for the SYSTEM role factory method
     *
     * @param role the role credential
     * @ts-legacy SYSTEM role might not have an ID or email
     */
    private AuthenticatedUser(Role role) {
        if (role != Role.SYSTEM) {
            throw new IllegalArgumentException("This constructor is only for the SYSTEM role.");
        }
        this.id = null;
        this.role = role;
        this.email = null;
    }

    /**
     * Static factory method for SYSTEM role
     *
     * @return String
     */
    public static AuthenticatedUser forSystemRole() {
        return new AuthenticatedUser(Role.SYSTEM);
    }


    /**
     * toString() for debugging or use in collections
     *
     * @return String
     */
    @Override
    public String toString() {
        return "AuthenticatedUser{" + "id='" + id + '\'' + ", role=" + role + ", email='" + email + '\'' + '}';
    }
}