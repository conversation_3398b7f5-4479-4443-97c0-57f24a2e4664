package de.interzero.oneepr.customer.entity;

import de.interzero.oneepr.customer.customer_commitment.dto.ReportSetFrequency;
import de.interzero.oneepr.customer.license_packaging_service.LicensePackagingService;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
@Entity
@Table(
        name = "license_report_set_frequency",
        schema = "public"
)
public class LicenseReportSetFrequency {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "license_report_set_frequency_id_gen"
    )
    @SequenceGenerator(
            name = "license_report_set_frequency_id_gen",
            sequenceName = "license_report_set_frequency_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @NotNull
    @Column(
            name = "setup_report_set_frequency_id",
            nullable = false
    )
    private Integer setupReportSetFrequencyId;

    @NotNull
    @OneToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "license_packaging_service_id",
            nullable = false
    )
    private LicensePackagingService licensePackagingService;

    @NotNull
    @Column(
            name = "frequency",
            nullable = false
    )
    @JdbcTypeCode(SqlTypes.JSON)
    private ReportSetFrequency.Frequency frequency; // seems this JSON object will always be the same type, so we can use a single field

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @Column(name = "deleted_at")
    private LocalDate deletedAt;

    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(name = "rhythm")
    private Rhythm rhythm;

    public enum Rhythm {
        ANNUALLY,
        MONTHLY,
        QUARTERLY
    }
}