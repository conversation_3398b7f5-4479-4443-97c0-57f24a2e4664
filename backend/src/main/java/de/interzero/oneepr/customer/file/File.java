package de.interzero.oneepr.customer.file;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.certificate.Certificate;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.entity.GeneralInformation;
import de.interzero.oneepr.customer.entity.PartnerContract;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license_required_information.LicenseRequiredInformation;
import de.interzero.oneepr.customer.license_third_party_invoice.LicenseThirdPartyInvoice;
import de.interzero.oneepr.customer.market_material.MarketingMaterial;
import de.interzero.oneepr.customer.termination.Termination;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.time.LocalDate;
import java.util.UUID;

@Getter
@Setter
@Entity
@Table(
        name = "file",
        schema = "public"
)
public class File {

    @Id
    @JsonProperty("id")
    @Column(
            name = "id",
            nullable = false,
            updatable = false,
            columnDefinition = "text"
    )
    private String id;

    @NotNull
    @Column(
            name = "user_id",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String userId;

    @NotNull
    @Column(
            name = "name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String name;

    @NotNull
    @Column(
            name = "original_name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String originalName;

    @NotNull
    @Column(
            name = "extension",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String extension;

    @NotNull
    @Column(
            name = "size",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String size;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @Column(name = "deleted_at")
    private LocalDate deletedAt;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "required_information_id")
    private LicenseRequiredInformation requiredInformation;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "contract_id")
    private Contract contract;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "certificate_id")
    private Certificate certificate;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "license_id")
    private License license;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "termination_id")
    private Termination termination;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "general_information_id")
    private GeneralInformation generalInformation;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "third_party_invoice_id")
    private LicenseThirdPartyInvoice thirdPartyInvoice;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "marketing_material_id")
    private MarketingMaterial marketingMaterial;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "partner_contract_id")
    private PartnerContract partnerContract;

    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(name = "type")
    private Type type;

    /**
     * @ts-legacy the original code do not have the order table so we just set the order_id to file
     */
    @Column(name = "order_id")
    private Integer orderId;

    public enum Type {
        GENERAL_INFORMATION,
        REQUIRED_INFORMATION,
        CONTRACT,
        CONTRACT_TERMINATION,
        LICENSE_CONTRACT,
        CERTIFICATE,
        INVOICE,
        PAYMENT,
        LICENSE_PROOF_OF_REGISTRATION,
        PROOF_OF_TERMINATION,
        THIRD_PARTY_INVOICE,
        MARKETING_MATERIAL,
        PARTNER_CONTRACT,
    }

    @PrePersist
    protected void onCreate() {
        if (this.id == null) {
            this.id = UUID.randomUUID().toString();
        }
        createdAt = Instant.now();
        updatedAt = Instant.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = Instant.now();
    }


}