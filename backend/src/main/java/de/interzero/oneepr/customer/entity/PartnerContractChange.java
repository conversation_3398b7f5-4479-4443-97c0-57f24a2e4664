package de.interzero.oneepr.customer.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

@Getter
@Setter
@Entity
@Table(
        name = "partner_contract_change",
        schema = "public"
)
public class PartnerContractChange {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "partner_contract_change_id_gen"
    )
    @SequenceGenerator(
            name = "partner_contract_change_id_gen",
            sequenceName = "partner_contract_change_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "partner_contract_id",
            nullable = false
    )
    private PartnerContract partnerContract;

}