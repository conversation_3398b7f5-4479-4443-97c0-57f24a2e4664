package de.interzero.oneepr.customer.invite;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.customer.Customer;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.math.BigDecimal;
import java.time.Instant;

@Getter
@Setter
@Entity
@Table(
        name = "customer_invitation",
        schema = "public"
)
public class CustomerInvitation {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "customer_invitation_id_gen"
    )
    @SequenceGenerator(
            name = "customer_invitation_id_gen",
            sequenceName = "customer_invitation_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "comission_date",
            nullable = false
    )
    @JsonProperty("comission_date")
    private Instant comissionDate;

    @NotNull
    @Column(
            name = "product",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("product")
    private String product;

    @NotNull
    @Column(
            name = "comission",
            nullable = false,
            precision = 65,
            scale = 30
    )
    @JsonProperty("comission")
    private BigDecimal comission;

    @NotNull
    @Column(
            name = "order_number",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("order_number")
    private String orderNumber;

    @NotNull
    @Column(
            name = "lead_source",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("lead_source")
    private String leadSource;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "customer_id",
            nullable = false
    )
    @JsonIgnore
    @JsonProperty("customer_id")
    private Customer customer;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "invited_customer_id")
    @JsonIgnore
    @JsonProperty("invited_customer_id")
    private Customer invitedCustomer;

    @Transient
    @JsonProperty("customer_id")
    public Integer getCustomerId() {
        return customer != null ? customer.getId() : null;
    }

    @Transient
    @JsonProperty("invited_customer_id")
    public Integer getInvitedCustomerId() {
        return invitedCustomer != null ? invitedCustomer.getId() : null;
    }
}