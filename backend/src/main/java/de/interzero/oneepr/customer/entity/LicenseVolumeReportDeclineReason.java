package de.interzero.oneepr.customer.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
@Entity
@Table(
        name = "license_volume_report_decline_reason",
        schema = "public"
)
public class LicenseVolumeReportDeclineReason {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "license_volume_report_decline_reason_id_gen"
    )
    @SequenceGenerator(
            name = "license_volume_report_decline_reason_id_gen",
            sequenceName = "license_volume_report_decline_reason_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "license_volume_report_error_id",
            nullable = false
    )
    private LicenseVolumeReportError licenseVolumeReportError;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "report_decline_reason_id",
            nullable = false
    )
    private ReportDeclineReason reportDeclineReason;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @Column(name = "deleted_at")
    private LocalDate deletedAt;

}