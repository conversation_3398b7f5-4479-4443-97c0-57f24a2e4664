package de.interzero.oneepr.customer.invite;

import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.invite.dto.CreateInviteDto;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;

/**
 * Implementation of the InviteService interface.
 */
@Service
@RequiredArgsConstructor
public class InviteService {

    private final CustomerInvitationRepository customerInvitationRepository;

    private final CustomerRepository customerRepository;

    /**
     * Creates a new customer invitation.
     *
     * @param createInviteDto the data transfer object containing invitation details
     * @return the created customer invitation
     */
    @Transactional
    public CustomerInvitation create(CreateInviteDto createInviteDto) {
        CustomerInvitation invitation = new CustomerInvitation();

        invitation.setComission(createInviteDto.getCommission());
        invitation.setComissionDate(Instant.now());
        invitation.setProduct(createInviteDto.getProduct());
        invitation.setOrderNumber(createInviteDto.getOrderNumber());
        invitation.setCustomer(customerRepository.getReferenceById(createInviteDto.getCustomerId()));
        invitation.setLeadSource(createInviteDto.getLeadSource());

        if (createInviteDto.getInvitedCustomerId() != null) {
            Customer invitedCustomer = customerRepository.findById(createInviteDto.getInvitedCustomerId()).orElse(null);
            invitation.setInvitedCustomer(invitedCustomer);
        }

        return customerInvitationRepository.save(invitation);
    }

    /**
     * Finds all customer invitations associated with a customer ID.
     *
     * @param customerId the ID of the customer
     * @return list of invitations for the customer
     */
    @Transactional(readOnly = true)
    public List<CustomerInvitation> findByCustomerId(Integer customerId) {
        return customerInvitationRepository.findByCustomer_Id(customerId);
    }
}
