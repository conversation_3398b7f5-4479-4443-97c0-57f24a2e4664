package de.interzero.oneepr.customer.cluster;

import de.interzero.oneepr.common.config.ModelMapperConfig;
import de.interzero.oneepr.customer.cluster.dto.ClustersPaginatedDto;
import de.interzero.oneepr.customer.cluster.dto.CreateClusterDto;
import de.interzero.oneepr.customer.cluster.dto.FindAllClustersPaginatedDto;
import de.interzero.oneepr.customer.cluster.dto.UpdateClusterDto;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Lazy
public class ClusterService {

    private final ClusterRepository clusterRepository;

    private final ClusterCustomerRepository clusterCustomerRepository;

    private final CustomerRepository customerRepository;

    /**
     * Creates a new cluster
     *
     * @param createClusterDto the DTO
     * @return the created cluster
     * @ts-legacy created and updated date should draw from the same value instead of being created separately.
     * @ts-legacy tests will fail, since the createdAt and updatedAt datas are not set in the original code, but are not nullable
     */
    @Transactional
    public Cluster create(CreateClusterDto createClusterDto) {
        List<Customer> allCustomers = customerRepository.findAllById(createClusterDto.getCustomers());

        if (allCustomers.size() != createClusterDto.getCustomers().size()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Some customers do not exist");
        }
        Cluster cluster = ModelMapperConfig.mapPresentFields(createClusterDto, Cluster.class);
        cluster.setCustomers(new ArrayList<>());

        for (Customer c : allCustomers) {
            ClusterCustomer clusterCustomer = new ClusterCustomer();
            clusterCustomer.setCluster(cluster);
            clusterCustomer.setCustomer(c);
            cluster.getCustomers().add(clusterCustomer);
        }
        return clusterRepository.save(cluster);
    }

    /**
     * Finds all clusters
     *
     * @return the list of clusters
     */
    public List<Cluster> findAll() {
        return clusterRepository.findAllByDeletedAtIsNull();
    }

    /**
     * Finds one cluster by id
     *
     * @return one cluster
     */
    public Cluster findOne(Integer id) {
        return clusterRepository.findByIdAndDeletedAtIsNull(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "this cluster not found " + id));
    }


    /**
     * Updates a cluster
     *
     * @param id               the ID of the cluster to update
     * @param updateClusterDto the DTO with the new values
     * @return the updated cluster
     * @throws ResponseStatusException with HttpStatus.NOT_FOUND when 'to update entity' is not found
     * @ts-legacy the ClusterCustomer entities are not updated, only created or deleted.
     * @ts-legacy the ModelMapper misbehaves when mapping the customers list from the DTO to the entity
     */
    @Transactional
    public Cluster update(Integer id,
                          UpdateClusterDto updateClusterDto) {
        Cluster cluster = findOne(id);
        if (cluster == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Cluster not found");
        }

        List<Integer> customers = updateClusterDto.getCustomers();

        if (customers != null) {
            final List<Customer> allCustomers = customerRepository.findAllById(customers);

            if (allCustomers.size() != customers.size()) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Some customers do not exist");
            }
        }

        List<ClusterCustomer> updatedClusterCustomers = null;   // not in original code, but needed to work around the ModelMapper issue
        if (customers != null && !customers.isEmpty()) {
            clusterCustomerRepository.deleteAllByCluster_Id(id);

            List<ClusterCustomer> clusterCustomers = customers.stream().map(customerId -> {
                ClusterCustomer clusterCustomer = new ClusterCustomer();
                clusterCustomer.setCluster(cluster);
                clusterCustomer.setCustomer(customerRepository.getReferenceById(customerId));
                return clusterCustomer;
            }).toList();
            updatedClusterCustomers = clusterCustomerRepository.saveAll(clusterCustomers);
        }

        // because the DTO and entity share the same field name "customers" but have different types, the ModelMapper
        // tries to map the customers list from the DTO to the entity, which is not needed here and is causing issues.
        // I've tried to set the ModelMapper to ignore fields like this in general, but it didn't work as expected.
        // So, the customer list of the entity is set manually after the mapping.
        ModelMapperConfig.mapPresentFields(updateClusterDto, cluster);
        cluster.setCustomers(updatedClusterCustomers);

        return clusterRepository.save(cluster);
    }

    /**
     * Removes a cluster (setting deletedAt date only)
     *
     * @param id the ID of the cluster to remove
     * @throws ResponseStatusException with HttpStatus.NOT_FOUND when 'to remove entity' is not found
     */
    public void remove(Integer id) {
        Cluster cluster = clusterRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "this cluster not found " + id));
        cluster.setDeletedAt(Instant.now());
        clusterRepository.save(cluster);
    }

    /**
     * Finds page of clusters
     *
     * @param dto Information for filter like page, size, start and end date
     * @return the page of clusters as a list
     */
    public ClustersPaginatedDto findAllPaginated(FindAllClustersPaginatedDto dto) {
        final int pageOffset = dto.getPage() - 1; // JS is 1-based, Spring is 0-based
        final int limit = dto.getLimit();
        Pageable pageable = PageRequest.of(pageOffset, limit);

        // create the specification for filtering
        Specification<Cluster> specification = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            predicates.add(cb.isNull(root.get("deletedAt")));

            if (dto.getName() != null) {
                predicates.add(cb.like(cb.lower(root.get("name")), dto.getName().toLowerCase() + "%"));
            }

            // parse the dates to Instant if they are provided
            Instant startDate = null;
            Instant endDate = null;

            if (dto.getStartDate() != null) {
                LocalDate localStart = LocalDate.parse(dto.getStartDate());
                startDate = localStart.atStartOfDay(ZoneOffset.UTC).toInstant();
            }

            if (dto.getEndDate() != null) {
                LocalDate localEnd = LocalDate.parse(dto.getEndDate());
                endDate = localEnd.plusDays(1)
                        .atStartOfDay(ZoneOffset.UTC)
                        .toInstant()
                        .minusNanos(1); // to include the whole end date
            }

            if (startDate != null && endDate != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("registrationStartDate"), startDate));
                predicates.add(cb.lessThanOrEqualTo(root.get("registrationEndDate"), endDate));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        Page<Cluster> page = clusterRepository.findAll(specification, pageable);

        ClustersPaginatedDto clustersPaginatedDto = new ClustersPaginatedDto();
        clustersPaginatedDto.setPages(page.getTotalPages());
        clustersPaginatedDto.setCurrentPage(pageOffset + 1);  //back to JS 1-based page number
        clustersPaginatedDto.setLimit(page.getSize());
        clustersPaginatedDto.setClusters(page.getContent());
        clustersPaginatedDto.setCount(page.getTotalElements());
        return clustersPaginatedDto;
    }

}
