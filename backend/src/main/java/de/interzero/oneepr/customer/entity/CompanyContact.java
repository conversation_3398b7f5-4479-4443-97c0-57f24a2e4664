package de.interzero.oneepr.customer.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import de.interzero.oneepr.customer.company.Company;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
@Entity
@Table(
        name = "company_contact",
        schema = "public"
)
public class CompanyContact {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "company_contact_id_gen"
    )
    @SequenceGenerator(
            name = "company_contact_id_gen",
            sequenceName = "company_contact_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @OneToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "company_id",
            unique = true
    )
    @JsonIgnore
    private Company company;

    @Column(
            name = "name",
            length = Integer.MAX_VALUE
    )
    private String name;

    @Column(
            name = "email",
            length = Integer.MAX_VALUE
    )
    private String email;

    @Column(
            name = "phone_mobile",
            length = Integer.MAX_VALUE
    )
    private String phoneMobile;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @Column(name = "deleted_at")
    private LocalDate deletedAt;

    /**
     * Get company ID from the associated company.
     * This matches the Prisma schema field: company_id Int? @unique
     *
     * @return Company ID or null if no company is associated
     */
    @Transient
    public Integer getCompanyId() {
        return company != null ? company.getId() : null;
    }

    /**
     * Set company ID by creating a Company entity with the given ID.
     * This is a convenience method for setting the company relationship.
     *
     * @param companyId Company ID
     */
    public void setCompanyId(Integer companyId) {
        if (companyId != null) {
            Company companyEntity = new Company();
            companyEntity.setId(companyId);
            this.company = companyEntity;
        } else {
            this.company = null;
        }
    }

}