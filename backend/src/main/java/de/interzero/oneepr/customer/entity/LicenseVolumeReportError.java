package de.interzero.oneepr.customer.entity;

import de.interzero.oneepr.customer.license_volume_report.LicenseVolumeReport;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
@Entity
@Table(
        name = "license_volume_report_error",
        schema = "public"
)
public class LicenseVolumeReportError {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "license_volume_report_error_id_gen"
    )
    @SequenceGenerator(
            name = "license_volume_report_error_id_gen",
            sequenceName = "license_volume_report_error_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "license_volume_report_id")
    private LicenseVolumeReport licenseVolumeReport;

    @ManyToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "license_volume_report_item_id")
    private LicenseVolumeReportItem licenseVolumeReportItem;

    @NotNull
    @Column(
            name = "description",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String description;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @Column(name = "deleted_at")
    private LocalDate deletedAt;

}