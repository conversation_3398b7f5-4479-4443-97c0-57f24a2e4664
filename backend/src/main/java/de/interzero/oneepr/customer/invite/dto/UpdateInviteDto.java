package de.interzero.oneepr.customer.invite.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Data Transfer Object for updating an existing customer invitation.
 * All fields are optional since this is for partial updates.
 *
 * @ts-legacy This class is unused in the current codebase but is included for consistency with the original code.
 */
@SuppressWarnings("unused") // present in original code but not used here
@Data
@EqualsAndHashCode(callSuper = true)
public class UpdateInviteDto extends CreateInviteDto {

}
