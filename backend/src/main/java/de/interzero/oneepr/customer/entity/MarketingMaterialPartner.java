package de.interzero.oneepr.customer.entity;

import de.interzero.oneepr.customer.market_material.MarketingMaterial;
import de.interzero.oneepr.customer.partner.Partner;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

@Getter
@Setter
@Entity
@Table(
        name = "marketing_material_partner",
        schema = "public"
)
public class MarketingMaterialPartner {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "marketing_material_partner_id_gen"
    )
    @SequenceGenerator(
            name = "marketing_material_partner_id_gen",
            sequenceName = "marketing_material_partner_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "marketing_material_id",
            nullable = false
    )
    private MarketingMaterial marketingMaterial;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "partner_id",
            nullable = false
    )
    private Partner partner;

}