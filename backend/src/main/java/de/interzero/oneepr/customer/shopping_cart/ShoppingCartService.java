package de.interzero.oneepr.customer.shopping_cart;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.action_guide.ActionGuide;
import de.interzero.oneepr.admin.mail.EmailMessage;
import de.interzero.oneepr.admin.mail.EmailOutboxGateway;
import de.interzero.oneepr.admin.other_cost.OtherCost;
import de.interzero.oneepr.admin.price_list.PriceList;
import de.interzero.oneepr.admin.price_list.PriceListService;
import de.interzero.oneepr.admin.service_setup.ServiceSetupService;
import de.interzero.oneepr.admin.service_setup.dto.CalculateLicenseCostsDto;
import de.interzero.oneepr.admin.service_setup.dto.CalculateLicenseCostsResponseDto;
import de.interzero.oneepr.admin.service_setup.dto.CountryPriceListResponseDto;
import de.interzero.oneepr.admin.service_setup.dto.PriceListDto;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.contract.ContractRepository;
import de.interzero.oneepr.customer.coupon.Coupon;
import de.interzero.oneepr.customer.coupon.CouponService;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.customer_commitment.CustomerCommitment;
import de.interzero.oneepr.customer.customer_commitment.CustomerCommitmentRepository;
import de.interzero.oneepr.customer.customer_commitment.dto.CustomerServiceSetup;
import de.interzero.oneepr.customer.customer_commitment.dto.RepresentativeTier;
import de.interzero.oneepr.customer.customer_io.CustomerIoService;
import de.interzero.oneepr.customer.entity.ActionGuidePriceList;
import de.interzero.oneepr.customer.entity.LicensePriceList;
import de.interzero.oneepr.customer.entity.LicenseVolumeReportItem;
import de.interzero.oneepr.customer.integration.LicenseServiceContract;
import de.interzero.oneepr.customer.integration.MondayService;
import de.interzero.oneepr.customer.integration.dto.DirectLicenseContractsDto;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license.LicenseRepository;
import de.interzero.oneepr.customer.license_packaging_service.LicensePackagingService;
import de.interzero.oneepr.customer.license_volume_report.LicenseVolumeReport;
import de.interzero.oneepr.customer.partner.Partner;
import de.interzero.oneepr.customer.partner.PartnerRepository;
import de.interzero.oneepr.customer.shared.DirectLicenseCalculator;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import de.interzero.oneepr.customer.shopping_cart.dto.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.util.*;


/**
 * @ts-legacy This DTO is derived from the original Prisma `shoppingCartInclude` interface in the legacy TypeScript codebase.
 *
 * <p>This class defines the structure expected by the frontend for a shopping cart response.
 * It includes only the explicitly selected fields and relations defined in the following interface:</p>
 *
 * <pre>{@code
 * const shoppingCartInclude = {
 *   customer: {
 *     select: {
 *       id: true,
 *       first_name: true,
 *       last_name: true,
 *       email: true,
 *       companies: {
 *         select: {
 *           id: true,
 *           vat: true,
 *           tin: true,
 *           lucid: true,
 *           address: true,
 *           billing: true,
 *         },
 *       },
 *     },
 *   },
 *   coupon: true,
 *   items: {
 *     orderBy: { id: "asc" },
 *     where: { deleted_at: null },
 *   },
 *   customer_commitments: {
 *     where: { deleted_at: null },
 *   },
 * } as const;
 * }</pre>
 *
 * <p><strong>Rules:</strong></p>
 * <ul>
 *   <li>Only fields and relations explicitly included in the interface above should be exposed in this DTO.</li>
 *   <li>Lazy relations and internal fields not part of the include structure must be excluded.</li>
 *   <li>Service layer methods return the {@link ShoppingCart} entity itself.</li>
 *   <li>This DTO is returned only at the controller level after mapping the entity to the response format.</li>
 * </ul>
 */
@SuppressWarnings({"java:S1192", "java:S3776"})
@Slf4j
@RequiredArgsConstructor
@Service
public class ShoppingCartService {

    private final ShoppingCartRepository shoppingCartRepository;

    private final ShoppingCartItemRepository shoppingCartItemRepository;

    private final CustomerCommitmentRepository customerCommitmentRepository;

    private final ContractRepository contractRepository;

    private final CustomerRepository customerRepository;

    private final PartnerRepository partnerRepository;

    private final LicenseRepository licenseRepository;

    private final MondayService mondayService;

    private final CouponService couponService;

    private final CustomerIoService customerIoService;

    private final PriceListService priceListService;

    private final ServiceSetupService serviceSetupService;

    private final ObjectMapper objectMapper;

    private final EmailOutboxGateway emailOutboxGateway;

    /**
     * @ts-legacy This method replicates the behavior of the original `findAll` method from the legacy TypeScript service.
     */
    @Transactional
    public String findAll() {
        return "This action returns all shoppingCart";
    }

    /**
     * <p>Finds an open shopping cart by ID with non-deleted status.</p>
     *
     * @param id the shopping cart ID
     * @return the found {@link ShoppingCart} entity
     * @throws IllegalArgumentException if no cart is found
     * @ts-legacy This method replicates the behavior of the original `findOne` method from the legacy TypeScript service.
     */
    @Transactional(readOnly = true)
    public ShoppingCart findOne(String id) {
        return shoppingCartRepository.findByIdAndStatusAndDeletedAtIsNull(id, ShoppingCart.Status.OPEN)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Cart not found"));
    }

    /**
     * <p>Finds an open shopping cart by email with non-deleted status and verifies access permission
     * based on the authenticated user.</p>
     *
     * @param email the customer email address associated with the cart
     * @param user  the authenticated user making the request
     * @return the found {@link ShoppingCart} entity
     * @throws ResponseStatusException if the cart is not found
     * @throws AccessDeniedException   if the user is not authorized to access the cart
     * @ts-legacy This method replicates the behavior of the original `findOneByEmail` method from the legacy TypeScript service.
     */
    @SuppressWarnings("java:S1066")
    @Transactional(readOnly = true)
    public ShoppingCart findOneByEmail(String email,
                                       AuthenticatedUser user) {
        Optional<ShoppingCart> optionalCart = shoppingCartRepository.findByEmailAndStatusAndDeletedAtIsNull(
                email,
                ShoppingCart.Status.OPEN);

        if (optionalCart.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Cart not found");
        }

        ShoppingCart cart = optionalCart.get();

        if (cart.getEmail() != null) {
            if (user == null || (user.getRole() == Role.CUSTOMER && !cart.getEmail().equals(user.getEmail()))) {
                throw new AccessDeniedException("You do not have access permission to add items to this cart");
            }
        }

        return cart;
    }

    /**
     * <p>Finds the most recently created shopping cart with status PURCHASED (not soft-deleted)
     * and checks access permissions.</p>
     *
     * @param email the customer email address associated with the cart
     * @param user  the authenticated user making the request
     * @return the last {@link ShoppingCart} with status PURCHASED and matching email
     * @throws ResponseStatusException if no cart is found
     * @throws AccessDeniedException   if the user is not authorized to access the cart
     * @ts-legacy This method replicates the behavior of the original `findLastPurchasedByEmail` method from the legacy TypeScript service.
     */
    @SuppressWarnings("java:S1066")
    @Transactional(readOnly = true)
    public ShoppingCart findLastPurchasedByEmail(String email,
                                                 AuthenticatedUser user) {
        Optional<ShoppingCart> optionalCart = shoppingCartRepository.findTopByEmailAndStatusAndDeletedAtIsNullOrderByCreatedAtDescIdDesc(email,
                                                                                                                                         ShoppingCart.Status.PURCHASED);

        if (optionalCart.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Last purchased cart not found");
        }

        ShoppingCart lastPurchased = optionalCart.get();

        if (lastPurchased.getEmail() != null) {
            if (user == null || (user.getRole() == Role.CUSTOMER && !lastPurchased.getEmail()
                    .equals(user.getEmail()))) {
                throw new AccessDeniedException("You do not have access permission to add items to this cart");
            }
        }

        return lastPurchased;
    }

    /**
     * <p>Creates a new shopping cart. If a cart already exists for the given email and {@code force} is false,
     * an exception is thrown. If {@code force} is true, the existing cart and its items are deleted.</p>
     *
     * <p>Triggers Customer.io processing if a matching customer exists for the provided email.</p>
     *
     * @param dto the request payload for cart creation
     * @return the created {@link ShoppingCart} entity with included relations
     * @throws ResponseStatusException if journey is missing or cart already exists
     * @ts-legacy This method replicates the behavior of the original `create` method from the legacy TypeScript service.
     */
    @Transactional
    public ShoppingCart create(CreateShoppingCartDto dto) {
        if (dto.getJourney() == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Journey is required");
        }

        if (dto.getEmail() != null) {
            Optional<ShoppingCart> existing = shoppingCartRepository.findByEmailAndStatusAndDeletedAtIsNull(
                    dto.getEmail(),
                    ShoppingCart.Status.OPEN);

            if (Boolean.TRUE.equals(dto.getForce()) && existing.isPresent()) {
                String existingCartId = existing.get().getId();

                shoppingCartItemRepository.deleteByShoppingCart_Id(existingCartId);
                shoppingCartRepository.deleteById(existingCartId);
            }

            if (!Boolean.TRUE.equals(dto.getForce()) && existing.isPresent()) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Cart already exists");
            }
        }

        ShoppingCart cart = new ShoppingCart();
        cart.setJourney(dto.getJourney());
        cart.setJourneyStep(dto.getJourneyStep());
        cart.setEmail(dto.getEmail());
        cart.setStatus(ShoppingCart.Status.OPEN);
        cart.setSubtotal(0);
        cart.setTotal(0);
        cart.setVatPercentage(0);
        cart.setVatValue(0);
        cart.setCouponValue(0);
        cart.setIsChurned(false);

        ShoppingCart savedCart = shoppingCartRepository.save(cart);

        if (dto.getEmail() != null) {
            customerRepository.findByEmailAndDeletedAtIsNull(dto.getEmail())
                    .filter(customer -> customer.getCompanies() != null && !customer.getCompanies().isEmpty())
                    .ifPresent(customer -> customerIoService.processCartData(customer.getId()));
        }

        return shoppingCartRepository.findByIdAndStatusAndDeletedAtIsNull(savedCart.getId(), ShoppingCart.Status.OPEN)
                .orElseThrow(() -> new ResponseStatusException(
                        HttpStatus.INTERNAL_SERVER_ERROR,
                        "Cart creation failed"));
    }

    /**
     * <p>Updates an existing shopping cart by ID, replacing or modifying its contents based on the provided data.</p>
     *
     * @param cartId the ID of the shopping cart to update
     * @param dto    the update data
     * @param user   the authenticated user performing the update
     * @return the updated ShoppingCart
     * @throws ResponseStatusException if the cart is not found
     * @throws ResponseStatusException if the user is not authorized to access the cart
     */
    @SuppressWarnings({"java:S6809", "java:S117", "java:S1192", "java:S6541", "java:S1066", "java:S125", "java:S3776"})
    @Transactional(timeout = 45)
    @SneakyThrows
    public ShoppingCart update(String cartId,
                               UpdateShoppingCartDto dto,
                               AuthenticatedUser user) {

        ShoppingCart foundCart = shoppingCartRepository.findByIdAndDeletedAtIsNull(cartId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Cart not found"));

        if (foundCart.getEmail() != null) {
            if (user == null || (user.getRole().equals(Role.CUSTOMER) && !foundCart.getEmail()
                    .equals(user.getEmail()))) {
                throw new ResponseStatusException(
                        HttpStatus.FORBIDDEN,
                                                  "You do not have access permission to add items to this cart");
            }
        }

        List<Contract> contracts = new ArrayList<>();

        if (foundCart.getEmail() != null) {
            contracts = contractRepository.findByCustomerEmailAndCustomerDeletedAtIsNull(foundCart.getEmail());
        }

        List<ShoppingCartItem> foundCartItems = shoppingCartItemRepository.findAllByShoppingCartIdAndDeletedAtIsNull(
                cartId);

        if (dto.getItems() != null && dto.getItems().isEmpty() && !foundCartItems.isEmpty()) {
            shoppingCartItemRepository.deleteByShoppingCartId(cartId);
        }

        if (dto.getItems() != null && !dto.getItems().isEmpty()) {
            List<Integer> idsToDelete = foundCartItems.stream()
                    .map(ShoppingCartItem::getId)
                    .filter(id -> dto.getItems()
                            .stream()
                            .noneMatch(newItem -> newItem.getId() != null && newItem.getId().equals(id)))
                    .toList();

            if (!idsToDelete.isEmpty()) {
                shoppingCartItemRepository.deleteByIdInAndShoppingCartId(idsToDelete, cartId);
            }
        }

        List<CustomerCommitment> customerCommitments = Collections.emptyList();

        if (foundCart.getEmail() != null && dto.getItems() != null && !dto.getItems().isEmpty()) {
            List<String> countryCodes = dto.getItems()
                    .stream()
                    .map(ItemDto::getCountryCode)
                    .filter(Objects::nonNull)
                    .distinct()
                    .toList();

            customerCommitments = customerCommitmentRepository.findByCustomerEmailAndCountryCodeInAndDeletedAtIsNull(foundCart.getEmail(),
                                                                                                                     countryCodes);
        }

        if (dto.getItems() != null) {
            for (ItemDto requestItem : dto.getItems()) {
                ShoppingCartItem existingItem = foundCartItems.stream()
                        .filter(item -> Objects.equals(
                                item.getServiceType(),
                                requestItem.getServiceType()) && Objects.equals(
                                item.getCountryCode(),
                                requestItem.getCountryCode()))
                        .findFirst()
                        .orElse(null);

                if (requestItem.getCountryId() == null || requestItem.getCountryCode() == null || requestItem.getCountryName() == null || requestItem.getCountryFlag() == null || requestItem.getServiceType() == null) {
                    throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "There is an error in the items");
                }

                Map<String, Object> priceList = existingItem != null ? existingItem.getPriceList() : null;

                if (priceList == null || !Objects.equals(requestItem.getYear(), existingItem.getYear())) {
                    Contract matchedContract = contracts.stream()
                            .filter(c -> Objects.equals(c.getType(), requestItem.getServiceType()))
                            .findFirst()
                            .orElse(null);

                    if (Contract.Type.ACTION_GUIDE.equals(requestItem.getServiceType())) {
                        priceList = matchedContract != null && matchedContract.getActionGuides() != null ? matchedContract.getActionGuides()
                                .stream()
                                .map(ActionGuide::getPriceList)
                                .filter(Objects::nonNull)
                                .flatMap(List::stream)
                                .map(this::mapActionGuidePriceListToMap)
                                .findFirst()
                                .orElse(null) : null;

                        if (priceList == null) {
                            priceList = this.getServicePriceList(requestItem.getServiceType(), requestItem.getYear());
                        }
                    } else {
                        priceList = matchedContract != null && matchedContract.getLicenses() != null ? matchedContract.getLicenses()
                                .stream()
                                .map(License::getPriceList)
                                .filter(Objects::nonNull)
                                .flatMap(List::stream)
                                .map(this::mapLicensePriceListToMap)
                                .findFirst()
                                .orElse(null) : null;

                        if (priceList == null) {
                            priceList = this.getServicePriceList(requestItem.getServiceType(), requestItem.getYear());
                        }
                    }


                    if (priceList == null) {
                        priceList = getServicePriceList(requestItem.getServiceType(), requestItem.getYear());
                    }
                }

                List<?> packagingServices = requestItem.getPackagingServices();
                if (Contract.Type.DIRECT_LICENSE.equals(requestItem.getServiceType()) && (existingItem == null || existingItem.getPackagingServices() == null) && (packagingServices == null || packagingServices.isEmpty())) {

                    packagingServices = List.of(Map.of(
                            "id",
                            0,
                            "name",
                            "Sales packaging (" + requestItem.getYear() + ")",
                            "fractions",
                            Map.of()));
                }

                ShoppingCartItem upsertedItem = existingItem != null ? existingItem : new ShoppingCartItem();
                upsertedItem.setShoppingCart(foundCart);
                upsertedItem.setCountryId(requestItem.getCountryId());
                upsertedItem.setCountryCode(requestItem.getCountryCode());
                upsertedItem.setCountryName(requestItem.getCountryName());
                upsertedItem.setCountryFlag(requestItem.getCountryFlag());
                upsertedItem.setServiceType(requestItem.getServiceType());
                upsertedItem.setSpecificationType(requestItem.getSpecificationType());
                upsertedItem.setYear(requestItem.getYear());
                upsertedItem.setPriceList(priceList);
                upsertedItem.setPackagingServices(packagingServices.isEmpty() ? null : objectMapper.convertValue(
                        packagingServices, new TypeReference<>() {
                        }));

                shoppingCartItemRepository.saveAndFlush(upsertedItem);

                updateCartItemCalculator(cartId, upsertedItem.getId());
            }
        }

        boolean isChangingJourney = dto.getJourney() != null && !dto.getJourney().equals(foundCart.getJourney());
        int vatPercentage;

        if (dto.getJourney() != null && isChangingJourney) {
            vatPercentage = 0;
        } else if (dto.getVatPercentage() != null) {
            vatPercentage = dto.getVatPercentage();
        } else {
            vatPercentage = foundCart.getVatPercentage() != null ? foundCart.getVatPercentage() : 0;
        }

        Coupon coupon;
        if (dto.getCoupon() != null && foundCart.getCustomer() != null) {
            coupon = couponService.checkCoupon(dto.getCoupon(), foundCart.getCustomer().getId());
        } else {
            coupon = foundCart.getCoupon();
        }

        String affiliateLink = foundCart.getAffiliateLink();
        ShoppingCart.TypeAffiliate affiliateType = foundCart.getAffiliateType();
        Customer affiliateCustomer = foundCart.getAffiliateCustomer();
        Partner affiliatePartner = foundCart.getAffiliatePartner();

        if (dto.getAffiliateCustomerId() != null) {
            Customer affiliate = customerRepository.findByIdAndDeletedAtIsNull(dto.getAffiliateCustomerId())
                    .filter(ac -> !ac.getEmail().equals(foundCart.getEmail()))
                    .orElse(null);
            if (affiliate != null) {
                affiliateCustomer = affiliate;
                affiliatePartner = null;
                affiliateLink = dto.getAffiliateLink() != null ? dto.getAffiliateLink() : affiliateLink;
                affiliateType = dto.getAffiliateType() != null ? dto.getAffiliateType() : affiliateType;
            }
        } else if (dto.getAffiliatePartnerId() != null) {
            Partner partner = partnerRepository.findByIdAndDeletedAtIsNull(dto.getAffiliatePartnerId()).orElse(null);
            if (partner != null) {
                affiliatePartner = partner;
                affiliateCustomer = null;
                affiliateLink = dto.getAffiliateLink() != null ? dto.getAffiliateLink() : affiliateLink;
                affiliateType = dto.getAffiliateType() != null ? dto.getAffiliateType() : affiliateType;
            }
        }

        foundCart.setEmail(dto.getEmail() != null ? dto.getEmail() : foundCart.getEmail());
        foundCart.setVatPercentage(vatPercentage);
        if (dto.getPayment() != null) {
            foundCart.setPayment(objectMapper.convertValue(
                    dto.getPayment(), new TypeReference<>() {
                    }));
        }
        foundCart.setCoupon(coupon);
        foundCart.setJourney(dto.getJourney() != null ? dto.getJourney() : foundCart.getJourney());
        foundCart.setJourneyStep(dto.getJourneyStep() != null ? dto.getJourneyStep() : foundCart.getJourneyStep());
        foundCart.setCouponType(dto.getCoupon() == null ? null : dto.getCouponType());
        foundCart.setCouponUrlLink(dto.getCoupon() == null ? null : dto.getCouponUrlLink());
        foundCart.setAffiliateLink(affiliateLink);
        foundCart.setAffiliateType(affiliateType);
        foundCart.setAffiliateCustomer(affiliateCustomer);
        foundCart.setAffiliatePartner(affiliatePartner);

        ShoppingCart updatedCart = shoppingCartRepository.saveAndFlush(foundCart);

        if (updatedCart.getEmail() != null && updatedCart.getCustomer() != null) {
            String customerName = updatedCart.getCustomer().getFirstName() + " " + updatedCart.getCustomer()
                    .getLastName();
            List<ShoppingCartItem> items = updatedCart.getItems();
            List<String> opportunityProductIds = new ArrayList<>();

            for (ShoppingCartItem item : items.stream()
                    .filter(i -> Contract.Type.EU_LICENSE.equals(i.getServiceType()))
                    .toList()) {

                CustomerCommitment matchingCommitment = customerCommitments.stream()
                        .filter(cc -> Objects.equals(cc.getCountryCode(), item.getCountryCode()))
                        .findFirst()
                        .orElse(null);

                if (matchingCommitment != null && matchingCommitment.getServiceSetup() != null) {
                    CustomerServiceSetup setup = objectMapper.convertValue(
                            matchingCommitment.getServiceSetup(),
                            CustomerServiceSetup.class);
                    List<String> groupedObligedPackagings = setup.getPackagingServices()
                            .stream()
                            .filter(CustomerServiceSetup.PackagingServiceExtended::getObliged)
                            .map(CustomerServiceSetup.PackagingServiceExtended::getName)
                            .toList();

                    mondayService.createLicenseObligation(
                            item.getCountryName(),
                            String.valueOf(foundCart.getCustomer().getId()),
                            foundCart.getCustomer().getEmail(),
                            groupedObligedPackagings);

                    LicenseServiceContract contractRequest = new LicenseServiceContract();
                    contractRequest.setCustomerName(customerName);
                    contractRequest.setCountry(item.getCountryName());
                    contractRequest.setStartingYear(String.valueOf(item.getYear()));
                    contractRequest.setEndingYear(String.valueOf(item.getYear() + 1));
                    contractRequest.setTerminationDate(LocalDate.of(item.getYear() + 1, 1, 1).toString());

                    Map<String, Object> priceList = item.getPriceList();
                    contractRequest.setHandlingFee((Integer) priceList.getOrDefault("handling_fee", 0));
                    contractRequest.setRegistrationFee((Integer) priceList.getOrDefault("registration_fee", 0));
                    contractRequest.setVolumeDependent((Integer) priceList.getOrDefault("variable_handling_fee", 0));

                    Integer licenseServiceId = mondayService.createLicenseServiceContract(
                            contractRequest,
                            foundCart.getCustomer()
                                    .getId());
                    opportunityProductIds.add(licenseServiceId.toString());
                }

                if (Contract.Type.DIRECT_LICENSE.equals(item.getServiceType())) {
                    int activeYear = LocalDate.now().getYear();
                    int endingYear = activeYear + 1;

                    String terminationDate = LocalDate.of(endingYear, 1, 1).toString();
                    String SERVICE_TYPE_SMART = "2";
                    String STATUS_DEAL_IN_PROGRESS = "0";

                    List<Map<String, Object>> packagingServices = objectMapper.convertValue(
                            item.getPackagingServices(), new TypeReference<List<Map<String, Object>>>() {
                            });
                    Map<String, Object> firstPackagingService = packagingServices != null && !packagingServices.isEmpty() ? packagingServices.getFirst() : Map.of();

                    List<DirectLicenseCalculator.Fraction> fractions = List.of();

                    if (firstPackagingService.get("fractions") instanceof Map<?, ?> rawFractionsMap) {
                        fractions = rawFractionsMap.values().stream().filter(v -> v instanceof Map<?, ?>).map(v -> {
                            Map<String, Object> fractionMap = objectMapper.convertValue(
                                    v, new TypeReference<Map<String, Object>>() {
                                    });
                            String name = (String) fractionMap.getOrDefault("name", "");
                            String code = (String) fractionMap.getOrDefault("code", "");
                            Number weightNumber = (Number) fractionMap.getOrDefault("weight", 0);
                            double weight = weightNumber.doubleValue();

                            return new DirectLicenseCalculator.Fraction(name, code, weight);
                        }).toList();
                    }

                    Integer contractVolumeValue = DirectLicenseCalculator.calculateDirectLicenseNetValue(
                            item.getPriceList(),
                            fractions,
                            null);

                    DirectLicenseContractsDto directLicenseContractsDto = new DirectLicenseContractsDto();
                    directLicenseContractsDto.setContractVolumeValue(contractVolumeValue);
                    directLicenseContractsDto.setStatus(STATUS_DEAL_IN_PROGRESS);
                    directLicenseContractsDto.setActiveYear(String.valueOf(activeYear));
                    directLicenseContractsDto.setServiceType(SERVICE_TYPE_SMART);
                    directLicenseContractsDto.setStartingYear(String.valueOf(activeYear));
                    directLicenseContractsDto.setEndingYear(String.valueOf(endingYear));
                    directLicenseContractsDto.setTerminationDate(terminationDate);
                    directLicenseContractsDto.setCustomerId(foundCart.getCustomer().getId().toString());
                    directLicenseContractsDto.setCountry(item.getCountryName());

                    Integer directLicenseId = mondayService.createDirectLicensingContract(directLicenseContractsDto);

                    opportunityProductIds.add(directLicenseId.toString());
                }
            }
            if (!opportunityProductIds.isEmpty()) {
                mondayService.createOpportunity(
                        foundCart.getCustomer().getId().toString(),
                        customerName,
                        opportunityProductIds);
            }

            //@ts-legacy This method directly translates the logic from the NestJS
            if (foundCart.getCustomer() != null) {
                customerIoService.processCartData(foundCart.getCustomer().getId());

                //                EmailMessage message = new EmailMessage(
                //                        "8",
                //                        foundCart.getCustomer().getEmail(),
                //                        "Lizenzero <<EMAIL>>",
                //                        "Save progress",
                //                        Map.of("name", foundCart.getCustomer().getFirstName()));
                //
                //                try {
                //                    emailOutboxGateway.sendEmail(message);
                //                } catch (Exception e) {
                //                    throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
                //                }
            }

            return this.refreshShoppingCartTotal(foundCart.getId());
        }
        return foundCart;
    }

    /**
     * <p>Adds a new shopping cart item for the given country, year and service type.
     * If such an item already exists in the cart, throws a BadRequestException.
     * Also validates that the user has permission to modify the cart based on its email.
     * Sets up packaging services if service type is DIRECT_LICENSE, links price list, and updates cart totals.</p>
     *
     * @param cartId The ID of the shopping cart
     * @param dto    The item creation data
     * @param user   The authenticated user
     * @return The updated shopping cart with totals
     * @throws ResponseStatusException if the cart is not found, the user has no permission, or the item already exists
     * @ts-legacy the shoppingItem lost some attribute that cannot be saved to DB
     */
    @SuppressWarnings({"java:S6809", "java:S1066"})
    @Transactional
    public ShoppingCart addItem(String cartId,
                                CreateShoppingCartItemDto dto,
                                AuthenticatedUser user) {
        ShoppingCart cart = shoppingCartRepository.findById(cartId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Shopping cart not found!"));

        Optional<ShoppingCartItem> existing = shoppingCartItemRepository.findFirstByShoppingCartIdAndYearAndServiceTypeAndCountryCode(cartId,
                                                                                                                                      dto.getYear(),
                                                                                                                                      dto.getServiceType(),
                                                                                                                                      dto.getCountryCode());

        if (existing.isPresent()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Item already exists");
        }

        if (cart.getEmail() != null) {
            if (user == null || (user.getRole() == Role.CUSTOMER && !cart.getEmail().equals(user.getEmail()))) {
                throw new ResponseStatusException(
                        HttpStatus.FORBIDDEN,
                                                  "You do not have access permission to add items to this cart");
            }
        }

        Map<String, Object> priceList = getServicePriceList(dto.getServiceType(), dto.getYear());

        List<PackagingServiceDto> packagingServices = null;

        if (Contract.Type.DIRECT_LICENSE.equals(dto.getServiceType())) {
            PackagingServiceDto packagingServiceDto = new PackagingServiceDto();
            packagingServiceDto.setId(0);
            packagingServiceDto.setName("Direct License");
            packagingServiceDto.setFractions(Map.of());
            packagingServices = List.of(packagingServiceDto);
        }

        ShoppingCartItem newItem = new ShoppingCartItem();
        newItem.setShoppingCart(cart);
        newItem.setCountryId(dto.getCountryId());
        newItem.setCountryCode(dto.getCountryCode());
        newItem.setCountryName(dto.getCountryName());
        newItem.setCountryFlag(dto.getCountryFlag());
        newItem.setServiceType(dto.getServiceType());
        newItem.setYear(dto.getYear());
        newItem.setPriceList(priceList.isEmpty() ? null : priceList);
        newItem.setPackagingServices(packagingServices);
        newItem.setSpecificationType(ShoppingCartItem.SpecificationType.PURCHASE);
        newItem.setPrice(0);
        ShoppingCartItem shoppingCartItem = shoppingCartItemRepository.save(newItem);
        List<ShoppingCartItem> items = cart.getItems();
        items.add(shoppingCartItem);
        cart.setItems(items);
        shoppingCartRepository.saveAndFlush(cart);
        updateCartItemCalculator(cartId, newItem.getId());

        return refreshShoppingCartTotal(cartId);
    }

    /**
     * <p>Updates a single shopping cart item inside a cart and recalculates totals.</p>
     *
     * @param shoppingCartId the shopping cart ID
     * @param itemId         the shopping cart item ID
     * @param data           update DTO for the item (year / packaging_services / customer_commitment_id)
     * @param user           authenticated user performing the operation
     * @return the updated {@link ShoppingCart} with refreshed totals
     * @throws ResponseStatusException 404 if item or cart not found
     * @throws ResponseStatusException 403 if user has no access
     */
    @SuppressWarnings({"java:S6541", "java:S1066", "java:S6809"})
    @Transactional(timeout = 45)
    public ShoppingCart updateItem(String shoppingCartId,
                                   Integer itemId,
                                   UpdateShoppingCartItemDto data,
                                   AuthenticatedUser user) {

        ShoppingCartItem item = shoppingCartItemRepository.findByIdAndShoppingCartId(itemId, shoppingCartId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Item Not Found"));

        ShoppingCart shoppingCart = item.getShoppingCart();
        if (shoppingCart == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Shopping cart not found");
        }

        if (shoppingCart.getEmail() != null) {
            if (user == null || (Role.CUSTOMER.equals(user.getRole()) && !shoppingCart.getEmail()
                    .equals(user.getEmail()))) {
                throw new ResponseStatusException(
                        HttpStatus.FORBIDDEN,
                                                  "You do not have access permission to add items to this cart");
            }
        }

        Map<String, Object> priceList = item.getPriceList();

        if (data.getYear() != null && !data.getYear().equals(item.getYear())) {
            if (Contract.Type.EU_LICENSE.equals(item.getServiceType())) {
                priceList = getCountryPriceList(item.getCountryCode(), data.getYear());
            } else {
                priceList = getServicePriceList(item.getServiceType(), data.getYear());
            }
        }

        List<PackagingServiceDto> packagingServices = data.getPackagingServices();
        if (Contract.Type.DIRECT_LICENSE.equals(item.getServiceType()) && data.getPackagingServices() == null) {
            PackagingServiceDto packagingServiceDto = new PackagingServiceDto();
            packagingServiceDto.setId(0);
            packagingServiceDto.setName("Direct License");
            packagingServiceDto.setFractions(Map.of());
            packagingServices = List.of(packagingServiceDto);
        }

        if (shoppingCart.getEmail() != null) {
            Customer customer = customerRepository.findByEmailAndDeletedAtIsNull(shoppingCart.getEmail()).orElse(null);

            if (customer != null) {
                String customerName = customer.getFirstName() + " " + customer.getLastName();

                Integer handlingFee = priceList != null ? ((Number) priceList.getOrDefault(
                        "handling_fee",
                        0)).intValue() : 0;
                Integer registrationFee = priceList != null ? ((Number) priceList.getOrDefault(
                        "registration_fee",
                        0)).intValue() : 0;
                Integer variableHandling = priceList != null ? ((Number) priceList.getOrDefault(
                        "variable_handling_fee",
                        0)).intValue() : 0;

                LicenseServiceContract createLicenseServiceContract = new LicenseServiceContract();
                createLicenseServiceContract.setCustomerName(customerName);
                createLicenseServiceContract.setCountry(item.getCountryName());
                createLicenseServiceContract.setStartingYear(String.valueOf(item.getYear()));
                createLicenseServiceContract.setEndingYear(String.valueOf(item.getYear() + 1));
                createLicenseServiceContract.setHandlingFee(handlingFee);
                createLicenseServiceContract.setRegistrationFee(registrationFee);
                createLicenseServiceContract.setVolumeDependent(variableHandling);
                createLicenseServiceContract.setTerminationDate(LocalDate.of(LocalDate.now().getYear() + 1, 1, 1)
                                                                        .toString());

                try {
                    mondayService.createLicenseServiceContract(createLicenseServiceContract, customer.getId());
                } catch (Exception ex) {
                    log.warn(
                            "mondayService.createLicenseServiceContract failed for cart={}, item={}: {}",
                            shoppingCartId,
                            itemId,
                            ex.getMessage());
                }

                CustomerCommitment customerCommitment = shoppingCart.getCustomerCommitments()
                        .stream()
                        .filter(cc -> Objects.equals(cc.getCountryCode(), item.getCountryCode()))
                        .findFirst()
                        .orElse(null);

                if (customerCommitment != null && customerCommitment.getServiceSetup() != null) {
                    CustomerServiceSetup setup = objectMapper.convertValue(
                            customerCommitment.getServiceSetup(),
                            CustomerServiceSetup.class);

                    List<String> groupedObligedPackagings = setup.getPackagingServices()
                            .stream()
                            .filter(CustomerServiceSetup.PackagingServiceExtended::getObliged)
                            .map(CustomerServiceSetup.PackagingServiceExtended::getName)
                            .toList();

                    if (!groupedObligedPackagings.isEmpty()) {
                        mondayService.createLicenseObligation(
                                item.getCountryName(),
                                String.valueOf(customer.getId()),
                                customer.getEmail(),
                                groupedObligedPackagings);
                    }
                }
            }
        }

        if (data.getYear() != null) {
            item.setYear(data.getYear());
        }
        if (priceList != null) {
            item.setPriceList(priceList);
        }
        if (packagingServices != null) {
            item.setPackagingServices(packagingServices);
        }
        shoppingCartItemRepository.save(item);

        updateCartItemCalculator(shoppingCartId, itemId);

        return refreshShoppingCartTotal(shoppingCartId);
    }

    /**
     * <p>Removes a single shopping cart item and recalculates totals.</p>
     *
     * @param shoppingCartId the shopping cart ID
     * @param itemId         the shopping cart item ID
     * @param user           authenticated user performing the operation
     * @return the updated {@link ShoppingCart} with refreshed totals
     * @throws ResponseStatusException 404 if item or cart not found
     * @throws ResponseStatusException 403 if user has no access
     */
    @SuppressWarnings({"squid:S1066", "java:S6809"})
    @Transactional(timeout = 45)
    public ShoppingCart removeItem(String shoppingCartId,
                                   Integer itemId,
                                   AuthenticatedUser user) {

        ShoppingCartItem item = shoppingCartItemRepository.findByIdAndShoppingCartIdAndShoppingCartDeletedAtIsNull(itemId,
                                                                                                                   shoppingCartId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Item Not Found"));

        ShoppingCart shoppingCart = item.getShoppingCart();
        if (shoppingCart == null) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Shopping cart not found");
        }

        if (shoppingCart.getEmail() != null) {
            if (user == null || (Role.CUSTOMER.equals(user.getRole()) && !shoppingCart.getEmail()
                    .equals(user.getEmail()))) {
                throw new ResponseStatusException(
                        HttpStatus.FORBIDDEN,
                                                  "You do not have access permission to add items to this cart");
            }
        }

        if (shoppingCart.getEmail() != null) {
            customerRepository.findByEmailAndDeletedAtIsNull(shoppingCart.getEmail()).ifPresent(customer -> {
                try {
                    mondayService.removeItem(customer.getId(), item.getCountryName());
                } catch (Exception ex) {
                    log.warn(
                            "mondayService.removeItem failed for cart={}, item={}, country={}: {}",
                            shoppingCartId,
                            itemId,
                            item.getCountryName(),
                            ex.getMessage());
                }
            });
        }

        shoppingCartItemRepository.deleteByIdAndShoppingCartId(itemId, shoppingCartId);

        return refreshShoppingCartTotal(shoppingCartId);
    }

    /**
     * <p>Calculates totals for a shopping cart and per-item prices (legacy parity kept).</p>
     *
     * @param cartId the shopping cart ID
     * @return a map containing {@code subtotal}, {@code total}, {@code couponTotal}, {@code vatTotal},
     * and {@code items} (map of itemId → price)
     * @ts-legacy Mirrors the original TypeScript calculateShoppingCartTotal behavior.
     */
    @SuppressWarnings({"java:S6541", "java:S3626"})
    public Map<String, Object> calculateShoppingCartTotal(String cartId) {
        ShoppingCart shoppingCart = shoppingCartRepository.findByIdAndStatusAndDeletedAtIsNull(
                        cartId,
                        ShoppingCart.Status.OPEN)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Shopping cart not found"));

        int subtotal;
        int total = 0;
        int vatTotal = 0;
        int couponTotal = 0;
        Map<Integer, Integer> items = new HashMap<>();

        for (ShoppingCartItem item : shoppingCart.getItems()) {
            switch (item.getServiceType()) {
                case EU_LICENSE -> {
                    Map<String, Object> priceList = item.getPriceList();
                    int regFee = (Integer) priceList.getOrDefault("registration_fee", 0);
                    int handlingFee = (Integer) priceList.getOrDefault("handling_fee", 0);
                    int value = regFee + handlingFee;
                    total += value;
                    items.put(item.getId(), value);
                }

                case DIRECT_LICENSE -> {
                    List<Map<String, Object>> packagingServices = objectMapper.convertValue(
                            item.getPackagingServices(), new TypeReference<>() {
                            });
                    Map<String, Object> firstPackagingService = (packagingServices != null && !packagingServices.isEmpty()) ? packagingServices.getFirst() : Map.of();

                    List<DirectLicenseCalculator.Fraction> fractions = firstPackagingService.get("fractions") instanceof Map ? ((Map<?, ?>) firstPackagingService.get(
                            "fractions")).values()
                            .stream()
                            .filter(Map.class::isInstance)
                            .map(v -> objectMapper.convertValue(
                                    v, new TypeReference<Map<String, Object>>() {
                                    }))
                            .map(f -> new DirectLicenseCalculator.Fraction(
                                    (String) f.get("name"),
                                    (String) f.get("code"),
                                    ((Number) f.get("weight")).intValue()))
                            .toList() : List.of();

                    Optional<License> licenseOpt = licenseRepository.findByContractCustomerEmailAndYearAndDeletedAtIsNull(shoppingCart.getEmail(),
                                                                                                                          item.getYear());

                    List<DirectLicenseCalculator.Fraction> currentFractions = List.of();
                    if (licenseOpt.isPresent()) {
                        License license = licenseOpt.get();

                        List<LicensePackagingService> licensePackagingServices = license.getPackagingServices();
                        if (licensePackagingServices != null && !licensePackagingServices.isEmpty()) {
                            List<LicenseVolumeReport> volumeReports = licensePackagingServices.getFirst()
                                    .getVolumeReports();
                            if (volumeReports != null && !volumeReports.isEmpty()) {
                                LicenseVolumeReport firstReport = volumeReports.getFirst();
                                List<LicenseVolumeReportItem> reportItems = firstReport.getVolumeReportItems();

                                currentFractions = reportItems.stream()
                                        .map(reportItem -> new DirectLicenseCalculator.Fraction(
                                                reportItem.getSetupFractionCode(),
                                                reportItem.getSetupFractionCode(),
                                                reportItem.getValue()))
                                        .toList();
                            }
                        }
                    }

                    int value = DirectLicenseCalculator.calculateDirectLicenseNetValue(
                            item.getPriceList(),
                            fractions,
                            !currentFractions.isEmpty() ? currentFractions : null);

                    total += value;
                    items.put(item.getId(), value);
                }


                case ACTION_GUIDE -> {
                    Map<String, Object> priceList = item.getPriceList();
                    int value = (Integer) priceList.getOrDefault("price", 0);
                    total += value;
                    items.put(item.getId(), value);
                }

                default -> {
                    continue;
                }
            }
        }

        subtotal = total;

        Coupon coupon = shoppingCart.getCoupon();
        if (coupon != null) {
            int couponValue = coupon.getValue();

            if (Coupon.DiscountType.PERCENTAGE.equals(coupon.getDiscountType())) {
                couponTotal = (total * couponValue) / 100;
                total -= couponTotal;
            } else if (Coupon.DiscountType.ABSOLUTE.equals(coupon.getDiscountType())) {
                couponTotal = couponValue;
                total -= couponTotal;
            }
        }

        if (shoppingCart.getVatPercentage() != null) {
            int vat = shoppingCart.getVatPercentage();
            vatTotal = (total * vat) / 100;
            total += vatTotal;
        }

        Map<String, Object> result = new HashMap<>();
        result.put("subtotal", subtotal);
        result.put("total", total);
        result.put("couponTotal", couponTotal);
        result.put("vatTotal", vatTotal);
        result.put("items", items);

        return result;
    }

    /**
     * <p>Recalculates cart totals and persists per-item prices.</p>
     *
     * @param cartId the shopping cart ID
     * @return the updated {@link ShoppingCart} with refreshed totals
     * @throws org.springframework.web.server.ResponseStatusException 404 if the cart is not found
     * @ts-legacy Mirrors the original {@code refreshShoppingCartTotal} behavior from the TypeScript service.
     */
    @Transactional
    public ShoppingCart refreshShoppingCartTotal(String cartId) {
        Map<String, Object> totals = this.calculateShoppingCartTotal(cartId);

        int subtotal = (int) totals.get("subtotal");
        int total = (int) totals.get("total");
        int vatTotal = (int) totals.get("vatTotal");
        int couponTotal = (int) totals.get("couponTotal");
        Map<Integer, Integer> items = objectMapper.convertValue(
                totals.get("items"), new TypeReference<Map<Integer, Integer>>() {
                });

        ShoppingCart shoppingCart = shoppingCartRepository.findByIdAndStatusAndDeletedAtIsNull(
                        cartId,
                        ShoppingCart.Status.OPEN)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Shopping cart not found"));

        for (ShoppingCartItem item : shoppingCart.getItems()) {
            Integer price = items.getOrDefault(item.getId(), 0);
            item.setPrice(price);
            shoppingCartItemRepository.saveAndFlush(item);
        }

        shoppingCart.setSubtotal(subtotal);
        shoppingCart.setTotal(total);
        shoppingCart.setVatValue(vatTotal);
        shoppingCart.setCouponValue(couponTotal);

        return shoppingCartRepository.saveAndFlush(shoppingCart);
    }


    /**
     * Retrieves the service price list based on the specified service type and license year.
     *
     * @param serviceType the type of service for which the price list is requested
     * @param licenseYear the license year for which the price list is requested
     * @return a map containing the service price list details
     * @throws ResponseStatusException if no price list is found or an error occurs while retrieving the price list
     */
    public Map<String, Object> getServicePriceList(Contract.Type serviceType,
                                                   int licenseYear) {
        try {
            List<PriceList> priceLists = priceListService.findAll(
                    null,
                    PriceList.Type.valueOf(serviceType.name()),
                    String.valueOf(licenseYear));

            if (priceLists == null || priceLists.isEmpty()) {
                throw new ResponseStatusException(
                        HttpStatus.INTERNAL_SERVER_ERROR,
                                                  "No price list found for: " + serviceType + "-" + licenseYear);
            }

            return objectMapper.convertValue(
                    priceLists.getFirst(), new TypeReference<>() {
                    });
        } catch (Exception e) {
            throw new ResponseStatusException(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    "Error retrieving price list for: " + serviceType + "-" + licenseYear,
                    e);
        }
    }

    /**
     * Retrieves the price list for a given country and year.
     *
     * @param countryCode the code of the country for which the price list is required
     * @param year        the year for which the price list is being requested
     * @return a map containing the price list details for the given country and year,
     * or null if an error occurs during the retrieval process
     */
    @SuppressWarnings("java:S1168")
    public Map<String, Object> getCountryPriceList(String countryCode,
                                                   int year) {
        try {
            List<CountryPriceListResponseDto> countryPriceLists = serviceSetupService.findServiceSetupPriceLists(
                    countryCode);

            if (countryPriceLists == null || countryPriceLists.isEmpty()) {
                throw new IllegalStateException("No price lists returned for country: " + countryCode);
            }

            List<PriceListDto> priceLists = countryPriceLists.stream()
                    .map(CountryPriceListResponseDto::getPriceList)
                    .filter(Objects::nonNull)
                    .toList();

            List<PriceListDto> yearPriceLists = priceLists.stream().filter(pl -> {
                try {
                    return Integer.parseInt(pl.getConditionTypeValue()) == year;
                } catch (NumberFormatException e) {
                    return false;
                }
            }).toList();

            if (yearPriceLists.isEmpty()) {
                throw new IllegalStateException("No price list found for year: " + year);
            }

            return objectMapper.convertValue(
                    priceLists.getFirst(), new TypeReference<>() {
                    });

        } catch (Exception e) {
            log.warn("Failed to load country price list for {}-{}: {}", countryCode, year, e.getMessage());
            return null;
        }
    }


    /**
     * Updates a single shopping cart item by recalculating its license costs using the current service setup.
     *
     * <p>This method finds the item, extracts its setup, converts the data into DTO form,
     * sends it to the license cost calculator, and applies the calculated results.
     *
     * @param cartId the ID of the shopping cart
     * @param itemId the ID of the item to update
     */
    @Transactional
    public void updateCartItemCalculator(String cartId,
                                         Integer itemId) {
        try {
            ShoppingCart shoppingCart = shoppingCartRepository.findById(cartId)
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Shopping cart not found"));

            ShoppingCartItem item = shoppingCart.getItems()
                    .stream()
                    .filter(i -> Objects.equals(i.getId(), itemId))
                    .findFirst()
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Item not found"));

            if (!Contract.Type.EU_LICENSE.equals(item.getServiceType())) {
                return;
            }

            CustomerCommitment customerCommitment = shoppingCart.getCustomerCommitments()
                    .stream()
                    .filter(c -> Objects.equals(c.getCountryCode(), item.getCountryCode()))
                    .findFirst()
                    .orElse(null);

            if (customerCommitment == null || customerCommitment.getServiceSetup() == null) {
                return;
            }

            Map<String, Object> serviceSetupRaw = customerCommitment.getServiceSetup();

            CustomerServiceSetup setup = objectMapper.convertValue(serviceSetupRaw, CustomerServiceSetup.class);


            List<CalculateLicenseCostsDto.ReportSetDto> reportSets = new ArrayList<>();

            List<PackagingServiceDto> parsedServices = objectMapper.convertValue(
                    item.getPackagingServices(), new TypeReference<>() {
                    });

            for (PackagingServiceDto service : parsedServices) {
                var packagingService = setup.getPackagingServices()
                        .stream()
                        .filter(p -> Objects.equals(p.getId(), service.getId()))
                        .findFirst()
                        .orElse(null);

                if (packagingService == null) {
                    continue;
                }

                List<CalculateLicenseCostsDto.FractionDto> fractions = service.getFractions()
                        .values()
                        .stream()
                        .map(fractionDto -> {
                            CalculateLicenseCostsDto.FractionDto dto = new CalculateLicenseCostsDto.FractionDto();
                            dto.setCode(fractionDto.getCode());
                            dto.setWeight(fractionDto.getWeight());
                            return dto;
                        })
                        .toList();

                CalculateLicenseCostsDto.ReportSetDto reportSetDto = new CalculateLicenseCostsDto.ReportSetDto();
                reportSetDto.setId(packagingService.getReportSet().getId());
                reportSetDto.setFractions(fractions);

                reportSets.add(reportSetDto);
            }


            CalculateLicenseCostsDto dto = new CalculateLicenseCostsDto();
            dto.setYear(item.getYear());
            dto.setReportSets(reportSets);

            CalculateLicenseCostsResponseDto result = serviceSetupService.calculateLicenseCosts(
                    item.getCountryCode(),
                    dto);

            item.setCalculator(Map.of("license_costs", result.getLicenseCosts()));
            shoppingCartItemRepository.saveAndFlush(item);

            if (result.isAuthorizeRepresentativeObligated()) {
                setup.setAuthorizeRepresentativeObligated(true);
            }


            RepresentativeTier representativeTier = objectMapper.convertValue(
                    result.getRepresentativeTier(),
                    RepresentativeTier.class);

            if (result.getRepresentativeTier() != null) {
                setup.setRepresentativeTier(representativeTier);
            }

            if (result.getOtherCosts() != null && !result.getOtherCosts().isEmpty()) {
                setup.setOtherCostsObligated(true);

                for (OtherCost cost : result.getOtherCosts()) {
                    boolean alreadyExists = setup.getOtherCosts()
                            .stream()
                            .anyMatch(c -> Objects.equals(c.getId(), cost.getId()));
                    if (!alreadyExists) {
                        setup.getOtherCosts()
                                .add(objectMapper.convertValue(
                                        cost,
                                        de.interzero.oneepr.customer.customer_commitment.dto.OtherCost.class));
                    }
                }
            }
            Map<String, Object> setupMapped = objectMapper.convertValue(
                    setup, new TypeReference<>() {
                    });

            customerCommitment.setServiceSetup(setupMapped);
            customerCommitmentRepository.saveAndFlush(customerCommitment);
        } catch (Exception e) {
            log.error(
                    "Error calculating license costs for cartId={}, itemId={}: {}",
                    cartId,
                    itemId,
                    e.getMessage(),
                    e);
            throw new ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR, "Error calculating license costs");
        }
    }


    /**
     * This method is scheduled to run daily at 12:00 PM and performs cleanup and churn processing
     * of shopping carts. It identifies eligible open shopping carts, categorizes them into churned carts
     * (non-empty carts that have been inactive for a defined number of days) and empty carts, and processes
     * them accordingly by either marking them as churned or deleting them. Additionally, it sends a churn
     * notification email to customers associated with the churned carts.
     * <p>
     * The method is transactional, ensuring that all cart updates are applied atomically.
     */
    @Scheduled(cron = "0 0 12 * * *") // Runs every day at 12:00 PM
    @Transactional
    public void cleanAndChurnShoppingCarts() {
        final int CHURN_DAYS = 7;
        Instant churnDate = Instant.now().minus(Duration.ofDays(CHURN_DAYS));

        List<ShoppingCart> openCarts = shoppingCartRepository.findByStatusAndEmailIsNotNullAndDeletedAtIsNullAndIsChurnedFalseAndUpdatedAtBefore(ShoppingCart.Status.OPEN,
                                                                                                                                                 churnDate);

        if (openCarts.isEmpty()) {
            return;
        }

        List<ShoppingCart> churnedCarts = openCarts.stream().filter(cart -> !cart.getItems().isEmpty()).toList();

        List<ShoppingCart> emptyCarts = openCarts.stream().filter(cart -> cart.getItems().isEmpty()).toList();

        if (!churnedCarts.isEmpty()) {
            List<String> churnedCartIds = churnedCarts.stream().map(ShoppingCart::getId).toList();
            shoppingCartRepository.markCartsAsChurned(churnedCartIds);
        }

        if (!emptyCarts.isEmpty()) {
            List<String> emptyCartIds = emptyCarts.stream().map(ShoppingCart::getId).toList();
            shoppingCartRepository.deleteCartsByIds(emptyCartIds);
        }

        List<String> churnedEmails = churnedCarts.stream()
                .map(ShoppingCart::getEmail)
                .filter(Objects::nonNull)
                .distinct()
                .toList();

        if (churnedEmails.isEmpty()) {
            return;
        }

        List<Customer> customers = customerRepository.findByEmailIn(churnedEmails);

        for (Customer customer : customers) {
            EmailMessage message = new EmailMessage(
                    "41", customer.getEmail(), "Lizenzero <<EMAIL>>",
                    "You still have an open shopping cart",
                    Map.of("name", customer.getFirstName()));
            message.setRecipientName(customer.getFirstName(), customer.getLastName());

            try {
                emailOutboxGateway.sendEmail(message);
            } catch (Exception e) {
                log.warn("FAILED TO SEND *CHURN* EMAIL TO {}: {}", customer.getEmail(), e.getMessage());
            }
        }
    }


    /**
     * Converts a LicensePriceList object to a map representation.
     *
     * @param list the LicensePriceList object to be converted
     * @return a map containing the properties of the given LicensePriceList object as key-value pairs
     */
    private Map<String, Object> mapLicensePriceListToMap(LicensePriceList list) {
        Map<String, Object> result = new HashMap<>();
        result.put("name", list.getName());
        result.put("description", list.getDescription());
        result.put("condition_type", list.getConditionType());
        result.put("condition_type_value", list.getConditionTypeValue());
        result.put("start_date", list.getStartDate());
        result.put("end_date", list.getEndDate());
        result.put("basic_price", list.getBasicPrice());
        result.put("minimum_price", list.getMinimumPrice());
        result.put("registration_fee", list.getRegistrationFee());
        result.put("handling_fee", list.getHandlingFee());
        result.put("variable_handling_fee", list.getVariableHandlingFee());
        result.put("thresholds", list.getThresholds());
        return result;
    }

    /**
     * Maps the properties of an ActionGuidePriceList object to a Map with String keys and Object values.
     *
     * @param list the ActionGuidePriceList object containing the data to be mapped
     * @return a Map where the keys are property names ("name", "description", "price"),
     * and the values are the corresponding properties from the provided ActionGuidePriceList object
     */
    private Map<String, Object> mapActionGuidePriceListToMap(ActionGuidePriceList list) {
        Map<String, Object> result = new HashMap<>();
        result.put("name", list.getName());
        result.put("description", list.getDescription());
        result.put("price", list.getPrice());
        return result;
    }

}
