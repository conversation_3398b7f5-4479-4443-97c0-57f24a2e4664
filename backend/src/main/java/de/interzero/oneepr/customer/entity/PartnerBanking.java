package de.interzero.oneepr.customer.entity;

import de.interzero.oneepr.customer.partner.Partner;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
@Entity
@Table(
        name = "partner_banking",
        schema = "public"
)
public class PartnerBanking {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "partner_banking_id_gen"
    )
    @SequenceGenerator(
            name = "partner_banking_id_gen",
            sequenceName = "partner_banking_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @OneToOne(fetch = FetchType.LAZY)
    @OnDelete(action = OnDeleteAction.SET_NULL)
    @JoinColumn(name = "partner_id")
    private Partner partner;

    @Column(
            name = "business_identifier_code",
            length = Integer.MAX_VALUE
    )
    private String businessIdentifierCode;

    @Column(
            name = "international_account_number",
            length = Integer.MAX_VALUE
    )
    private String internationalAccountNumber;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @Column(name = "deleted_at")
    private LocalDate deletedAt;

}