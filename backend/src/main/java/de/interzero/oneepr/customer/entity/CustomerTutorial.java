package de.interzero.oneepr.customer.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.customer.Customer;
import io.swagger.v3.oas.models.info.Contact;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
@Entity
@Table(
        name = "customer_tutorial",
        schema = "public"
)
public class CustomerTutorial {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "customer_tutorial_id_gen"
    )
    @SequenceGenerator(
            name = "customer_tutorial_id_gen",
            sequenceName = "customer_tutorial_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "customer_id",
            nullable = false
    )
    private Customer customer;

    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(name = "service_type")
    @JsonProperty("service_type")
    private Contract.Type serviceType;

    @NotNull
    @Column(
            name = "is_finished",
            nullable = false
    )
    private Boolean isFinished = false;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @Column(name = "deleted_at")
    private LocalDate deletedAt;

}