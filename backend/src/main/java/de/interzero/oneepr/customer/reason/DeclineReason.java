package de.interzero.oneepr.customer.reason;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.decline.Decline;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
@Entity
@NoArgsConstructor
@Table(
        name = "decline_reason",
        schema = "public"
)
public class DeclineReason {

    public DeclineReason(Decline decline,
                         Reason reason) {
        this.decline = decline;
        this.reason = reason;
        this.createdAt = Instant.now();
        this.updatedAt = Instant.now();
    }

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "decline_reason_id_gen"
    )
    @SequenceGenerator(
            name = "decline_reason_id_gen",
            sequenceName = "decline_reason_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @Transient
    @JsonProperty("decline_id")
    public Integer getDeclineId() {
        return decline != null ? decline.getId() : null;
    }

    @Transient
    @JsonProperty("reason_id")
    public Integer getReasonId() {
        return reason != null ? reason.getId() : null;
    }

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt = Instant.now();

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    @ManyToOne
    @JoinColumn(
            name = "decline_id",
            nullable = false
    )
    @JsonProperty("decline")
    private Decline decline;

    @ManyToOne
    @JoinColumn(
            name = "reason_id",
            nullable = false
    )
    @JsonProperty("reason")
    private Reason reason;

}