package de.interzero.oneepr.customer.cluster.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.common.BaseDto;
import de.interzero.oneepr.customer.cluster.Cluster;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Schema(description = "Fields required to create a new cluster")
@Data
@EqualsAndHashCode(callSuper = true)
public class CreateClusterDto extends BaseDto {

    @JsonProperty("name")
    @Schema(
            description = "Name of the cluster",
            example = "random name"
    )
    private String name;

    @JsonProperty("registration_start_date")
    @Schema(
            description = "Registration start date",
            example = "2025-03-06T00:00:33.944Z"
    )
    private Instant registrationStartDate;

    @JsonProperty("registration_end_date")
    @Schema(
            description = "Registration end date",
            example = "2025-03-06T00:00:33.944Z"
    )
    private Instant registrationEndDate;

    @JsonProperty("status")
    @Schema(
            description = "Status of the cluster",
            example = "ACTIVE"
    )
    private Cluster.Status status;

    @JsonProperty("min_household_packaging")
    @Schema(
            description = "Minimum household packaging (in cents)",
            example = "100"
    )
    private Integer minHouseholdPackaging;

    @JsonProperty("max_household_packaging")
    @Schema(
            description = "Maximum household packaging (in cents)",
            example = "100"
    )
    private Integer maxHouseholdPackaging;

    @JsonProperty("type_of_services")
    @Schema(
            description = "Type of services",
            example = "{ license_service_sales_packaging: true, license_service_b2b_packaging: false, action_guide: true, direct_license: false }"
    )
    private Map<String, Boolean> typeOfServices;

    @JsonProperty("participating_countries")
    @Schema(
            description = "Participating countries",
            examples = {"FR", "DE", "ES"}
    )
    private List<String> participatingCountries;

    @JsonProperty("customers")
    @Schema(
            description = "Customers",
            examples = {"[1, 2, 3]", "[1,2]"}
    )
    private List<Integer> customers;
}
