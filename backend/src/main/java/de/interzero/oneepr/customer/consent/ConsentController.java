package de.interzero.oneepr.customer.consent;

import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.consent.dto.CreateConsentDto;
import de.interzero.oneepr.customer.consent.dto.UpdateConsentDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static de.interzero.oneepr.common.string.Role.*;

@Tag(
        name = "Consent",
        description = "Operations related to consent"
)
@SecurityRequirement(name = "bearerAuth")
@RestController
@RequestMapping(Api.CONSENT)
@Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER})
@RequiredArgsConstructor
public class ConsentController {

    private final ConsentService consentService;

    @Operation(summary = "Create a new consent")
    @PostMapping
    @ResponseStatus(HttpStatus.CREATED)
    public Consent create(@io.swagger.v3.oas.annotations.parameters.RequestBody(
            required = true,
            content = @Content(
                    mediaType = "application/json",
                    schema = @Schema(implementation = CreateConsentDto.class)
            )
    ) @RequestBody CreateConsentDto dto) {
        return consentService.create(dto);
    }

    @Operation(summary = "Get all consents")
    @GetMapping
    @PreAuthorize("permitAll()")
    // TODO figure out if this is correct. Doesn't seem right to allow anyone to see all consents, but luckily this is overriden by parent-level
    public List<Consent> findAll() {
        return consentService.findAll();
    }

    @Operation(summary = "Get a consent by ID")
    @GetMapping("{id}")
    public Consent findOne(@Parameter(description = "ID of consent to get") @PathVariable Integer id) {
        return consentService.findOne(id);
    }

    @Operation(summary = "get consents by type")
    @GetMapping("type/{type}")
    public List<Consent> findManyByType(@Parameter(description = "consent type") @PathVariable Consent.Type type) {
        return consentService.findManyByType(type);
    }

    @Operation(summary = "Update a consent")
    @PatchMapping("{id}")
    public Consent update(@Parameter(description = "ID of consent to update") @PathVariable Integer id,
                          @io.swagger.v3.oas.annotations.parameters.RequestBody(
                                  required = true,
                                  content = @Content(
                                          mediaType = "application/json",
                                          schema = @Schema(implementation = UpdateConsentDto.class)
                                  )
                          ) @RequestBody UpdateConsentDto dto) {
        return consentService.update(id, dto);
    }

    @Operation(summary = "Delete a consent")
    @DeleteMapping("{id}")
    public void remove(@Parameter(description = "ID of consent to delete") @PathVariable Integer id) {
        consentService.remove(id);
    }
}
