package de.interzero.oneepr.customer.consent.dto;

import de.interzero.oneepr.common.BaseDto;
import de.interzero.oneepr.customer.consent.Consent;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "Fields required to create a new consent")
@Data
public class CreateConsentDto extends BaseDto {

    @Schema(description = "Name of the consent")
    private String name;

    @Schema(description = "Type of the consent")
    private Consent.Type type;

    @Schema(description = "description of the consent")
    private String description;
}
