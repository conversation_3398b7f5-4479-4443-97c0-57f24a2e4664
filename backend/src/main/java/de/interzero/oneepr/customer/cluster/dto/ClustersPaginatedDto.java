package de.interzero.oneepr.customer.cluster.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.cluster.Cluster;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * Return DTO for Pagination. Standard Spring Boot Page doesn't fit for current frontend.
 * Maybe using this in general in the future for pagination?!
 */
@Schema(description = "Return dto for paginated clusters request")
@Data
public class ClustersPaginatedDto {

    @JsonProperty("clusters")
    @Schema(description = "List of clusters of page")
    private List<Cluster> clusters;

    @JsonProperty("count")
    @Schema(description = "Count of clusters in page")
    private Long count;

    @JsonProperty("pages")
    @Schema(description = "Number of pages")
    private Integer pages;

    @JsonProperty("current_page")
    @Schema(description = "Number of current page")
    private Integer currentPage;

    @JsonProperty("limit")
    @Schema(description = "Number of clusters per page")
    private Integer limit;
}
