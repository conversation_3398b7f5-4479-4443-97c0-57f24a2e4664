package de.interzero.oneepr.customer.consent;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ConsentRepository extends JpaRepository<Consent, Integer> {

    /**
     * Finds all consents by type
     *
     * @param type the type of consent
     * @return the list of consents
     */
    List<Consent> findByType(Consent.Type type);

    /**
     * Find all consents of a given type where deletedAt is null.
     *
     * @param type type of consent (e.g. "ACCOUNT")
     * @return list of consents
     */
    List<Consent> findAllByTypeAndDeletedAtIsNull(Consent.Type type);

    /**
     * Finds all Consent entities of a specific type that have not been soft-deleted.
     *
     * @param type The type of consent to search for.
     * @return A list of active Consent entities matching the given type.
     */
    List<Consent> findByTypeAndDeletedAtIsNull(Consent.Type type);
}
