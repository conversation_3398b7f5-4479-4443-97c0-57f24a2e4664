package de.interzero.oneepr.customer.file;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface FileRepository extends JpaRepository<File, String> {

    /**
     * Find file by relation field and related entity ID
     * This method uses dynamic query to find files based on different relation fields
     * @ts-legacy the condition should include AND f.deleted_at IS NULL
     * @param relation the relation field name (e.g., "required_information_id", "contract_id", etc.)
     * @param relativeId the ID of the related entity
     * @return Optional containing the file if found
     */
    @Query(value = """
            SELECT * FROM file f
            WHERE CASE
                WHEN :relation = 'required_information_id' THEN f.required_information_id = :relativeId
                WHEN :relation = 'contract_id' THEN f.contract_id = :relativeId
                WHEN :relation = 'certificate_id' THEN f.certificate_id = :relativeId
                WHEN :relation = 'license_id' THEN f.license_id = :relativeId
                WHEN :relation = 'termination_id' THEN f.termination_id = :relativeId
                WHEN :relation = 'general_information_id' THEN f.general_information_id = :relativeId
                WHEN :relation = 'third_party_invoice_id' THEN f.third_party_invoice_id = :relativeId
                WHEN :relation = 'marketing_material_id' THEN f.marketing_material_id = :relativeId
                WHEN :relation = 'partner_contract_id' THEN f.partner_contract_id = :relativeId
                WHEN :relation = 'order_id' THEN f.order_id = :relativeId
                ELSE FALSE
            END
            LIMIT 1
            """, nativeQuery = true)
    Optional<File> findByRelation(@Param("relation") String relation, @Param("relativeId") Integer relativeId);

}
