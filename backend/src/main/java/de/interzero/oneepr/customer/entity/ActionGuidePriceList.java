package de.interzero.oneepr.customer.entity;

import de.interzero.oneepr.action_guide.ActionGuide;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;

import java.time.Instant;

@Getter
@Setter
@Entity
@Table(
        name = "action_guide_price_list",
        schema = "public"
)
public class ActionGuidePriceList {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "action_guide_price_list_id_gen"
    )
    @SequenceGenerator(
            name = "action_guide_price_list_id_gen",
            sequenceName = "action_guide_price_list_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    private Integer id;

    @NotNull
    @Column(
            name = "setup_price_list_id",
            nullable = false
    )
    private Integer setupPriceListId;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @OnDelete(action = OnDeleteAction.RESTRICT)
    @JoinColumn(
            name = "action_guide_id",
            nullable = false
    )
    private ActionGuide actionGuide;

    @NotNull
    @Column(
            name = "name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String name;

    @NotNull
    @Column(
            name = "description",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    private String description;

    @NotNull
    @Column(
            name = "price",
            nullable = false
    )
    private Integer price;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    private Instant updatedAt;

    @Column(name = "deleted_at")
    private Instant deletedAt;

}