package de.interzero.oneepr.customer.reason.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.reason.Reason;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * Data Transfer Object for finding all reasons by type.
 * <p>
 * This DTO is for requesting all reasons by given reason-type.
 */
@Data
public class FindAllReasonsDto {

    @Schema(description = "Reason Type")
    @JsonProperty("reason_type")
    Reason.Type type;

}
