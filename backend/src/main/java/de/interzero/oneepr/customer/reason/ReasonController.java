package de.interzero.oneepr.customer.reason;

import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.customer.reason.dto.FindAllReasonsDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static de.interzero.oneepr.common.string.Role.*;

@Tag(
        name = "Reason",
        description = "Operations related to reason"
)
@SecurityRequirement(name = "bearerAuth")
@RestController
@RequestMapping(Api.REASONS)
@Secured({SUPER_ADMIN, ADMIN, CLERK, CUSTOMER})
@RequiredArgsConstructor
public class ReasonController {

    private final ReasonService reasonService;

    @Operation(summary = "Find all reasons by type")
    @GetMapping
    @PreAuthorize("permitAll()")
    public List<Reason> findAll(@RequestBody FindAllReasonsDto dto) {
        return reasonService.findAll(dto);
    }

}
