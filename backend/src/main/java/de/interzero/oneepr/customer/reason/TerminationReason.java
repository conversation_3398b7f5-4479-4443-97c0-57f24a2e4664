package de.interzero.oneepr.customer.reason;

import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.termination.Termination;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.time.Instant;
import java.time.LocalDate;

@Getter
@Setter
@Entity
@Table(
        name = "termination_reason",
        schema = "public"
)
@AllArgsConstructor
public class TerminationReason {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "termination_reason_id_gen"
    )
    @SequenceGenerator(
            name = "termination_reason_id_gen",
            sequenceName = "termination_reason_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt = Instant.now();

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    @OneToOne
    @JoinColumn(name = "termination")
    @JsonProperty("termination")
    private Termination termination;

    @ManyToOne
    @JoinColumn(
            name = "reason",
            nullable = false
    )
    @JsonProperty("reason")
    private Reason reason;

}
