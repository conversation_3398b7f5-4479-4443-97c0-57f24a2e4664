package de.interzero.oneepr.customer.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.file.File;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "general_information")
public class GeneralInformation {

    /**
     * The type of information required.
     * Corresponds to the Prisma {@code GeneralInformationType} enum.
     */
    public enum Type {
        TEXT,
        NUMBER,
        DOCUMENT,
        FILE,
        IMAGE
    }

    /**
     * The status of the information request.
     * Corresponds to the Prisma {@code GeneralInformationStatus} enum.
     */
    public enum Status {
        NEW,
        DONE,
        DECLINED,
        APPROVED,
        OPEN
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "setup_general_information_id",
            nullable = false
    )
    @JsonProperty("setup_general_information_id")
    private Integer setupGeneralInformationId;

    @NotNull
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "type",
            nullable = false
    )
    @JsonProperty("type")
    private Type type;

    @NotNull
    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(
            name = "status",
            nullable = false
    )
    @JsonProperty("status")
    private Status status = Status.OPEN;

    @NotNull
    @Column(
            name = "name",
            nullable = false
    )
    @JsonProperty("name")
    private String name;

    @NotNull
    @Column(
            name = "description",
            nullable = false
    )
    @JsonProperty("description")
    private String description;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private Instant deletedAt;

    @Column(name = "question")
    @JsonProperty("question")
    private String question;

    @Column(name = "file_id")
    @JsonProperty("file_id")
    private String fileId;

    @Column(name = "answer")
    @JsonProperty("answer")
    private String answer;

    @NotNull
    @ManyToOne(
            fetch = FetchType.LAZY,
            optional = false
    )
    @JoinColumn(
            name = "contract_id",
            nullable = false
    )
    @JsonIgnore
    private Contract contract;

    @OneToMany(mappedBy = "generalInformation")
    @JsonIgnore
    @JsonProperty("file")
    private List<File> files = new ArrayList<>();

    @Transient
    @JsonProperty("contract_id")
    public Integer getContractId() {
        return contract != null ? contract.getId() : null;
    }
}