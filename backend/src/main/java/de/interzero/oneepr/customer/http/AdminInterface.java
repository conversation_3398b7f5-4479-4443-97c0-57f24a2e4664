package de.interzero.oneepr.customer.http;

import lombok.NonNull;
import org.springframework.http.HttpMethod;

import java.util.Map;

/**
 * This class is a non-working placeholder for services that would call the admin service. Originally implemented in the
 * http.service.ts package, this is a replacement for admin().
 * <p>
 * As Admin API is implemented within this project, code that would normally call this function should instead call
 * those functions directly. However, if the work you are doing falls outside the scope, you can use
 * this class as a placeholder.
 */
public class AdminInterface {

    private AdminInterface() {
        throw new IllegalStateException("Utility class");
    }

    @SuppressWarnings("ConstantConditions")
    public static Object admin(@NonNull String url,
                               @NonNull Map<String, Object> params,
                               @NonNull HttpMethod method) {
        if (true) {
            throw new RuntimeException(
                    "This method should not be called. Please re-implement the auth service in Spring Boot.");
        }
        return new Object();
    }
}