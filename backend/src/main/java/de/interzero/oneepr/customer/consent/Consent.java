package de.interzero.oneepr.customer.consent;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import de.interzero.oneepr.customer.customer_consent.CustomerConsent;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

/**
 * Entity based on the database table "consent", which was defined in the Senno prisma model
 */
@Getter
@Setter
@Entity
@Table(
        name = "consent",
        schema = "public"
)
public class Consent {

    @Id
    @GeneratedValue(
            strategy = GenerationType.SEQUENCE,
            generator = "consent_id_gen"
    )
    @SequenceGenerator(
            name = "consent_id_gen",
            sequenceName = "consent_id_seq",
            allocationSize = 1
    )
    @Column(
            name = "id",
            nullable = false
    )
    @JsonProperty("id")
    private Integer id;

    @NotNull
    @Column(
            name = "name",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("name")
    private String name;

    @NotNull
    @Column(
            name = "description",
            nullable = false,
            length = Integer.MAX_VALUE
    )
    @JsonProperty("description")
    private String description;

    @NotNull
    @Column(
            name = "version",
            nullable = false
    )
    @JsonProperty("version")
    private Integer version;

    @NotNull
    @Column(
            name = "created_at",
            nullable = false
    )
    @JsonProperty("created_at")
    private Instant createdAt;

    @NotNull
    @Column(
            name = "updated_at",
            nullable = false
    )
    @JsonProperty("updated_at")
    private Instant updatedAt;

    @Column(name = "deleted_at")
    @JsonProperty("deleted_at")
    private LocalDate deletedAt;

    @Enumerated(EnumType.STRING)
    @JdbcTypeCode(SqlTypes.NAMED_ENUM)
    @Column(name = "type")
    @JsonProperty("type")
    private Type type;

    @OneToMany(
            mappedBy = "consent",
            fetch = FetchType.LAZY
    )
    @JsonProperty("consents")
    @JsonIgnore
    private List<CustomerConsent> consents;

    public enum Type {
        ACCOUNT,
        PURCHASE
    }
}