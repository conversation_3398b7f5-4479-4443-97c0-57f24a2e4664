-- SQL to insert all countries and regions in the world
-- This script will clear existing countries and insert a comprehensive list of all world countries
-- organized by regions/continents

-- First, clear existing countries (be careful with foreign key constraints)
-- DELETE FROM country WHERE id > 0;

-- Reset the sequence if needed
-- ALTER SEQUENCE country_id_seq RESTART WITH 1;

-- Insert all world countries organized by regions
-- Format: (name, code, flag_url, created_at, updated_at, authorize_representative_obligated, other_costs_obligated, is_published)
-- AFRICA
INSERT INTO country (name, code, flag_url, created_at, updated_at, authorize_representative_obligated, other_costs_obligated, is_published) VALUES
('Algeria', 'DZ', 'https://cdn.kcak11.com/CountryFlags/countries/dz.svg', now(), now(), false, false, false),
('Angola', 'AO', 'https://cdn.kcak11.com/CountryFlags/countries/ao.svg', now(), now(), false, false, false),
('Benin', 'BJ', 'https://cdn.kcak11.com/CountryFlags/countries/bj.svg', now(), now(), false, false, false),
('Botswana', 'BW', 'https://cdn.kcak11.com/CountryFlas/countries/bw.svg', now(), now(), false, false, false),
('Burkina Faso', 'BF', 'https://cdn.kcak11.com/CountryFlags/countries/bf.svg', now(), now(), false, false, false),
('Burundi', 'BI', 'https://cdn.kcak11.com/CountryFlags/countries/bi.svg', now(), now(), false, false, false),
('Cameroon', 'CM', 'https://cdn.kcak11.com/CountryFlags/countries/cm.svg', now(), now(), false, false, false),
('Cape Verde', 'CV', 'https://cdn.kcak11.com/CountryFlags/countries/cv.svg', now(), now(), false, false, false),
('Central African Republic', 'CF', 'https://cdn.kcak11.com/CountryFlags/countries/cf.svg', now(), now(), false, false, false),
('Chad', 'TD', 'https://cdn.kcak11.com/CountryFlags/countries/td.svg', now(), now(), false, false, false),
('Comoros', 'KM', 'https://cdn.kcak11.com/CountryFlags/countries/km.svg', now(), now(), false, false, false),
('Congo', 'CG', 'https://cdn.kcak11.com/CountryFlags/countries/cg.svg', now(), now(), false, false, false),
('Democratic Republic of the Congo', 'CD', 'https://cdn.kcak11.com/CountryFlags/countries/cd.svg', now(), now(), false, false, false),
('Djibouti', 'DJ', 'https://cdn.kcak11.com/CountryFlags/countries/dj.svg', now(), now(), false, false, false),
('Egypt', 'EG', 'https://cdn.kcak11.com/CountryFlags/countries/eg.svg', now(), now(), false, false, false),
('Equatorial Guinea', 'GQ', 'https://cdn.kcak11.com/CountryFlags/countries/gq.svg', now(), now(), false, false, false),
('Eritrea', 'ER', 'https://cdn.kcak11.com/CountryFlags/countries/er.svg', now(), now(), false, false, false),
('Eswatini', 'SZ', 'https://cdn.kcak11.com/CountryFlags/countries/sz.svg', now(), now(), false, false, false),
('Ethiopia', 'ET', 'https://cdn.kcak11.com/CountryFlags/countries/et.svg', now(), now(), false, false, false),
('Gabon', 'GA', 'https://cdn.kcak11.com/CountryFlags/countries/ga.svg', now(), now(), false, false, false),
('Gambia', 'GM', 'https://cdn.kcak11.com/CountryFlags/countries/gm.svg', now(), now(), false, false, false),
('Ghana', 'GH', 'https://cdn.kcak11.com/CountryFlags/countries/gh.svg', now(), now(), false, false, false),
('Guinea', 'GN', 'https://cdn.kcak11.com/CountryFlags/countries/gn.svg', now(), now(), false, false, false),
('Guinea-Bissau', 'GW', 'https://cdn.kcak11.com/CountryFlags/countries/gw.svg', now(), now(), false, false, false),
('Ivory Coast', 'CI', 'https://cdn.kcak11.com/CountryFlags/countries/ci.svg', now(), now(), false, false, false),
('Kenya', 'KE', 'https://cdn.kcak11.com/CountryFlags/countries/ke.svg', now(), now(), false, false, false),
('Lesotho', 'LS', 'https://cdn.kcak11.com/CountryFlags/countries/ls.svg', now(), now(), false, false, false),
('Liberia', 'LR', 'https://cdn.kcak11.com/CountryFlags/countries/lr.svg', now(), now(), false, false, false),
('Libya', 'LY', 'https://cdn.kcak11.com/CountryFlags/countries/ly.svg', now(), now(), false, false, false),
('Madagascar', 'MG', 'https://cdn.kcak11.com/CountryFlags/countries/mg.svg', now(), now(), false, false, false),
('Malawi', 'MW', 'https://cdn.kcak11.com/CountryFlags/countries/mw.svg', now(), now(), false, false, false),
('Mali', 'ML', 'https://cdn.kcak11.com/CountryFlags/countries/ml.svg', now(), now(), false, false, false),
('Mauritania', 'MR', 'https://cdn.kcak11.com/CountryFlags/countries/mr.svg', now(), now(), false, false, false),
('Mauritius', 'MU', 'https://cdn.kcak11.com/CountryFlags/countries/mu.svg', now(), now(), false, false, false),
('Morocco', 'MA', 'https://cdn.kcak11.com/CountryFlags/countries/ma.svg', now(), now(), false, false, false),
('Mozambique', 'MZ', 'https://cdn.kcak11.com/CountryFlags/countries/mz.svg', now(), now(), false, false, false),
('Namibia', 'NA', 'https://cdn.kcak11.com/CountryFlags/countries/na.svg', now(), now(), false, false, false),
('Niger', 'NE', 'https://cdn.kcak11.com/CountryFlags/countries/ne.svg', now(), now(), false, false, false),
('Nigeria', 'NG', 'https://cdn.kcak11.com/CountryFlags/countries/ng.svg', now(), now(), false, false, false),
('Rwanda', 'RW', 'https://cdn.kcak11.com/CountryFlags/countries/rw.svg', now(), now(), false, false, false),
('Sao Tome and Principe', 'ST', 'https://cdn.kcak11.com/CountryFlags/countries/st.svg', now(), now(), false, false, false),
('Senegal', 'SN', 'https://cdn.kcak11.com/CountryFlags/countries/sn.svg', now(), now(), false, false, false),
('Seychelles', 'SC', 'https://cdn.kcak11.com/CountryFlags/countries/sc.svg', now(), now(), false, false, false),
('Sierra Leone', 'SL', 'https://cdn.kcak11.com/CountryFlags/countries/sl.svg', now(), now(), false, false, false),
('Somalia', 'SO', 'https://cdn.kcak11.com/CountryFlags/countries/so.svg', now(), now(), false, false, false),
('South Africa', 'ZA', 'https://cdn.kcak11.com/CountryFlags/countries/za.svg', now(), now(), false, false, false),
('South Sudan', 'SS', 'https://cdn.kcak11.com/CountryFlags/countries/ss.svg', now(), now(), false, false, false),
('Sudan', 'SD', 'https://cdn.kcak11.com/CountryFlags/countries/sd.svg', now(), now(), false, false, false),
('Tanzania', 'TZ', 'https://cdn.kcak11.com/CountryFlags/countries/tz.svg', now(), now(), false, false, false),
('Togo', 'TG', 'https://cdn.kcak11.com/CountryFlags/countries/tg.svg', now(), now(), false, false, false),
('Tunisia', 'TN', 'https://cdn.kcak11.com/CountryFlags/countries/tn.svg', now(), now(), false, false, false),
('Uganda', 'UG', 'https://cdn.kcak11.com/CountryFlags/countries/ug.svg', now(), now(), false, false, false),
('Zambia', 'ZM', 'https://cdn.kcak11.com/CountryFlags/countries/zm.svg', now(), now(), false, false, false),
('Zimbabwe', 'ZW', 'https://cdn.kcak11.com/CountryFlags/countries/zw.svg', now(), now(), false, false, false);

-- ASIA
INSERT INTO country (name, code, flag_url, created_at, updated_at, authorize_representative_obligated, other_costs_obligated, is_published) VALUES
('Afghanistan', 'AF', 'https://cdn.kcak11.com/CountryFlags/countries/af.svg', now(), now(), false, false, false),
('Armenia', 'AM', 'https://cdn.kcak11.com/CountryFlags/countries/am.svg', now(), now(), false, false, false),
('Azerbaijan', 'AZ', 'https://cdn.kcak11.com/CountryFlags/countries/az.svg', now(), now(), false, false, false),
('Bahrain', 'BH', 'https://cdn.kcak11.com/CountryFlags/countries/bh.svg', now(), now(), false, false, false),
('Bangladesh', 'BD', 'https://cdn.kcak11.com/CountryFlags/countries/bd.svg', now(), now(), false, false, false),
('Bhutan', 'BT', 'https://cdn.kcak11.com/CountryFlags/countries/bt.svg', now(), now(), false, false, false),
('Brunei', 'BN', 'https://cdn.kcak11.com/CountryFlags/countries/bn.svg', now(), now(), false, false, false),
('Cambodia', 'KH', 'https://cdn.kcak11.com/CountryFlags/countries/kh.svg', now(), now(), false, false, false),
('China', 'CN', 'https://cdn.kcak11.com/CountryFlags/countries/cn.svg', now(), now(), false, false, false),
('Georgia', 'GE', 'https://cdn.kcak11.com/CountryFlags/countries/ge.svg', now(), now(), false, false, false),
('India', 'IN', 'https://cdn.kcak11.com/CountryFlags/countries/in.svg', now(), now(), false, false, false),
('Indonesia', 'ID', 'https://cdn.kcak11.com/CountryFlags/countries/id.svg', now(), now(), false, false, false),
('Iran', 'IR', 'https://cdn.kcak11.com/CountryFlags/countries/ir.svg', now(), now(), false, false, false),
('Iraq', 'IQ', 'https://cdn.kcak11.com/CountryFlags/countries/iq.svg', now(), now(), false, false, false),
('Israel', 'IL', 'https://cdn.kcak11.com/CountryFlags/countries/il.svg', now(), now(), false, false, false),
('Japan', 'JP', 'https://cdn.kcak11.com/CountryFlags/countries/jp.svg', now(), now(), false, false, false),
('Jordan', 'JO', 'https://cdn.kcak11.com/CountryFlags/countries/jo.svg', now(), now(), false, false, false),
('Kazakhstan', 'KZ', 'https://cdn.kcak11.com/CountryFlags/countries/kz.svg', now(), now(), false, false, false),
('Kuwait', 'KW', 'https://cdn.kcak11.com/CountryFlags/countries/kw.svg', now(), now(), false, false, false),
('Kyrgyzstan', 'KG', 'https://cdn.kcak11.com/CountryFlags/countries/kg.svg', now(), now(), false, false, false),
('Laos', 'LA', 'https://cdn.kcak11.com/CountryFlags/countries/la.svg', now(), now(), false, false, false),
('Lebanon', 'LB', 'https://cdn.kcak11.com/CountryFlags/countries/lb.svg', now(), now(), false, false, false),
('Malaysia', 'MY', 'https://cdn.kcak11.com/CountryFlags/countries/my.svg', now(), now(), false, false, false),
('Maldives', 'MV', 'https://cdn.kcak11.com/CountryFlags/countries/mv.svg', now(), now(), false, false, false),
('Mongolia', 'MN', 'https://cdn.kcak11.com/CountryFlags/countries/mn.svg', now(), now(), false, false, false),
('Myanmar', 'MM', 'https://cdn.kcak11.com/CountryFlags/countries/mm.svg', now(), now(), false, false, false),
('Nepal', 'NP', 'https://cdn.kcak11.com/CountryFlags/countries/np.svg', now(), now(), false, false, false),
('North Korea', 'KP', 'https://cdn.kcak11.com/CountryFlags/countries/kp.svg', now(), now(), false, false, false),
('Oman', 'OM', 'https://cdn.kcak11.com/CountryFlags/countries/om.svg', now(), now(), false, false, false),
('Pakistan', 'PK', 'https://cdn.kcak11.com/CountryFlags/countries/pk.svg', now(), now(), false, false, false),
('Palestine', 'PS', 'https://cdn.kcak11.com/CountryFlags/countries/ps.svg', now(), now(), false, false, false),
('Philippines', 'PH', 'https://cdn.kcak11.com/CountryFlags/countries/ph.svg', now(), now(), false, false, false),
('Qatar', 'QA', 'https://cdn.kcak11.com/CountryFlags/countries/qa.svg', now(), now(), false, false, false),
('Saudi Arabia', 'SA', 'https://cdn.kcak11.com/CountryFlags/countries/sa.svg', now(), now(), false, false, false),
('Singapore', 'SG', 'https://cdn.kcak11.com/CountryFlags/countries/sg.svg', now(), now(), false, false, false),
('South Korea', 'KR', 'https://cdn.kcak11.com/CountryFlags/countries/kr.svg', now(), now(), false, false, false),
('Sri Lanka', 'LK', 'https://cdn.kcak11.com/CountryFlags/countries/lk.svg', now(), now(), false, false, false),
('Syria', 'SY', 'https://cdn.kcak11.com/CountryFlags/countries/sy.svg', now(), now(), false, false, false),
('Taiwan', 'TW', 'https://cdn.kcak11.com/CountryFlags/countries/tw.svg', now(), now(), false, false, false),
('Tajikistan', 'TJ', 'https://cdn.kcak11.com/CountryFlags/countries/tj.svg', now(), now(), false, false, false),
('Thailand', 'TH', 'https://cdn.kcak11.com/CountryFlags/countries/th.svg', now(), now(), false, false, false),
('Timor-Leste', 'TL', 'https://cdn.kcak11.com/CountryFlags/countries/tl.svg', now(), now(), false, false, false),
('Turkey', 'TR', 'https://cdn.kcak11.com/CountryFlags/countries/tr.svg', now(), now(), false, false, false),
('Turkmenistan', 'TM', 'https://cdn.kcak11.com/CountryFlags/countries/tm.svg', now(), now(), false, false, false),
('United Arab Emirates', 'AE', 'https://cdn.kcak11.com/CountryFlags/countries/ae.svg', now(), now(), false, false, false),
('Uzbekistan', 'UZ', 'https://cdn.kcak11.com/CountryFlags/countries/uz.svg', now(), now(), false, false, false),
('Vietnam', 'VN', 'https://cdn.kcak11.com/CountryFlags/countries/vn.svg', now(), now(), false, false, false),
('Yemen', 'YE', 'https://cdn.kcak11.com/CountryFlags/countries/ye.svg', now(), now(), false, false, false);

-- EUROPE (including existing EU countries with correct flag URLs)
INSERT INTO country (name, code, flag_url, created_at, updated_at, authorize_representative_obligated, other_costs_obligated, is_published) VALUES
('Albania', 'AL', 'https://cdn.kcak11.com/CountryFlags/countries/al.svg', now(), now(), false, false, false),
('Andorra', 'AD', 'https://cdn.kcak11.com/CountryFlags/countries/ad.svg', now(), now(), false, false, false),
('Belarus', 'BY', 'https://cdn.kcak11.com/CountryFlags/countries/by.svg', now(), now(), false, false, false),
('Bosnia and Herzegovina', 'BA', 'https://cdn.kcak11.com/CountryFlags/countries/ba.svg', now(), now(), false, false, false),
('Iceland', 'IS', 'https://cdn.kcak11.com/CountryFlags/countries/is.svg', now(), now(), false, false, false),
('Kosovo', 'XK', 'https://cdn.kcak11.com/CountryFlags/countries/xk.svg', now(), now(), false, false, false),
('Liechtenstein', 'LI', 'https://cdn.kcak11.com/CountryFlags/countries/li.svg', now(), now(), false, false, false),
('Moldova', 'MD', 'https://cdn.kcak11.com/CountryFlags/countries/md.svg', now(), now(), false, false, false),
('Monaco', 'MC', 'https://cdn.kcak11.com/CountryFlags/countries/mc.svg', now(), now(), false, false, false),
('Montenegro', 'ME', 'https://cdn.kcak11.com/CountryFlags/countries/me.svg', now(), now(), false, false, false),
('North Macedonia', 'MK', 'https://cdn.kcak11.com/CountryFlags/countries/mk.svg', now(), now(), false, false, false),
('Russia', 'RU', 'https://cdn.kcak11.com/CountryFlags/countries/ru.svg', now(), now(), false, false, false),
('San Marino', 'SM', 'https://cdn.kcak11.com/CountryFlags/countries/sm.svg', now(), now(), false, false, false),
('Serbia', 'RS', 'https://cdn.kcak11.com/CountryFlags/countries/rs.svg', now(), now(), false, false, false),
('Ukraine', 'UA', 'https://cdn.kcak11.com/CountryFlags/countries/ua.svg', now(), now(), false, false, false),
('Vatican City', 'VA', 'https://cdn.kcak11.com/CountryFlags/countries/va.svg', now(), now(), false, false, false);

-- NORTH AMERICA
INSERT INTO country (name, code, flag_url, created_at, updated_at, authorize_representative_obligated, other_costs_obligated, is_published) VALUES
('Antigua and Barbuda', 'AG', 'https://cdn.kcak11.com/CountryFlags/countries/ag.svg', now(), now(), false, false, false),
('Bahamas', 'BS', 'https://cdn.kcak11.com/CountryFlags/countries/bs.svg', now(), now(), false, false, false),
('Barbados', 'BB', 'https://cdn.kcak11.com/CountryFlags/countries/bb.svg', now(), now(), false, false, false),
('Belize', 'BZ', 'https://cdn.kcak11.com/CountryFlags/countries/bz.svg', now(), now(), false, false, false),
('Canada', 'CA', 'https://cdn.kcak11.com/CountryFlags/countries/ca.svg', now(), now(), false, false, false),
('Costa Rica', 'CR', 'https://cdn.kcak11.com/CountryFlags/countries/cr.svg', now(), now(), false, false, false),
('Cuba', 'CU', 'https://cdn.kcak11.com/CountryFlags/countries/cu.svg', now(), now(), false, false, false),
('Dominica', 'DM', 'https://cdn.kcak11.com/CountryFlags/countries/dm.svg', now(), now(), false, false, false),
('Dominican Republic', 'DO', 'https://cdn.kcak11.com/CountryFlags/countries/do.svg', now(), now(), false, false, false),
('El Salvador', 'SV', 'https://cdn.kcak11.com/CountryFlags/countries/sv.svg', now(), now(), false, false, false),
('Grenada', 'GD', 'https://cdn.kcak11.com/CountryFlags/countries/gd.svg', now(), now(), false, false, false),
('Guatemala', 'GT', 'https://cdn.kcak11.com/CountryFlags/countries/gt.svg', now(), now(), false, false, false),
('Haiti', 'HT', 'https://cdn.kcak11.com/CountryFlags/countries/ht.svg', now(), now(), false, false, false),
('Honduras', 'HN', 'https://cdn.kcak11.com/CountryFlags/countries/hn.svg', now(), now(), false, false, false),
('Jamaica', 'JM', 'https://cdn.kcak11.com/CountryFlags/countries/jm.svg', now(), now(), false, false, false),
('Mexico', 'MX', 'https://cdn.kcak11.com/CountryFlags/countries/mx.svg', now(), now(), false, false, false),
('Nicaragua', 'NI', 'https://cdn.kcak11.com/CountryFlags/countries/ni.svg', now(), now(), false, false, false),
('Panama', 'PA', 'https://cdn.kcak11.com/CountryFlags/countries/pa.svg', now(), now(), false, false, false),
('Saint Kitts and Nevis', 'KN', 'https://cdn.kcak11.com/CountryFlags/countries/kn.svg', now(), now(), false, false, false),
('Saint Lucia', 'LC', 'https://cdn.kcak11.com/CountryFlags/countries/lc.svg', now(), now(), false, false, false),
('Saint Vincent and the Grenadines', 'VC', 'https://cdn.kcak11.com/CountryFlags/countries/vc.svg', now(), now(), false, false, false),
('Trinidad and Tobago', 'TT', 'https://cdn.kcak11.com/CountryFlags/countries/tt.svg', now(), now(), false, false, false),
('United States', 'US', 'https://cdn.kcak11.com/CountryFlags/countries/us.svg', now(), now(), false, false, false);

-- SOUTH AMERICA
INSERT INTO country (name, code, flag_url, created_at, updated_at, authorize_representative_obligated, other_costs_obligated, is_published) VALUES
('Argentina', 'AR', 'https://cdn.kcak11.com/CountryFlags/countries/ar.svg', now(), now(), false, false, false),
('Bolivia', 'BO', 'https://cdn.kcak11.com/CountryFlags/countries/bo.svg', now(), now(), false, false, false),
('Brazil', 'BR', 'https://cdn.kcak11.com/CountryFlags/countries/br.svg', now(), now(), false, false, false),
('Chile', 'CL', 'https://cdn.kcak11.com/CountryFlags/countries/cl.svg', now(), now(), false, false, false),
('Colombia', 'CO', 'https://cdn.kcak11.com/CountryFlags/countries/co.svg', now(), now(), false, false, false),
('Ecuador', 'EC', 'https://cdn.kcak11.com/CountryFlags/countries/ec.svg', now(), now(), false, false, false),
('Guyana', 'GY', 'https://cdn.kcak11.com/CountryFlags/countries/gy.svg', now(), now(), false, false, false),
('Paraguay', 'PY', 'https://cdn.kcak11.com/CountryFlags/countries/py.svg', now(), now(), false, false, false),
('Peru', 'PE', 'https://cdn.kcak11.com/CountryFlags/countries/pe.svg', now(), now(), false, false, false),
('Suriname', 'SR', 'https://cdn.kcak11.com/CountryFlags/countries/sr.svg', now(), now(), false, false, false),
('Uruguay', 'UY', 'https://cdn.kcak11.com/CountryFlags/countries/uy.svg', now(), now(), false, false, false),
('Venezuela', 'VE', 'https://cdn.kcak11.com/CountryFlags/countries/ve.svg', now(), now(), false, false, false);

-- OCEANIA
INSERT INTO country (name, code, flag_url, created_at, updated_at, authorize_representative_obligated, other_costs_obligated, is_published) VALUES
('Australia', 'AU', 'https://cdn.kcak11.com/CountryFlags/countries/au.svg', now(), now(), false, false, false),
('Fiji', 'FJ', 'https://cdn.kcak11.com/CountryFlags/countries/fj.svg', now(), now(), false, false, false),
('Kiribati', 'KI', 'https://cdn.kcak11.com/CountryFlags/countries/ki.svg', now(), now(), false, false, false),
('Marshall Islands', 'MH', 'https://cdn.kcak11.com/CountryFlags/countries/mh.svg', now(), now(), false, false, false),
('Micronesia', 'FM', 'https://cdn.kcak11.com/CountryFlags/countries/fm.svg', now(), now(), false, false, false),
('Nauru', 'NR', 'https://cdn.kcak11.com/CountryFlags/countries/nr.svg', now(), now(), false, false, false),
('New Zealand', 'NZ', 'https://cdn.kcak11.com/CountryFlags/countries/nz.svg', now(), now(), false, false, false),
('Palau', 'PW', 'https://cdn.kcak11.com/CountryFlags/countries/pw.svg', now(), now(), false, false, false),
('Papua New Guinea', 'PG', 'https://cdn.kcak11.com/CountryFlags/countries/pg.svg', now(), now(), false, false, false),
('Samoa', 'WS', 'https://cdn.kcak11.com/CountryFlags/countries/ws.svg', now(), now(), false, false, false),
('Solomon Islands', 'SB', 'https://cdn.kcak11.com/CountryFlags/countries/sb.svg', now(), now(), false, false, false),
('Tonga', 'TO', 'https://cdn.kcak11.com/CountryFlags/countries/to.svg', now(), now(), false, false, false),
('Tuvalu', 'TV', 'https://cdn.kcak11.com/CountryFlags/countries/tv.svg', now(), now(), false, false, false),
('Vanuatu', 'VU', 'https://cdn.kcak11.com/CountryFlags/countries/vu.svg', now(), now(), false, false, false);

-- NOTES:
-- 1. This script includes all 195 UN member countries plus some additional territories
-- 2. European countries that were already in your database are included with is_published=false
-- 3. All flag URLs use the same CDN pattern as your existing data
-- 4. You may need to handle conflicts with existing countries by either:
--    a) Running DELETE FROM country; first (be careful with foreign key constraints)
--    b) Using INSERT ... ON CONFLICT DO UPDATE syntax
--    c) Manually removing duplicates before running this script
-- 5. To add regions/continents, consider creating a separate 'region' table and adding a region_id foreign key to the country table

-- Example query to get countries by region after running this script:
-- SELECT name, code,
--   CASE
--     WHEN code IN ('DZ','AO','BJ','BW','BF','BI','CM','CV','CF','TD','KM','CG','CD','DJ','EG','GQ','ER','SZ','ET','GA','GM','GH','GN','GW','CI','KE','LS','LR','LY','MG','MW','ML','MR','MU','MA','MZ','NA','NE','NG','RW','ST','SN','SC','SL','SO','ZA','SS','SD','TZ','TG','TN','UG','ZM','ZW') THEN 'Africa'
--     WHEN code IN ('AF','AM','AZ','BH','BD','BT','BN','KH','CN','GE','IN','ID','IR','IQ','IL','JP','JO','KZ','KW','KG','LA','LB','MY','MV','MN','MM','NP','KP','OM','PK','PS','PH','QA','SA','SG','KR','LK','SY','TW','TJ','TH','TL','TR','TM','AE','UZ','VN','YE') THEN 'Asia'
--     WHEN code IN ('AL','AD','AT','BY','BE','BA','BG','HR','CY','CZ','DK','EE','FI','FR','DE','GR','HU','IS','IE','IT','XK','LV','LI','LT','LU','MT','MD','MC','ME','NL','MK','NO','PL','PT','RO','RU','SM','RS','SK','SI','ES','SE','CH','UA','GB','VA') THEN 'Europe'
--     WHEN code IN ('AG','BS','BB','BZ','CA','CR','CU','DM','DO','SV','GD','GT','HT','HN','JM','MX','NI','PA','KN','LC','VC','TT','US') THEN 'North America'
--     WHEN code IN ('AR','BO','BR','CL','CO','EC','GY','PY','PE','SR','UY','VE') THEN 'South America'
--     WHEN code IN ('AU','FJ','KI','MH','FM','NR','NZ','PW','PG','WS','SB','TO','TV','VU') THEN 'Oceania'
--     ELSE 'Other'
--   END as region
-- FROM country
-- ORDER BY region, name;
