-- Migration to create the many-to-many relationship table between required_information and packaging_service
-- This table establishes the relationship where a required information can be associated with multiple packaging services
-- and a packaging service can be associated with multiple required information entries

-- Create the junction table for the many-to-many relationship
CREATE TABLE IF NOT EXISTS required_information_packaging_service
(
    required_information_id
    INTEGER
    NOT
    NULL,
    packaging_service_id
    INTEGER
    NOT
    NULL,
    created_at
    TIMESTAMP
    WITH
    TIME
    ZONE
    DEFAULT
    CURRENT_TIMESTAMP,

    -- Primary key constraint on the combination of both foreign keys
    PRIMARY
    KEY
(
    required_information_id,
    packaging_service_id
),

    -- Foreign key constraint to required_information table
    CONSTRAINT fk_required_information_packaging_service_required_information
    FOREIGN KEY
(
    required_information_id
)
    REFERENCES required_information
(
    id
)
    ON DELETE CASCADE,

    -- Foreign key constraint to packaging_service table
    CONSTRAINT fk_required_information_packaging_service_packaging_service
    FOREIGN KEY
(
    packaging_service_id
)
    REFERENCES packaging_service
(
    id
)
    ON DELETE CASCADE
    );

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_required_information_packaging_service_required_information_id
    ON required_information_packaging_service(required_information_id);

CREATE INDEX IF NOT EXISTS idx_required_information_packaging_service_packaging_service_id
    ON required_information_packaging_service(packaging_service_id);

-- Add comments for documentation
COMMENT
ON TABLE required_information_packaging_service IS 'Junction table for many-to-many relationship between required_information and packaging_service';
COMMENT
ON COLUMN required_information_packaging_service.required_information_id IS 'Foreign key reference to required_information.id';
COMMENT
ON COLUMN required_information_packaging_service.packaging_service_id IS 'Foreign key reference to packaging_service.id';
COMMENT
ON COLUMN required_information_packaging_service.created_at IS 'Timestamp when the relationship was created';
