package de.interzero.oneepr.admin.price_list;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.price_list.dto.CreatePriceListDto;
import de.interzero.oneepr.admin.price_list.dto.UpdatePriceListDto;
import de.interzero.oneepr.common.string.Api;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the {@link PriceListController}.
 * This class validates the full HTTP request-response cycle for the price list module,
 * ensuring endpoints for creation, retrieval, filtering, updates, and deletion work as expected.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class PriceListControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private PriceListRepository priceListRepository;

    private PriceList testPriceList;

    /**
     * Sets up a consistent database state before each test method runs.
     * This data is automatically rolled back by the @Transactional annotation after each test.
     */
    @BeforeEach
    void setUp() {
        // Clear repository to ensure a clean state for each test
        priceListRepository.deleteAll();
        testPriceList = createAndSaveTestPriceList("EU License 2024", "2024", PriceList.Type.EU_LICENSE);
    }

    /**
     * Verifies that a POST request to {@link PriceListController#create(CreatePriceListDto)}
     * successfully creates a new price list.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void create_shouldCreateNewPriceList() throws Exception {
        mockMvc.perform(post(Api.PRICE_LISTS).contentType(MediaType.APPLICATION_JSON).content("""
                                                                                                      {
                                                                                                        "name": "Direct License 2025",
                                                                                                        "type": "EU_LICENSE",
                                                                                                        "description": "DE price list",
                                                                                                        "condition_type": "LICENSE_YEAR",
                                                                                                        "condition_type_value": "2025",
                                                                                                        "start_date": "2025-01-01",
                                                                                                        "end_date": "2028-01-01",
                                                                                                        "registration_fee": 100,
                                                                                                        "handling_fee": 100,
                                                                                                        "variable_handling_fee": 12,
                                                                                                        "basic_price": 1500,
                                                                                                        "minimum_price": null,
                                                                                                        "thresholds": null,
                                                                                                        "price": null
                                                                                                      }
                                                                                                      """))
                .andExpect(status().isOk()) // The controller returns the created object with status 200, not 201
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.name", is("Direct License 2025")))
                .andExpect(jsonPath("$.basic_price", is(1500)))
                .andExpect(jsonPath("$.condition_type_value", is("2025")));
    }

    /**
     * Verifies that a GET request to {@link PriceListController#findAll(String, PriceList.Type, String)}
     * returns a list of all active price lists.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findAll_shouldReturnListOfPriceLists() throws Exception {
        mockMvc.perform(get(Api.PRICE_LISTS))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(testPriceList.getId())));
    }

    /**
     * Verifies that a GET request with query parameters correctly filters the price lists.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findAll_withFilters_shouldReturnFilteredList() throws Exception {
        // Create another price list to test filtering
        createAndSaveTestPriceList("Action Guide 2025", "2025", PriceList.Type.ACTION_GUIDE);

        // Test filter by license_year
        mockMvc.perform(get(Api.PRICE_LISTS).queryParam("license_year", "2025"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].name", is("Action Guide 2025")));

        // Test filter by service_type
        mockMvc.perform(get(Api.PRICE_LISTS).queryParam("service_type", "EU_LICENSE"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(testPriceList.getId())));

        // Test filter by search term
        mockMvc.perform(get(Api.PRICE_LISTS).queryParam("search", "action"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].name", is("Action Guide 2025")));
    }


    /**
     * Verifies that a GET request to {@link PriceListController#findOne(String)} returns the correct price list.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findOne_shouldReturnCorrectPriceList() throws Exception {
        mockMvc.perform(get(Api.PRICE_LISTS + "/{id}", testPriceList.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testPriceList.getId())))
                .andExpect(jsonPath("$.name", is("EU License 2024")));
    }

    /**
     * Verifies that a PUT request to {@link PriceListController#update(String, UpdatePriceListDto)}
     * successfully updates an existing price list.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void update_shouldModifyExistingPriceList() throws Exception {
        UpdatePriceListDto updateDto = new UpdatePriceListDto();
        updateDto.setName("Updated EU License 2024");
        updateDto.setBasicPrice(9999);

        mockMvc.perform(put(Api.PRICE_LISTS + "/{id}", testPriceList.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testPriceList.getId())))
                .andExpect(jsonPath("$.name", is("Updated EU License 2024")))
                .andExpect(jsonPath("$.basic_price", is(9999)));
    }

    /**
     * Verifies that a DELETE request to {@link PriceListController#remove(String)}
     * successfully soft-deletes a price list.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void remove_shouldSoftDeletePriceList() throws Exception {
        mockMvc.perform(delete(Api.PRICE_LISTS + "/{id}", testPriceList.getId())).andExpect(status().isOk());

        // Verify it's gone from the "active" list via the controller
        mockMvc.perform(get(Api.PRICE_LISTS + "/{id}", testPriceList.getId())).andExpect(status().isNotFound());

        // Verify in the database that the deleted_at field is now set
        PriceList deletedPriceList = priceListRepository.findById(testPriceList.getId()).orElseThrow();
        assertNotNull(deletedPriceList.getDeletedAt());
    }

    // --- Helper Methods for Test Setup ---

    /**
     * Creates and persists a valid {@link PriceList} entity for test setup.
     *
     * @param name The name of the price list.
     * @param year The license year value.
     * @param type The type of the price list.
     * @return The persisted PriceList entity.
     */
    private PriceList createAndSaveTestPriceList(String name,
                                                 String year,
                                                 PriceList.Type type) {
        PriceList priceList = new PriceList();
        priceList.setName(name);
        priceList.setDescription("Test description for " + name);
        priceList.setType(type);
        priceList.setConditionType(PriceList.ConditionType.LICENSE_YEAR);
        priceList.setConditionTypeValue(year);
        priceList.setStartDate(Instant.now().minus(10, ChronoUnit.DAYS));
        priceList.setEndDate(Instant.now().plus(365, ChronoUnit.DAYS));
        priceList.setBasicPrice(1000);
        priceList.setMinimumPrice(100);
        return priceListRepository.saveAndFlush(priceList);
    }
}