package de.interzero.oneepr.admin.fraction_icon;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.PropertyWriter;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.dto.FractionIconDetailDto;
import de.interzero.oneepr.admin.service_setup.dto.FractionIconNestedDto;
import de.interzero.oneepr.common.service.AwsService;
import lombok.Getter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Duration;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Unit tests for {@link FractionIconImageUrlSerializer}.
 *
 * <p>This test class provides comprehensive coverage of all conditions and branches
 * in the serializer logic, including:</p>
 *
 * <ul>
 *   <li><strong>serializeAsField method:</strong> Tests different property writer names,
 *       null image URLs, absolute URLs (https://), and relative URLs requiring presigning</li>
 *   <li><strong>getImageUrlFromObject method:</strong> Tests extraction from null objects,
 *       FractionIcon entities, FractionIconNestedDto, FractionIconDetailDto, and fallback
 *       to reflection for unknown object types</li>
 *   <li><strong>getImageUrlViaReflection method:</strong> Tests reflection-based URL extraction
 *       including successful extraction, non-String return types, missing methods, and
 *       exception handling</li>
 * </ul>
 *
 * <p>The tests use Mockito to mock dependencies and verify interactions with the AWS service
 * and JSON generation components.</p>
 */
@ExtendWith(MockitoExtension.class)
class FractionIconImageUrlSerializerTest {

    @Mock
    private AwsService awsService;

    @Mock
    private JsonGenerator jsonGenerator;

    @Mock
    private SerializerProvider serializerProvider;

    @Mock
    private PropertyWriter propertyWriter;

    @Mock
    private AwsService.PresignedUrlResponse presignedUrlResponse;

    private FractionIconImageUrlSerializer serializer;

    @BeforeEach
    void setUp() {
        serializer = new FractionIconImageUrlSerializer(awsService);
    }

    // --- serializeAsField method tests ---

    /**
     * Tests that when the property writer name is not "image_url", the serializer
     * delegates to the default serialization behavior without any custom processing.
     * This ensures the serializer only processes image_url fields.
     */
    @Test
    void serializeAsField_whenWriterNameIsNotImageUrl_shouldCallWriterSerializeAsField() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("other_field");
        Object testPojo = new Object();

        // Act
        serializer.serializeAsField(testPojo, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(propertyWriter).serializeAsField(testPojo, jsonGenerator, serializerProvider);
        verifyNoInteractions(awsService);
        verify(jsonGenerator, never()).writeStringField(anyString(), anyString());
    }

    /**
     * Tests that when the image URL cannot be extracted from the object (returns null),
     * the serializer falls back to default serialization behavior.
     * This covers the case where getImageUrlFromObject returns null.
     */
    @Test
    void serializeAsField_whenImageUrlIsNull_shouldCallWriterSerializeAsField() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");
        Object testPojo = new Object(); // Object without getImageUrl method

        // Act
        serializer.serializeAsField(testPojo, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(propertyWriter).serializeAsField(testPojo, jsonGenerator, serializerProvider);
        verifyNoInteractions(awsService);
        verify(jsonGenerator, never()).writeStringField(anyString(), anyString());
    }

    /**
     * Tests that when the image URL already starts with "https://", it is written directly
     * to the JSON output without generating a presigned URL. This optimizes performance
     * by avoiding unnecessary AWS service calls for already absolute URLs.
     */
    @Test
    void serializeAsField_whenImageUrlStartsWithHttps_shouldWriteUrlDirectly() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");
        FractionIcon fractionIcon = new FractionIcon();
        fractionIcon.setImageUrl("https://example.com/image.png");

        // Act
        serializer.serializeAsField(fractionIcon, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(jsonGenerator).writeStringField("image_url", "https://example.com/image.png");
        verifyNoInteractions(awsService);
        verify(propertyWriter, never()).serializeAsField(any(), any(), any());
    }

    /**
     * Tests that when the image URL does not start with "https://", a presigned URL
     * is generated using the AWS service and written to the JSON output.
     * This covers the main functionality of converting relative paths to presigned URLs.
     */
    @Test
    void serializeAsField_whenImageUrlDoesNotStartWithHttps_shouldGeneratePresignedUrl() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");
        when(presignedUrlResponse.getPreSignedUrl()).thenReturn("https://presigned-url.com/image.png");
        when(awsService.generatePresignedDownloadUrl("relative/path/image.png", Duration.ofMinutes(15))).thenReturn(
                presignedUrlResponse);

        FractionIcon fractionIcon = new FractionIcon();
        fractionIcon.setImageUrl("relative/path/image.png");

        // Act
        serializer.serializeAsField(fractionIcon, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(awsService).generatePresignedDownloadUrl("relative/path/image.png", Duration.ofMinutes(15));
        verify(jsonGenerator).writeStringField("image_url", "https://presigned-url.com/image.png");
        verify(propertyWriter, never()).serializeAsField(any(), any(), any());
    }

    // --- getImageUrlFromObject method tests (via serializeAsField) ---

    /**
     * Tests that when the input object is null, the serializer falls back to default
     * serialization behavior. This covers the null case in the switch expression
     * of getImageUrlFromObject method.
     */
    @Test
    void serializeAsField_withNullPojo_shouldCallWriterSerializeAsField() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");

        // Act
        serializer.serializeAsField(null, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(propertyWriter).serializeAsField(null, jsonGenerator, serializerProvider);
        verifyNoInteractions(awsService);
    }

    /**
     * Tests that when the input object is a FractionIcon entity, the serializer
     * correctly extracts the image URL using the entity's getImageUrl() method.
     * This covers the FractionIcon case in the switch expression.
     */
    @Test
    void serializeAsField_withFractionIconEntity_shouldExtractImageUrl() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");
        FractionIcon fractionIcon = new FractionIcon();
        fractionIcon.setImageUrl("https://example.com/fraction-icon.png");

        // Act
        serializer.serializeAsField(fractionIcon, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(jsonGenerator).writeStringField("image_url", "https://example.com/fraction-icon.png");
    }

    /**
     * Tests that when the input object is a FractionIconNestedDto, the serializer
     * correctly extracts the image URL using the DTO's getImageUrl() method.
     * This covers the FractionIconNestedDto case in the switch expression.
     */
    @Test
    void serializeAsField_withFractionIconNestedDto_shouldExtractImageUrl() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");
        FractionIconNestedDto dto = new FractionIconNestedDto();
        dto.setImageUrl("https://example.com/nested-icon.png");

        // Act
        serializer.serializeAsField(dto, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(jsonGenerator).writeStringField("image_url", "https://example.com/nested-icon.png");
    }

    /**
     * Tests that when the input object is a FractionIconDetailDto, the serializer
     * correctly extracts the image URL using the DTO's getImageUrl() method.
     * This covers the FractionIconDetailDto case in the switch expression.
     */
    @Test
    void serializeAsField_withFractionIconDetailDto_shouldExtractImageUrl() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");
        FractionIconDetailDto dto = new FractionIconDetailDto();
        dto.setImageUrl("https://example.com/detail-icon.png");

        // Act
        serializer.serializeAsField(dto, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(jsonGenerator).writeStringField("image_url", "https://example.com/detail-icon.png");
    }

    /**
     * Tests that when the input object is not one of the known types but has a getImageUrl() method,
     * the serializer uses reflection to extract the image URL. This covers the default case
     * in the switch expression that calls getImageUrlViaReflection.
     */
    @Test
    void serializeAsField_withObjectHavingGetImageUrlMethod_shouldUseReflection() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");
        TestObjectWithImageUrl testObject = new TestObjectWithImageUrl("https://example.com/reflection-icon.png");

        // Act
        serializer.serializeAsField(testObject, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(jsonGenerator).writeStringField("image_url", "https://example.com/reflection-icon.png");
    }

    /**
     * Tests that when an object has a getImageUrl() method but it returns a non-String value,
     * the serializer falls back to default serialization. This covers the case where
     * reflection finds the method but the return type is not String.
     */
    @Test
    void serializeAsField_withObjectHavingGetImageUrlReturningNonString_shouldCallWriterSerializeAsField() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");
        TestObjectWithNonStringImageUrl testObject = new TestObjectWithNonStringImageUrl();

        // Act
        serializer.serializeAsField(testObject, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(propertyWriter).serializeAsField(testObject, jsonGenerator, serializerProvider);
        verifyNoInteractions(awsService);
    }

    /**
     * Tests that when an object doesn't have a getImageUrl() method, the serializer
     * falls back to default serialization. This covers the NoSuchMethodException
     * case in the getImageUrlViaReflection method.
     */
    @Test
    void serializeAsField_withObjectWithoutGetImageUrlMethod_shouldCallWriterSerializeAsField() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");
        TestObjectWithoutImageUrl testObject = new TestObjectWithoutImageUrl();

        // Act
        serializer.serializeAsField(testObject, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(propertyWriter).serializeAsField(testObject, jsonGenerator, serializerProvider);
        verifyNoInteractions(awsService);
    }

    /**
     * Tests that when an object's getImageUrl() method throws a ReflectiveOperationException,
     * the serializer handles the exception gracefully and falls back to default serialization.
     * This covers the ReflectiveOperationException case in the getImageUrlViaReflection method.
     */
    @Test
    void serializeAsField_withObjectThrowingReflectiveOperationException_shouldCallWriterSerializeAsField() throws Exception {
        // Arrange
        when(propertyWriter.getName()).thenReturn("image_url");
        TestObjectThrowingException testObject = new TestObjectThrowingException();

        // Act
        serializer.serializeAsField(testObject, jsonGenerator, serializerProvider, propertyWriter);

        // Assert
        verify(propertyWriter).serializeAsField(testObject, jsonGenerator, serializerProvider);
        verifyNoInteractions(awsService);
    }

    // --- Test helper classes ---

    /**
     * Test class that has a getImageUrl() method returning a String.
     * Used to test the reflection-based URL extraction for objects that follow
     * the standard naming convention but are not explicitly handled by the serializer.
     */
    @Getter
    public static class TestObjectWithImageUrl {

        private final String imageUrl;

        public TestObjectWithImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

    }

    /**
     * Test class that has a getImageUrl() method returning a non-String.
     * Used to test the case where reflection finds the method but the return type
     * is not String, which should result in fallback to default serialization.
     */
    public static class TestObjectWithNonStringImageUrl {

        public Integer getImageUrl() {
            return 123;
        }
    }

    /**
     * Test class that doesn't have a getImageUrl() method.
     * Used to test the NoSuchMethodException handling in the reflection-based
     * URL extraction, which should result in fallback to default serialization.
     */
    public static class TestObjectWithoutImageUrl {

        public String getSomeOtherField() {
            return "other";
        }
    }

    /**
     * Test class that throws an exception when getImageUrl() is called.
     * Used to test the ReflectiveOperationException handling in the reflection-based
     * URL extraction, which should result in fallback to default serialization.
     */
    public static class TestObjectThrowingException {

        public String getImageUrl() throws Exception {
            throw new RuntimeException("Test exception");
        }
    }
}
