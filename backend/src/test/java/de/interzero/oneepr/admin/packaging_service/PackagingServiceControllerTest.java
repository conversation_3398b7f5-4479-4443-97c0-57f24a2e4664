package de.interzero.oneepr.admin.packaging_service;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingService;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingServiceController;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingServiceRepository;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.dto.CreatePackagingServiceDto;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.dto.UpdatePackagingServiceDto;
import de.interzero.oneepr.common.string.Api;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the {@link PackagingServiceController}.
 * This class validates the full HTTP request-response cycle for the packaging service module.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class PackagingServiceControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CountryRepository countryRepository;

    @Autowired
    private PackagingServiceRepository packagingServiceRepository;

    private Country testCountry;

    private PackagingService testPackagingService;

    /**
     * Sets up a consistent database state before each test method runs.
     * This data is automatically rolled back by the @Transactional annotation after each test.
     */
    @BeforeEach
    void setUp() {
        testCountry = createAndSaveTestCountry();
        testPackagingService = createAndSaveTestPackagingService(testCountry);
    }

    /**
     * Verifies that a POST request to {@link PackagingServiceController#create(CreatePackagingServiceDto)}
     * successfully creates a new packaging service.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void create_shouldCreateNewPackagingService() throws Exception {
        CreatePackagingServiceDto createDto = new CreatePackagingServiceDto();
        createDto.setName("New Test Service");
        createDto.setDescription("Description for the new service.");
        createDto.setCountryId(testCountry.getId());

        mockMvc.perform(post(Api.PACKAGING_SERVICES).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.name", is("New Test Service")))
                .andExpect(jsonPath("$.country_id", is(testCountry.getId())));
    }

    /**
     * Verifies that a GET request to {@link PackagingServiceController#findAll(Integer)}
     * returns a list of all active packaging services.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void findAll_shouldReturnListOfPackagingServices() throws Exception {
        mockMvc.perform(get(Api.PACKAGING_SERVICES))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(testPackagingService.getId())));
    }

    /**
     * Verifies that a GET request to {@link PackagingServiceController#findOne(Integer)}
     * returns the correct packaging service.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void findOne_shouldReturnCorrectPackagingService() throws Exception {
        mockMvc.perform(get(Api.PACKAGING_SERVICES + "/{id}", testPackagingService.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testPackagingService.getId())))
                .andExpect(jsonPath("$.name", is("Initial Service")));
    }

    /**
     * Verifies that a PUT request to {@link PackagingServiceController#update(Integer, UpdatePackagingServiceDto)}
     * successfully updates an existing packaging service.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void update_shouldModifyExistingPackagingService() throws Exception {
        UpdatePackagingServiceDto updateDto = new UpdatePackagingServiceDto();
        updateDto.setName("Updated Service Name");
        updateDto.setDescription("Updated Description");

        mockMvc.perform(put(
                        Api.PACKAGING_SERVICES + "/{id}",
                        testPackagingService.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testPackagingService.getId())))
                .andExpect(jsonPath("$.name", is("Updated Service Name")))
                .andExpect(jsonPath("$.description", is("Updated Description")));
    }

    /**
     * Verifies that a DELETE request to {@link PackagingServiceController#remove(Integer)}
     * successfully soft-deletes a packaging service.
     */
    @Test
    @WithMockUser(
            username = "101",
            roles = {"ADMIN"}
    )
    void remove_shouldSoftDeletePackagingService() throws Exception {
        mockMvc.perform(delete(Api.PACKAGING_SERVICES + "/{id}", testPackagingService.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.deleted_at").exists());

        // Verify it's gone from the "active" list
        mockMvc.perform(get(Api.PACKAGING_SERVICES + "/{id}", testPackagingService.getId()))
                .andExpect(status().isNotFound());

        // Verify in the database that the deleted_at field is set
        PackagingService deletedService = packagingServiceRepository.findById(testPackagingService.getId())
                .orElseThrow();
        assertNotNull(deletedService.getDeletedAt());
    }

    // --- Helper Methods for Test Setup ---

    /**
     * Creates and persists a valid {@link Country} entity for test setup.
     */
    private Country createAndSaveTestCountry() {
        Country country = new Country();
        country.setName("Testland");
        country.setCode("TL");
        country.setFlagUrl("http://example.com/flag.png");
        country.setCreatedAt(Instant.now());
        country.setUpdatedAt(Instant.now());
        return countryRepository.saveAndFlush(country);
    }

    /**
     * Creates and persists a valid {@link PackagingService} entity for test setup.
     */
    private PackagingService createAndSaveTestPackagingService(@NotNull Country country) {
        PackagingService service = new PackagingService();
        service.setName("Initial Service");
        service.setDescription("Initial Description");
        service.setCountry(country);
        return packagingServiceRepository.saveAndFlush(service);
    }
}
