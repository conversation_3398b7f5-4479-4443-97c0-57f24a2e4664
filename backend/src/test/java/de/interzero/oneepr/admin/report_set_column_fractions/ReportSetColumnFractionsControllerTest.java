package de.interzero.oneepr.admin.report_set_column_fractions;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingService;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingServiceRepository;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSet;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSetRepository;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_column_fractions.ReportSetColumnFraction;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_column_fractions.ReportSetColumnFractionRepository;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_column_fractions.ReportSetColumnFractionsController;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_column_fractions.dto.CreateReportSetColumnFractionDto;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_columns.ReportSetColumn;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_columns.ReportSetColumnRepository;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions.ReportSetFraction;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions.ReportSetFractionRepository;
import de.interzero.oneepr.common.string.Api;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.UUID;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Integration tests for the {@link ReportSetColumnFractionsController}.
 * This class validates the full HTTP request-response cycle.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class ReportSetColumnFractionsControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ReportSetRepository reportSetRepository;

    @Autowired
    private ReportSetColumnRepository reportSetColumnRepository;

    @Autowired
    private ReportSetFractionRepository reportSetFractionRepository;

    @Autowired
    private ReportSetColumnFractionRepository reportSetColumnFractionRepository;

    @Autowired
    private PackagingServiceRepository packagingServiceRepository;

    @Autowired
    private CountryRepository countryRepository;

    private ReportSetColumn testColumn;

    private ReportSetFraction testFraction;

    private ReportSetColumnFraction testAssociation;

    /**
     * Sets up a consistent and valid database state before each test method runs.
     */
    @BeforeEach
    void setUp() {
        // Clear repositories in reverse order of dependency
        reportSetColumnFractionRepository.deleteAll();
        reportSetColumnRepository.deleteAll();
        reportSetFractionRepository.deleteAll();
        reportSetRepository.deleteAll();
        packagingServiceRepository.deleteAll();
        countryRepository.deleteAll();

        // Create prerequisite entities
        Country country = createAndSaveTestCountry();
        PackagingService packagingService = createAndSaveTestPackagingService(country);
        ReportSet reportSet = createAndSaveTestReportSet(packagingService);

        testColumn = createAndSaveTestReportSetColumn("Test Column", reportSet);
        testFraction = createAndSaveTestReportSetFraction("Test Fraction", reportSet);
        testAssociation = createAndSaveTestAssociation(testColumn, testFraction);
    }

    /**
     * Verifies that a POST request successfully creates a new column-fraction association.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void create_shouldCreateNewAssociation() throws Exception {
        ReportSetColumn newColumn = createAndSaveTestReportSetColumn("New Column", testColumn.getReportSet());
        ReportSetFraction newFraction = createAndSaveTestReportSetFraction("New Fraction", testFraction.getReportSet());

        CreateReportSetColumnFractionDto createDto = new CreateReportSetColumnFractionDto();
        createDto.setColumnCode(newColumn.getCode());
        createDto.setFractionCode(newFraction.getCode());

        mockMvc.perform(post(Api.REPORT_SET_COLUMN_FRACTIONS).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.id").exists())
                .andExpect(jsonPath("$.column_code", is(newColumn.getCode())))
                .andExpect(jsonPath("$.fraction_code", is(newFraction.getCode())));
    }

    /**
     * Verifies that a GET request returns all active associations.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findAll_shouldReturnListOfAssociations() throws Exception {
        mockMvc.perform(get(Api.REPORT_SET_COLUMN_FRACTIONS))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(testAssociation.getId())));
    }

    /**
     * Verifies that a GET request for a specific ID returns the correct association.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findOne_shouldReturnCorrectAssociation() throws Exception {
        mockMvc.perform(get(Api.REPORT_SET_COLUMN_FRACTIONS + "/{id}", testAssociation.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testAssociation.getId())));
    }

    /**
     * Verifies that a DELETE request correctly soft-deletes an association.
     */
    @Test
    @WithMockUser(roles = {"ADMIN"})
    void remove_shouldSoftDeleteAssociation() throws Exception {
        mockMvc.perform(delete(Api.REPORT_SET_COLUMN_FRACTIONS + "/{id}", testAssociation.getId()))
                .andExpect(status().isOk());

        // Verify it's gone from the "active" list via the API
        mockMvc.perform(get(Api.REPORT_SET_COLUMN_FRACTIONS + "/{id}", testAssociation.getId()))
                .andExpect(status().isNotFound());

        // Verify in the database that the deleted_at field is now set
        ReportSetColumnFraction deletedAssociation = reportSetColumnFractionRepository.findById(testAssociation.getId())
                .orElseThrow();
        assertNotNull(deletedAssociation.getDeletedAt());
    }

    // --- Helper Methods for Test Setup ---

    private Country createAndSaveTestCountry() {
        Country country = new Country();
        country.setName("Testland");
        country.setCode("TL");
        country.setFlagUrl("http://example.com/flag.png");
        country.setCreatedAt(Instant.now());
        return countryRepository.saveAndFlush(country);
    }

    private PackagingService createAndSaveTestPackagingService(Country country) {
        PackagingService service = new PackagingService();
        service.setName("Test Service");
        service.setDescription("A test packaging service");
        service.setCountry(country);
        return packagingServiceRepository.saveAndFlush(service);
    }

    private ReportSet createAndSaveTestReportSet(PackagingService packagingService) {
        ReportSet reportSet = new ReportSet();
        reportSet.setName("Annual Test Report Set");
        reportSet.setMode(ReportSet.ReportSetMode.ON_PLATAFORM);
        reportSet.setType(ReportSet.ReportSetType.FRACTIONS);
        reportSet.setPackagingService(packagingService);
        return reportSetRepository.saveAndFlush(reportSet);
    }

    private ReportSetColumn createAndSaveTestReportSetColumn(String name,
                                                             ReportSet reportSet) {
        ReportSetColumn column = new ReportSetColumn();
        column.setName(name);
        column.setDescription("Description for " + name);
        column.setUnitType(ReportSetColumn.UnitType.KG);
        column.setCode(UUID.randomUUID().toString());
        column.setReportSet(reportSet);
        return reportSetColumnRepository.saveAndFlush(column);
    }

    private ReportSetFraction createAndSaveTestReportSetFraction(String name,
                                                                 ReportSet reportSet) {
        ReportSetFraction fraction = new ReportSetFraction();
        fraction.setName(name);
        fraction.setDescription("Description for " + name);
        fraction.setCode(UUID.randomUUID().toString());
        fraction.setReportSet(reportSet);
        return reportSetFractionRepository.saveAndFlush(fraction);
    }

    private ReportSetColumnFraction createAndSaveTestAssociation(ReportSetColumn column,
                                                                 ReportSetFraction fraction) {
        ReportSetColumnFraction association = new ReportSetColumnFraction();
        association.setReportSetColumn(column);
        association.setReportSetFraction(fraction);
        return reportSetColumnFractionRepository.saveAndFlush(association);
    }
}