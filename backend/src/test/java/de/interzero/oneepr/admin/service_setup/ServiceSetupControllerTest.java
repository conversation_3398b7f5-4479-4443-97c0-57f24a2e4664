package de.interzero.oneepr.admin.service_setup;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.country.Country;
import de.interzero.oneepr.admin.country.CountryRepository;
import de.interzero.oneepr.admin.country_price_list.CountryPriceList;
import de.interzero.oneepr.admin.country_price_list.CountryPriceListRepository;
import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.Criteria;
import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.CriteriaOption;
import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.CriteriaOptionRepository;
import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.CriteriaRepository;
import de.interzero.oneepr.admin.fraction_icon.Files;
import de.interzero.oneepr.admin.fraction_icon.FilesRepository;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingService;
import de.interzero.oneepr.admin.service_setup.service_detail.packaging_service.PackagingServiceRepository;
import de.interzero.oneepr.admin.price_list.PriceList;
import de.interzero.oneepr.admin.price_list.PriceListRepository;
import de.interzero.oneepr.admin.price_list.ReportSetPriceListItem;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSet;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSetPriceListItemRepository;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set.ReportSetRepository;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_columns.ReportSetColumn;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_columns.ReportSetColumnRepository;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions.ReportSetFraction;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_fractions.ReportSetFractionRepository;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list.ReportSetPriceList;
import de.interzero.oneepr.admin.service_setup.fraction_set.report_set_price_list.ReportSetPriceListRepository;
import de.interzero.oneepr.admin.representative_tier.RepresentativeTier;
import de.interzero.oneepr.admin.representative_tier.RepresentativeTierRepository;
import de.interzero.oneepr.admin.service_setup.dto.CalculateLicenseCostsDto;
import de.interzero.oneepr.admin.service_setup.dto.SubmitCommitmentDto;
import de.interzero.oneepr.admin.service_setup.report_set_frequency.ReportSetFrequency;
import de.interzero.oneepr.admin.service_setup.report_set_frequency.ReportSetFrequencyRepository;
import de.interzero.oneepr.admin.service_setup.required_information.RequiredInformation;
import de.interzero.oneepr.admin.service_setup.required_information.RequiredInformationRepository;
import de.interzero.oneepr.admin.service_setup.third_party_cost.ThirdPartyCost;
import de.interzero.oneepr.admin.service_setup.third_party_cost.ThirdPartyCostRepository;
import de.interzero.oneepr.common.string.Api;
import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.time.Instant;
import java.time.Year;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.UUID;

import static org.hamcrest.CoreMatchers.is;
import static org.hamcrest.Matchers.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for the {@link ServiceSetupController}.
 * <p>
 * These tests validate the service setup retrieval endpoint, ensuring that the
 * complex data graph is correctly assembled and serialized for a given country.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class ServiceSetupControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private CountryRepository countryRepository;

    @Autowired
    private PackagingServiceRepository packagingServiceRepository;

    @Autowired
    private ReportSetFrequencyRepository reportSetFrequencyRepository;

    @Autowired
    private EntityManager entityManager;

    @Autowired
    private ReportSetRepository reportSetRepository;

    @Autowired
    private ReportSetFractionRepository reportSetFractionRepository;

    @Autowired
    private ReportSetColumnRepository reportSetColumnRepository;

    @Autowired
    private RequiredInformationRepository requiredInformationRepository;

    @Autowired
    private CountryPriceListRepository countryPriceListRepository;

    @Autowired
    private ReportSetPriceListRepository reportSetPriceListRepository;

    @Autowired
    private RepresentativeTierRepository representativeTierRepository;

    @Autowired
    private ThirdPartyCostRepository thirdPartyCostRepository;

    @Autowired
    private PriceListRepository priceListRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private ReportSetPriceListItemRepository reportSetPriceListItemRepository;

    @Autowired
    private CriteriaOptionRepository criteriaOptionRepository;

    @Autowired
    private CriteriaRepository criteriaRepository;

    private PackagingService packagingService;

    private Country country;

    @Autowired
    private FilesRepository filesRepository;

    /**
     * Sets up a complete and valid hierarchy of entities required for the
     * service setup endpoint. This prevents ConstraintViolationExceptions and
     * ensures the test target has a realistic data graph to query.
     */
    @BeforeEach
    void setUp() throws JsonProcessingException {

        // 1. Delete dependent entities first.
        // The order among these dependents does not matter.
        countryPriceListRepository.deleteAllInBatch();
        criteriaRepository.deleteAllInBatch(); // <-- From Country entity
        thirdPartyCostRepository.deleteAllInBatch();
        packagingServiceRepository.deleteAllInBatch();
        representativeTierRepository.deleteAllInBatch();
        requiredInformationRepository.deleteAllInBatch();

        // Also include any other repositories from your test
        reportSetFractionRepository.deleteAllInBatch();
        reportSetFrequencyRepository.deleteAllInBatch();
        reportSetColumnRepository.deleteAllInBatch();
        priceListRepository.deleteAllInBatch();
        reportSetPriceListRepository.deleteAllInBatch();
        reportSetRepository.deleteAllInBatch();


        // 2. Delete the Country entity last.
        countryRepository.deleteAllInBatch();

        int currentYear = Year.now().getValue();

        // 1. Create Country
        country = new Country();
        country.setCode("DE");
        country.setName("Germany");
        country.setFlagUrl("https://example.com/de.png");
        country.setIsPublished(true);
        countryRepository.save(country);

        // 2. Create PackagingService
        this.packagingService = new PackagingService();
        packagingService.setCountry(country);
        packagingService.setName("Test Packaging Service");
        packagingService.setDescription("Service Description");
        packagingServiceRepository.save(packagingService);

        // 3. Create ReportSetFrequency
        ReportSetFrequency rsf = new ReportSetFrequency();
        rsf.setPackagingService(packagingService);
        rsf.setRhythm(ReportSetFrequency.Rhythm.MONTHLY);
        var frequencyData = Map.of("deadline", Map.of("day", 15), "open", Map.of("day", 1));
        rsf.setFrequency(objectMapper.writeValueAsString(frequencyData));
        reportSetFrequencyRepository.save(rsf);

        // 4. Create ReportSet
        ReportSet rs = new ReportSet();
        rs.setPackagingService(packagingService);
        rs.setName("Test Report Set");
        rs.setMode(ReportSet.ReportSetMode.ON_PLATAFORM);
        rs.setType(ReportSet.ReportSetType.FRACTIONS);
        reportSetRepository.save(rs);

        // 5. Create ReportSetColumn
        ReportSetColumn column = new ReportSetColumn();
        column.setReportSet(rs);
        column.setName("Weight");
        column.setDescription("Weight in KG");
        column.setUnitType(ReportSetColumn.UnitType.KG);
        column.setCode(UUID.randomUUID().toString());
        column.setParent(null);
        reportSetColumnRepository.save(column);

        // 6. Create ReportSetFraction
        ReportSetFraction parentFraction = new ReportSetFraction();
        parentFraction.setReportSet(rs);
        parentFraction.setName("Parent Fraction");
        parentFraction.setDescription("Parent fraction description");
        parentFraction.setCode(UUID.randomUUID().toString());
        parentFraction.setIsActive(true);
        parentFraction.setIcon("paper");
        reportSetFractionRepository.save(parentFraction);

        ReportSetFraction childFraction = new ReportSetFraction();
        childFraction.setReportSet(rs);
        childFraction.setParent(parentFraction); // <-- Sets the parent relationship
        childFraction.setName("Child Fraction");
        childFraction.setDescription("Child fraction description");
        childFraction.setCode(UUID.randomUUID().toString()); // Must be unique
        childFraction.setOrder(1);
        childFraction.setLevel(2);
        childFraction.setParentId(parentFraction.getId());
        childFraction.setIsActive(true);
        childFraction.setIcon("cardboard");
        reportSetFractionRepository.save(childFraction);
        ReportSetPriceList reportSetPriceList = new ReportSetPriceList();
        reportSetPriceList.setReportSet(rs);
        reportSetPriceList.setTitle("Annual Price List " + currentYear);
        reportSetPriceList.setStartDate(Instant.now());
        reportSetPriceList.setEndDate(Instant.now().plusSeconds(9999999));
        reportSetPriceList.setType(ReportSetPriceList.Type.FIXED_PRICE);
        reportSetPriceList.setLicenseYear(currentYear);
        reportSetPriceListRepository.save(reportSetPriceList);

        PriceList priceList = new PriceList();
        priceList.setType(PriceList.Type.DIRECT_LICENSE);
        priceList.setName("Country License " + currentYear);
        priceList.setDescription("desc");
        priceList.setStartDate(Instant.now());
        priceList.setEndDate(Instant.now().plusSeconds(9999999));
        priceList.setConditionType(PriceList.ConditionType.LICENSE_YEAR);
        priceList.setConditionTypeValue(String.valueOf(currentYear));
        priceListRepository.save(priceList);

        CountryPriceList cpl = new CountryPriceList();
        cpl.setCountry(country);
        cpl.setPriceList(priceList);
        cpl.setCreatedAt(Instant.now());
        cpl.setUpdatedAt(Instant.now());
        countryPriceListRepository.save(cpl);

        RequiredInformation ri = new RequiredInformation();
        ri.setCountry(country);
        ri.setName("Test Required Info");
        ri.setDescription("Some description");
        ri.setType(RequiredInformation.Type.TEXT);
        ri.setKind(RequiredInformation.Kind.COUNTRY_INFORMATION);
        requiredInformationRepository.save(ri);

        RepresentativeTier rt = new RepresentativeTier();
        rt.setCountry(country);
        rt.setName("Tier 1");
        rt.setPrice(1000);
        representativeTierRepository.save(rt);

        ThirdPartyCost oc = new ThirdPartyCost();
        oc.setCountry(country);
        oc.setName("Recycling Fee");
        oc.setPrice(50);
        thirdPartyCostRepository.save(oc);


    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findServiceSetup_whenCountryExists_returnsFullDto() throws Exception {
        mockMvc.perform(get(Api.SERVICE_SETUPS + "/{countryCode}", "DE").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id", is(country.getId())))
                .andExpect(jsonPath("$.code", is("DE")))
                .andExpect(jsonPath("$.packaging_services").isArray())
                .andExpect(jsonPath("$.packaging_services[0].id").isNumber())
                .andExpect(jsonPath("$.packaging_services[0].name", is("Test Packaging Service")))
                .andExpect(jsonPath("$.packaging_services[0].report_set_frequencies").isArray())
                .andExpect(jsonPath("$.packaging_services[0].report_set_frequencies[0].id").isNumber())
                .andExpect(jsonPath("$.packaging_services[0].report_set_frequencies[0].frequency").exists())
                .andExpect(jsonPath("$.packaging_services[0].report_set_frequencies[0].rhythm", is("MONTHLY")))
                .andExpect(jsonPath(
                        "$.packaging_services[0].report_set_frequencies[0].frequency.rhythm",
                        is("MONTHLY")))
                .andExpect(jsonPath(
                        "$.packaging_services[0].report_set_frequencies[0].frequency.frequency.deadline.day",
                        is(15)))
                .andExpect(jsonPath(
                        "$.packaging_services[0].report_set_frequencies[0].frequency.frequency.open.day",
                        is(1)))
                .andExpect(jsonPath("$.packaging_services[0].report_sets[0].fractions").isArray())
                .andExpect(jsonPath("$.packaging_services[0].report_sets[0].fractions[0].name", is("Parent Fraction")))
                .andExpect(jsonPath("$.packaging_services[0].report_sets[0].fractions[0].children").isArray())
                .andExpect(jsonPath(
                        "$.packaging_services[0].report_sets[0].fractions[0].children[0].name",
                        is("Child Fraction")))
                .andExpect(jsonPath("$.required_informations").isArray())
                .andExpect(jsonPath("$.required_informations[0].name", is("Test Required Info")));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findServiceSetup_whenCountryNotFound_returnsNotFound() throws Exception {
        mockMvc.perform(get(Api.SERVICE_SETUPS + "/{countryCode}", "XX").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isNotFound());
    }

    @Test
    void findServiceSetup_withoutAuth_returnsUnauthorized() throws Exception {
        mockMvc.perform(get(Api.SERVICE_SETUPS + "/{countryCode}", "DE").contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isUnauthorized());
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void getServiceSetupStatus_whenCountryIsValid_returnsCompletedTrue() throws Exception {
        mockMvc.perform(get(
                        Api.SERVICE_SETUPS + "/{countryCode}/status",
                        country.getCode()).contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.completed", is(true)))
                .andExpect(jsonPath("$.message").doesNotExist());
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findServiceSetupPackagingServices_whenCountryExists_returnsDtoWithCriteriaFlags() throws Exception {
        Criteria c1 = new Criteria();
        c1.setPackagingService(packagingService);
        c1.setCountry(country);
        c1.setMode(Criteria.Mode.COMMITMENT);
        c1.setType(Criteria.Type.PACKAGING_SERVICE);
        c1.setTitle("Commitment PS Criteria");
        criteriaRepository.saveAndFlush(c1);
        packagingService.addCriteria(c1);
        Criteria c2 = new Criteria();
        c2.setPackagingService(packagingService);
        c2.setCountry(country);
        c2.setMode(Criteria.Mode.COMMITMENT);
        c2.setType(Criteria.Type.REPORT_SET);
        c2.setTitle("Commitment RS Criteria");
        criteriaRepository.saveAndFlush(c2);
        packagingService.addCriteria(c2);
        Criteria c3 = new Criteria();
        c3.setPackagingService(packagingService);
        c3.setCountry(country);
        c3.setMode(Criteria.Mode.COMMITMENT);
        c3.setType(Criteria.Type.REPORT_FREQUENCY);
        c3.setTitle("Commitment RF Criteria");
        criteriaRepository.saveAndFlush(c3);
        packagingService.addCriteria(c3);
        // This one should be filtered out by the service logic
        Criteria c4 = new Criteria();
        c4.setPackagingService(packagingService);
        c4.setCountry(country);
        c4.setMode(Criteria.Mode.CALCULATOR); // Different mode
        c4.setType(Criteria.Type.PACKAGING_SERVICE);
        c4.setTitle("Calculator PS Criteria");
        criteriaRepository.saveAndFlush(c4);
        packagingService.addCriteria(c4);
        mockMvc.perform(get(
                        Api.SERVICE_SETUPS + "/{countryCode}/packaging-services",
                        country.getCode()).contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].name", is("Test Packaging Service")))
                .andExpect(jsonPath("$[0].criterias").isArray())
                .andExpect(jsonPath("$[0].criterias", hasSize(3))) // Verifies only COMMITMENT criteria are included
                .andExpect(jsonPath("$[0].has_criteria", is(true)))
                .andExpect(jsonPath("$[0].has_report_set_criteria", is(true)))
                .andExpect(jsonPath("$[0].has_report_frequency_criteria", is(true)));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findServiceSetupReportSets_whenCountryExists_returnsDtoWithCriteriaFlags() throws Exception {
        // 1. Create base entities
        Country testCountry = new Country();
        testCountry.setCode("FR");
        testCountry.setName("France");
        testCountry.setFlagUrl("url");
        countryRepository.save(testCountry);

        PackagingService psWithCriteria = new PackagingService();
        psWithCriteria.setCountry(testCountry);
        psWithCriteria.setName("Service A");
        psWithCriteria.setDescription("Service with specific criteria");

        PackagingService psWithoutCriteria = new PackagingService();
        psWithoutCriteria.setCountry(testCountry);
        psWithoutCriteria.setName("Service B");
        psWithoutCriteria.setDescription("Service without specific criteria");

        // 2. Create and associate criteria only with the first packaging service
        Criteria commitmentCriteria = new Criteria();
        commitmentCriteria.setCountry(testCountry);
        commitmentCriteria.setMode(Criteria.Mode.COMMITMENT);
        commitmentCriteria.setType(Criteria.Type.REPORT_SET);
        commitmentCriteria.setTitle("Report Set Commitment");
        psWithCriteria.addCriteria(commitmentCriteria);
        criteriaRepository.save(commitmentCriteria);
        packagingServiceRepository.saveAndFlush(psWithCriteria);
        packagingServiceRepository.saveAndFlush(psWithoutCriteria);
        // Note: criteria is saved via cascade from psWithCriteria

        // 3. Create a fully populated ReportSet (rs1) for psWithCriteria
        Files sheetFile = new Files();
        sheetFile.setId(UUID.randomUUID().toString());
        sheetFile.setName("template.xlsx");
        sheetFile.setOriginalName("template.xlsx");
        sheetFile.setExtension("xlsx");
        sheetFile.setSize("128KB");
        sheetFile.setCreatorType("ADMIN");
        sheetFile.setCreatedAt(Instant.now());
        sheetFile.setDocumentType("TEMPLATE");
        filesRepository.save(sheetFile);

        ReportSet rs1 = new ReportSet();
        rs1.setPackagingService(psWithCriteria);
        rs1.setName("Report Set for Service A");
        rs1.setMode(ReportSet.ReportSetMode.ON_PLATAFORM);
        rs1.setType(ReportSet.ReportSetType.FRACTIONS);
        rs1.setSheetFile(sheetFile); // Link the file
        rs1.setSheetFileDescription("Template for Service A");
        rs1.setCreatedAt(Instant.now());
        reportSetRepository.save(rs1);

        ReportSetColumn column = new ReportSetColumn();
        column.setName("Own Brand");
        column.setDescription("Products sold under your brand");
        column.setCode(UUID.randomUUID().toString());
        column.setUnitType(ReportSetColumn.UnitType.KG);
        rs1.addColumn(column); // Use helper to link
        reportSetColumnRepository.saveAndFlush(column);

        ReportSetFraction fraction = new ReportSetFraction();
        fraction.setName("Plastics");
        fraction.setDescription("All plastic materials");
        fraction.setCode(UUID.randomUUID().toString());
        fraction.setIcon("plastics");
        rs1.addFraction(fraction); // Use helper to link
        reportSetFractionRepository.saveAndFlush(fraction);

        // 4. Create a simple ReportSet (rs2) for psWithoutCriteria
        ReportSet rs2 = new ReportSet();
        rs2.setPackagingService(psWithoutCriteria);
        rs2.setName("Report Set for Service B");
        rs2.setMode(ReportSet.ReportSetMode.BY_EXCEL);
        rs2.setType(ReportSet.ReportSetType.CATEGORIES);
        reportSetRepository.save(rs2);

        // 5. Perform the request and assert the results
        mockMvc.perform(get(Api.SERVICE_SETUPS + "/{countryCode}/report-sets", testCountry.getCode())
                                .contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))

                // Assertions for the fully populated Report Set (Service A)
                .andExpect(jsonPath("$[?(@.name == 'Report Set for Service A')].has_criteria", contains(true)))
                .andExpect(jsonPath("$[?(@.name == 'Report Set for Service A')].sheet_file_id", contains(sheetFile.getId())))
                .andExpect(jsonPath("$[?(@.name == 'Report Set for Service A')].sheet_file_description", contains("Template for Service A")))
                .andExpect(jsonPath("$[?(@.name == 'Report Set for Service A')].columns", hasSize(1)))
                .andExpect(jsonPath("$[?(@.name == 'Report Set for Service A')].columns[0].name", contains("Own Brand")))
                .andExpect(jsonPath("$[?(@.name == 'Report Set for Service A')].fractions", hasSize(1)))
                .andExpect(jsonPath("$[?(@.name == 'Report Set for Service A')].fractions[0].name", contains("Plastics")))

                // Assertions for the simple Report Set (Service B)
                .andExpect(jsonPath("$[?(@.name == 'Report Set for Service B')].has_criteria", contains(false)))
                .andExpect(jsonPath("$[?(@.name == 'Report Set for Service B')][0].sheet_file_id",hasSize(0)))
                .andExpect(jsonPath("$[?(@.name == 'Report Set for Service B')][0].columns", hasSize(0)))
                .andExpect(jsonPath("$[?(@.name == 'Report Set for Service B')][0].fractions", hasSize(0)));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findServiceSetupReportSet_whenDataIsCreatedWithinTest_returnsFullDetailDto() throws Exception {
        // 1. ARRANGE: Create a complete data graph for a single, detailed ReportSet.
        Country testCountry = new Country();
        testCountry.setCode("UK");
        testCountry.setName("England");
        testCountry.setFlagUrl("url");
        countryRepository.saveAndFlush(testCountry);

        PackagingService testPs = new PackagingService();
        testPs.setCountry(testCountry);
        testPs.setName("Austrian Service");
        testPs.setDescription("A service in Austria");

        // Add a criteria to test the 'has_criteria' flag
        Criteria commitmentCriteria = new Criteria();
        commitmentCriteria.setCountry(testCountry);
        commitmentCriteria.setMode(Criteria.Mode.COMMITMENT);
        commitmentCriteria.setType(Criteria.Type.REPORT_SET);
        commitmentCriteria.setTitle("Report Set Commitment for AT");
        testPs.addCriteria(commitmentCriteria);
        packagingServiceRepository.saveAndFlush(testPs);

        ReportSet testRs = new ReportSet();
        testRs.setPackagingService(testPs);
        testRs.setName("Detailed Report Set");
        testRs.setMode(ReportSet.ReportSetMode.ON_PLATAFORM);
        testRs.setType(ReportSet.ReportSetType.FRACTIONS);
        reportSetRepository.saveAndFlush(testRs);

        // Create nested fractions to test the fraction hierarchy fetch
        ReportSetFraction parentFraction = new ReportSetFraction();
        parentFraction.setReportSet(testRs);
        parentFraction.setName("Parent Fraction");
        parentFraction.setOrder(1);
        parentFraction.setDescription("Fraction");
        parentFraction.setCode(UUID.randomUUID().toString());
        reportSetFractionRepository.saveAndFlush(parentFraction);

        ReportSetFraction childFraction = new ReportSetFraction();
        childFraction.setReportSet(testRs);
        childFraction.setParent(parentFraction);
        childFraction.setName("Child Fraction");
        childFraction.setOrder(1);
        childFraction.setDescription("childFraction");
        childFraction.setCode(UUID.randomUUID().toString());
        reportSetFractionRepository.saveAndFlush(childFraction);

        // Create columns to test the column fetch
        ReportSetColumn column = new ReportSetColumn();
        column.setReportSet(testRs);
        column.setName("Weight");
        column.setOrder(1);
        column.setCode(UUID.randomUUID().toString());
        column.setUnitType(ReportSetColumn.UnitType.KG);
        column.setDescription("column");
        reportSetColumnRepository.saveAndFlush(column);

        // Create price lists with items to test the price list fetch
        ReportSetPriceList priceList = new ReportSetPriceList();
        priceList.setReportSet(testRs);
        priceList.setTitle("2025 Prices");
        priceList.setLicenseYear(2025);
        priceList.setStartDate(Instant.now());
        priceList.setEndDate(Instant.now().plusSeconds(9999999));
        priceList.setType(ReportSetPriceList.Type.PRICE_PER_CATEGORY);
        priceList.setCreatedAt(Instant.now()); // Set for consistent ordering
        reportSetPriceListRepository.saveAndFlush(priceList);

        ReportSetPriceListItem item = new ReportSetPriceListItem();
        item.setPriceList(priceList);
        item.setReportSetFraction(parentFraction);
        priceList.addItem(item);
        item.setPrice(150);
        reportSetPriceListItemRepository.saveAndFlush(item);

        Files parentIconFile = new Files();
        parentIconFile.setId(UUID.randomUUID().toString());
        parentIconFile.setName("parent_icon.svg");
        parentIconFile.setOriginalName("parent_icon.svg");
        parentIconFile.setExtension("svg");
        parentIconFile.setSize("10KB");
        parentIconFile.setCreatorType("SYSTEM");
        parentIconFile.setDocumentType("ICON");
        parentIconFile.setCreatedAt(Instant.now());
        filesRepository.saveAndFlush(parentIconFile);

        mockMvc.perform(get(
                        Api.SERVICE_SETUPS + "/{countryCode}/report-sets/{reportSetId}",
                        testCountry.getCode(),
                        testRs.getId()).contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                // Assert has_criteria is false as we did not add criteria in this specific test
                .andExpect(jsonPath("$.has_criteria", is(false)))
                // Assert root-level properties
                .andExpect(jsonPath("$.id", is(testRs.getId())))
                // Assert the collections are at the root level and were fetched
                .andExpect(jsonPath("$.fractions", hasSize(1)))
                .andExpect(jsonPath("$.columns", hasSize(1)))
                .andExpect(jsonPath("$.price_lists", hasSize(1)))
                // Assert the nested price list item was fetched correctly
                .andExpect(jsonPath("$.price_lists[0].items", hasSize(1)))
                .andExpect(jsonPath("$.price_lists[0].items[0].price", is(150)));
    }


    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findServiceSetupReportFrequencies_whenDataExists_returnsFrequenciesWithCorrectCriteriaFlag() throws Exception {
        // 1. ARRANGE: Create a data graph to test the specific logic of the endpoint.
        Country de = new Country();
        de.setCode("UK");
        de.setName("England");
        de.setFlagUrl("url.jpg");
        countryRepository.saveAndFlush(de);

        // Create a packaging service that will have a related commitment criteria
        PackagingService psWithCriteria = new PackagingService();
        psWithCriteria.setCountry(de);
        psWithCriteria.setName("Service With Criteria");
        psWithCriteria.setDescription("A service in Germany");
        packagingServiceRepository.saveAndFlush(psWithCriteria);

        // Create a packaging service that will NOT have a related commitment criteria
        PackagingService psWithoutCriteria = new PackagingService();
        psWithoutCriteria.setCountry(de);
        psWithoutCriteria.setName("Service Without Criteria");
        psWithoutCriteria.setDescription("Another service in Germany");
        packagingServiceRepository.saveAndFlush(psWithoutCriteria);

        // Create the specific commitment criteria linked to the first service
        Criteria commitmentCriteria = new Criteria();
        commitmentCriteria.setCountry(de);
        commitmentCriteria.setPackagingService(psWithCriteria);
        commitmentCriteria.setMode(Criteria.Mode.COMMITMENT);
        commitmentCriteria.setType(Criteria.Type.REPORT_FREQUENCY);
        commitmentCriteria.setTitle("Report Frequency Commitment");
        criteriaRepository.saveAndFlush(commitmentCriteria);

        // Create a frequency for the service that WILL have a criteria
        ReportSetFrequency freq1 = new ReportSetFrequency();
        freq1.setPackagingService(psWithCriteria);
        freq1.setRhythm(ReportSetFrequency.Rhythm.MONTHLY);
        freq1.setFrequency("{\"day\": 15}");
        reportSetFrequencyRepository.saveAndFlush(freq1);

        // Create a frequency for the service that will NOT have a criteria
        ReportSetFrequency freq2 = new ReportSetFrequency();
        freq2.setPackagingService(psWithoutCriteria);
        freq2.setRhythm(ReportSetFrequency.Rhythm.ANNUALLY);
        freq2.setFrequency("{\"month\": 1}");
        reportSetFrequencyRepository.saveAndFlush(freq2);

        // 2. ACT & 3. ASSERT
        mockMvc.perform(get(Api.SERVICE_SETUPS + "/{countryCode}/report-frequencies", de.getCode()).contentType(
                        MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))

                // Assert properties for the frequency that HAS a criteria
                .andExpect(jsonPath("$[?(@.id == " + freq1.getId() + ")].has_criteria", contains(true)))
                .andExpect(jsonPath("$[?(@.id == " + freq1.getId() + ")].rhythm", contains("MONTHLY")))
                .andExpect(jsonPath("$[?(@.id == " + freq1.getId() + ")].frequency.day", contains(15)))
                .andExpect(jsonPath(
                        "$[?(@.id == " + freq1.getId() + ")].packaging_service.id",
                        contains(psWithCriteria.getId())))
                .andExpect(jsonPath(
                        "$[?(@.id == " + freq1.getId() + ")].packaging_service.criterias[0].id",
                        contains(commitmentCriteria.getId())))

                // Assert properties for the frequency that does NOT have a criteria
                .andExpect(jsonPath("$[?(@.id == " + freq2.getId() + ")].has_criteria", contains(false)))
                .andExpect(jsonPath("$[?(@.id == " + freq2.getId() + ")].rhythm", contains("ANNUALLY")))
                .andExpect(jsonPath(
                        "$[?(@.id == " + freq2.getId() + ")].packaging_service.id",
                        contains(psWithoutCriteria.getId())))
                .andExpect(jsonPath(
                        "$[?(@.id == " + freq2.getId() + ")].packaging_service.criterias",
                        contains(empty())));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findServiceSetupRepresentativeTiers_whenDataExists_returnsTiersWithCorrectCriteriaFlag() throws Exception {
        // 1. ARRANGE: Create a data graph for representative tiers and their specific criteria.
        Country de = new Country();
        de.setCode("UK");
        de.setName("England");
        de.setFlagUrl("url.jpg");
        countryRepository.saveAndFlush(de);

        // Create a country that should NOT appear in the results
        Country fr = new Country();
        fr.setCode("FR");
        fr.setName("France");
        fr.setFlagUrl("url.jpg");
        countryRepository.saveAndFlush(fr);

        // Create representative tiers for Germany.
        RepresentativeTier tier1 = new RepresentativeTier();
        tier1.setCountry(de);
        tier1.setName("DE Tier 1");
        tier1.setPrice(100);
        representativeTierRepository.saveAndFlush(tier1);

        RepresentativeTier tier2 = new RepresentativeTier();
        tier2.setCountry(de);
        tier2.setName("DE Tier 2");
        tier2.setPrice(200);
        representativeTierRepository.saveAndFlush(tier2);

        // Create a tier for France, which should be filtered out.
        RepresentativeTier tierFr = new RepresentativeTier();
        tierFr.setCountry(fr);
        tierFr.setName("FR Tier");
        tierFr.setPrice(150);
        representativeTierRepository.saveAndFlush(tierFr);

        // Create the specific commitment criteria for Germany for 'REPRESENTATIVE_TIER'.
        Criteria commitmentCriteria = new Criteria();
        commitmentCriteria.setCountry(de);
        commitmentCriteria.setMode(Criteria.Mode.COMMITMENT);
        commitmentCriteria.setType(Criteria.Type.REPRESENTATIVE_TIER);
        commitmentCriteria.setTitle("Rep Tier Commitment for DE");
        criteriaRepository.saveAndFlush(commitmentCriteria);

        // Create a non-matching criteria for Germany to ensure it's not included.
        Criteria nonMatchingCriteria = new Criteria();
        nonMatchingCriteria.setCountry(de);
        nonMatchingCriteria.setMode(Criteria.Mode.CALCULATOR); // Different mode
        nonMatchingCriteria.setType(Criteria.Type.REPRESENTATIVE_TIER);
        nonMatchingCriteria.setTitle("Non-matching criteria");
        criteriaRepository.saveAndFlush(nonMatchingCriteria);

        // 2. ACT & 3. ASSERT
        mockMvc.perform(get(Api.SERVICE_SETUPS + "/{countryCode}/representative-tiers", de.getCode()).contentType(
                        MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))

                // Assert that ALL returned tiers have has_criteria = true
                .andExpect(jsonPath("$[0].has_criteria", is(true)))
                .andExpect(jsonPath("$[1].has_criteria", is(true)))

                // Assert properties of the first tier
                .andExpect(jsonPath("$[0].id", is(tier1.getId())))
                .andExpect(jsonPath("$[0].name", is("DE Tier 1")))

                // Assert nested country object and its filtered criteria list
                .andExpect(jsonPath("$[0].country.id", is(de.getId())))
                .andExpect(jsonPath("$[0].country.criterias", hasSize(1)))
                .andExpect(jsonPath("$[0].country.criterias[0].id", is(commitmentCriteria.getId())));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findServiceSetupOtherCosts_whenDataExists_returnsCostsWithCorrectCriteriaFlag() throws Exception {
        // 1. ARRANGE: Create data graph for other costs and their specific criteria.
        Country de = new Country();
        de.setCode("UK");
        de.setName("England");
        de.setFlagUrl("url.jpg");
        countryRepository.saveAndFlush(de);

        // Create other costs for Germany.
        ThirdPartyCost cost1 = new ThirdPartyCost();
        cost1.setCountry(de);
        cost1.setName("Handling Fee");
        cost1.setPrice(50);
        thirdPartyCostRepository.saveAndFlush(cost1);

        // Create the specific commitment criteria for Germany for 'OTHER_COST'.
        Criteria commitmentCriteria = new Criteria();
        commitmentCriteria.setCountry(de);
        commitmentCriteria.setMode(Criteria.Mode.COMMITMENT);
        commitmentCriteria.setType(Criteria.Type.OTHER_COST);
        commitmentCriteria.setTitle("Other Cost Commitment for DE");
        criteriaRepository.saveAndFlush(commitmentCriteria);

        // 2. ACT & 3. ASSERT
        mockMvc.perform(get(
                        Api.SERVICE_SETUPS + "/{countryCode}/other-costs",
                        de.getCode()).contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))

                // Assert properties of the returned cost
                .andExpect(jsonPath("$[0].id", is(cost1.getId())))
                .andExpect(jsonPath("$[0].name", is("Handling Fee")))
                .andExpect(jsonPath("$[0].price", is(50)))
                .andExpect(jsonPath("$[0].has_criteria", is(true)))

                // Assert nested country object and its filtered criteria list
                .andExpect(jsonPath("$[0].country.id", is(de.getId())))
                .andExpect(jsonPath("$[0].country.criterias", hasSize(1)))
                .andExpect(jsonPath("$[0].country.criterias[0].id", is(commitmentCriteria.getId())));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findServiceSetupOtherCosts_whenNoCriteriaExist_returnsCostsWithFalseCriteriaFlag() throws Exception {
        // 1. ARRANGE: Create data graph with other costs but NO matching criteria.
        Country at = new Country();
        at.setCode("AT");
        at.setName("Austria");
        at.setFlagUrl("url.jpg");
        countryRepository.saveAndFlush(at);

        // Create an other cost for Austria.
        ThirdPartyCost cost1 = new ThirdPartyCost();
        cost1.setCountry(at);
        cost1.setName("Austrian Fee");
        cost1.setPrice(40);
        thirdPartyCostRepository.saveAndFlush(cost1);

        // 2. ACT & 3. ASSERT
        mockMvc.perform(get(
                        Api.SERVICE_SETUPS + "/{countryCode}/other-costs",
                        at.getCode()).contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))

                // Assert properties of the returned cost
                .andExpect(jsonPath("$[0].id", is(cost1.getId())))
                .andExpect(jsonPath("$[0].has_criteria", is(false)))

                // Assert nested country object has an empty criteria list
                .andExpect(jsonPath("$[0].country.id", is(at.getId())))
                .andExpect(jsonPath("$[0].country.criterias", empty()));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findServiceSetupPriceLists_whenDataExists_returnsOnlyActiveAndMatchingPriceLists() throws Exception {
        // 1. ARRANGE: Create a comprehensive data graph to test all filtering logic.
        Country de = new Country();
        de.setCode("UK");
        de.setName("England");
        de.setFlagUrl("url.jpg");
        countryRepository.saveAndFlush(de);

        Country fr = new Country();
        fr.setCode("FR");
        fr.setName("France");
        fr.setFlagUrl("url.jpg");
        countryRepository.saveAndFlush(fr);

        // Create an active price list. This is the one we expect to find.
        PriceList activePl = new PriceList();
        activePl.setName("Active DE Price List");
        activePl.setType(PriceList.Type.EU_LICENSE);
        activePl.setDescription("desc");
        activePl.setStartDate(Instant.now());
        activePl.setEndDate(Instant.now().plus(1, ChronoUnit.DAYS));
        activePl.setConditionType(PriceList.ConditionType.LICENSE_YEAR);
        activePl.setConditionTypeValue("2025");
        priceListRepository.saveAndFlush(activePl);

        // Create a soft-deleted price list.
        PriceList deletedPl = new PriceList();
        deletedPl.setName("Deleted DE Price List");
        deletedPl.setType(PriceList.Type.EU_LICENSE);
        deletedPl.setDescription("desc");
        deletedPl.setStartDate(Instant.now());
        deletedPl.setEndDate(Instant.now().plus(1, ChronoUnit.DAYS));
        deletedPl.setConditionType(PriceList.ConditionType.LICENSE_YEAR);
        deletedPl.setConditionTypeValue("2025");
        deletedPl.setDeletedAt(Instant.now());
        priceListRepository.saveAndFlush(deletedPl);

        // Link the active price list to Germany. This should be returned.
        CountryPriceList cplActive = new CountryPriceList();
        cplActive.setCountry(de);
        cplActive.setPriceList(activePl);
        cplActive.setCreatedAt(Instant.now());
        cplActive.setUpdatedAt(Instant.now());
        countryPriceListRepository.saveAndFlush(cplActive);

        // Link the active price list to France. This should NOT be returned for a DE query.
        CountryPriceList cplForFr = new CountryPriceList();
        cplForFr.setCountry(fr);
        cplForFr.setPriceList(activePl);
        cplForFr.setUpdatedAt(Instant.now());
        cplForFr.setCreatedAt(Instant.now());
        countryPriceListRepository.saveAndFlush(cplForFr);

        // Link the deleted price list to Germany. This should NOT be returned.
        CountryPriceList cplForDeletedPl = new CountryPriceList();
        cplForDeletedPl.setCountry(de);
        cplForDeletedPl.setPriceList(deletedPl);
        cplForDeletedPl.setCreatedAt(Instant.now());
        cplForDeletedPl.setUpdatedAt(Instant.now());
        countryPriceListRepository.saveAndFlush(cplForDeletedPl);

        // Link the active price list to Germany but soft-delete the link itself. This should NOT be returned.
        CountryPriceList deletedCplLink = new CountryPriceList();
        deletedCplLink.setCountry(de);
        deletedCplLink.setPriceList(activePl);
        deletedCplLink.setDeletedAt(Instant.now());
        deletedCplLink.setUpdatedAt(Instant.now());
        deletedCplLink.setCreatedAt(Instant.now());
        countryPriceListRepository.saveAndFlush(deletedCplLink);


        // 2. ACT & 3. ASSERT
        mockMvc.perform(get(
                        Api.SERVICE_SETUPS + "/{countryCode}/price-lists",
                        de.getCode()).contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1))) // Only the one truly active link should be found
                .andExpect(jsonPath("$[0].id", is(cplActive.getId())))
                .andExpect(jsonPath("$[0].country_id", is(de.getId())))
                .andExpect(jsonPath("$[0].price_list_id", is(activePl.getId())))
                .andExpect(jsonPath("$[0].price_list.name", is("Active DE Price List")));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findServiceSetupPriceLists_whenNoDataExists_returnsEmptyArray() throws Exception {
        // 1. ARRANGE: No data is created for the target country code.

        // 2. ACT & 3. ASSERT
        mockMvc.perform(get(
                        Api.SERVICE_SETUPS + "/{countryCode}/price-lists",
                        "XX").contentType(MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", empty()));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findServiceSetupRequiredInformations_whenDataExists_returnsCorrectlyShapedData() throws Exception {
        // 1. ARRANGE: Create a data graph to test file inclusion and criteria filtering.
        Country de = new Country();
        de.setCode("UK");
        de.setName("England");
        de.setFlagUrl("url.jpg");
        countryRepository.saveAndFlush(de);

        Files attachedFile = new Files();
        attachedFile.setId(UUID.randomUUID().toString());
        attachedFile.setName("registration.pdf");
        attachedFile.setExtension("pdf");
        attachedFile.setSize("123KB");
        attachedFile.setCreatorType("ADMIN");
        attachedFile.setDocumentType("DOCUMENT");
        attachedFile.setOriginalName("original_reg.pdf");
        attachedFile.setCreatedAt(Instant.now());
        filesRepository.saveAndFlush(attachedFile);

        // Create a required information that has both a file and a matching criteria.
        RequiredInformation info1 = new RequiredInformation();
        info1.setCountry(de);
        info1.setName("VAT Registration");
        info1.setType(RequiredInformation.Type.DOCUMENT);
        info1.setKind(RequiredInformation.Kind.COUNTRY_INFORMATION);
        info1.setDescription("desc1");
        info1.setFiles(attachedFile);
        requiredInformationRepository.saveAndFlush(info1);

        // Create a required information with no file and no criteria.
        RequiredInformation info2 = new RequiredInformation();
        info2.setCountry(de);
        info2.setName("Company Name");
        info2.setType(RequiredInformation.Type.TEXT);
        info2.setKind(RequiredInformation.Kind.GENERAL_INFORMATION);
        info2.setDescription("desc2");
        requiredInformationRepository.saveAndFlush(info2);

        // Create the specific commitment criteria linked to the first info.
        Criteria commitmentCriteria = new Criteria();
        commitmentCriteria.setCountry(de);
        commitmentCriteria.setRequiredInformation(info1);
        commitmentCriteria.setMode(Criteria.Mode.COMMITMENT);
        commitmentCriteria.setType(Criteria.Type.REQUIRED_INFORMATION);
        commitmentCriteria.setTitle("VAT Doc Commitment");
        criteriaRepository.saveAndFlush(commitmentCriteria);

        // 2. ACT & 3. ASSERT
        mockMvc.perform(get(Api.SERVICE_SETUPS + "/{countryCode}/required-informations", de.getCode()).contentType(
                        MediaType.APPLICATION_JSON))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(2)))

                // Assert properties for the info record that has criteria and a file
                .andExpect(jsonPath("$[?(@.id == " + info1.getId() + ")].has_criteria", contains(true)))
                .andExpect(jsonPath("$[?(@.id == " + info1.getId() + ")].criterias", hasSize(1)))
                .andExpect(jsonPath(
                        "$[?(@.id == " + info1.getId() + ")].criterias[0].id",
                        contains(commitmentCriteria.getId())))
                .andExpect(jsonPath("$[?(@.id == " + info1.getId() + ")].file.id", contains(attachedFile.getId())))
                .andExpect(jsonPath("$[?(@.id == " + info1.getId() + ")].file.name", contains("registration.pdf")))

                // Assert properties for the info record with no criteria and no file
                .andExpect(jsonPath("$[?(@.id == " + info2.getId() + ")].has_criteria", contains(false)))
                .andExpect(jsonPath("$[?(@.id == " + info2.getId() + ")].criterias", contains(empty())))
                .andExpect(jsonPath("$[?(@.id == " + info2.getId() + ")].file[0]").doesNotExist());
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findServiceSetupCriterias_whenFiltered_returnsMatchingCriteriasWithOptions() throws Exception {
        // 1. ARRANGE: Create a data graph to test the dynamic filtering.
        Country de = new Country();
        de.setCode("UK");
        de.setName("England");
        de.setFlagUrl("url.jpg");
        countryRepository.saveAndFlush(de);

        PackagingService ps = new PackagingService();
        ps.setCountry(de);
        ps.setName("Test Service");
        ps.setDescription("desc");
        packagingServiceRepository.saveAndFlush(ps);

        RequiredInformation ri = new RequiredInformation();
        ri.setCountry(de);
        ri.setName("Test Info");
        ri.setType(RequiredInformation.Type.TEXT);
        ri.setKind(RequiredInformation.Kind.COUNTRY_INFORMATION);
        ri.setDescription("desc");
        requiredInformationRepository.saveAndFlush(ri);

        // Criteria #1: Linked to Packaging Service, with an option.
        Criteria criteria1 = new Criteria();
        criteria1.setCountry(de);
        criteria1.setPackagingService(ps);
        criteria1.setType(Criteria.Type.PACKAGING_SERVICE);
        criteria1.setTitle("PS Criteria");
        criteria1.setMode(Criteria.Mode.COMMITMENT);
        criteriaRepository.saveAndFlush(criteria1);

        CriteriaOption option1 = new CriteriaOption();
        option1.setCriteria(criteria1);
        option1.setValue("Option A");
        option1.setOptionValue("A");
        option1.setUpdatedAt(Instant.now());
        option1.setCreatedAt(Instant.now());
        option1.setCriteria(criteria1);
        criteria1.getOptions().add(option1);
        criteriaOptionRepository.save(option1);
        criteriaRepository.saveAndFlush(criteria1);

        // Criteria #2: Linked to Required Information, no options.
        Criteria criteria2 = new Criteria();
        criteria2.setCountry(de);
        criteria2.setRequiredInformation(ri);
        criteria2.setType(Criteria.Type.REQUIRED_INFORMATION);
        criteria2.setTitle("RI Criteria");
        criteria2.setMode(Criteria.Mode.CALCULATOR);
        criteriaRepository.saveAndFlush(criteria2);

        // Criteria #3: Different type, should be filtered out.
        Criteria criteria3 = new Criteria();
        criteria3.setCountry(de);
        criteria3.setPackagingService(ps);
        criteria3.setType(Criteria.Type.REPORT_SET);
        criteria3.setTitle("Should be filtered");
        criteria3.setMode(Criteria.Mode.CALCULATOR);
        criteriaRepository.saveAndFlush(criteria3);

        // 2. ACT & 3. ASSERT - Scenario 1: Filter by packaging_service_id
        mockMvc.perform(get(Api.SERVICE_SETUPS + "/{countryCode}/criterias", de.getCode()).param(
                                "type",
                                "PACKAGING_SERVICE")
                                .param("packaging_service_id", ps.getId().toString()))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(criteria1.getId())))
                .andExpect(jsonPath("$[0].title", is("PS Criteria")))
                .andExpect(jsonPath("$[0].options", hasSize(1)))
                .andExpect(jsonPath("$[0].options[0].value", is("Option A")));

        // 2. ACT & 3. ASSERT - Scenario 2: Filter by required_information_id
        mockMvc.perform(get(Api.SERVICE_SETUPS + "/{countryCode}/criterias", de.getCode()).param(
                                "type",
                                "REQUIRED_INFORMATION")
                                .param("required_information_id", ri.getId().toString()))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(criteria2.getId())))
                .andExpect(jsonPath("$[0].title", is("RI Criteria")))
                .andExpect(jsonPath("$[0].options", empty()));

        // 2. ACT & 3. ASSERT - Scenario 3: No results for wrong type
        mockMvc.perform(get(Api.SERVICE_SETUPS + "/{countryCode}/criterias", de.getCode()).param(
                        "type",
                        "REPORT_FREQUENCY")) // A type with no criteria
                .andDo(print()).andExpect(status().isOk()).andExpect(jsonPath("$", empty()));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findServiceSetupCommitment_whenDataExists_returnsCommitmentCriteria() throws Exception {
        // 1. ARRANGE
        Country de = new Country();
        de.setCode("UK");
        de.setName("England");
        de.setFlagUrl("url.jpg");
        countryRepository.saveAndFlush(de);

        // Expected COMMITMENT criteria
        Criteria commitmentCriteria = new Criteria();
        commitmentCriteria.setCountry(de);
        commitmentCriteria.setMode(Criteria.Mode.COMMITMENT);
        commitmentCriteria.setType(Criteria.Type.OTHER_COST);
        commitmentCriteria.setTitle("Commitment Test");

        CriteriaOption option = new CriteriaOption();
        option.setValue("Yes");
        option.setOptionValue("YES");
        commitmentCriteria.getOptions().add(option);
        option.setCriteria(commitmentCriteria);

        criteriaRepository.saveAndFlush(commitmentCriteria);

        // Non-matching CALCULATOR criteria
        Criteria calculatorCriteria = new Criteria();
        calculatorCriteria.setCountry(de);
        calculatorCriteria.setMode(Criteria.Mode.CALCULATOR);
        calculatorCriteria.setType(Criteria.Type.OTHER_COST);
        calculatorCriteria.setTitle("Calculator Test");
        criteriaRepository.saveAndFlush(calculatorCriteria);

        // 2. ACT & 3. ASSERT
        mockMvc.perform(get(Api.SERVICE_SETUPS + "/{countryCode}/commitment", de.getCode()))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].id", is(commitmentCriteria.getId())))
                .andExpect(jsonPath("$[0].title", is("Commitment Test")))
                .andExpect(jsonPath("$[0].mode", is("COMMITMENT")))
                .andExpect(jsonPath("$[0].options", hasSize(1)));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void findServiceSetupCommitment_whenNoDataExists_throwsNotFound() throws Exception {
        Country de = new Country();
        de.setCode("UK");
        de.setName("England");
        de.setFlagUrl("url.jpg");
        countryRepository.saveAndFlush(de);

        // 2. ACT & 3. ASSERT
        mockMvc.perform(get(Api.SERVICE_SETUPS + "/{countryCode}/commitment", de.getCode()))
                .andDo(print())
                .andExpect(status().isNotFound())
                .andExpect(result -> Assertions.assertInstanceOf(
                        ResponseStatusException.class,
                        result.getResolvedException()))
                .andExpect(result -> Assertions.assertEquals(
                        "404 NOT_FOUND \"Commitment is empty\"",
                        Objects.requireNonNull(result.getResolvedException()).getMessage()));
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void submitServiceSetupCommitment_whenValidDataIsProvided_returnsCorrectSetup() throws Exception {
        // 1. ARRANGE

        // Setup Country
        Country de = new Country();
        de.setCode("UK");
        de.setName("England");
        de.setFlagUrl("url.jpg");
        de.setAuthorizeRepresentativeObligated(false);
        de.setOtherCostsObligated(true);
        countryRepository.saveAndFlush(de);

        // Setup related entities
        PackagingService ps1 = new PackagingService();
        ps1.setCountry(de);
        ps1.setName("Service 1");
        ps1.setDescription("Description 1");
        de.getPackagingServices().add(ps1);
        countryRepository.saveAndFlush(de);
        packagingServiceRepository.saveAndFlush(ps1);

        ReportSet rs1 = new ReportSet();
        rs1.setPackagingService(ps1);
        rs1.setName("Report Set 1");
        rs1.setMode(ReportSet.ReportSetMode.ON_PLATAFORM);
        rs1.setType(ReportSet.ReportSetType.FRACTIONS);
        reportSetRepository.saveAndFlush(rs1);

        ReportSetFrequency rf1 = new ReportSetFrequency();
        rf1.setPackagingService(ps1);
        rf1.setRhythm(ReportSetFrequency.Rhythm.ANNUALLY);
        rf1.setFrequency("{}");
        reportSetFrequencyRepository.saveAndFlush(rf1);

        PriceList pl1 = new PriceList();
        pl1.setName("2025 Price List");
        pl1.setType(PriceList.Type.DIRECT_LICENSE);
        pl1.setDescription("desc");
        pl1.setStartDate(Instant.now());
        pl1.setEndDate(Instant.now().plusSeconds(99999999));
        pl1.setConditionType(PriceList.ConditionType.LICENSE_YEAR);
        pl1.setConditionTypeValue("2025");
        priceListRepository.saveAndFlush(pl1);

        CountryPriceList cpl1 = new CountryPriceList();
        cpl1.setCountry(de);
        cpl1.setPriceList(pl1);
        cpl1.setCreatedAt(Instant.now());
        cpl1.setUpdatedAt(Instant.now());
        de.getCountryPriceLists().add(cpl1);
        countryPriceListRepository.saveAndFlush(cpl1);
        countryRepository.saveAndFlush(de);

        RepresentativeTier rt1 = new RepresentativeTier();
        rt1.setCountry(de);
        rt1.setName("Gold Tier");
        rt1.setPrice(1000);
        rt1.setCreatedAt(Instant.now());
        rt1.setUpdatedAt(Instant.now());
        representativeTierRepository.saveAndFlush(rt1);

        ThirdPartyCost oc1 = new ThirdPartyCost();
        oc1.setCountry(de);
        oc1.setName("Setup Fee");
        oc1.setPrice(50);
        thirdPartyCostRepository.saveAndFlush(oc1);

        RequiredInformation ri1 = new RequiredInformation();
        ri1.setCountry(de);
        ri1.setName("Initial Info");
        ri1.setType(RequiredInformation.Type.TEXT);
        ri1.setDescription("desc");
        requiredInformationRepository.saveAndFlush(ri1);

        de.getRequiredInformations().add(ri1);
        countryRepository.saveAndFlush(de);
        final String countryCode = de.getCode();
        entityManager.clear();

        // Setup Criteria and Options
        Criteria cPs = createCriteria(de, Criteria.Type.PACKAGING_SERVICE, "Is packaging service obliged?", ps1);
        createOption(cPs, "OBLIGED", "OBLIGED");

        Criteria cRs = createCriteria(de, Criteria.Type.REPORT_SET, "Select report set", ps1);
        createOption(cRs, String.valueOf(rs1.getId()), "Report Set 1");

        Criteria cRf = createCriteria(de, Criteria.Type.REPORT_FREQUENCY, "Select frequency", ps1);
        createOption(cRf, String.valueOf(rf1.getId()), "Annually");

        Criteria cAr = createCriteria(de, Criteria.Type.AUTHORIZE_REPRESENTATIVE, "Authorize Rep?", null);
        createOption(cAr, "OBLIGED", "OBLIGED");

        Criteria cRt = createCriteria(de, Criteria.Type.REPRESENTATIVE_TIER, "Select tier", null);
        createOption(cRt, String.valueOf(rt1.getId()), "Gold Tier");

        Criteria cOc = createCriteria(de, Criteria.Type.OTHER_COST, "Add other cost?", null);
        createOption(cOc, String.valueOf(oc1.getId()), "Setup Fee");

        // Setup request DTO
        SubmitCommitmentDto requestDto = new SubmitCommitmentDto();
        requestDto.setYear(2025);
        requestDto.setCommitment(List.of(
                new SubmitCommitmentDto.CommitmentAnswerDto(cPs.getId(), "OBLIGED"),
                new SubmitCommitmentDto.CommitmentAnswerDto(cRs.getId(), String.valueOf(rs1.getId())),
                new SubmitCommitmentDto.CommitmentAnswerDto(cRf.getId(), String.valueOf(rf1.getId())),
                new SubmitCommitmentDto.CommitmentAnswerDto(cAr.getId(), "OBLIGED"),
                new SubmitCommitmentDto.CommitmentAnswerDto(cRt.getId(), String.valueOf(rt1.getId())),
                new SubmitCommitmentDto.CommitmentAnswerDto(cOc.getId(), String.valueOf(oc1.getId()))));

        // 2. ACT & 3. ASSERT
        mockMvc.perform(post(
                        Api.SERVICE_SETUPS + "/{countryCode}/commitment",
                        countryCode).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(requestDto)))
                .andDo(print())
                .andExpect(status().isOk())
                // Top-level structure
                .andExpect(jsonPath("$.year", is("2025")))
                .andExpect(jsonPath("$.setup").exists())
                .andExpect(jsonPath("$.commitment", hasSize(6)))
                // Setup Details
                .andExpect(jsonPath("$.setup.country.code", is(de.getCode())))
                .andExpect(jsonPath("$.setup.price_list.id", is(pl1.getId())))
                // Logic based on answers
                .andExpect(jsonPath("$.setup.packaging_services[0].id", is(ps1.getId())))
                .andExpect(jsonPath("$.setup.packaging_services[0].obliged", is(true)))
                .andExpect(jsonPath("$.setup.packaging_services[0].report_set.id", is(rs1.getId())))
                .andExpect(jsonPath("$.setup.packaging_services[0].report_set_frequency.id", is(rf1.getId())))
                .andExpect(jsonPath("$.setup.authorize_representative_obligated", is(true)))
                .andExpect(jsonPath("$.setup.representative_tier.id", is(rt1.getId())))
                .andExpect(jsonPath("$.setup.other_costs", hasSize(1)))
                .andExpect(jsonPath("$.setup.other_costs[0].id", is(oc1.getId())))
                .andExpect(jsonPath("$.setup.required_informations", hasSize(1)))
                .andExpect(jsonPath("$.setup.required_informations[0].id", is(ri1.getId())))
                // Commitment echo
                .andExpect(jsonPath("$.commitment[0].answer", is("OBLIGED")));
    }

    private Criteria createCriteria(Country country,
                                    Criteria.Type type,
                                    String title,
                                    PackagingService ps) {
        Criteria criteria = new Criteria();
        criteria.setCountry(country);
        criteria.setMode(Criteria.Mode.COMMITMENT);
        criteria.setType(type);
        criteria.setTitle(title);
        if (ps != null) {
            criteria.setPackagingService(ps);
        }
        criteria.setRequiredInformation(null);
        return criteriaRepository.saveAndFlush(criteria);
    }

    private void createOption(Criteria criteria,
                              String value,
                              String optionValue) {
        CriteriaOption option = new CriteriaOption();
        option.setValue(value);
        option.setCreatedAt(Instant.now());
        option.setUpdatedAt(Instant.now());
        option.setOptionValue(optionValue);
        option.setCriteria(criteria);

        criteriaOptionRepository.saveAndFlush(option);

        criteria.getOptions().add(option);
        criteriaRepository.saveAndFlush(criteria);
    }

    @Test
    @WithMockUser(roles = {"ADMIN"})
    void calculateLicenseCosts_whenValidData_returnsCorrectCalculation() throws Exception {
        // 1. ARRANGE
        // Setup Country, Packaging Service, Report Set
        Country de = new Country();
        de.setCode("UK");
        de.setName("England");
        de.setFlagUrl("url.jpg");
        countryRepository.saveAndFlush(de);

        PackagingService ps = new PackagingService();
        ps.setCountry(de);
        ps.setName("PS");
        ps.setDescription("Des");
        packagingServiceRepository.saveAndFlush(ps);

        ReportSet rs = new ReportSet();
        rs.setPackagingService(ps);
        rs.setName("RS");
        rs.setMode(ReportSet.ReportSetMode.ON_PLATAFORM);
        rs.setType(ReportSet.ReportSetType.CATEGORIES);
        reportSetRepository.saveAndFlush(rs);

        // Setup Fractions for the Report Set
        ReportSetFraction fraction1 = new ReportSetFraction();
        fraction1.setReportSet(rs);
        fraction1.setCode("F001");
        fraction1.setName("Fraction 1");
        fraction1.setDescription("d");
        fraction1.setIcon("i");
        reportSetFractionRepository.saveAndFlush(fraction1);

        ReportSetFraction fraction2 = new ReportSetFraction();
        fraction2.setReportSet(rs);
        fraction2.setCode("F002");
        fraction2.setName("Fraction 2");
        fraction2.setDescription("d");
        fraction2.setIcon("i");
        reportSetFractionRepository.saveAndFlush(fraction2);

        // Setup Price List for the Report Set and Year
        ReportSetPriceList priceList = new ReportSetPriceList();
        priceList.setReportSet(rs);
        priceList.setLicenseYear(2025);
        priceList.setType(ReportSetPriceList.Type.PRICE_PER_VOLUME_MINIMUM_FEE);
        priceList.setMinimumFee(500); // 5 EUR
        priceList.setTitle("PL 2025");
        priceList.setStartDate(Instant.now());
        priceList.setEndDate(Instant.now().plusSeconds(99999));
        reportSetPriceListRepository.saveAndFlush(priceList);

        // Setup Price List Items
        ReportSetPriceListItem item1 = new ReportSetPriceListItem();
        item1.setPriceList(priceList);
        item1.setReportSetFraction(fraction1);
        item1.setPrice(100); // 10 EUR per kg
        reportSetPriceListItemRepository.saveAndFlush(item1);

        ReportSetPriceListItem item2 = new ReportSetPriceListItem();
        item2.setPriceList(priceList);
        item2.setReportSetFraction(fraction2);
        item2.setPrice(200); // 20 EUR per kg
        reportSetPriceListItemRepository.saveAndFlush(item2);

        // Setup Calculator Criteria
        ThirdPartyCost thirdPartyCost = new ThirdPartyCost();
        thirdPartyCost.setCountry(de);
        thirdPartyCost.setName("Calculator Fee");
        thirdPartyCost.setPrice(150);
        thirdPartyCostRepository.saveAndFlush(thirdPartyCost);

        Criteria calcCriteria = new Criteria();
        calcCriteria.setCountry(de);
        calcCriteria.setMode(Criteria.Mode.CALCULATOR);
        calcCriteria.setType(Criteria.Type.OTHER_COST);
        calcCriteria.setCalculatorType(Criteria.CalculatorType.TOTAL_IN_KG);
        criteriaRepository.saveAndFlush(calcCriteria);

        createOptionForCalculator(calcCriteria, String.valueOf(thirdPartyCost.getId()));

        // Setup request DTO
        CalculateLicenseCostsDto.FractionDto fractionDto1 = new CalculateLicenseCostsDto.FractionDto();
        fractionDto1.setCode("F001");
        fractionDto1.setWeight(100); // 0.1 kg

        CalculateLicenseCostsDto.FractionDto fractionDto2 = new CalculateLicenseCostsDto.FractionDto();
        fractionDto2.setCode("F002");
        fractionDto2.setWeight(200); // 0.2 kg

        CalculateLicenseCostsDto.ReportSetDto reportSetDto = new CalculateLicenseCostsDto.ReportSetDto();
        reportSetDto.setId(rs.getId());
        reportSetDto.setFractions(List.of(fractionDto1, fractionDto2));

        CalculateLicenseCostsDto requestDto = new CalculateLicenseCostsDto();
        requestDto.setYear(2025);
        requestDto.setReportSets(List.of(reportSetDto));

        // Expected cost calculation:
        // Item 1: (100 / 10) * (100 / 1000) = 10 * 0.1 = 1.0 EUR
        // Item 2: (200 / 10) * (200 / 1000) = 20 * 0.2 = 4.0 EUR
        // Total fraction costs: 1.0 + 4.0 = 5.0 EUR
        // Minimum fee is 500 cents = 5.0 EUR.
        // Since costs (5.0) are not less than min fee (5.0), result is 5.0 EUR.
        // Total weight is 300g = 0.3 kg, which is between 0.1 and 1.0, so other_cost is triggered.

        // 2. ACT & 3. ASSERT
        mockMvc.perform(post(
                        Api.SERVICE_SETUPS + "/{countryCode}/calculator",
                        de.getCode()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(requestDto)))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.license_costs", is(5.0)))
                .andExpect(jsonPath("$.authorize_representative_obligated", is(false)))
                .andExpect(jsonPath("$.representive_tier").doesNotExist())
                .andExpect(jsonPath("$.required_informations", hasSize(0)))
                .andExpect(jsonPath("$.other_costs", hasSize(1)))
                .andExpect(jsonPath("$.other_costs[0].id", is(thirdPartyCost.getId())))
                .andExpect(jsonPath("$.other_costs[0].name", is("Calculator Fee")));
    }

    private void createOptionForCalculator(Criteria criteria,
                                           String value) {
        CriteriaOption option = new CriteriaOption();
        option.setValue(value);
        option.setOptionValue("100");
        option.setOptionToValue("500");

        option.setUpdatedAt(Instant.now());
        option.setCreatedAt(Instant.now());
        option.setCriteria(criteria);
        criteria.getOptions().add(option);
        criteria.setCreatedAt(Instant.now());
        criteria.setUpdatedAt(Instant.now());
        criteriaOptionRepository.saveAndFlush(option);
        criteriaRepository.saveAndFlush(criteria);
    }
}