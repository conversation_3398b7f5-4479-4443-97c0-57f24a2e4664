package de.interzero.oneepr.admin.country;

import com.fasterxml.jackson.databind.ObjectMapper;
import de.interzero.oneepr.admin.country.dto.CreateCountryDto;
import de.interzero.oneepr.admin.country.dto.UpdateCountryDto;
import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.Criteria;
import de.interzero.oneepr.admin.service_setup.obligation_check.criteria.CriteriaRepository;
import de.interzero.oneepr.admin.service_setup.required_information.RequiredInformation;
import de.interzero.oneepr.admin.service_setup.required_information.RequiredInformationRepository;
import de.interzero.oneepr.common.string.Api;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.customer.CustomerService;
import de.interzero.oneepr.customer.customer.dto.GroupByCountryDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Map;

import static org.hamcrest.Matchers.hasSize;
import static org.hamcrest.Matchers.is;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for the {@link CountryController}.
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class CountryControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private CountryRepository countryRepository;

    @Autowired
    private CriteriaRepository criteriaRepository;

    @Autowired
    private RequiredInformationRepository requiredInformationRepository;

    @MockBean
    private CustomerService customerService;

    private Country testCountry;

    @BeforeEach
    void setUp() {
        criteriaRepository.deleteAll();
        requiredInformationRepository.deleteAll();
        countryRepository.deleteAll();

        testCountry = createAndSaveTestCountry("Germany", "DE", true);
    }

    /**
     * Test for {@link CountryController#create(CreateCountryDto)}.
     * Verifies successful creation of a new country and a 201 Created response.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_shouldCreateNewCountry() throws Exception {
        CreateCountryDto createDto = new CreateCountryDto();
        createDto.setName("France");
        createDto.setCode("FR");
        createDto.setFlagUrl("http://example.com/fr.png");

        mockMvc.perform(post(Api.COUNTRIES).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andExpect(header().exists("Location"))
                .andExpect(jsonPath("$.name", is("France")));
    }

    /**
     * Test for {@link CountryController#findAll()}.
     * Verifies that the endpoint is public and returns a list of all countries.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_asAuthenticatedUser_shouldSucceed() throws Exception {
        mockMvc.perform(get(Api.COUNTRIES)).andExpect(status().isOk()).andExpect(jsonPath("$", hasSize(1)));
    }

    /**
     * Test for {@link CountryController#overview(String)}.
     * Verifies successful aggregation of country and customer data by mocking the CustomerService.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void overview_shouldReturnAggregatedData() throws Exception {
        // --- Mocking Setup ---
        GroupByCountryDto germanyInfo = new GroupByCountryDto();
        germanyInfo.setLicensedCustomerCount(10);
        germanyInfo.setUnlicensedCustomerCount(2);
        Map<String, GroupByCountryDto> mockApiResponse = Collections.singletonMap("DE", germanyInfo);

        when(customerService.groupByCountry()).thenReturn(mockApiResponse);

        // --- Test Execution & Assertions ---
        mockMvc.perform(get(Api.COUNTRIES + "/overview"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].name", is("Germany")))
                .andExpect(jsonPath("$[0].licensed_customer_count", is(10)))
                .andExpect(jsonPath("$[0].unlicensed_customer_count", is(2)))
                .andExpect(jsonPath("$[0].tasks", is(0)));
    }

    /**
     * Test for {@link CountryController#published()}.
     * Verifies that the endpoint is public and returns only published countries.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void published_asAuthenticatedUser_shouldSucceed() throws Exception {
        createAndSaveTestCountry("Spain", "ES", false);

        mockMvc.perform(get(Api.COUNTRIES + "/published"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$", hasSize(1)))
                .andExpect(jsonPath("$[0].name", is("Germany")));
    }

    /**
     * Test for {@link CountryController#findOne(String)}.
     * Verifies retrieval of a single country by its ID.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOne_shouldReturnCountryById() throws Exception {
        mockMvc.perform(get(Api.COUNTRIES + "/{id}", testCountry.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testCountry.getId())))
                .andExpect(jsonPath("$.name", is("Germany")))
                .andExpect(jsonPath("$.code", is("DE")));
    }

    /**
     * Test for {@link CountryController#findOneByCode(String)}.
     * Verifies retrieval of a country by its code, including derived criteria status flags.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOneByCode_shouldReturnCountryWithCriteriaStatus() throws Exception {
        RequiredInformation info = createAndSaveTestRequiredInformation(testCountry);
        createAndSaveTestCriteria(info);

        mockMvc.perform(get(Api.COUNTRIES + "/code/{code}", testCountry.getCode()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id", is(testCountry.getId())))
                .andExpect(jsonPath("$.has_other_cost_criteria", is(true)))
                .andExpect(jsonPath("$.has_representative_tier_criteria", is(false)));
    }

    /**
     * Test for {@link CountryController#countryOverview(String)}.
     * Verifies retrieval of aggregated overview data for a single country by its code.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void countryOverview_shouldReturnAggregatedDataByCode() throws Exception {
        // --- Mocking Setup ---
        GroupByCountryDto germanyInfo = new GroupByCountryDto();
        germanyInfo.setLicensedCustomerCount(50);
        germanyInfo.setUnlicensedCustomerCount(15);
        Map<String, GroupByCountryDto> mockApiResponse = Collections.singletonMap("DE", germanyInfo);

        when(customerService.groupByCountry()).thenReturn(mockApiResponse);

        // --- Test Execution & Assertions ---
        mockMvc.perform(get(Api.COUNTRIES + "/code/{code}/overview", testCountry.getCode()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.name", is("Germany")))
                .andExpect(jsonPath("$.licensed_customer_count", is(50)))
                .andExpect(jsonPath("$.unlicensed_customer_count", is(15)))
                .andExpect(jsonPath("$.tasks", is(0)))
                .andExpect(jsonPath("$.followers", hasSize(0)));
    }

    /**
     * Test for {@link CountryController#update(String, UpdateCountryDto)}.
     * Verifies successful partial update of an existing country.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_shouldModifyCountry() throws Exception {
        UpdateCountryDto updateDto = new UpdateCountryDto();
        updateDto.setIsPublished(false);

        mockMvc.perform(put(Api.COUNTRIES + "/{id}", testCountry.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.is_published", is(false)));
    }

    /**
     * Test for {@link CountryController#remove(String)}.
     * Verifies successful hard deletion of a country and a 204 No Content response.
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void remove_shouldHardDeleteCountry() throws Exception {
        mockMvc.perform(delete(Api.COUNTRIES + "/{id}", testCountry.getId())).andExpect(status().isNoContent());

        assertFalse(countryRepository.existsById(testCountry.getId()));
    }

    // --- Helper Methods for Test Setup ---

    private Country createAndSaveTestCountry(String name,
                                             String code,
                                             boolean isPublished) {
        Country country = countryRepository.findByCode(code).orElse(new Country());
        country.setName(name);
        country.setCode(code);
        country.setFlagUrl("http://example.com/" + code.toLowerCase() + ".png");
        country.setIsPublished(isPublished);
        country.setLicenseRequired(true);
        return countryRepository.saveAndFlush(country);
    }

    private RequiredInformation createAndSaveTestRequiredInformation(Country country) {
        RequiredInformation info = new RequiredInformation();
        info.setName("Test Info for " + country.getName());
        info.setDescription("Test Desc");
        info.setType(RequiredInformation.Type.DOCUMENT);
        info.setCountry(country);
        return requiredInformationRepository.saveAndFlush(info);
    }

    private void createAndSaveTestCriteria(RequiredInformation info) {
        Criteria criteria = new Criteria();
        criteria.setTitle("Test Criteria");
        criteria.setMode(Criteria.Mode.CALCULATOR);
        criteria.setType(Criteria.Type.OTHER_COST);
        criteria.setCountry(info.getCountry());
        criteria.setRequiredInformation(info);
        criteriaRepository.saveAndFlush(criteria);
    }
}