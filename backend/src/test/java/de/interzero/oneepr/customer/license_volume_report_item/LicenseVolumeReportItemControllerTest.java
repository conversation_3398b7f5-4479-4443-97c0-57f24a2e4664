package de.interzero.oneepr.customer.license_volume_report_item;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import de.interzero.oneepr.common.string.TestRole;
import de.interzero.oneepr.customer.contract.Contract;
import de.interzero.oneepr.customer.contract.ContractRepository;
import de.interzero.oneepr.customer.customer.Customer;
import de.interzero.oneepr.customer.customer.CustomerRepository;
import de.interzero.oneepr.customer.entity.LicenseVolumeReportItem;
import de.interzero.oneepr.customer.license.License;
import de.interzero.oneepr.customer.license.LicenseRepository;
import de.interzero.oneepr.customer.license_packaging_service.LicensePackagingService;
import de.interzero.oneepr.customer.license_packaging_service.LicensePackagingServiceRepository;
import de.interzero.oneepr.customer.license_volume_report.LicenseVolumeReport;
import de.interzero.oneepr.customer.license_volume_report.LicenseVolumeReportRepository;
import de.interzero.oneepr.customer.license_volume_report_item.dto.CreateLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.DeclineLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.UpdateLicenseVolumeReportItemDto;
import de.interzero.oneepr.customer.license_volume_report_item.dto.UpdateLicenseVolumeReportItemValueDto;
import de.interzero.oneepr.customer.reason.Reason;
import de.interzero.oneepr.customer.reason.ReasonRepository;
import de.interzero.oneepr.customer.shared.auth.AuthenticatedUser;
import de.interzero.oneepr.customer.shared.auth.Role;
import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.*;

import static com.github.tomakehurst.wiremock.client.WireMock.*;
import static com.github.tomakehurst.wiremock.client.WireMock.post;
import static de.interzero.oneepr.common.string.Api.LICENSE_VOLUME_REPORT_ITEM;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Comprehensive integration tests for LicenseVolumeReportItemController.
 * Uses WireMock for external service mocking. Uses real service and repository instances.
 * Test Coverage:
 * - All 8 controller endpoints (create, findAll, findOne, update, decline, remove, createBulk, updateBulkValues)
 * - All HTTP methods (GET, POST, PUT, DELETE)
 * - All success and error scenarios
 * - All authentication and authorization cases
 * - All parameter validation scenarios
 * - All edge cases and boundary conditions
 */
@SpringBootTest
@AutoConfigureMockMvc
@Transactional
class LicenseVolumeReportItemControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private LicenseVolumeReportItemRepository licenseVolumeReportItemRepository;

    @Autowired
    private LicenseVolumeReportRepository licenseVolumeReportRepository;

    @Autowired
    private ReasonRepository reasonRepository;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private ContractRepository contractRepository;

    @Autowired
    private LicenseRepository licenseRepository;

    @Autowired
    private LicensePackagingServiceRepository licensePackagingServiceRepository;

    @Autowired
    private EntityManager entityManager;

    private WireMockServer wireMockServer;

    private MockedStatic<de.interzero.oneepr.common.AuthUtil> authUtilMock;

    // Test data constants
    private static final Integer TEST_CUSTOMER_ID = 1;

    private static final Integer TEST_CONTRACT_ID = 1;

    private static final Integer TEST_LICENSE_ID = 1;

    private static final Integer TEST_PACKAGING_SERVICE_ID = 1;

    private static final Integer TEST_LICENSE_VOLUME_REPORT_ID = 1;

    private static final String TEST_USER_EMAIL = "<EMAIL>";

    private static final Role TEST_USER_ROLE = Role.CUSTOMER;

    // Test entities
    private Customer testCustomer;

    private LicenseVolumeReport testVolumeReport;

    private LicenseVolumeReportItem testItem;

    private Reason testReason;

    @BeforeEach
    void setUp() {
        // Create test data in database
        createTestData();
        // Setup WireMock server for external HTTP calls
        wireMockServer = new WireMockServer(8089);
        wireMockServer.start();
        WireMock.configureFor("localhost", 8089);

        // Mock AuthUtil static method
        authUtilMock = Mockito.mockStatic(de.interzero.oneepr.common.AuthUtil.class);
        AuthenticatedUser mockUser = new AuthenticatedUser(
                String.valueOf(testCustomer.getId()),
                TEST_USER_ROLE,
                TEST_USER_EMAIL);
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails).thenReturn(mockUser);

    }

    @AfterEach
    void tearDown() {
        if (wireMockServer != null) {
            wireMockServer.stop();
        }
        if (authUtilMock != null) {
            authUtilMock.close();
        }
        entityManager.clear();
    }

    private void createTestData() {

        // Create Customer
        testCustomer = new Customer();
        testCustomer.setId(TEST_CUSTOMER_ID);
        testCustomer.setUserId(TEST_CUSTOMER_ID);
        testCustomer.setEmail(TEST_USER_EMAIL);
        testCustomer.setFirstName("test first name");
        testCustomer.setLastName("test last name");
        testCustomer.setCreatedAt(Instant.now());
        testCustomer.setUpdatedAt(Instant.now());
        testCustomer = customerRepository.save(testCustomer);

        // Create Contract
        Contract testContract = new Contract();
        testContract.setId(TEST_CONTRACT_ID);
        testContract.setCustomer(testCustomer);
        testContract.setType(Contract.Type.EU_LICENSE);
        testContract.setStatus(Contract.Status.ACTIVE);
        testContract.setCreatedAt(Instant.now());
        testContract.setUpdatedAt(Instant.now());
        testContract.setTitle("test contract title");
        testContract.setEndDate(Instant.now());
        testContract.setStartDate(Instant.now());
        testContract = contractRepository.save(testContract);

        // Create License
        License testLicense = new License();
        testLicense.setId(TEST_LICENSE_ID);
        testLicense.setContract(testContract);
        testLicense.setCountryName("Germany");
        testLicense.setYear(2024);
        testLicense.setCreatedAt(Instant.now());
        testLicense.setUpdatedAt(Instant.now());
        testLicense.setStartDate(Instant.now());
        testLicense.setCountryId(1);
        testLicense.setCountryName("Germany");
        testLicense.setRegistrationNumber("test registration number");
        testLicense.setCountryCode("Germany");
        testLicense.setCountryFlag("Germany");
        testLicense = licenseRepository.save(testLicense);

        // Create LicensePackagingService
        LicensePackagingService testPackagingService = new LicensePackagingService();
        testPackagingService.setId(TEST_PACKAGING_SERVICE_ID);
        testPackagingService.setLicense(testLicense);
        testPackagingService.setCreatedAt(Instant.now());
        testPackagingService.setUpdatedAt(Instant.now());
        testPackagingService.setName("test packaging service");
        testPackagingService.setSetupPackagingServiceId(TEST_PACKAGING_SERVICE_ID);
        testPackagingService.setDescription("test packaging service description");
        testPackagingService = licensePackagingServiceRepository.save(testPackagingService);

        // Create LicenseVolumeReport
        testVolumeReport = new LicenseVolumeReport();
        testVolumeReport.setId(TEST_LICENSE_VOLUME_REPORT_ID);
        testVolumeReport.setPackagingService(testPackagingService);
        testVolumeReport.setYear(2024);
        testVolumeReport.setInterval("Q1");
        testVolumeReport.setStatus(LicenseVolumeReport.Status.OPEN);

        Map<String, Object> reportTable = new HashMap<>();
        reportTable.put("data", "test");
        testVolumeReport.setReportTable(reportTable);
        testVolumeReport.setCreatedAt(Instant.now());
        testVolumeReport.setUpdatedAt(Instant.now());
        testVolumeReport.setDeletedAt(null);
        testVolumeReport = licenseVolumeReportRepository.save(testVolumeReport);

        // Create test Reason
        testReason = new Reason();
        testReason.setTitle("Test Decline Reason");
        testReason.setType(Reason.Type.TERMINATION);
        testReason.setCreatedAt(Instant.now());
        testReason.setUpdatedAt(Instant.now());
        testReason.setValue("test value");
        testReason = reasonRepository.save(testReason);

    }

    // ========== CREATE METHOD TESTS (POST /license-volume-report-item) ==========

    /**
     * Test create method - POST /license-volume-report-item
     * Integration test with real service and repositories
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_ShouldReturnCreatedItem_WhenValidDto() throws Exception {
        // Given
        CreateLicenseVolumeReportItemDto createDto = createTestCreateDto();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(LICENSE_VOLUME_REPORT_ITEM)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.setupFractionId").value(1))
                .andExpect(jsonPath("$.setupColumnId").value(2))
                .andExpect(jsonPath("$.value").value(100))
                .andExpect(jsonPath("$.setupFractionCode").value("DEOK34"))
                .andExpect(jsonPath("$.setupColumnCode").value("COL01"))
                .andExpect(jsonPath("$.createdAt").exists())
                .andExpect(jsonPath("$.updatedAt").exists());
    }

    /**
     * Test create method with invalid JSON
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(LICENSE_VOLUME_REPORT_ITEM)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content("\"invalid json\"")).andExpect(status().isBadRequest());
    }

    /**
     * Test create method with missing content type
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_ShouldReturnUnsupportedMediaType_WhenMissingContentType() throws Exception {
        // Given
        CreateLicenseVolumeReportItemDto createDto = createTestCreateDto();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(LICENSE_VOLUME_REPORT_ITEM)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isUnsupportedMediaType());
    }

    /**
     * Test create method with non-existent license volume report
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_ShouldReturnNotFound_WhenLicenseVolumeReportNotExists() throws Exception {
        // Given
        CreateLicenseVolumeReportItemDto createDto = createTestCreateDto();
        createDto.setLicenseVolumeReportId(999); // Non-existent ID

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(LICENSE_VOLUME_REPORT_ITEM)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto))).andExpect(status().isNotFound());
    }


    /**
     * Test create method with null DTO fields
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_ShouldReturnBadRequest_WhenDtoFieldsAreNull() throws Exception {
        // Given
        CreateLicenseVolumeReportItemDto createDto = new CreateLicenseVolumeReportItemDto();
        // Leave fields null

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(LICENSE_VOLUME_REPORT_ITEM)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto))).andExpect(status().isOk());
    }

    /**
     * Test findAll method with non-existent license volume report
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_ShouldReturnNotFound_WhenLicenseVolumeReportNotExists() throws Exception {
        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.get(LICENSE_VOLUME_REPORT_ITEM).param("license_volume_report_id", "999"))
                .andExpect(status().isNotFound());
    }

    /**
     * Test findAll method with customer role accessing other customer's data
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_ShouldReturnForbidden_WhenCustomerAccessesOtherCustomerData() throws Exception {
        // Given - Mock different user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.CUSTOMER, "<EMAIL>"));

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.get(LICENSE_VOLUME_REPORT_ITEM)
                                .param("license_volume_report_id", TEST_LICENSE_VOLUME_REPORT_ID.toString()))
                .andExpect(status().isNotFound());
    }

    /**
     * Test findAll method with admin role - should have access
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_ShouldReturnList_WhenAdminRole() throws Exception {
        // Given - Mock admin user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.ADMIN, "<EMAIL>"));

        // First create a test item
        testItem = createAndSaveTestItem();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.get(LICENSE_VOLUME_REPORT_ITEM)
                                .param("license_volume_report_id", String.valueOf(testVolumeReport.getId())))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(1));
    }


    /**
     * Test findOne method with invalid ID format
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOne_ShouldReturnBadRequest_WhenInvalidIdFormat() throws Exception {
        // When & Then
        mockMvc.perform(get(LICENSE_VOLUME_REPORT_ITEM + "/{id}", "invalid-id")).andExpect(status().isBadRequest());
    }

    /**
     * Test findOne method with customer role accessing other customer's data
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findOne_ShouldReturnForbidden_WhenCustomerAccessesOtherCustomerData() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        // Given - Mock different user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.CUSTOMER, "<EMAIL>"));

        // When & Then
        mockMvc.perform(get(LICENSE_VOLUME_REPORT_ITEM + "/{id}", testItem.getId())).andExpect(status().isForbidden());
    }

    /**
     * Test update method with non-existent item
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_ShouldReturnNotFound_WhenItemNotExists() throws Exception {
        // Given
        UpdateLicenseVolumeReportItemDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put(LICENSE_VOLUME_REPORT_ITEM + "/{id}", 999).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto))).andExpect(status().isNotFound());
    }

    /**
     * Test update method with invalid JSON
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        // When & Then
        mockMvc.perform(put(
                LICENSE_VOLUME_REPORT_ITEM + "/{id}",
                testItem.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content("\"invalid json\"")).andExpect(status().isBadRequest());
    }

    /**
     * Test update method with customer role accessing other customer's data
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_ShouldReturnForbidden_WhenCustomerAccessesOtherCustomerData() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        // Given - Mock different user
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.CUSTOMER, "<EMAIL>"));

        UpdateLicenseVolumeReportItemDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put(
                LICENSE_VOLUME_REPORT_ITEM + "/{id}",
                testItem.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto))).andExpect(status().isForbidden());
    }


    /**
     * Test decline method with non-existent item
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void decline_ShouldReturnNotFound_WhenItemNotExists() throws Exception {
        // Given
        DeclineLicenseVolumeReportItemDto declineDto = createTestDeclineDto();

        // When & Then
        mockMvc.perform(post(LICENSE_VOLUME_REPORT_ITEM + "/{id}/decline", 999).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(declineDto))).andExpect(status().isNotFound());
    }

    /**
     * Test decline method with invalid JSON
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void decline_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        // When & Then
        mockMvc.perform(post(
                LICENSE_VOLUME_REPORT_ITEM + "/{id}/decline",
                testItem.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content("\"invalid json\"")).andExpect(status().isBadRequest());
    }

    /**
     * Test decline method with large ID values
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void decline_ShouldWork_WithLargeId() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        DeclineLicenseVolumeReportItemDto declineDto = createTestDeclineDto();

        // When & Then
        mockMvc.perform(post(
                        LICENSE_VOLUME_REPORT_ITEM + "/{id}/decline",
                        Integer.MAX_VALUE).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(declineDto)))
                .andExpect(status().isNotFound()); // Should not find item with MAX_VALUE ID
    }

    /**
     * Test remove method - DELETE LICENSE_VOLUME_REPORT_ITEM+"/{id}
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void remove_ShouldReturnRemovedItem_WhenValidId() throws Exception {
        AuthenticatedUser mockUser = new AuthenticatedUser(
                String.valueOf(testCustomer.getId()),
                Role.ADMIN,
                TEST_USER_EMAIL);
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails).thenReturn(mockUser);
        // First create a test item
        testItem = createAndSaveTestItem();

        // When & Then
        mockMvc.perform(delete(LICENSE_VOLUME_REPORT_ITEM + "/{id}", testItem.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testItem.getId()))
                .andExpect(jsonPath("$.deletedAt").exists());

    }

    /**
     * Test remove method with non-existent item
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void remove_ShouldReturnNotFound_WhenItemNotExists() throws Exception {
        // When & Then
        mockMvc.perform(delete(LICENSE_VOLUME_REPORT_ITEM + "/{id}", 999)).andExpect(status().isNotFound());
    }

    /**
     * Test remove method with negative ID
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void remove_ShouldWork_WithNegativeId() throws Exception {
        // When & Then
        mockMvc.perform(delete(LICENSE_VOLUME_REPORT_ITEM + "/{id}", -1))
                .andExpect(status().isNotFound()); // Should not find item with negative ID
    }

    /**
     * Test remove method with zero ID
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void remove_ShouldWork_WithZeroId() throws Exception {
        // When & Then
        mockMvc.perform(delete(LICENSE_VOLUME_REPORT_ITEM + "/{id}", 0))
                .andExpect(status().isNotFound()); // Should not find item with zero ID
    }

    /**
     * Test createBulk method - POST LICENSE_VOLUME_REPORT_ITEM+"/bulk
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void createBulk_ShouldCallService_WhenValidDtoList() throws Exception {
        // Given
        List<CreateLicenseVolumeReportItemDto> createDtoList = Arrays.asList(
                createTestCreateDto(),
                createTestCreateDto());

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(LICENSE_VOLUME_REPORT_ITEM + "/bulk")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDtoList))).andExpect(status().isOk());
    }

    /**
     * Test createBulk method with empty list
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void createBulk_ShouldReturnBadRequest_WhenEmptyList() throws Exception {
        // Given
        List<CreateLicenseVolumeReportItemDto> emptyList = List.of();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(LICENSE_VOLUME_REPORT_ITEM + "/bulk")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(emptyList)))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test createBulk method with invalid JSON
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void createBulk_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(LICENSE_VOLUME_REPORT_ITEM + "/bulk")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content("\"invalid json\"")).andExpect(status().isBadRequest());
    }

    /**
     * Test createBulk method with missing content type
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void createBulk_ShouldReturnUnsupportedMediaType_WhenMissingContentType() throws Exception {
        // Given
        List<CreateLicenseVolumeReportItemDto> createDtoList = List.of(createTestCreateDto());

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(LICENSE_VOLUME_REPORT_ITEM + "/bulk")
                                .content(objectMapper.writeValueAsString(createDtoList)))
                .andExpect(status().isUnsupportedMediaType());
    }

    /**
     * Test updateBulkValues method - POST LICENSE_VOLUME_REPORT_ITEM+"/bulk-update
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void updateBulkValues_ShouldCallService_WhenValidDtoList() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        List<UpdateLicenseVolumeReportItemValueDto> updateDtoList = List.of(createTestUpdateValueDto(testItem.getId()));

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(LICENSE_VOLUME_REPORT_ITEM + "/bulk-update")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDtoList))).andExpect(status().isOk());
    }

    /**
     * Test updateBulkValues method with empty list
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void updateBulkValues_ShouldReturnBadRequest_WhenEmptyList() throws Exception {
        // Given
        List<UpdateLicenseVolumeReportItemValueDto> emptyList = List.of();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(LICENSE_VOLUME_REPORT_ITEM + "/bulk-update")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(emptyList)))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test updateBulkValues method with invalid JSON
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void updateBulkValues_ShouldReturnBadRequest_WhenInvalidJson() throws Exception {
        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(LICENSE_VOLUME_REPORT_ITEM + "/bulk-update")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content("\"invalid json\"")).andExpect(status().isBadRequest());
    }

    /**
     * Test updateBulkValues method with invalid value
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void updateBulkValues_ShouldReturnBadRequest_WhenInvalidValue() throws Exception {
        // First create a test item
        testItem = createAndSaveTestItem();

        UpdateLicenseVolumeReportItemValueDto dto = createTestUpdateValueDto(testItem.getId());
        dto.setValue(-1); // Invalid negative value
        List<UpdateLicenseVolumeReportItemValueDto> updateDtoList = List.of(dto);

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(LICENSE_VOLUME_REPORT_ITEM + "/bulk-update")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDtoList)))
                .andExpect(status().isBadRequest());
    }

    /**
     * Test updateBulkValues method with missing volume report item ID
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void updateBulkValues_ShouldReturnBadRequest_WhenMissingVolumeReportItemId() throws Exception {
        // Given
        UpdateLicenseVolumeReportItemValueDto dto = createTestUpdateValueDto(null);
        dto.setVolumeReportItemId(null); // Missing ID
        List<UpdateLicenseVolumeReportItemValueDto> updateDtoList = List.of(dto);

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(LICENSE_VOLUME_REPORT_ITEM + "/bulk-update")
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDtoList)))
                .andExpect(status().isBadRequest());
    }


    /**
     * Test with different user roles - CLERK
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void update_ShouldWork_WithClerkRole() throws Exception {
        // Given
        authUtilMock.when(de.interzero.oneepr.common.AuthUtil::getRelevantUserDetails)
                .thenReturn(new AuthenticatedUser("999", Role.CLERK, "<EMAIL>"));

        // First create a test item
        testItem = createAndSaveTestItem();

        UpdateLicenseVolumeReportItemDto updateDto = createTestUpdateDto();

        // When & Then
        mockMvc.perform(put(
                        LICENSE_VOLUME_REPORT_ITEM + "/{id}",
                        testItem.getId()).contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(testItem.getId()));
    }

    /**
     * Test WireMock integration - External service integration
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void create_ShouldIntegrateWithExternalService_WhenUsingWireMock() throws Exception {
        // Given
        wireMockServer.stubFor(post(urlEqualTo("/external-service/license-volume-report-item")).willReturn(aResponse().withStatus(
                200).withHeader("Content-Type", "application/json").withBody("{\"status\":\"success\"}")));

        CreateLicenseVolumeReportItemDto createDto = createTestCreateDto();

        // When & Then
        mockMvc.perform(MockMvcRequestBuilders.post(LICENSE_VOLUME_REPORT_ITEM)
                                .contentType(MediaType.APPLICATION_JSON)
                                .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.setupFractionId").value(1));
    }

    /**
     * Test parameter validation - String to Integer conversion
     */
    @Test
    @WithMockUser(roles = {TestRole.ADMIN})
    void findAll_ShouldWork_WithStringLicenseVolumeReportId() throws Exception {
        // First create a test item
        createAndSaveTestItem();
        // When & Then - Test string to integer conversion in controller
        mockMvc.perform(MockMvcRequestBuilders.get(LICENSE_VOLUME_REPORT_ITEM)
                                .param("license_volume_report_id", String.valueOf(testVolumeReport.getId())))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.length()").value(1));
    }

    // Helper methods for creating test data
    private CreateLicenseVolumeReportItemDto createTestCreateDto() {
        CreateLicenseVolumeReportItemDto dto = new CreateLicenseVolumeReportItemDto();
        dto.setLicenseVolumeReportId(testVolumeReport.getId());
        dto.setSetupFractionId(1);
        dto.setSetupColumnId(2);
        dto.setValue(100);
        dto.setSetupFractionCode("DEOK34");
        dto.setSetupColumnCode("COL01");
        return dto;
    }

    private UpdateLicenseVolumeReportItemDto createTestUpdateDto() {
        UpdateLicenseVolumeReportItemDto dto = new UpdateLicenseVolumeReportItemDto();
        dto.setLicenseVolumeReportId(testVolumeReport.getId());
        dto.setValue(200);
        dto.setSetupFractionCode("UPDATED_CODE");
        return dto;
    }

    private DeclineLicenseVolumeReportItemDto createTestDeclineDto() {
        DeclineLicenseVolumeReportItemDto dto = new DeclineLicenseVolumeReportItemDto();
        dto.setReasonIds(Collections.singletonList(testReason.getId()));
        dto.setTitle("Test decline reason");
        return dto;
    }

    private UpdateLicenseVolumeReportItemValueDto createTestUpdateValueDto(Integer itemId) {
        UpdateLicenseVolumeReportItemValueDto dto = new UpdateLicenseVolumeReportItemValueDto();
        dto.setLicenseVolumeReportId(TEST_LICENSE_VOLUME_REPORT_ID);
        dto.setVolumeReportItemId(itemId);
        dto.setValue(150);
        return dto;
    }

    private LicenseVolumeReportItem createAndSaveTestItem() {
        LicenseVolumeReportItem item = new LicenseVolumeReportItem();
        item.setLicenseVolumeReport(testVolumeReport);
        item.setSetupFractionId(1);
        item.setSetupColumnId(2);
        item.setValue(100);
        item.setPrice(50);
        item.setSetupFractionCode("DEOK34");
        item.setSetupColumnCode("COL01");
        item.setCreatedAt(Instant.now());
        item.setUpdatedAt(Instant.now());
        return licenseVolumeReportItemRepository.save(item);
    }

}