import { Test, TestingModule } from "@nestjs/testing";
import { PriceListService } from "./price-list.service";
import { DatabaseService } from "../database/database.service";
import { CreatePriceListDto } from "./dto/price-list-create.dto";
import { PriceListType, PriceListConditionType } from "@prisma/client";
import { NotFoundException } from "@nestjs/common";

describe("CountryPriceListService", () => {
  let service: PriceListService;
  let databaseService: DatabaseService;

  const databaseServiceMock = {
    priceList: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PriceListService, { provide: DatabaseService, useValue: databaseServiceMock }],
    }).compile();

    service = module.get<PriceListService>(PriceListService);
    databaseService = module.get<DatabaseService>(DatabaseService);
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("findAll", () => {
    it("should return all price list entries with required parameters", async () => {
      const params = {
        search: "Test",
        service_type: PriceListType.DIRECT_LICENSE,
        license_year: "2024",
      };
      const mockResponse = [{ id: 1, name: "Test Price List" }];
      databaseServiceMock.priceList.findMany.mockResolvedValue(mockResponse);

      const result = await service.findAll(params);
      expect(result).toEqual(mockResponse);
      expect(databaseServiceMock.priceList.findMany).toHaveBeenCalledWith({
        orderBy: {
          condition_type_value: "asc",
        },
        where: {
          deleted_at: null,
          type: params.service_type,
          condition_type_value: params.license_year,
          name: { contains: params.search, mode: "insensitive" },
        },
      });
    });
  });

  describe("findOne", () => {
    it("should return a price list entry by ID", async () => {
      const mockResponse = { id: 1, name: "Test Price List" };
      databaseServiceMock.priceList.findUnique.mockResolvedValue(mockResponse);

      const result = await service.findOne(1);
      expect(result).toEqual(mockResponse);
      expect(databaseServiceMock.priceList.findUnique).toHaveBeenCalledWith({
        where: { id: 1, deleted_at: null },
      });
    });

    it("should throw NotFoundException if entry is not found", async () => {
      databaseServiceMock.priceList.findUnique.mockResolvedValue(null);
      await expect(service.findOne(1)).rejects.toThrow("Price list not found");
    });
  });

  describe("remove", () => {
    it("should soft delete a price list entry", async () => {
      const mockResponse = { id: 1, deleted_at: new Date() };
      databaseServiceMock.priceList.findUnique.mockResolvedValue({ id: 1 });
      databaseServiceMock.priceList.update.mockResolvedValue(mockResponse);

      await service.remove(1);
      expect(databaseServiceMock.priceList.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: { deleted_at: expect.any(Date) },
      });
    });

    it("should throw NotFoundException if entry is not found", async () => {
      databaseServiceMock.priceList.findUnique.mockResolvedValue(null);

      await expect(service.remove(1)).rejects.toThrowError(new NotFoundException("Price list not found"));
    });
  });

  describe("update", () => {
    it("should update a price list entry successfully", async () => {
      const mockPriceList = { id: 1, name: "Old Price List" };
      const updateData = { name: "Updated Price List" };
      const updatedPriceList = { id: 1, name: "Updated Price List" };

      databaseServiceMock.priceList.findUnique.mockResolvedValue(mockPriceList);

      databaseServiceMock.priceList.update.mockResolvedValue(updatedPriceList);

      const result = await service.update(1, updateData);

      expect(result).toEqual(updatedPriceList);
      expect(databaseServiceMock.priceList.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: updateData,
      });
    });

    it("should throw NotFoundException if price list is not found", async () => {
      databaseServiceMock.priceList.findUnique.mockResolvedValue(null);

      const updateData = { name: "Updated Price List" };

      await expect(service.update(1, updateData)).rejects.toThrowError(new NotFoundException("Price list not found"));
    });
  });
});
