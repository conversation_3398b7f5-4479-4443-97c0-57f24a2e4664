import { Test, TestingModule } from "@nestjs/testing";
import { ReportSetFractionsService } from "./report-set-fractions.service";
import { DatabaseService } from "../database/database.service";
import { NotFoundException } from "@nestjs/common";
import { CreateReportSetFractionDto } from "./dto/report-set-fraction-create.dto";
import { UpdateReportSetFractionDto } from "./dto/report-set-fraction-update.dto";

const mockDatabaseService = {
  reportSetFraction: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    updateMany: jest.fn(),
  },
  $transaction: jest.fn((callback): any => callback(mockDatabaseService)),
};

describe("ReportSetFractionsService", () => {
  let service: ReportSetFractionsService;
  let databaseService: jest.Mocked<typeof mockDatabaseService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ReportSetFractionsService,
        {
          provide: DatabaseService,
          useValue: mockDatabaseService,
        },
      ],
    }).compile();

    service = module.get<ReportSetFractionsService>(ReportSetFractionsService);
    databaseService = module.get(DatabaseService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("create", () => {
    it("should create a report set fraction", async () => {
      const dto: CreateReportSetFractionDto = {
        name: "Test Fraction",
        description: "Test Description",
        icon: "test-icon",
        fraction_icon_id: 1,
        is_active: true,
        report_set_id: 123,
        parent_id: undefined,
      };

      const createdFraction = { id: 1, ...dto };
      databaseService.reportSetFraction.create.mockResolvedValue(createdFraction);

      const result = await service.create(dto);
      expect(result).toEqual(createdFraction);
      expect(databaseService.reportSetFraction.create).toHaveBeenCalledWith({
        data: expect.objectContaining(dto),
      });
    });
  });

  describe("findAll", () => {
    it("should return an array of report set fractions", async () => {
      const fractions = [{ id: 1, name: "Test" }];
      databaseService.reportSetFraction.findMany.mockResolvedValue(fractions);

      const result = await service.findAll();
      expect(result).toEqual(fractions);
      expect(databaseService.reportSetFraction.findMany).toHaveBeenCalledWith({ where: { deleted_at: null } });
    });
  });

  describe("findOne", () => {
    it("should return a report set fraction when found", async () => {
      const fraction = { id: 1, name: "Test" };
      databaseService.reportSetFraction.findUnique.mockResolvedValue(fraction);

      const result = await service.findOne(1);
      expect(result).toEqual(fraction);
    });

    it("should throw NotFoundException when not found", async () => {
      databaseService.reportSetFraction.findUnique.mockResolvedValue(null);
      await expect(service.findOne(1)).rejects.toThrow(NotFoundException);
    });
  });

  describe("update", () => {
    it("should update a report set fraction", async () => {
      const dto: UpdateReportSetFractionDto = { name: "Updated Name" };
      const updatedFraction = { id: 1, name: "Updated Name" };

      databaseService.reportSetFraction.findUnique.mockResolvedValue(updatedFraction);
      databaseService.reportSetFraction.update.mockResolvedValue(updatedFraction);

      const result = await service.update(1, dto);
      expect(result).toEqual(updatedFraction);
    });

    it("should throw NotFoundException when fraction not found", async () => {
      databaseService.reportSetFraction.findUnique.mockResolvedValue(null);
      await expect(service.update(1, { name: "Updated Name" })).rejects.toThrow(NotFoundException);
    });
  });

  describe("remove", () => {
    it("should mark a report set fraction as deleted", async () => {
      const fraction = { id: 1, name: "Test" };
      databaseService.reportSetFraction.findUnique.mockResolvedValue(fraction);
      databaseService.reportSetFraction.update.mockResolvedValue({ ...fraction, deleted_at: new Date() });

      await service.remove(1);
      expect(databaseService.reportSetFraction.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: { deleted_at: expect.any(Date) },
      });
    });

    it("should throw NotFoundException when fraction not found", async () => {
      databaseService.reportSetFraction.findUnique.mockResolvedValue(null);
      await expect(service.remove(1)).rejects.toThrow(NotFoundException);
    });
  });
});
