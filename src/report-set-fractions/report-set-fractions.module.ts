import { Modu<PERSON> } from "@nestjs/common";
import { DatabaseModule } from "../database/database.module";
import { ReportSetFractionsController } from "./report-set-fractions.controller";
import { ReportSetFractionsService } from "./report-set-fractions.service";

@Module({
  imports: [DatabaseModule],
  controllers: [ReportSetFractionsController],
  providers: [ReportSetFractionsService],
})
export class ReportSetFractionsModule {}
