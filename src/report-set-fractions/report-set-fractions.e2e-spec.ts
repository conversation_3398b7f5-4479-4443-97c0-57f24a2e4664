import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { AppModule } from "../app.module";
import { Role } from "../shared/auth/role.enum";
import { HEADER_SYSTEM_API_KEY, HEADER_USER_ID, HEADER_USER_ROLE, HEADER_USER_EMAIL } from "../shared/auth/const";
import { DatabaseService } from "../database/database.service";
import { randomBytes } from "node:crypto";

jest.setTimeout(30000);

describe("ReportSetFractionsController (e2e)", () => {
  let app: INestApplication;
  let databaseService: DatabaseService;

  const validApiKey = "test-api-key";

  beforeAll(() => {
    process.env.SYSTEM_API_KEY = validApiKey;
  });

  const authHeaders = {
    [HEADER_SYSTEM_API_KEY]: validApi<PERSON><PERSON>,
    [HEADER_USER_ID]: "1",
    [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
    [HEADER_USER_EMAIL]: "<EMAIL>",
  };

  const mockReportSetFraction = {
    id: 1,
    name: "Plastics",
    description: "All plastic materials",
    icon: "plastic",
    code: randomBytes(16).toString("hex"),
    is_active: true,
    report_set_id: 1,
    parent_id: null,
    level: 1,
    order: 1,
    parent_code: null,
    fraction_icon_id: 1,
    has_second_level: false,
    has_third_level: false,
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
  };

  const mockReportSetFractions = [
    mockReportSetFraction,
    {
      id: 2,
      name: "Glass",
      description: "All glass materials",
      icon: "glass",
      code: randomBytes(16).toString("hex"),
      is_active: true,
      report_set_id: 1,
      parent_id: null,
      level: 1,
      order: 2,
      parent_code: null,
      fraction_icon_id: 2,
      has_second_level: false,
      has_third_level: false,
      created_at: "2025-04-07T22:58:17.004Z",
      updated_at: "2025-04-07T22:58:17.004Z",
    },
  ];

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(DatabaseService)
      .useValue({
        reportSetFraction: {
          create: jest.fn().mockResolvedValue(mockReportSetFraction),
          findMany: jest.fn().mockResolvedValue(mockReportSetFractions),
          findUnique: jest.fn().mockResolvedValue(mockReportSetFraction),
          update: jest.fn().mockImplementation((params) => {
            return Promise.resolve({
              ...mockReportSetFraction,
              ...params.data,
            });
          }),
        },
        $transaction: jest.fn().mockImplementation(async (callback) => {
          return await callback({
            reportSetFraction: {
              update: jest.fn().mockResolvedValue(mockReportSetFraction),
              updateMany: jest.fn().mockResolvedValue({ count: 1 }),
            },
          });
        }),
      })
      .compile();

    app = moduleFixture.createNestApplication();
    databaseService = moduleFixture.get<DatabaseService>(DatabaseService);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("/report-set-fractions (GET)", () => {
    it("should return report set fractions when authenticated as SUPER_ADMIN with valid API key", () => {
      return request(app.getHttpServer())
        .get("/report-set-fractions")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.reportSetFraction.findMany).toHaveBeenCalled();
          expect(response.body).toEqual(mockReportSetFractions);
        });
    });

    it("should return report set fractions when authenticated as ADMIN with valid API key", () => {
      const adminHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.ADMIN,
      };

      return request(app.getHttpServer()).get("/report-set-fractions").set(adminHeaders).expect(200);
    });

    it("should return report set fractions when authenticated as CLERK with valid API key", () => {
      const clerkHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CLERK,
      };

      return request(app.getHttpServer()).get("/report-set-fractions").set(clerkHeaders).expect(200);
    });

    it("should reject when authenticated with unauthorized role even with valid API key", () => {
      const unauthorizedHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/report-set-fractions").set(unauthorizedHeaders).expect(403);
    });

    it("should reject when API key is invalid regardless of role", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/report-set-fractions").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject when not authenticated at all", () => {
      return request(app.getHttpServer()).get("/report-set-fractions").expect(401);
    });
  });

  describe("/report-set-fractions/:id (GET)", () => {
    it("should return a single report set fraction when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .get("/report-set-fractions/1")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.reportSetFraction.findUnique).toHaveBeenCalledWith({
            where: { id: 1, deleted_at: null },
          });
          expect(response.body).toEqual(mockReportSetFraction);
        });
    });

    it("should reject when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/report-set-fractions/1").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject when not authenticated", () => {
      return request(app.getHttpServer()).get("/report-set-fractions/1").expect(401);
    });
  });

  describe("/report-set-fractions (POST)", () => {
    const createReportSetFractionDto = {
      name: "Aluminum",
      description: "Aluminum materials",
      icon: "aluminum",
      fraction_icon_id: 3,
      is_active: true,
      report_set_id: 1,
      parent_id: null,
    };

    it("should create a new report set fraction when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .post("/report-set-fractions")
        .set(authHeaders)
        .send(createReportSetFractionDto)
        .expect(201)
        .then((response) => {
          expect(databaseService.reportSetFraction.create).toHaveBeenCalledWith({
            data: {
              code: expect.any(String),
              name: createReportSetFractionDto.name,
              description: createReportSetFractionDto.description,
              icon: createReportSetFractionDto.icon,
              fraction_icon_id: createReportSetFractionDto.fraction_icon_id,
              is_active: createReportSetFractionDto.is_active,
              report_set_id: createReportSetFractionDto.report_set_id,
              parent_id: createReportSetFractionDto.parent_id,
            },
          });
          expect(response.body).toEqual(mockReportSetFraction);
        });
    });

    it("should reject creation when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .post("/report-set-fractions")
        .set(invalidApiKeyHeaders)
        .send(createReportSetFractionDto)
        .expect(401);
    });

    it("should reject creation when not authenticated", () => {
      return request(app.getHttpServer()).post("/report-set-fractions").send(createReportSetFractionDto).expect(401);
    });
  });

  describe("/report-set-fractions/:id (PUT)", () => {
    const updateReportSetFractionDto = {
      name: "Updated Plastic",
      description: "Updated plastic materials description",
      is_active: false,
    };

    it("should reject update when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .put("/report-set-fractions/1")
        .set(invalidApiKeyHeaders)
        .send(updateReportSetFractionDto)
        .expect(401);
    });

    it("should reject update when not authenticated", () => {
      return request(app.getHttpServer()).put("/report-set-fractions/1").send(updateReportSetFractionDto).expect(401);
    });
  });

  describe("/report-set-fractions/:id (DELETE)", () => {
    it("should delete a report set fraction when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .delete("/report-set-fractions/1")
        .set(authHeaders)
        .expect(200)
        .then(() => {
          expect(databaseService.$transaction).toHaveBeenCalled();
        });
    });

    it("should reject deletion when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).delete("/report-set-fractions/1").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject deletion when not authenticated", () => {
      return request(app.getHttpServer()).delete("/report-set-fractions/1").expect(401);
    });
  });

  describe("Role access", () => {
    it("should allow access with SYSTEM role and valid API key", () => {
      const systemHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/report-set-fractions").set(systemHeaders).expect(200);
    });

    it("should reject access with SYSTEM role but invalid API key", () => {
      const invalidSystemHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/report-set-fractions").set(invalidSystemHeaders).expect(401);
    });
  });
});
