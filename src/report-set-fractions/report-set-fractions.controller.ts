import { Body, Controller, Delete, Get, Param, Put, Post } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { ReportSetFractionsService } from "./report-set-fractions.service";
import { CreateReportSetFractionDto } from "./dto/report-set-fraction-create.dto";
import { UpdateReportSetFractionDto } from "./dto/report-set-fraction-update.dto";
import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK)
@ApiTags("ReportSetFractions")
@Controller("report-set-fractions")
export class ReportSetFractionsController {
  constructor(private readonly reportSetFractionsService: ReportSetFractionsService) {}

  @Post()
  @ApiOperation({ summary: "Create a new report set fraction" })
  @ApiResponse({
    status: 201,
    description: "Report set fraction created successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        name: { type: "string" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
        description: { type: "string" },
        report_set_id: { type: "number" },
        parent_id: { type: "number", nullable: true },
        level: { type: "number" },
        order: { type: "number" },
        code: { type: "string" },
        parent_code: { type: "string", nullable: true },
        is_active: { type: "boolean" },
        fraction_icon_id: { type: "number", nullable: true },
        has_second_level: { type: "boolean" },
        has_third_level: { type: "boolean" },
      },
    },
  })
  @ApiResponse({ status: 400, description: "Invalid report set fraction data" })
  @ApiResponse({ status: 404, description: "Report set not found" })
  create(@Body() data: CreateReportSetFractionDto) {
    return this.reportSetFractionsService.create(data);
  }

  @Get()
  @ApiOperation({ summary: "Get all report set fractions" })
  @ApiResponse({
    status: 201,
    description: "Report set fraction created successfully",
    schema: {
      type: "array",
      items: {
        type: "object",
        properties: {
          id: { type: "number" },
          name: { type: "string" },
          created_at: { type: "string", format: "date-time" },
          updated_at: { type: "string", format: "date-time" },
          deleted_at: { type: "string", format: "date-time", nullable: true },
          description: { type: "string" },
          report_set_id: { type: "number" },
          parent_id: { type: "number", nullable: true },
          level: { type: "number" },
          order: { type: "number" },
          code: { type: "string" },
          parent_code: { type: "string", nullable: true },
          is_active: { type: "boolean" },
          fraction_icon_id: { type: "number", nullable: true },
          has_second_level: { type: "boolean" },
          has_third_level: { type: "boolean" },
        },
      },
      example: [
        {
          id: 1,
          name: "Fraction 1",
          created_at: "2021-01-01T00:00:00.000Z",
          updated_at: "2021-01-01T00:00:00.000Z",
          deleted_at: null,
          description: "Fraction 1 description",
          report_set_id: 1,
          parent_id: null,
          level: 1,
          order: 1,
          code: "1",
          parent_code: null,
          is_active: true,
          fraction_icon_id: null,
          has_second_level: false,
          has_third_level: false,
        },
      ],
    },
  })
  findAll() {
    return this.reportSetFractionsService.findAll();
  }

  @Get(":id")
  @ApiOperation({ summary: "Get report set fraction by ID" })
  @ApiResponse({
    status: 200,
    description: "Report set fraction retrieved successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        name: { type: "string" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
        description: { type: "string" },
        report_set_id: { type: "number" },
        parent_id: { type: "number", nullable: true },
        level: { type: "number" },
        order: { type: "number" },
        code: { type: "string" },
        parent_code: { type: "string", nullable: true },
        is_active: { type: "boolean" },
        fraction_icon_id: { type: "number", nullable: true },
        has_second_level: { type: "boolean" },
        has_third_level: { type: "boolean" },
      },
    },
    example: {
      id: 1,
      name: "Fraction 1",
      created_at: "2021-01-01T00:00:00.000Z",
      updated_at: "2021-01-01T00:00:00.000Z",
      deleted_at: null,
      description: "Fraction 1 description",
      report_set_id: 1,
      parent_id: null,
      level: 1,
      order: 1,
      code: "1",
      parent_code: null,
      is_active: true,
      fraction_icon_id: null,
      has_second_level: false,
      has_third_level: false,
    },
  })
  @ApiResponse({ status: 404, description: "Report set fraction not found" })
  @ApiResponse({ status: 400, description: "Invalid report set fraction ID" })
  findOne(@Param("id") id: string) {
    return this.reportSetFractionsService.findOne(+id);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update report set fraction by ID" })
  @ApiResponse({
    status: 200,
    description: "Report set fraction retrieved successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        name: { type: "string" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
        description: { type: "string" },
        report_set_id: { type: "number" },
        parent_id: { type: "number", nullable: true },
        level: { type: "number" },
        order: { type: "number" },
        code: { type: "string" },
        parent_code: { type: "string", nullable: true },
        is_active: { type: "boolean" },
        fraction_icon_id: { type: "number", nullable: true },
        has_second_level: { type: "boolean" },
        has_third_level: { type: "boolean" },
      },
    },
    example: {
      id: 1,
      name: "Fraction 1",
      created_at: "2021-01-01T00:00:00.000Z",
      updated_at: "2021-01-01T00:00:00.000Z",
      deleted_at: null,
      description: "Fraction 1 description",
      report_set_id: 1,
      parent_id: null,
      level: 1,
      order: 1,
      code: "1",
      parent_code: null,
      is_active: true,
      fraction_icon_id: null,
      has_second_level: false,
      has_third_level: false,
    },
  })
  @ApiResponse({ status: 404, description: "Report set fraction not found" })
  @ApiResponse({ status: 400, description: "Invalid report set fraction ID" })
  update(@Param("id") id: string, @Body() data: UpdateReportSetFractionDto) {
    return this.reportSetFractionsService.update(+id, data);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete report set fraction by ID" })
  @ApiResponse({ status: 200, description: "Report set fraction deleted successfully" })
  @ApiResponse({ status: 404, description: "Report set fraction not found" })
  @ApiResponse({ status: 400, description: "Invalid report set fraction ID" })
  remove(@Param("id") id: string) {
    return this.reportSetFractionsService.remove(+id);
  }
}
