import { Test, TestingModule } from "@nestjs/testing";
import { OtherCostService } from "./other-cost.service";
import { DatabaseService } from "../database/database.service";
import { CreateOtherCostDto } from "./dto/other-cost-create.dto";
import { UpdateOtherCostDto } from "./dto/other-cost-update.dto";
import { BadRequestException, NotFoundException } from "@nestjs/common";

describe("OtherCostService", () => {
  let service: OtherCostService;
  let databaseService: DatabaseService;

  const databaseServiceMock = {
    otherCost: {
      create: jest.fn(),
      findMany: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
    },
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [OtherCostService, { provide: DatabaseService, useValue: databaseServiceMock }],
    }).compile();

    service = module.get<OtherCostService>(OtherCostService);
    databaseService = module.get<DatabaseService>(DatabaseService);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("create", () => {
    it("should create an other cost successfully", async () => {
      const createOtherCostDto: CreateOtherCostDto = {
        name: "Test Cost",
        price: 100,
        country_id: 1,
      };

      const mockResponse = {
        id: 1,
        ...createOtherCostDto,
      };

      databaseServiceMock.otherCost.create.mockReturnValueOnce(mockResponse);

      const result = await service.create(createOtherCostDto);
      expect(result).toEqual(mockResponse);
      expect(databaseServiceMock.otherCost.create).toHaveBeenCalledWith({
        data: createOtherCostDto,
      });
    });
  });

  describe("findAll", () => {
    it("should return a list of other costs", async () => {
      const mockResponse = [{ id: 1, name: "Test Cost", price: 100, country_id: 1 }];

      databaseServiceMock.otherCost.findMany.mockReturnValueOnce(mockResponse);

      const result = await service.findAll();
      expect(result).toEqual(mockResponse);
      expect(databaseServiceMock.otherCost.findMany).toHaveBeenCalledWith({
        where: { deleted_at: null },
      });
    });
  });

  describe("findOne", () => {
    it("should return an other cost by ID", async () => {
      const otherCostId = 1;
      const mockResponse = { id: otherCostId, name: "Test Cost", price: 100, country_id: 1 };

      databaseServiceMock.otherCost.findUnique.mockReturnValueOnce(mockResponse);

      const result = await service.findOne(otherCostId);
      expect(result).toEqual(mockResponse);
      expect(databaseServiceMock.otherCost.findUnique).toHaveBeenCalledWith({
        where: { id: otherCostId, deleted_at: null },
      });
    });

    it("should throw NotFoundException if other cost is not found", async () => {
      const otherCostId = 1;
      databaseServiceMock.otherCost.findUnique.mockReturnValueOnce(null);

      await expect(service.findOne(otherCostId)).rejects.toThrow(NotFoundException);
    });
  });

  describe("update", () => {
    it("should update an other cost successfully", async () => {
      const otherCostId = 1;
      const updateOtherCostDto: UpdateOtherCostDto = { name: "Updated Cost", price: 120 };
      const mockResponse = { id: otherCostId, ...updateOtherCostDto };

      databaseServiceMock.otherCost.findUnique.mockReturnValueOnce(mockResponse);
      databaseServiceMock.otherCost.update.mockReturnValueOnce(mockResponse);

      const result = await service.update(otherCostId, updateOtherCostDto);
      expect(result).toEqual(mockResponse);
      expect(databaseServiceMock.otherCost.findUnique).toHaveBeenCalledWith({
        where: { id: otherCostId },
      });
      expect(databaseServiceMock.otherCost.update).toHaveBeenCalledWith({
        where: { id: otherCostId },
        data: updateOtherCostDto,
      });
    });

    it("should throw NotFoundException if other cost is not found", async () => {
      const otherCostId = 1;
      const updateOtherCostDto: UpdateOtherCostDto = { name: "Updated Cost", price: 120 };

      databaseServiceMock.otherCost.findUnique.mockReturnValueOnce(null);

      await expect(service.update(otherCostId, updateOtherCostDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe("remove", () => {
    it("should remove an other cost successfully", async () => {
      const otherCostId = 1;
      const mockResponse = {
        id: otherCostId,
        name: "Test Cost",
        price: 100,
        country_id: 1,
        deleted_at: new Date(),
      };

      databaseServiceMock.otherCost.findUnique.mockReturnValueOnce(mockResponse);
      databaseServiceMock.otherCost.update.mockReturnValueOnce(mockResponse);

      const result = await service.remove(otherCostId);
      expect(result).toEqual(mockResponse);
      expect(databaseServiceMock.otherCost.update).toHaveBeenCalledWith({
        where: { id: otherCostId },
        data: { deleted_at: expect.any(Date) },
      });
    });

    it("should throw BadRequestException if ID is invalid", async () => {
      await expect(service.remove(NaN)).rejects.toThrow(BadRequestException);
    });

    it("should throw NotFoundException if other cost is not found", async () => {
      const otherCostId = 1;
      databaseServiceMock.otherCost.findUnique.mockReturnValueOnce(null);

      await expect(service.remove(otherCostId)).rejects.toThrow(NotFoundException);
    });
  });
});
