import { Body, Controller, Delete, Get, Param, Post, Put } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { OtherCostService } from "./other-cost.service";
import { CreateOtherCostDto } from "./dto/other-cost-create.dto";
import { UpdateOtherCostDto } from "./dto/other-cost-update.dto";
import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK)
@ApiTags("OtherCost")
@Controller("other-costs")
export class OtherCostController {
  constructor(private readonly otherCostService: OtherCostService) {}

  @Post()
  @ApiOperation({ summary: "Create a new other cost" })
  @ApiResponse({
    status: 201,
    description: "Other cost created successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        name: { type: "string" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
        country_id: { type: "number" },
        price: { type: "number" },
      },
    },
    example: {
      id: 1,
      name: "Other cost name",
      created_at: "2024-03-20T10:00:00Z",
      updated_at: "2024-03-20T10:00:00Z",
      deleted_at: null,
      country_id: 1,
      price: 100,
    },
  })
  @ApiResponse({ status: 400, description: "Bad Request" })
  @ApiResponse({ status: 404, description: "Country not found" })
  create(@Body() data: CreateOtherCostDto) {
    return this.otherCostService.create(data);
  }

  @Get()
  @ApiOperation({ summary: "Get all other costs" })
  @ApiResponse({
    status: 200,
    description: "Other costs retrieved successfully",
    schema: {
      type: "array",
      items: {
        type: "object",
        properties: {
          id: { type: "number" },
          name: { type: "string" },
          created_at: { type: "string", format: "date-time" },
          updated_at: { type: "string", format: "date-time" },
          deleted_at: { type: "string", format: "date-time", nullable: true },
          country_id: { type: "number" },
          price: { type: "number" },
        },
      },
      example: [
        {
          id: 1,
          name: "Other cost name",
          created_at: "2024-03-20T10:00:00Z",
          updated_at: "2024-03-20T10:00:00Z",
          deleted_at: null,
          country_id: 1,
          price: 100,
        },
      ],
    },
  })
  findAll() {
    return this.otherCostService.findAll();
  }

  @Get(":id")
  @ApiOperation({ summary: "Get other cost by ID" })
  @ApiResponse({
    status: 200,
    description: "Other cost retrieved successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        name: { type: "string" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
        country_id: { type: "number" },
        price: { type: "number" },
      },
    },
    example: {
      id: 1,
      name: "Other cost name",
      created_at: "2024-03-20T10:00:00Z",
      updated_at: "2024-03-20T10:00:00Z",
      deleted_at: null,
      country_id: 1,
      price: 100,
    },
  })
  @ApiResponse({ status: 404, description: "Other cost not found" })
  @ApiResponse({ status: 400, description: "Invalid other cost ID" })
  findOne(@Param("id") id: string) {
    return this.otherCostService.findOne(+id);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update other cost by ID" })
  @ApiResponse({
    status: 200,
    description: "Other cost updated successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        name: { type: "string" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
        country_id: { type: "number" },
        price: { type: "number" },
      },
    },
    example: {
      id: 1,
      name: "Other cost name",
      created_at: "2024-03-20T10:00:00Z",
      updated_at: "2024-03-20T10:00:00Z",
      deleted_at: null,
      country_id: 1,
      price: 100,
    },
  })
  @ApiResponse({ status: 404, description: "Other cost not found" })
  @ApiResponse({ status: 400, description: "Invalid other cost ID" })
  update(@Param("id") id: string, @Body() data: UpdateOtherCostDto) {
    return this.otherCostService.update(+id, data);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete other cost by ID" })
  @ApiResponse({ status: 200, description: "Other cost deleted successfully" })
  @ApiResponse({ status: 404, description: "Other cost not found" })
  @ApiResponse({ status: 400, description: "Invalid other cost ID" })
  remove(@Param("id") id: string) {
    return this.otherCostService.remove(+id);
  }
}
