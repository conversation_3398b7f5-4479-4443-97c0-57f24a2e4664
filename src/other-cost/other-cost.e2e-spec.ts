import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { AppModule } from "../app.module";
import { Role } from "../shared/auth/role.enum";
import { HEADER_SYSTEM_API_KEY, HEADER_USER_ID, HEADER_USER_ROLE, HEADER_USER_EMAIL } from "../shared/auth/const";
import { DatabaseService } from "../database/database.service";

jest.setTimeout(30000);

describe("OtherCostsController (e2e)", () => {
  let app: INestApplication;
  let databaseService: DatabaseService;

  const validApiKey = "test-api-key";

  beforeAll(() => {
    process.env.SYSTEM_API_KEY = validApiKey;
  });

  const authHeaders = {
    [HEADER_SYSTEM_API_KEY]: validApi<PERSON>ey,
    [HEADER_USER_ID]: "1",
    [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
    [HEADER_USER_EMAIL]: "<EMAIL>",
  };

  const mockOtherCost = {
    id: 1,
    name: "Processing Fee",
    price: 100,
    country_id: 1,
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
    deleted_at: null,
  };

  const mockOtherCosts = [
    mockOtherCost,
    {
      id: 2,
      name: "Registration Fee",
      price: 200,
      country_id: 1,
      created_at: "2025-04-07T22:58:17.004Z",
      updated_at: "2025-04-07T22:58:17.004Z",
      deleted_at: null,
    },
  ];

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(DatabaseService)
      .useValue({
        otherCost: {
          create: jest.fn().mockResolvedValue(mockOtherCost),
          findMany: jest.fn().mockResolvedValue(mockOtherCosts),
          findUnique: jest.fn().mockResolvedValue(mockOtherCost),
          update: jest.fn().mockImplementation((params) => {
            return Promise.resolve({
              ...mockOtherCost,
              ...params.data,
            });
          }),
        },
      })
      .compile();

    app = moduleFixture.createNestApplication();
    databaseService = moduleFixture.get<DatabaseService>(DatabaseService);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("/other-costs (GET)", () => {
    it("should return other costs when authenticated as SUPER_ADMIN with valid API key", () => {
      return request(app.getHttpServer())
        .get("/other-costs")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.otherCost.findMany).toHaveBeenCalled();
          expect(response.body).toEqual(mockOtherCosts);
        });
    });

    it("should return other costs when authenticated as ADMIN with valid API key", () => {
      const adminHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.ADMIN,
      };

      return request(app.getHttpServer()).get("/other-costs").set(adminHeaders).expect(200);
    });

    it("should return other costs when authenticated as CLERK with valid API key", () => {
      const clerkHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CLERK,
      };

      return request(app.getHttpServer()).get("/other-costs").set(clerkHeaders).expect(200);
    });

    it("should reject when authenticated with unauthorized role even with valid API key", () => {
      const unauthorizedHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/other-costs").set(unauthorizedHeaders).expect(403);
    });

    it("should reject when API key is invalid regardless of role", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/other-costs").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject when not authenticated at all", () => {
      return request(app.getHttpServer()).get("/other-costs").expect(401);
    });
  });

  describe("/other-costs/:id (GET)", () => {
    it("should return a single other cost when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .get("/other-costs/1")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.otherCost.findUnique).toHaveBeenCalledWith({
            where: { id: 1, deleted_at: null },
          });
          expect(response.body).toEqual(mockOtherCost);
        });
    });

    it("should reject when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/other-costs/1").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject when not authenticated", () => {
      return request(app.getHttpServer()).get("/other-costs/1").expect(401);
    });
  });

  describe("/other-costs (POST)", () => {
    const createOtherCostDto = {
      name: "Administration Fee",
      price: 150,
      country_id: 1,
    };

    it("should create a new other cost when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .post("/other-costs")
        .set(authHeaders)
        .send(createOtherCostDto)
        .expect(201)
        .then((response) => {
          expect(databaseService.otherCost.create).toHaveBeenCalledWith({
            data: createOtherCostDto,
          });
          expect(response.body).toEqual(mockOtherCost);
        });
    });

    it("should reject creation when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .post("/other-costs")
        .set(invalidApiKeyHeaders)
        .send(createOtherCostDto)
        .expect(401);
    });

    it("should reject creation when not authenticated", () => {
      return request(app.getHttpServer()).post("/other-costs").send(createOtherCostDto).expect(401);
    });
  });

  describe("/other-costs/:id (PUT)", () => {
    const updateOtherCostDto = {
      name: "Updated Processing Fee",
      price: 120,
    };

    it("should update an other cost when authenticated with valid API key", () => {
      const expectedResponse = {
        ...mockOtherCost,
        ...updateOtherCostDto,
      };

      return request(app.getHttpServer())
        .put("/other-costs/1")
        .set(authHeaders)
        .send(updateOtherCostDto)
        .expect(200)
        .then((response) => {
          expect(databaseService.otherCost.update).toHaveBeenCalledWith({
            where: { id: 1 },
            data: updateOtherCostDto,
          });
          expect(response.body).toEqual(expectedResponse);
        });
    });

    it("should reject update when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .put("/other-costs/1")
        .set(invalidApiKeyHeaders)
        .send(updateOtherCostDto)
        .expect(401);
    });

    it("should reject update when not authenticated", () => {
      return request(app.getHttpServer()).put("/other-costs/1").send(updateOtherCostDto).expect(401);
    });
  });

  describe("/other-costs/:id (DELETE)", () => {
    it("should reject deletion when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).delete("/other-costs/1").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject deletion when not authenticated", () => {
      return request(app.getHttpServer()).delete("/other-costs/1").expect(401);
    });
  });

  describe("System role access", () => {
    it("should allow access with SYSTEM role and valid API key", () => {
      const systemHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/other-costs").set(systemHeaders).expect(200);
    });

    it("should reject access with SYSTEM role but invalid API key", () => {
      const invalidSystemHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/other-costs").set(invalidSystemHeaders).expect(401);
    });
  });
});
