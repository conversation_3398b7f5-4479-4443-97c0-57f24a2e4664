import { Test, TestingModule } from "@nestjs/testing";
import { AdminService } from "./admin.service";
import { HttpModuleService } from "../http/http.service";
import { DatabaseService } from "../database/database.service";
import { CreateAdminDto } from "./dto/admin-create.dto";
import { FindAllAdminDto } from "./dto/admin-find-all";
import { UpdateAdminDto } from "./dto/admin-update.dto";
import { of } from "rxjs";

describe("AdminService", () => {
  let service: AdminService;
  let httpModuleService: HttpModuleService;

  const httpModuleServiceMock = {
    admin: jest.fn(),
    auth: jest.fn(),
  };

  beforeAll(() => {
    jest.spyOn(console, "error").mockImplementation(() => {});
    jest.spyOn(console, "log").mockImplementation(() => {});
  });

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminService,
        { provide: HttpModuleService, useValue: httpModuleServiceMock },
        { provide: DatabaseService, useValue: {} },
      ],
    }).compile();

    service = module.get<AdminService>(AdminService);
    httpModuleService = module.get<HttpModuleService>(HttpModuleService);
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe("create", () => {
    it("should create an admin successfully", async () => {
      const createAdminDto: CreateAdminDto = {
        email: "<EMAIL>",
        name: "Test User",
        password: "securepassword",
        role_id: 1,
      };

      const mockResponse = { id: 1, ...createAdminDto, is_active: true, type: "ADMIN" };
      httpModuleServiceMock.admin.mockReturnValueOnce(of({ data: mockResponse }));

      const result = await service.create(createAdminDto);
      expect(result).toEqual(mockResponse);
      expect(httpModuleServiceMock.admin).toHaveBeenCalledWith({
        url: "/user",
        params: {
          email: createAdminDto.email,
          name: createAdminDto.name,
          password: createAdminDto.password,
          is_active: true,
          type: "ADMIN",
          role_id: createAdminDto.role_id,
        },
        method: "post",
      });
    });
  });

  describe("findAll", () => {
    it("should return a list of admins", async () => {
      const query: FindAllAdminDto = { role: "ADMIN", is_active: "true" };
      const mockResponse = [{ id: 1, name: "Admin User", role: "ADMIN" }];
      httpModuleServiceMock.auth.mockReturnValueOnce(of({ data: mockResponse }));

      const result = await service.findAll(query);
      expect(result).toEqual(mockResponse);
      expect(httpModuleServiceMock.auth).toHaveBeenCalledWith({
        url: "/user",
        params: { role: "ADMIN", is_active: "true" },
        method: "GET",
      });
    });
  });

  describe("findOne", () => {
    it("should return an admin by ID", async () => {
      const mockResponse = { id: 1, name: "Admin User" };
      httpModuleServiceMock.auth.mockReturnValueOnce(of({ data: mockResponse }));

      const result = await service.findOne(1);
      expect(result).toEqual(mockResponse);
      expect(httpModuleServiceMock.auth).toHaveBeenCalledWith({
        url: "/user/1",
        method: "GET",
        params: {},
      });
    });
  });

  describe("update", () => {
    it("should update an admin successfully", async () => {
      const updateAdminDto: UpdateAdminDto = { name: "Updated Admin" };
      const mockResponse = { id: 1, ...updateAdminDto };
      httpModuleServiceMock.admin.mockReturnValueOnce(of({ data: mockResponse }));

      const result = await service.update(1, updateAdminDto);
      expect(result).toEqual(mockResponse);
      expect(httpModuleServiceMock.admin).toHaveBeenCalledWith({
        url: "/user/1",
        params: updateAdminDto,
        method: "patch",
      });
    });
  });

  describe("remove", () => {
    it("should delete an admin successfully", async () => {
      const mockResponse = { message: "Deleted successfully" };
      httpModuleServiceMock.admin.mockReturnValueOnce(of({ data: mockResponse }));

      const result = await service.remove(1);
      expect(result).toEqual(mockResponse);
      expect(httpModuleServiceMock.admin).toHaveBeenCalledWith({
        url: "/user/1",
        params: { type: "ADMIN" },
        method: "delete",
      });
    });
  });
});
