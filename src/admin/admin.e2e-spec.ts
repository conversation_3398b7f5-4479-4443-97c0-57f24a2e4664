import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { AppModule } from "../app.module";
import { Role } from "../shared/auth/role.enum";
import { HEADER_SYSTEM_API_KEY, HEADER_USER_ID, HEADER_USER_ROLE, HEADER_USER_EMAIL } from "../shared/auth/const";
import { HttpModuleService } from "../http/http.service";
import { of } from "rxjs";
import { DatabaseService } from "@/database/database.service";

jest.setTimeout(30000);

describe("AdminController (e2e)", () => {
  let app: INestApplication;
  let httpModuleService: HttpModuleService;
  let databaseService: DatabaseService;

  const validApiKey = "test-api-key";

  beforeAll(() => {
    process.env.SYSTEM_API_KEY = validApiKey;
  });

  const authHeaders = {
    [HEADER_SYSTEM_API_KEY]: validApiKey,
    [HEADER_USER_ID]: "1",
    [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
    [HEADER_USER_EMAIL]: "<EMAIL>",
  };

  const mockAdminUser = {
    id: 1,
    email: "<EMAIL>",
    name: "Admin User",
    is_active: true,
    type: "ADMIN",
    role_id: 1,
  };

  const mockAdminList = {
    data: [
      mockAdminUser,
      {
        id: 2,
        email: "<EMAIL>",
        name: "Clerk User",
        is_active: true,
        type: "ADMIN",
        role_id: 2,
      },
    ],
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(HttpModuleService)
      .useValue({
        admin: jest.fn(),
        auth: jest.fn(),
      })
      .overrideProvider(DatabaseService)
      .useValue({
        admin: {
          create: jest.fn().mockResolvedValue(mockAdminUser),
          findMany: jest.fn().mockResolvedValue([mockAdminUser]),
          findUnique: jest.fn().mockResolvedValue(mockAdminUser),
          update: jest.fn().mockImplementation((params) => {
            return Promise.resolve({
              ...mockAdminUser,
              ...params.data,
            });
          }),
          delete: jest.fn().mockResolvedValue(mockAdminUser),
        },
      })
      .compile();

    app = moduleFixture.createNestApplication();
    httpModuleService = moduleFixture.get<HttpModuleService>(HttpModuleService);

    jest.spyOn(httpModuleService, "auth").mockImplementation((config): any => {
      if (config.url === "/user" && config.method === "GET") {
        return of(mockAdminList);
      }

      if (config.url.includes("/user/") && config.method === "GET") {
        return of({ data: mockAdminUser });
      }

      return of({ data: {} });
    });

    jest.spyOn(httpModuleService, "admin").mockImplementation((config): any => {
      if (config.url === "/user" && config.method === "post") {
        return of({
          data: {
            id: 3,
            ...config.params,
            is_active: true,
            type: "ADMIN",
          },
        });
      }

      if (config.url.includes("/user/") && config.method === "patch") {
        return of({
          data: {
            ...mockAdminUser,
            ...config.params,
          },
        });
      }

      if (config.url.includes("/user/") && config.method === "delete") {
        return of({ data: { success: true } });
      }

      return of({ data: {} });
    });

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("/admin (GET)", () => {
    it("should return admin users when authenticated as SUPER_ADMIN with valid API key", () => {
      return request(app.getHttpServer())
        .get("/admin")
        .set(authHeaders)
        .query({ role: "ADMIN,CLERK" })
        .expect(200)
        .then((response) => {
          expect(httpModuleService.auth).toHaveBeenCalled();
          expect(response.body).toEqual(mockAdminList.data);
        });
    });

    it("should return admin users when authenticated as ADMIN with valid API key", () => {
      const adminHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.ADMIN,
      };

      return request(app.getHttpServer()).get("/admin").set(adminHeaders).query({ role: "ADMIN,CLERK" }).expect(200);
    });

    it("should return admin users when authenticated as CLERK with valid API key", () => {
      const clerkHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CLERK,
      };

      return request(app.getHttpServer()).get("/admin").set(clerkHeaders).query({ role: "ADMIN,CLERK" }).expect(200);
    });

    it("should reject when authenticated with unauthorized role even with valid API key", () => {
      const unauthorizedHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .get("/admin")
        .set(unauthorizedHeaders)
        .query({ role: "ADMIN,CLERK" })
        .expect(403);
    });

    it("should reject when API key is invalid regardless of role", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .get("/admin")
        .set(invalidApiKeyHeaders)
        .query({ role: "ADMIN,CLERK" })
        .expect(401);
    });

    it("should reject when API key is missing regardless of role", () => {
      const noApiKeyHeaders = {
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/admin").set(noApiKeyHeaders).query({ role: "ADMIN,CLERK" }).expect(401);
    });

    it("should reject when not authenticated at all", () => {
      return request(app.getHttpServer()).get("/admin").query({ role: "ADMIN,CLERK" }).expect(401);
    });
  });

  describe("/admin/:id (GET)", () => {
    it("should return a single admin user when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .get("/admin/1")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(httpModuleService.auth).toHaveBeenCalled();
          expect(response.body).toEqual(mockAdminUser);
        });
    });

    it("should reject when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/admin/1").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject when not authenticated", () => {
      return request(app.getHttpServer()).get("/admin/1").expect(401);
    });
  });

  describe("/admin (POST)", () => {
    const createAdminDto = {
      email: "<EMAIL>",
      name: "New Admin",
      password: "password123",
      role_id: 1,
    };

    it("should create a new admin user when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .post("/admin")
        .set(authHeaders)
        .send(createAdminDto)
        .expect(201)
        .then((response) => {
          expect(httpModuleService.admin).toHaveBeenCalled();
          expect(response.body).toEqual(
            expect.objectContaining({
              id: expect.any(Number),
              email: createAdminDto.email,
              name: createAdminDto.name,
              is_active: true,
              type: "ADMIN",
              role_id: createAdminDto.role_id,
            })
          );
        });
    });

    it("should reject creation when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).post("/admin").set(invalidApiKeyHeaders).send(createAdminDto).expect(401);
    });

    it("should reject creation when not authenticated", () => {
      return request(app.getHttpServer()).post("/admin").send(createAdminDto).expect(401);
    });
  });

  describe("/admin/:id (PATCH)", () => {
    const updateAdminDto = {
      name: "Updated Admin Name",
    };

    it("should update an admin user when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .patch("/admin/1")
        .set(authHeaders)
        .send(updateAdminDto)
        .expect(200)
        .then((response) => {
          expect(httpModuleService.admin).toHaveBeenCalled();
          expect(response.body).toEqual(
            expect.objectContaining({
              id: mockAdminUser.id,
              name: updateAdminDto.name,
            })
          );
        });
    });

    it("should reject update when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).patch("/admin/1").set(invalidApiKeyHeaders).send(updateAdminDto).expect(401);
    });

    it("should reject update when not authenticated", () => {
      return request(app.getHttpServer()).patch("/admin/1").send(updateAdminDto).expect(401);
    });
  });

  describe("/admin/:id (DELETE)", () => {
    it("should delete an admin user when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .delete("/admin/1")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(httpModuleService.admin).toHaveBeenCalled();
          expect(response.body).toEqual({ success: true });
        });
    });

    it("should reject deletion when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).delete("/admin/1").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject deletion when not authenticated", () => {
      return request(app.getHttpServer()).delete("/admin/1").expect(401);
    });
  });

  describe("System role access", () => {
    it("should allow access with SYSTEM role and valid API key", () => {
      const systemHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/admin").set(systemHeaders).query({ role: "ADMIN" }).expect(200);
    });

    it("should reject access with SYSTEM role but invalid API key", () => {
      const invalidSystemHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/admin").set(invalidSystemHeaders).query({ role: "ADMIN" }).expect(401);
    });

    it("should reject when user role is missing even with valid API key", () => {
      const missingRoleHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .get("/admin")
        .set(missingRoleHeaders)
        .query({ role: "ADMIN,CLERK" })
        .expect(403);
    });

    it("should reject when user ID is missing even with valid API key and role", () => {
      const missingIdHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .get("/admin")
        .set(missingIdHeaders)
        .query({ role: "ADMIN,CLERK" })
        .expect(401);
    });
  });
});
