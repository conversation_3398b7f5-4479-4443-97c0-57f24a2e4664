import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";
import { Body, Controller, Delete, Get, Param, Patch, Post, Query } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { AdminService } from "./admin.service";
import { CreateAdminDto } from "./dto/admin-create.dto";
import { FindAllAdminDto } from "./dto/admin-find-all";
import { UpdateAdminDto } from "./dto/admin-update.dto";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK)
@ApiTags("Admin")
@Controller("admin")
export class AdminController {
  constructor(private readonly adminService: AdminService) {}

  @Post()
  @ApiOperation({ summary: "Create admin user" })
  @ApiResponse({
    status: 200,
    description: "Admin user created successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "string" },
        email: { type: "string" },
        name: { type: "string" },
        Role: {
          type: "object",
          properties: {
            id: { type: "number" },
            name: { type: "string" },
            display_name: { type: "string" },
          },
        },
        type: { type: "string" },
        is_active: { type: "boolean" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
      },
    },
    example: {
      id: "1",
      email: "<EMAIL>",
      name: "Admin User",
      Role: { id: 9, name: "Admin", display_name: "Admin" },
      type: "ADMIN",
      is_active: true,
      created_at: "2021-01-01T00:00:00.000Z",
      updated_at: "2021-01-01T00:00:00.000Z",
    },
  })
  @ApiResponse({ status: 500, description: "Failed to create admin user" })
  create(@Body() createAdminDto: CreateAdminDto) {
    return this.adminService.create(createAdminDto);
  }

  @Get()
  @ApiOperation({ summary: "Get all admin users" })
  @ApiResponse({
    status: 200,
    description: "Admin users retrieved successfully",
    schema: {
      type: "array",
      items: {
        type: "object",
        properties: {
          id: { type: "string" },
          email: { type: "string" },
          name: { type: "string" },
          Role: {
            type: "object",
            properties: {
              id: { type: "number" },
              name: { type: "string" },
              display_name: { type: "string" },
            },
          },
          type: { type: "string" },
          is_active: { type: "boolean" },
          created_at: { type: "string", format: "date-time" },
          updated_at: { type: "string", format: "date-time" },
        },
      },
    },
    example: [
      {
        id: "1",
        email: "<EMAIL>",
        name: "Admin User",
        Role: { id: 9, name: "Admin", display_name: "Admin" },
        type: "ADMIN",
        is_active: true,
        created_at: "2021-01-01T00:00:00.000Z",
        updated_at: "2021-01-01T00:00:00.000Z",
      },
    ],
  })
  @ApiResponse({ status: 500, description: "Failed to retrieve admin users" })
  findAll(@Query() query: FindAllAdminDto) {
    return this.adminService.findAll(query);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get admin user by ID" })
  @ApiResponse({
    status: 200,
    description: "Admin user retrieved successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "string" },
        email: { type: "string" },
        name: { type: "string" },
        Role: {
          type: "object",
          properties: {
            id: { type: "number" },
            name: { type: "string" },
            display_name: { type: "string" },
          },
        },
        type: { type: "string" },
        is_active: { type: "boolean" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
      },
    },
    example: {
      id: "1",
      email: "<EMAIL>",
      name: "Admin User",
      Role: { id: 9, name: "Admin", display_name: "Admin" },
      type: "ADMIN",
      is_active: true,
      created_at: "2021-01-01T00:00:00.000Z",
      updated_at: "2021-01-01T00:00:00.000Z",
    },
  })
  @ApiResponse({ status: 404, description: "Admin user not found" })
  @ApiResponse({ status: 500, description: "Failed to retrieve admin user" })
  findOne(@Param("id") id: string) {
    return this.adminService.findOne(+id);
  }

  @Patch(":id")
  @ApiOperation({ summary: "Update admin user by ID" })
  @ApiResponse({
    status: 200,
    description: "Admin user updated successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "string" },
        email: { type: "string" },
        name: { type: "string" },
        Role: {
          type: "object",
          properties: {
            id: { type: "number" },
            name: { type: "string" },
            display_name: { type: "string" },
          },
        },
        type: { type: "string" },
        is_active: { type: "boolean" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
      },
    },
    example: {
      id: "1",
      email: "<EMAIL>",
      name: "Admin User",
      Role: { id: 9, name: "Admin", display_name: "Admin" },
      type: "ADMIN",
      is_active: true,
      created_at: "2021-01-01T00:00:00.000Z",
      updated_at: "2021-01-01T00:00:00.000Z",
    },
  })
  @ApiResponse({ status: 404, description: "Admin user not found" })
  @ApiResponse({ status: 500, description: "Failed to update admin user" })
  update(@Param("id") id: string, @Body() updateAdminDto: UpdateAdminDto) {
    return this.adminService.update(+id, updateAdminDto);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete admin user by ID" })
  @ApiResponse({
    status: 200,
    description: "Admin user deleted successfully",
  })
  @ApiResponse({ status: 404, description: "Admin user not found" })
  @ApiResponse({ status: 500, description: "Failed to delete admin user" })
  remove(@Param("id") id: string) {
    return this.adminService.remove(+id);
  }
}
