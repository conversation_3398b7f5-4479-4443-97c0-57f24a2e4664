import { ApiProperty } from "@nestjs/swagger";

export class CreateAdminDto {
  @ApiProperty({
    required: true,
    description: "Action performed",
  })
  email: string;

  @ApiProperty({
    required: true,
    description: "Name of the entity involved",
  })
  name: string;

  @ApiProperty({
    required: true,
    description: "Password of the entity involved",
  })
  password: string;

  @ApiProperty({
    required: true,
    description: "Role ID of the entity involved",
  })
  role_id: number;
}
