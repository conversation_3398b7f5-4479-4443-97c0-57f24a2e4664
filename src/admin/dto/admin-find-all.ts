import { ApiProperty } from "@nestjs/swagger";

export class FindAllAdminDto {
  @ApiProperty({
    required: true,
    description: "Role of the admin",
    example: "ADMIN,CLERK",
  })
  role: string;

  @ApiProperty({
    required: false,
    description: "User is active",
    example: "true",
  })
  is_active?: string;

  @ApiProperty({
    required: false,
    description: "Name of the user",
    example: "John Doe",
  })
  name?: string;
}
