import { Injectable } from "@nestjs/common";
// import { DatabaseService } from "../database/database.service";
import {
  // UploadFileDto,
  UploadFileResponse,
} from "./dto/upload-file.dto";

@Injectable()
export class FileUploadService {
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  constructor() {} // private prisma: DatabaseService

  // private bucketName = "";

  async uploadFile(): // dto: UploadFileDto
  Promise<UploadFileResponse> {
    return {
      url: "",
    };
  }
}
