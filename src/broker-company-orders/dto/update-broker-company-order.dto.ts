import { IsInt, <PERSON>Optional, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsDateString } from "class-validator";
import { BrokerCompanyOrderStatus, BrokerCompanyOrderType } from "@prisma/client";
import { ApiPropertyOptional } from "@nestjs/swagger";

export class UpdateBrokerCompanyOrderDto {
  @ApiPropertyOptional({ description: "Customer number" })
  @IsOptional()
  @IsString()
  customer_number?: string;

  @ApiPropertyOptional({ description: "Company ID" })
  @IsOptional()
  @IsInt()
  company_id?: number;

  @ApiPropertyOptional({ description: "Transfer date in ISO format" })
  @IsOptional()
  @IsDateString()
  transfer_date?: string;

  @ApiPropertyOptional({ description: "Order number" })
  @IsOptional()
  @IsString()
  order_number?: string;

  @ApiPropertyOptional({ description: "Year of the order" })
  @IsOptional()
  @IsInt()
  year?: number;

  @ApiPropertyOptional({ description: "List of fractions", type: [Object] })
  @IsOptional()
  @IsArray()
  fractions?: any[];

  @ApiPropertyOptional({ enum: BrokerCompanyOrderStatus, description: "Order status" })
  @IsOptional()
  @IsEnum(BrokerCompanyOrderStatus)
  status?: BrokerCompanyOrderStatus;

  @ApiPropertyOptional({ enum: BrokerCompanyOrderType, description: "Order type" })
  @IsOptional()
  @IsEnum(BrokerCompanyOrderType)
  type?: BrokerCompanyOrderType;

  @ApiPropertyOptional({ description: "Net value of the order" })
  @IsOptional()
  @IsInt()
  net_value?: number;
}
