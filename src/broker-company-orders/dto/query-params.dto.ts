import { ApiPropertyOptional } from "@nestjs/swagger";
import { IsInt, IsOptional, IsString } from "class-validator";
import { PaginationDto } from "../../shared/dto/pagination.dto";
import { Type } from "class-transformer";

export class QueryParams extends PaginationDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: "Broker ID", example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  companyId?: number;

  @ApiPropertyOptional({ description: "Broker ID", example: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  brokerId?: number;
}
