import { Modu<PERSON> } from "@nestjs/common";
import { BrokerCompanyOrdersService } from "./broker-company-orders.service";
import { BrokerCompanyOrdersController } from "./broker-company-orders.controller";
import { DatabaseModule } from "../database/database.module";
import { UploadDataService } from "../upload-data/upload-data.service";

@Module({
  imports: [DatabaseModule],
  controllers: [BrokerCompanyOrdersController],
  providers: [BrokerCompanyOrdersService, UploadDataService],
})
export class BrokerCompanyOrdersModule {}
