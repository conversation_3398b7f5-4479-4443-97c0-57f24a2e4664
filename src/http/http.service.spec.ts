import { HttpException, HttpStatus } from "@nestjs/common";
import { Test, TestingModule } from "@nestjs/testing";
import { HttpModuleService } from "./http.service";
import { throwError } from "rxjs";

describe("HttpModuleService", () => {
  let service: HttpModuleService;
  let httpService: { request: jest.Mock };

  beforeAll(() => {
    jest.spyOn(console, "error").mockImplementation(() => {});
    jest.spyOn(console, "log").mockImplementation(() => {});
  });

  beforeEach(async () => {
    jest.clearAllMocks();
    httpService = { request: jest.fn() };
    const module: TestingModule = await Test.createTestingModule({
      providers: [{ provide: HttpModuleService, useValue: httpService }],
    }).compile();

    service = module.get<HttpModuleService>(HttpModuleService);
  });

  afterAll(() => {
    jest.restoreAllMocks();
  });

  describe("subscriptions", () => {
    it("should throw an HttpException on error", (done) => {
      jest
        .spyOn(httpService, "request")
        .mockReturnValue(throwError(() => new HttpException("Error", HttpStatus.INTERNAL_SERVER_ERROR)));

      httpService.request({ url: "/test", params: {}, method: "POST" }).subscribe({
        next: () => done.fail("Expected an error, but got a response"),
        error: (err: any) => {
          expect(err).toBeInstanceOf(HttpException);
          expect(err.getStatus()).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
          done();
        },
      });
    });
  });

  describe("auth", () => {
    it("should handle API errors correctly", (done) => {
      jest
        .spyOn(httpService, "request")
        .mockReturnValue(throwError(() => new HttpException("Unauthorized", HttpStatus.UNAUTHORIZED)));

      httpService.request({ url: "/test", params: {}, method: "POST" }).subscribe({
        next: () => done.fail("Expected an error, but got a response"),
        error: (err: any) => {
          expect(err).toBeInstanceOf(HttpException);
          expect(err.getStatus()).toBe(HttpStatus.UNAUTHORIZED);
          done();
        },
      });
    });
  });
});
