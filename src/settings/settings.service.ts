import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { DatabaseService } from "@/database/database.service";
import { UpsertSettingDto } from "./dto/upsert-setting.dto";

@Injectable()
export class SettingsService {
  constructor(private readonly prisma: DatabaseService) {}

  findAll() {
    return this.prisma.settings.findMany();
  }

  findOne(key: string) {
    const setting = this.prisma.settings.findUnique({
      where: { key },
      include: { file: true },
    });

    if (!setting) {
      throw new NotFoundException("Setting not found");
    }

    return setting;
  }

  async upsert(key: string, dto: UpsertSettingDto) {
    if (!key) {
      throw new BadRequestException("Key is required");
    }

    const existingSetting = await this.prisma.settings.findUnique({
      where: { key: key },
    });

    try {
      JSON.parse(dto.value);
    } catch (error) {
      throw new BadRequestException("Invalid value");
    }

    if (existingSetting) {
      return this.prisma.settings.update({
        where: { id: existingSetting.id },
        data: dto,
        include: { file: true },
      });
    }

    return this.prisma.settings.create({
      data: {
        key,
        value: dto.value,
        term_or_condition_file_id: dto.term_or_condition_file_id,
      },
      include: { file: true },
    });
  }

  async remove(key: string) {
    const setting = this.prisma.settings.findUnique({
      where: { key },
    });

    if (!setting) {
      throw new NotFoundException("Setting not found");
    }

    await this.prisma.settings.delete({
      where: { key },
    });
  }
}
