import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { DatabaseService } from "../database/database.service";
import { CreateFractionIconDto } from "./dto/fraction-icon-create.dto";

@Injectable()
export class FractionIconService {
  constructor(private readonly databaseService: DatabaseService) {}

  async create(data: CreateFractionIconDto) {
    if (!data.file_id) throw new BadRequestException("File ID is required");

    const file = await this.databaseService.files.findUnique({
      where: { id: data.file_id },
    });

    if (!file) throw new NotFoundException("File not found");

    return this.databaseService.fractionIcon.create({
      data: {
        file_id: data.file_id,
        image_url: `https://liz-generic-files.s3.us-east-2.amazonaws.com/${file.name}`,
      },
      include: {
        file: true,
      },
    });
  }

  async findAll() {
    return this.databaseService.fractionIcon.findMany({ where: { deleted_at: null }, include: { file: true } });
  }

  async findOne(id: number) {
    const fractionIcon = await this.databaseService.fractionIcon.findUnique({
      where: { id, deleted_at: null },
      include: { file: true },
    });

    if (!fractionIcon) {
      throw new NotFoundException("Fraction icon not found");
    }

    return fractionIcon;
  }

  async remove(id: number) {
    if (!id || Number.isNaN(Number(id))) throw new BadRequestException("Invalid fraction icon ID");

    const fractionIcon = await this.databaseService.fractionIcon.findUnique({
      where: { id },
    });

    if (!fractionIcon) {
      throw new NotFoundException("Fraction icon not found");
    }

    await this.databaseService.fractionIcon.update({
      where: { id },
      data: { deleted_at: new Date() },
    });
  }
}
