import { Test, TestingModule } from "@nestjs/testing";
import { CountryPriceListService } from "./country-price-list.service";
import { DatabaseService } from "../database/database.service";
import { BadRequestException, NotFoundException } from "@nestjs/common";

const mockDatabaseService = {
  country: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  countryFollower: {
    createMany: jest.fn(),
    findFirst: jest.fn(),
    deleteMany: jest.fn(),
  },
  countryPriceList: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
  },
};

describe("CountryPriceListService", () => {
  let service: CountryPriceListService;
  let databaseService: typeof mockDatabaseService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CountryPriceListService, { provide: DatabaseService, useValue: mockDatabaseService }],
    }).compile();

    service = module.get<CountryPriceListService>(CountryPriceListService);
    databaseService = module.get(DatabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("create", () => {
    it("should create a country price list", async () => {
      const dto = { country_id: 1, price_list_id: 2 };
      databaseService.countryPriceList.create.mockResolvedValue(dto);

      const result = await service.create(dto);
      expect(result).toEqual(dto);
      expect(databaseService.countryPriceList.create).toHaveBeenCalledWith({ data: dto });
    });
  });

  describe("findAll", () => {
    it("should return all country price lists", async () => {
      const mockData = [{ id: 1, country_id: 1, price_list_id: 2, deleted_at: null }];
      databaseService.countryPriceList.findMany.mockResolvedValue(mockData);

      const result = await service.findAll();
      expect(result).toEqual(mockData);
    });
  });

  describe("findOne", () => {
    it("should return a country price list", async () => {
      const mockData = { id: 1, country_id: 1, price_list_id: 2, deleted_at: null };
      databaseService.countryPriceList.findUnique.mockResolvedValue(mockData);

      const result = await service.findOne(1);
      expect(result).toEqual(mockData);
    });

    it("should throw NotFoundException if country price list not found", async () => {
      databaseService.countryPriceList.findUnique.mockResolvedValue(null);
      await expect(service.findOne(1)).rejects.toThrow(NotFoundException);
    });
  });

  describe("remove", () => {
    it("should soft delete a country price list", async () => {
      const mockData = { id: 1, country_id: 1, price_list_id: 2, deleted_at: null };
      databaseService.countryPriceList.findUnique.mockResolvedValue(mockData);
      databaseService.countryPriceList.update.mockResolvedValue({ ...mockData, deleted_at: new Date() });

      const result = await service.remove(1);
      expect(result).toBeDefined();
    });

    it("should throw BadRequestException if ID is invalid", async () => {
      await expect(service.remove(NaN)).rejects.toThrow(BadRequestException);
    });

    it("should throw NotFoundException if country price list not found", async () => {
      databaseService.countryPriceList.findUnique.mockResolvedValue(null);
      await expect(service.remove(1)).rejects.toThrow(NotFoundException);
    });
  });
});
