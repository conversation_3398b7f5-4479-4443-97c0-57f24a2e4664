import { Test, TestingModule } from "@nestjs/testing";
import { RequiredInformationService } from "./required-information.service";
import { DatabaseService } from "../database/database.service";
import { CreateRequiredInformationDto } from "./dto/required-information-create.dto";
import { UpdateRequiredInformationDto } from "./dto/required-information-update.dto";
import { NotFoundException, BadRequestException } from "@nestjs/common";
import { RequiredInformationType } from "@prisma/client";

describe("RequiredInformationService", () => {
  let service: RequiredInformationService;
  let databaseServiceMock: any;

  beforeEach(async () => {
    databaseServiceMock = {
      requiredInformation: {
        create: jest.fn(),
        findMany: jest.fn(),
        findUnique: jest.fn(),
        update: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [RequiredInformationService, { provide: DatabaseService, useValue: databaseServiceMock }],
    }).compile();

    service = module.get<RequiredInformationService>(RequiredInformationService);
  });

  describe("create", () => {
    it("should create required information successfully", async () => {
      const createDto: CreateRequiredInformationDto = {
        country_id: 1,
        type: RequiredInformationType.TEXT,
        name: "Required Info 1",
        description: "Description of required info",
        question: "What is your name?",
        file_id: "file123",
      };

      const mockResponse = {
        id: 1,
        ...createDto,
      };

      databaseServiceMock.requiredInformation.create.mockResolvedValue(mockResponse);

      const result = await service.create(createDto);

      expect(result).toEqual(mockResponse);
      expect(databaseServiceMock.requiredInformation.create).toHaveBeenCalledWith({
        data: createDto,
      });
    });
  });

  describe("findAll", () => {
    it("should return all required information", async () => {
      const mockResponse = [
        { id: 1, country_id: 1, type: RequiredInformationType.TEXT, name: "Info 1", description: "Description 1" },
        { id: 2, country_id: 2, type: RequiredInformationType.DOCUMENT, name: "Info 2", description: "Description 2" },
      ];

      databaseServiceMock.requiredInformation.findMany.mockResolvedValue(mockResponse);

      const result = await service.findAll();

      expect(result).toEqual(mockResponse);
      expect(databaseServiceMock.requiredInformation.findMany).toHaveBeenCalledWith({
        where: { deleted_at: null },
      });
    });
  });

  describe("findOne", () => {
    it("should return required information by ID", async () => {
      const mockResponse = {
        id: 1,
        country_id: 1,
        type: RequiredInformationType.TEXT,
        name: "Info 1",
        description: "Description 1",
      };

      databaseServiceMock.requiredInformation.findUnique.mockResolvedValue(mockResponse);

      const result = await service.findOne(1);

      expect(result).toEqual(mockResponse);
      expect(databaseServiceMock.requiredInformation.findUnique).toHaveBeenCalledWith({
        where: { id: 1, deleted_at: null },
      });
    });

    it("should throw NotFoundException if required information not found", async () => {
      databaseServiceMock.requiredInformation.findUnique.mockResolvedValue(null);

      await expect(service.findOne(1)).rejects.toThrow(NotFoundException);
      await expect(service.findOne(1)).rejects.toThrow("Required information not found");
    });
  });

  describe("update", () => {
    it("should update required information successfully", async () => {
      const updateDto: UpdateRequiredInformationDto = {
        name: "Updated Info",
        description: "Updated description",
        question: "Updated question?",
        file_id: "file124",
      };

      const mockResponse = { id: 1, ...updateDto };

      databaseServiceMock.requiredInformation.findUnique.mockResolvedValue(mockResponse);
      databaseServiceMock.requiredInformation.update.mockResolvedValue(mockResponse);

      const result = await service.update(1, updateDto);

      expect(result).toEqual(mockResponse);
      expect(databaseServiceMock.requiredInformation.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: updateDto,
      });
    });

    it("should throw NotFoundException if required information not found for update", async () => {
      const updateDto: UpdateRequiredInformationDto = {
        name: "Updated Info",
        description: "Updated description",
        question: "Updated question?",
        file_id: "file124",
      };

      databaseServiceMock.requiredInformation.findUnique.mockResolvedValue(null);

      await expect(service.update(1, updateDto)).rejects.toThrow(NotFoundException);
      await expect(service.update(1, updateDto)).rejects.toThrow("Required information not found");
    });
  });

  describe("remove", () => {
    it("should throw BadRequestException if ID is invalid", async () => {
      await expect(service.remove(NaN)).rejects.toThrow(BadRequestException);
      await expect(service.remove(NaN)).rejects.toThrow("Invalid required information ID");
    });
  });
});
