import { Body, Controller, Delete, Get, Param, Post, Put } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";
import { RequiredInformationKind, RequiredInformationType } from "@prisma/client";
import { CreateRequiredInformationDto } from "./dto/required-information-create.dto";
import { UpdateRequiredInformationDto } from "./dto/required-information-update.dto";
import { RequiredInformationService } from "./required-information.service";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK)
@ApiTags("RequiredInformation")
@Controller("required-informations")
export class RequiredInformationController {
  constructor(private readonly requiredInformationService: RequiredInformationService) {}

  @Post()
  @ApiOperation({ summary: "Create a new required information" })
  @ApiResponse({
    status: 200,
    description: "Required information created successfully",
    schema: {
      type: "object",
      properties: {
        description: { type: "string" },
        type: { type: "string", enum: Object.values(RequiredInformationType) },
        country_id: { type: "number", nullable: true },
        name: { type: "string" },
        question: { type: "string", nullable: true },
        file_id: { type: "string", nullable: true },
        id: { type: "number" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
        kind: { type: "string", enum: Object.values(RequiredInformationKind) },
      },
    },
    example: {
      description: "Required information description",
      type: RequiredInformationType.TEXT,
      name: "Required information name",
      question: "Required information question",
      file_id: "1",
      kind: RequiredInformationKind.COUNTRY_INFORMATION,
    },
  })
  @ApiResponse({ status: 400, description: "Invalid country ID" })
  @ApiResponse({ status: 404, description: "Country not found" })
  create(@Body() data: CreateRequiredInformationDto) {
    return this.requiredInformationService.create(data);
  }

  @Get()
  @ApiOperation({ summary: "Get all required information" })
  @ApiResponse({
    status: 200,
    description: "Required information retrieved successfully",
    schema: {
      type: "array",
      items: {
        type: "object",
        properties: {
          id: { type: "number" },
          description: { type: "string" },
          type: { type: "string", enum: Object.values(RequiredInformationType) },
          country_id: { type: "number", nullable: true },
          name: { type: "string" },
          question: { type: "string", nullable: true },
          file_id: { type: "string", nullable: true },
          created_at: { type: "string", format: "date-time" },
          updated_at: { type: "string", format: "date-time" },
          deleted_at: { type: "string", format: "date-time", nullable: true },
          kind: { type: "string", enum: Object.values(RequiredInformationKind) },
        },
      },
    },
    example: [
      {
        id: 1,
        description: "Required information description",
        type: RequiredInformationType.TEXT,
        country_id: 1,
        name: "Required information name",
        question: "Required information question",
        file_id: "1",
        kind: RequiredInformationKind.COUNTRY_INFORMATION,
      },
    ],
  })
  findAll() {
    return this.requiredInformationService.findAll();
  }

  @Get(":id")
  @ApiOperation({ summary: "Get required information by ID" })
  @ApiResponse({
    status: 200,
    description: "Required information retrieved successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        description: { type: "string" },
        type: { type: "string", enum: Object.values(RequiredInformationType) },
        country_id: { type: "number", nullable: true },
        name: { type: "string" },
        question: { type: "string", nullable: true },
        file_id: { type: "string", nullable: true },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
        kind: { type: "string", enum: Object.values(RequiredInformationKind) },
      },
    },
    example: {
      id: 1,
      description: "Required information description",
      type: RequiredInformationType.TEXT,
      country_id: 1,
      name: "Required information name",
      question: "Required information question",
      file_id: "1",
      kind: RequiredInformationKind.COUNTRY_INFORMATION,
    },
  })
  @ApiResponse({ status: 404, description: "Required information not found" })
  @ApiResponse({ status: 400, description: "Invalid required information ID" })
  findOne(@Param("id") id: string) {
    return this.requiredInformationService.findOne(+id);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update required information by ID" })
  @ApiResponse({
    status: 200,
    description: "Required information updated successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        description: { type: "string" },
        type: { type: "string", enum: Object.values(RequiredInformationType) },
        country_id: { type: "number", nullable: true },
        name: { type: "string" },
        question: { type: "string", nullable: true },
        file_id: { type: "string", nullable: true },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
        kind: { type: "string", enum: Object.values(RequiredInformationKind) },
      },
    },
    example: {
      id: 1,
      description: "Required information description",
      type: RequiredInformationType.TEXT,
      country_id: 1,
      name: "Required information name",
      question: "Required information question",
      file_id: "1",
      kind: RequiredInformationKind.COUNTRY_INFORMATION,
    },
  })
  @ApiResponse({ status: 404, description: "Required information not found" })
  @ApiResponse({ status: 400, description: "Invalid required information ID" })
  update(@Param("id") id: string, @Body() data: UpdateRequiredInformationDto) {
    return this.requiredInformationService.update(+id, data);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete required information by ID" })
  @ApiResponse({
    status: 200,
    description: "Required information deleted successfully",
  })
  @ApiResponse({ status: 404, description: "Required information not found" })
  @ApiResponse({ status: 400, description: "Invalid required information ID" })
  remove(@Param("id") id: string) {
    return this.requiredInformationService.remove(+id);
  }
}
