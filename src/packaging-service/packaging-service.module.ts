import { Module } from "@nestjs/common";
import { DatabaseModule } from "../database/database.module";
import { PackagingServiceController } from "./packaging-service.controller";
import { PackagingServiceService } from "./packaging-service.service";

@Module({
  imports: [DatabaseModule],
  controllers: [PackagingServiceController],
  providers: [PackagingServiceService],
})
export class PackagingServiceModule {}
