import { ApiProperty } from "@nestjs/swagger";

class Country {
  @ApiProperty({ example: "Portugal" })
  name: string;
}

class Company {
  @ApiProperty({ example: "Empresa XYZ" })
  name: string;

  @ApiProperty({ required: false })
  taxNumber?: string;

  @ApiProperty({ required: false })
  vatNumber?: string;
}

class Address {
  @ApiProperty({ example: "Rua Principal, 123" })
  streetAndNumber: string;

  @ApiProperty({ example: "12345-678" })
  zipCode: string;

  @ApiProperty({ type: () => Country })
  country: Country;
}

class Customer {
  @ApiProperty({ example: "<PERSON>" })
  name: string;

  @ApiProperty({ example: "<EMAIL>" })
  email: string;

  @ApiProperty({ type: () => Address })
  address: Address;
}

export class CreateContractDto {
  @ApiProperty({ type: () => Company })
  company: Company;

  @ApiProperty({ type: () => Customer })
  customer: Customer;
}
