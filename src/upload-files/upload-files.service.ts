import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from "@nestjs/common";
import { RequestPresignedUrlDto } from "./dto/request-presigned-url.dto";
import axios from "axios";
import { CreateFileDto, DocumentType } from "./dto/create-file.dto";
import { DatabaseService } from "@/database/database.service";
import { CustomHeadersDto } from "@/shared/dto/custom-headers.dto";
import { PaginationDto } from "@/shared/dto/pagination.dto";
import { Blob } from "blob-polyfill";
import * as crypto from "crypto";

@Injectable()
export class UploadFilesService {
  constructor(private readonly prisma: DatabaseService) {}
  async requestUrl(requestPresignedUrlDto: RequestPresignedUrlDto) {
    const lambdaRequesPresignedUrl = "https://3pdf2nhm7hl4n37hjo4upqgjme0pdvsd.lambda-url.us-east-2.on.aws/";
    const response = await axios.post(lambdaRequesPresignedUrl, requestPresignedUrlDto);

    const { data } = response;
    if (!data) {
      throw new BadRequestException("Problems generating url");
    }

    return data;
  }

  async uploadFile(data: CreateFileDto, customHeadersDto: CustomHeadersDto, file: Express.Multer.File) {
    const { userId, userRole } = customHeadersDto;

    const { size, originalname, mimetype } = file;

    if (data.country_id) {
      if (Number.isNaN(Number(data.country_id))) {
        throw new BadRequestException("Invalid country ID");
      }

      const country = await this.prisma.country.findUnique({
        where: { id: data.country_id },
      });

      if (!country) {
        throw new NotFoundException("Country not found");
      }
    }

    return await this.prisma.$transaction(
      async (tx) => {
        const filename = this.generateFilename(data.document_type, originalname);

        const { uploadUrl, fields } = await this.requestUrl({ filename, fileType: mimetype });

        if (!uploadUrl || !fields) {
          throw new BadRequestException("Presigned url or Fields not found!");
        }

        const formData = new FormData();

        Object.entries(fields).forEach(([key, value]) => {
          if (typeof value === "string" || value instanceof Blob) {
            formData.append(key, value as string | Blob);
          }
        });

        const blob = new Blob([file.buffer]);
        formData.append("file", blob);

        await axios.post(uploadUrl, formData);

        return tx.files.create({
          data: {
            user_id: userId,
            creator_type: userRole,
            document_type: data.document_type,
            country_id: data.country_id ? Number(data.country_id) : null,
            size: String(size),
            name: filename,
            extension: mimetype,
            original_name: originalname,
          },
        });
      },
      {
        timeout: 20000,
        maxWait: 20000,
      }
    );
  }

  // keep IDs the same in prod/dev in DB
  async getFilesByCountry(countryId: number, pagination: PaginationDto) {
    const { limit, page } = pagination;

    const take = limit || 10;
    const skip = ((page || 1) - 1) * take;

    const [data, total] = await this.prisma.$transaction([
      this.prisma.files.findMany({
        where: {
          country_id: countryId,
        },
        take,
        skip,
      }),
      this.prisma.files.count({
        where: {
          country_id: countryId,
        },
      }),
    ]);

    return {
      data,
      total,
      page,
      limit,
    };
  }

  filterByAuthorizedRole(customHeadersDto: CustomHeadersDto) {
    let whereClause = {};
    switch (customHeadersDto.userRole) {
      case "admin":
        break;
      case "clerk":
        whereClause = {
          creator_type: "customer",
        };
        break;
      case "customer":
        whereClause = {
          creator_type: "customer",
          user_id: customHeadersDto.userId,
        };
        break;
      default:
        throw new Error("Invalid role");
    }

    return whereClause;
  }

  async getFiles(pagination: PaginationDto, customHeadersDto: CustomHeadersDto) {
    const { limit, page } = pagination;

    const take = limit || 10;
    const skip = ((page || 1) - 1) * take;

    const whereClause = this.filterByAuthorizedRole(customHeadersDto);

    const [data, total] = await this.prisma.$transaction([
      this.prisma.files.findMany({
        where: whereClause,
        take,
        skip,
      }),
      this.prisma.files.count({
        where: whereClause,
      }),
    ]);

    return {
      data,
      total,
      page,
      limit,
    };
  }

  async getFile(fileId: string) {
    const file = await this.prisma.files.findUnique({
      where: {
        id: fileId,
      },
    });

    if (!file) throw new NotFoundException("File not found");

    // this.validateAuthorization(file.creator_type, customHeadersDto);

    const lambdaDownloadFile = "https://3qthmn2fk2clgkiikb3dzy6o7e0fyttk.lambda-url.us-east-2.on.aws";

    try {
      const response = await axios.get(`${lambdaDownloadFile}?fileName=${file.name}`, {
        responseType: "arraybuffer",
      });

      return { file, buffer: Buffer.from(response.data) };
    } catch (error) {
      console.log(error);
      throw new BadRequestException("Error downloading PDF file");
    }
  }

  validateAuthorization(authorizedRole: string, customHeadersDto: CustomHeadersDto, userId?: string) {
    if (!customHeadersDto.userId || !customHeadersDto.userRole) {
      throw new ForbiddenException("User must be authenticated");
    }

    if (customHeadersDto.userRole === "admin") {
      return;
    }

    if (customHeadersDto.userId === "clerk" && authorizedRole === "admin") {
      throw new UnauthorizedException("User not authorized");
    }

    if (
      customHeadersDto.userId === "customer" &&
      (authorizedRole !== "customer" || userId !== customHeadersDto.userId)
    ) {
      throw new UnauthorizedException("User not authorized");
    }
  }

  generateFilename(docType: DocumentType, filename: string): string {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, "0");
    const day = String(today.getDate()).padStart(2, "0");

    const hash = this.generateHash();
    const formatedFilename = `${docType}/${year}/${month}/${day}/${hash}-${filename}`;

    return formatedFilename;
  }

  generateHash() {
    const randomBytes = crypto.randomBytes(8);
    const hash = Array.from(randomBytes, (byte) => (byte % 36).toString(36))
      .join("")
      .toUpperCase()
      .substring(0, 8);
    return hash;
  }
}
