import { Modu<PERSON> } from "@nestjs/common";
import { DatabaseModule } from "../database/database.module";
import { ReportSetColumnFractionsController } from "./report-set-column-fractions.controller";
import { ReportSetColumnFractionsService } from "./report-set-column-fractions.service";

@Module({
  imports: [DatabaseModule],
  controllers: [ReportSetColumnFractionsController],
  providers: [ReportSetColumnFractionsService],
})
export class ReportSetColumnFractionsModule {}
