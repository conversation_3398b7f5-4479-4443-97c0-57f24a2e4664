import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { DatabaseService } from "../database/database.service";
import { CreateReportSetColumnFractionDto } from "./dto/report-set-column-fraction-create.dto";

@Injectable()
export class ReportSetColumnFractionsService {
  constructor(private readonly databaseService: DatabaseService) {}

  async create(data: CreateReportSetColumnFractionDto) {
    if (!data.column_code || !data.fraction_code)
      throw new BadRequestException("Invalid report set column fraction data");

    const reportSetColumn = await this.databaseService.reportSetColumn.findUnique({
      where: { code: data.column_code },
    });

    if (!reportSetColumn) throw new NotFoundException("Report set column not found");

    const reportSetFraction = await this.databaseService.reportSetFraction.findUnique({
      where: { code: data.fraction_code },
    });

    if (!reportSetFraction) throw new NotFoundException("Report set fraction not found");

    return this.databaseService.reportSetColumnFraction.create({
      data: {
        column_code: data.column_code,
        fraction_code: data.fraction_code,
      },
    });
  }

  async findAll() {
    return this.databaseService.reportSetColumnFraction.findMany({
      where: { deleted_at: null },
    });
  }

  async findOne(id: number) {
    if (!id || Number.isNaN(Number(id))) throw new BadRequestException("Invalid report set column fraction ID");

    const reportSetColumn = await this.databaseService.reportSetColumnFraction.findUnique({
      where: { id, deleted_at: null },
    });

    if (!reportSetColumn) {
      throw new NotFoundException("Report set column fraction not found");
    }

    return reportSetColumn;
  }

  async remove(id: number) {
    if (!id || Number.isNaN(Number(id))) throw new BadRequestException("Invalid report set column fraction ID");

    const reportSetColumn = await this.databaseService.reportSetColumnFraction.findUnique({
      where: { id },
    });

    if (!reportSetColumn) {
      throw new NotFoundException("Report set column fraction not found");
    }

    await this.databaseService.$transaction(async (prisma) => {
      await prisma.reportSetColumnFraction.update({
        where: { id },
        data: { deleted_at: new Date() },
      });
    });
  }
}
