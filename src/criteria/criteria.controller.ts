import { Body, Controller, Delete, Get, Param, Post, Put } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { CriteriaService } from "./criteria.service";
import { CreateCriteriaDto } from "./dto/criteria-create.dto";
import { UpdateCriteriaDto } from "./dto/criteria-update.dto";
import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK)
@ApiTags("Criteria")
@Controller("criterias")
export class CriteriaController {
  constructor(private readonly criteriaService: CriteriaService) {}

  @Post()
  @ApiOperation({ summary: "Create a new criteria" })
  @ApiResponse({
    status: 201,
    description: "Criteria created successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        country_id: { type: "number" },
        help_text: { type: "string" },
        input_type: {
          type: "enum",
          enum: ["RADIO", "SELECT", "YES_NO", "RANGE"],
        },
        mode: { type: "enum", enum: ["COMMITMENT", "CALCULATOR"] },
        title: { type: "string" },
        type: {
          type: "enum",
          enum: [
            "PACKAGING_SERVICE",
            "REPORT_SET",
            "REPORT_FREQUENCY",
            "AUTHORIZE_REPRESENTATIVE",
            "REPRESENTATIVE_TIER",
            "OTHER_COST",
            "PRICE_LIST",
            "REQUIRED_INFORMATION",
          ],
        },
        packaging_service_id: { type: "number", nullable: true },
        required_information_id: { type: "number", nullable: true },
        calculator_type: {
          type: "enum",
          enum: ["LICENSE_FEES", "TOTAL_IN_TONS", "TOTAL_IN_KG"],
          nullable: true,
        },
        options: {
          type: "array",
          items: {
            type: "object",
            properties: {
              id: { type: "number" },
              criteria_id: { type: "number" },
              value: { type: "string" },
              option_value: { type: "string" },
              option_to_value: { type: "string", nullable: true },
              created_at: { type: "string", format: "date-time" },
              updated_at: { type: "string", format: "date-time", nullable: true },
              deleted_at: { type: "string", format: "date-time", nullable: true },
            },
          },
        },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time", nullable: true },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
      example: {
        id: 1,
        country_id: 1,
        help_text: "Help text",
        input_type: "RADIO",
        mode: "COMMITMENT",
        title: "Title",
        type: "PACKAGING_SERVICE",
        packaging_service_id: 1,
        required_information_id: 1,
        calculator_type: null,
        options: [{ id: 1, criteria_id: 1, value: "Option 1", option_value: "Option 1", option_to_value: null }],
        created_at: "2021-01-01T00:00:00.000Z",
        updated_at: "2021-01-01T00:00:00.000Z",
        deleted_at: null,
      },
    },
  })
  create(@Body() data: CreateCriteriaDto) {
    return this.criteriaService.create(data);
  }

  @Get()
  @ApiOperation({ summary: "Get all criterias" })
  @ApiResponse({
    status: 200,
    description: "All criterias retrieved successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        country_id: { type: "number" },
        help_text: { type: "string" },
        input_type: {
          type: "enum",
          enum: ["RADIO", "SELECT", "YES_NO", "RANGE"],
        },
        mode: { type: "enum", enum: ["COMMITMENT", "CALCULATOR"] },
        title: { type: "string" },
        type: {
          type: "enum",
          enum: [
            "PACKAGING_SERVICE",
            "REPORT_SET",
            "REPORT_FREQUENCY",
            "AUTHORIZE_REPRESENTATIVE",
            "REPRESENTATIVE_TIER",
            "OTHER_COST",
            "PRICE_LIST",
            "REQUIRED_INFORMATION",
          ],
        },
        packaging_service_id: { type: "number", nullable: true },
        required_information_id: { type: "number", nullable: true },
        calculator_type: {
          type: "enum",
          enum: ["LICENSE_FEES", "TOTAL_IN_TONS", "TOTAL_IN_KG"],
          nullable: true,
        },
        options: {
          type: "array",
          items: {
            type: "object",
            properties: {
              id: { type: "number" },
              criteria_id: { type: "number" },
              value: { type: "string" },
              option_value: { type: "string" },
              option_to_value: { type: "string", nullable: true },
              created_at: { type: "string", format: "date-time" },
              updated_at: { type: "string", format: "date-time", nullable: true },
              deleted_at: { type: "string", format: "date-time", nullable: true },
            },
          },
        },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time", nullable: true },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
      example: [
        {
          id: 1,
          country_id: 1,
          help_text: "Help text",
          input_type: "RADIO",
          mode: "COMMITMENT",
          title: "Title",
          type: "PACKAGING_SERVICE",
          packaging_service_id: 1,
          required_information_id: 1,
          calculator_type: null,
          options: [{ id: 1, criteria_id: 1, value: "Option 1", option_value: "Option 1", option_to_value: null }],
          created_at: "2021-01-01T00:00:00.000Z",
          updated_at: "2021-01-01T00:00:00.000Z",
          deleted_at: null,
        },
        {
          id: 2,
          country_id: 1,
          help_text: "Help text",
          input_type: "RADIO",
          mode: "CALCULATOR",
          title: "Title 2",
          type: "PACKAGING_SERVICE",
          packaging_service_id: 1,
          required_information_id: 1,
          calculator_type: "LICENSE_FEES",
          options: [{ id: 2, criteria_id: 2, value: "Option 2", option_value: "Option 2", option_to_value: null }],
          created_at: "2021-01-01T00:00:00.000Z",
          updated_at: "2021-01-01T00:00:00.000Z",
          deleted_at: null,
        },
      ],
    },
  })
  findAll() {
    return this.criteriaService.findAll();
  }

  @Get(":id")
  @ApiOperation({ summary: "Get criteria by ID" })
  @ApiResponse({
    status: 200,
    description: "Criteria retrieved successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        country_id: { type: "number" },
        help_text: { type: "string" },
        input_type: {
          type: "enum",
          enum: ["RADIO", "SELECT", "YES_NO", "RANGE"],
        },
        mode: { type: "enum", enum: ["COMMITMENT", "CALCULATOR"] },
        title: { type: "string" },
        type: {
          type: "enum",
          enum: [
            "PACKAGING_SERVICE",
            "REPORT_SET",
            "REPORT_FREQUENCY",
            "AUTHORIZE_REPRESENTATIVE",
            "REPRESENTATIVE_TIER",
            "OTHER_COST",
            "PRICE_LIST",
            "REQUIRED_INFORMATION",
          ],
        },
        packaging_service_id: { type: "number", nullable: true },
        required_information_id: { type: "number", nullable: true },
        calculator_type: {
          type: "enum",
          enum: ["LICENSE_FEES", "TOTAL_IN_TONS", "TOTAL_IN_KG"],
          nullable: true,
        },
        options: {
          type: "array",
          items: {
            type: "object",
            properties: {
              id: { type: "number" },
              criteria_id: { type: "number" },
              value: { type: "string" },
              option_value: { type: "string" },
              option_to_value: { type: "string", nullable: true },
              created_at: { type: "string", format: "date-time" },
              updated_at: { type: "string", format: "date-time", nullable: true },
              deleted_at: { type: "string", format: "date-time", nullable: true },
            },
          },
        },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time", nullable: true },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
      example: {
        id: 1,
        country_id: 1,
        help_text: "Help text",
        input_type: "RADIO",
        mode: "COMMITMENT",
        title: "Title",
        type: "PACKAGING_SERVICE",
        packaging_service_id: 1,
        required_information_id: 1,
        calculator_type: null,
        options: [{ id: 1, criteria_id: 1, value: "Option 1", option_value: "Option 1", option_to_value: null }],
        created_at: "2021-01-01T00:00:00.000Z",
        updated_at: "2021-01-01T00:00:00.000Z",
        deleted_at: null,
      },
    },
  })
  @ApiResponse({ status: 404, description: "Criteria not found" })
  findOne(@Param("id") id: string) {
    return this.criteriaService.findOne(+id);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update criteria by ID" })
  @ApiResponse({
    status: 200,
    description: "Criteria updated successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        country_id: { type: "number" },
        help_text: { type: "string" },
        input_type: {
          type: "enum",
          enum: ["RADIO", "SELECT", "YES_NO", "RANGE"],
        },
        mode: { type: "enum", enum: ["COMMITMENT", "CALCULATOR"] },
        title: { type: "string" },
        type: {
          type: "enum",
          enum: [
            "PACKAGING_SERVICE",
            "REPORT_SET",
            "REPORT_FREQUENCY",
            "AUTHORIZE_REPRESENTATIVE",
            "REPRESENTATIVE_TIER",
            "OTHER_COST",
            "PRICE_LIST",
            "REQUIRED_INFORMATION",
          ],
        },
        packaging_service_id: { type: "number", nullable: true },
        required_information_id: { type: "number", nullable: true },
        calculator_type: {
          type: "enum",
          enum: ["LICENSE_FEES", "TOTAL_IN_TONS", "TOTAL_IN_KG"],
          nullable: true,
        },
        options: {
          type: "array",
          items: {
            type: "object",
            properties: {
              id: { type: "number" },
              criteria_id: { type: "number" },
              value: { type: "string" },
              option_value: { type: "string" },
              option_to_value: { type: "string", nullable: true },
              created_at: { type: "string", format: "date-time" },
              updated_at: { type: "string", format: "date-time", nullable: true },
              deleted_at: { type: "string", format: "date-time", nullable: true },
            },
          },
        },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time", nullable: true },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
      example: {
        id: 1,
        country_id: 1,
        help_text: "Help text",
        input_type: "RADIO",
        mode: "COMMITMENT",
        title: "Title",
        type: "PACKAGING_SERVICE",
        packaging_service_id: 1,
        required_information_id: 1,
        calculator_type: null,
        options: [{ id: 1, criteria_id: 1, value: "Option 1", option_value: "Option 1", option_to_value: null }],
        created_at: "2021-01-01T00:00:00.000Z",
        updated_at: "2021-01-01T00:00:00.000Z",
        deleted_at: null,
      },
    },
  })
  @ApiResponse({ status: 404, description: "Criteria not found" })
  update(@Param("id") id: string, @Body() data: UpdateCriteriaDto) {
    return this.criteriaService.update(+id, data);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete criteria by ID" })
  @ApiResponse({ status: 200, description: "Criteria deleted successfully" })
  @ApiResponse({ status: 404, description: "Criteria not found" })
  remove(@Param("id") id: string) {
    return this.criteriaService.remove(+id);
  }
}
