import { Test, TestingModule } from "@nestjs/testing";
import { CountryPriceListService } from "../country-price-list/country-price-list.service";
import { CriteriaService } from "./criteria.service";
import { DatabaseService } from "../database/database.service";
import { NotFoundException, InternalServerErrorException, BadRequestException } from "@nestjs/common";
import { CriteriaMode } from "@prisma/client";

const mockDatabaseService = {
  $transaction: jest.fn((callback): any => callback(mockDatabaseService)),
  country: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  countryFollower: {
    createMany: jest.fn(),
    findFirst: jest.fn(),
    deleteMany: jest.fn(),
  },
  countryPriceList: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
  },
  criteria: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  criteriaOption: {
    createMany: jest.fn(),
    deleteMany: jest.fn(),
  },
};

describe("CountryPriceListService", () => {
  let service: CountryPriceListService;
  let databaseService: typeof mockDatabaseService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CountryPriceListService, { provide: DatabaseService, useValue: mockDatabaseService }],
    }).compile();

    jest.spyOn(console, "error").mockImplementation(() => {});
    jest.spyOn(console, "log").mockImplementation(() => {});

    service = module.get<CountryPriceListService>(CountryPriceListService);
    databaseService = module.get(DatabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("create", () => {
    it("should create a country price list", async () => {
      const dto = { country_id: 1, price_list_id: 2 };
      databaseService.countryPriceList.create.mockResolvedValue(dto);

      const result = await service.create(dto);
      expect(result).toEqual(dto);
      expect(databaseService.countryPriceList.create).toHaveBeenCalledWith({ data: dto });
    });

    it("should throw InternalServerErrorException on unexpected error", async () => {
      databaseService.countryPriceList.create.mockRejectedValue(new BadRequestException("Error creating criteria"));
      await expect(service.create({ country_id: 1, price_list_id: 2 })).rejects.toThrow(BadRequestException);
    });
  });

  describe("findAll", () => {
    it("should return all country price lists", async () => {
      const mockData = [{ id: 1, country_id: 1, price_list_id: 2, deleted_at: null }];
      databaseService.countryPriceList.findMany.mockResolvedValue(mockData);

      const result = await service.findAll();
      expect(result).toEqual(mockData);
    });
  });

  describe("findOne", () => {
    it("should return a country price list", async () => {
      const mockData = { id: 1, country_id: 1, price_list_id: 2, deleted_at: null };
      databaseService.countryPriceList.findUnique.mockResolvedValue(mockData);

      const result = await service.findOne(1);
      expect(result).toEqual(mockData);
    });

    it("should throw NotFoundException if country price list not found", async () => {
      databaseService.countryPriceList.findUnique.mockResolvedValue(null);
      await expect(service.findOne(1)).rejects.toThrow(NotFoundException);
    });
  });
});

describe("CriteriaService", () => {
  let service: CriteriaService;
  let databaseService: typeof mockDatabaseService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CriteriaService, { provide: DatabaseService, useValue: mockDatabaseService }],
    }).compile();

    service = module.get<CriteriaService>(CriteriaService);
    databaseService = module.get(DatabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("create", () => {
    it("should create a criteria with options", async () => {
      const dto: any = { mode: "MODE", type: "TYPE", options: [{ name: "Option1" }] };
      const createdCriteria = { id: 1, ...dto };
      databaseService.criteria.create.mockResolvedValue(createdCriteria);

      const result = await service.create(dto);
      expect(result).toEqual(createdCriteria);
      expect(databaseService.criteria.create).toHaveBeenCalled();
      expect(databaseService.criteriaOption.createMany).toHaveBeenCalled();
    });
  });

  describe("update", () => {
    it("should update a criteria", async () => {
      const dto: any = { mode: "NEW_MODE", type: "NEW_TYPE", options: [{ name: "NewOption" }] };
      const existingCriteria = { id: 1, mode: "MODE", type: "TYPE", country_id: 1 };
      databaseService.criteria.findUnique.mockResolvedValue(existingCriteria);
      databaseService.criteria.update.mockResolvedValue({ ...existingCriteria, ...dto });

      const result = await service.update(1, dto);
      expect(result).toEqual({ ...existingCriteria, ...dto });
      expect(databaseService.criteriaOption.deleteMany).toHaveBeenCalled();
      expect(databaseService.criteriaOption.createMany).toHaveBeenCalled();
    });

    it("should throw NotFoundException if criteria not found", async () => {
      databaseService.criteria.findUnique.mockResolvedValue(null);
      await expect(service.update(1, { mode: CriteriaMode.COMMITMENT })).rejects.toThrow(NotFoundException);
    });
  });

  describe("remove", () => {
    it("should soft delete a criteria", async () => {
      const existingCriteria = { id: 1, mode: "MODE", type: "TYPE", country_id: 1 };
      databaseService.criteria.findUnique.mockResolvedValue(existingCriteria);
      databaseService.criteria.update.mockResolvedValue({ ...existingCriteria, deleted_at: new Date() });

      const result = await service.remove(1);
      expect(result).toBeDefined();
    });

    it("should throw NotFoundException if criteria not found", async () => {
      databaseService.criteria.findUnique.mockResolvedValue(null);
      await expect(service.remove(1)).rejects.toThrow(NotFoundException);
    });
  });
});
