import { Modu<PERSON> } from "@nestjs/common";
import { DatabaseModule } from "../database/database.module";
import { ReportSetFrequencyController } from "./report-set-frequency.controller";
import { ReportSetFrequencyService } from "./report-set-frequency.service";

@Module({
  imports: [DatabaseModule],
  controllers: [ReportSetFrequencyController],
  providers: [ReportSetFrequencyService],
})
export class ReportSetFrequencyModule {}
