import { Test, TestingModule } from "@nestjs/testing";
import { ReportSetFrequencyService } from "./report-set-frequency.service";
import { DatabaseService } from "../database/database.service";
import { NotFoundException } from "@nestjs/common";

const mockDatabaseService = {
  reportSetFrequency: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
  },
};

describe("ReportSetFrequencyService", () => {
  let service: ReportSetFrequencyService;
  let databaseService: typeof mockDatabaseService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ReportSetFrequencyService, { provide: DatabaseService, useValue: mockDatabaseService }],
    }).compile();

    service = module.get<ReportSetFrequencyService>(ReportSetFrequencyService);
    databaseService = module.get(DatabaseService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("create", () => {
    it("should create a report set frequency", async () => {
      const dto: any = {
        rhythm: "MONTHLY",
        frequency: { deadline: { day: 10 }, open: { day: 1 } },
        packaging_service_id: 1,
      };
      const createdFrequency = { id: 1, ...dto, frequency: JSON.stringify(dto.frequency) };
      databaseService.reportSetFrequency.create.mockResolvedValue(createdFrequency);

      const result = await service.create(dto);
      expect(result).toEqual(createdFrequency);
      expect(databaseService.reportSetFrequency.create).toHaveBeenCalledWith({
        data: {
          rhythm: dto.rhythm,
          frequency: JSON.stringify(dto.frequency),
          packaging_service_id: dto.packaging_service_id,
        },
      });
    });
  });

  describe("findAll", () => {
    it("should return all report set frequencies", async () => {
      const frequencies = [{ id: 1 }, { id: 2 }];
      databaseService.reportSetFrequency.findMany.mockResolvedValue(frequencies);

      const result = await service.findAll();
      expect(result).toEqual(frequencies);
      expect(databaseService.reportSetFrequency.findMany).toHaveBeenCalledWith({ where: { deleted_at: null } });
    });
  });

  describe("findOne", () => {
    it("should return a report set frequency by id", async () => {
      const frequency = { id: 1 };
      databaseService.reportSetFrequency.findUnique.mockResolvedValue(frequency);

      const result = await service.findOne(1);
      expect(result).toEqual(frequency);
      expect(databaseService.reportSetFrequency.findUnique).toHaveBeenCalledWith({
        where: { id: 1, deleted_at: null },
      });
    });

    it("should throw NotFoundException if frequency is not found", async () => {
      databaseService.reportSetFrequency.findUnique.mockResolvedValue(null);

      await expect(service.findOne(1)).rejects.toThrow(NotFoundException);
    });
  });

  describe("update", () => {
    it("should update a report set frequency", async () => {
      const dto = { frequency: { deadline: { day: 15 }, open: { day: 5 } } };
      const updatedFrequency = { id: 1, ...dto, frequency: JSON.stringify(dto.frequency) };
      databaseService.reportSetFrequency.findUnique.mockResolvedValue(updatedFrequency);
      databaseService.reportSetFrequency.update.mockResolvedValue(updatedFrequency);

      const result = await service.update(1, dto);
      expect(result).toEqual(updatedFrequency);
      expect(databaseService.reportSetFrequency.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: { frequency: JSON.stringify(dto.frequency) },
      });
    });

    it("should throw NotFoundException if frequency is not found", async () => {
      databaseService.reportSetFrequency.findUnique.mockResolvedValue(null);

      await expect(service.update(1, { frequency: undefined })).rejects.toThrow(NotFoundException);
    });
  });

  describe("remove", () => {
    it("should soft delete a report set frequency", async () => {
      const frequency = { id: 1 };
      databaseService.reportSetFrequency.findUnique.mockResolvedValue(frequency);
      databaseService.reportSetFrequency.update.mockResolvedValue({ ...frequency, deleted_at: new Date() });

      await service.remove(1);
      expect(databaseService.reportSetFrequency.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: { deleted_at: expect.any(Date) },
      });
    });

    it("should throw NotFoundException if frequency is not found", async () => {
      databaseService.reportSetFrequency.findUnique.mockResolvedValue(null);

      await expect(service.remove(1)).rejects.toThrow(NotFoundException);
    });
  });
});
