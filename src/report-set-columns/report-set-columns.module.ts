import { Modu<PERSON> } from "@nestjs/common";
import { DatabaseModule } from "../database/database.module";
import { ReportSetColumnsController } from "./report-set-columns.controller";
import { ReportSetColumnsService } from "./report-set-columns.service";

@Module({
  imports: [DatabaseModule],
  controllers: [ReportSetColumnsController],
  providers: [ReportSetColumnsService],
})
export class ReportSetColumnsModule {}
