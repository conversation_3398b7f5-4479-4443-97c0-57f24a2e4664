import { Body, Controller, Delete, Get, Param, Post } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { ReportSetColumnsService } from "./report-set-columns.service";
import { CreateReportSetColumnDto } from "./dto/report-set-column-create.dto";
import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";
import { $Enums, ReportSetColumnUnitType } from "@prisma/client";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK)
@ApiTags("ReportSetColumns")
@Controller("report-set-columns")
export class ReportSetColumnsController {
  constructor(private readonly reportSetColumnsService: ReportSetColumnsService) {}

  @Post()
  @ApiOperation({ summary: "Create a new report set column" })
  @ApiResponse({
    status: 201,
    description: "Report set column created successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        description: { type: "string" },
        name: { type: "string" },
        unit_type: { type: "string", enum: Object.values(ReportSetColumnUnitType) },
        report_set_id: { type: "number" },
        parent_id: { type: "number", nullable: true },
        level: { type: "number" },
        order: { type: "number" },
        code: { type: "string" },
        parent_code: { type: "string", nullable: true },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
    },
  })
  @ApiResponse({ status: 404, description: "Report set not found" })
  @ApiResponse({ status: 400, description: "Invalid report set ID" })
  @ApiResponse({ status: 400, description: "Invalid parent ID" })
  @ApiResponse({ status: 404, description: "Parent column not found" })
  create(@Body() data: CreateReportSetColumnDto) {
    return this.reportSetColumnsService.create(data);
  }

  @Get()
  @ApiOperation({ summary: "Get all report set columns" })
  @ApiResponse({
    status: 200,
    description: "Report set columns retrieved successfully",
    schema: {
      type: "array",
      items: {
        type: "object",
        properties: {
          id: { type: "number" },
          description: { type: "string" },
          name: { type: "string" },
          unit_type: { type: "string", enum: Object.values(ReportSetColumnUnitType) },
          report_set_id: { type: "number" },
          parent_id: { type: "number", nullable: true },
          level: { type: "number" },
          order: { type: "number" },
          created_at: { type: "string", format: "date-time" },
          updated_at: { type: "string", format: "date-time" },
          deleted_at: { type: "string", format: "date-time", nullable: true },
        },
      },
    },
  })
  findAll() {
    return this.reportSetColumnsService.findAll();
  }

  @Get(":id")
  @ApiOperation({ summary: "Get report set column by ID" })
  @ApiResponse({
    status: 200,
    description: "Report set column retrieved successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        description: { type: "string" },
        name: { type: "string" },
        unit_type: { type: "string", enum: Object.values(ReportSetColumnUnitType) },
        report_set_id: { type: "number" },
        parent_id: { type: "number", nullable: true },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
    },
  })
  @ApiResponse({ status: 404, description: "Report set column not found" })
  @ApiResponse({ status: 400, description: "Invalid report set column ID" })
  findOne(@Param("id") id: string) {
    return this.reportSetColumnsService.findOne(+id);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete report set column by ID" })
  @ApiResponse({ status: 200, description: "Report set column deleted successfully" })
  @ApiResponse({ status: 404, description: "Report set column not found" })
  @ApiResponse({ status: 400, description: "Invalid report set column ID" })
  remove(@Param("id") id: string) {
    return this.reportSetColumnsService.remove(+id);
  }
}
