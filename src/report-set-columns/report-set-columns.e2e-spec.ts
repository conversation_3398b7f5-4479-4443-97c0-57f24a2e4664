import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { AppModule } from "../app.module";
import { Role } from "../shared/auth/role.enum";
import { HEADER_SYSTEM_API_KEY, HEADER_USER_ID, HEADER_USER_ROLE, HEADER_USER_EMAIL } from "../shared/auth/const";
import { DatabaseService } from "../database/database.service";
import { ReportSetColumnUnitType } from "@prisma/client";

jest.setTimeout(30000);

describe("ReportSetColumnsController (e2e)", () => {
  let app: INestApplication;
  let databaseService: DatabaseService;

  const validApiKey = "test-api-key";

  beforeAll(() => {
    process.env.SYSTEM_API_KEY = validApiKey;
  });

  const authHeaders = {
    [HEADER_SYSTEM_API_KEY]: validApi<PERSON><PERSON>,
    [HEADER_USER_ID]: "1",
    [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
    [HEADER_USER_EMAIL]: "<EMAIL>",
  };

  const mockReportSetColumn = {
    id: 1,
    name: "Weight",
    description: "Weight of the product in kilograms",
    unit_type: ReportSetColumnUnitType.KG,
    report_set_id: 1,
    parent_id: null,
    level: 1,
    order: 1,
    code: "mock-code-123",
    parent_code: null,
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
    deleted_at: null,
  };

  const mockReportSetColumns = [
    mockReportSetColumn,
    {
      id: 2,
      name: "Units",
      description: "Number of units",
      unit_type: ReportSetColumnUnitType.UNITS,
      report_set_id: 1,
      parent_id: null,
      level: 1,
      order: 2,
      code: "mock-code-456",
      parent_code: null,
      created_at: "2025-04-07T22:58:17.004Z",
      updated_at: "2025-04-07T22:58:17.004Z",
      deleted_at: null,
    },
  ];

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(DatabaseService)
      .useValue({
        reportSetColumn: {
          create: jest.fn().mockResolvedValue(mockReportSetColumn),
          findMany: jest.fn().mockResolvedValue(mockReportSetColumns),
          findUnique: jest.fn().mockResolvedValue(mockReportSetColumn),
          update: jest.fn().mockImplementation((params) => {
            return Promise.resolve({
              ...mockReportSetColumn,
              ...params.data,
            });
          }),
          updateMany: jest.fn().mockResolvedValue({ count: 1 }),
        },
        $transaction: jest.fn().mockImplementation(async (callback) => {
          return callback({
            reportSetColumn: {
              update: jest.fn().mockResolvedValue(mockReportSetColumn),
              updateMany: jest.fn().mockResolvedValue({ count: 1 }),
            },
          });
        }),
      })
      .compile();

    app = moduleFixture.createNestApplication();
    databaseService = moduleFixture.get<DatabaseService>(DatabaseService);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("/report-set-columns (GET)", () => {
    it("should return report set columns when authenticated as SUPER_ADMIN with valid API key", () => {
      return request(app.getHttpServer())
        .get("/report-set-columns")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.reportSetColumn.findMany).toHaveBeenCalledWith({
            where: { deleted_at: null },
          });
          expect(response.body).toEqual(mockReportSetColumns);
        });
    });

    it("should return report set columns when authenticated as ADMIN with valid API key", () => {
      const adminHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.ADMIN,
      };

      return request(app.getHttpServer()).get("/report-set-columns").set(adminHeaders).expect(200);
    });

    it("should return report set columns when authenticated as CLERK with valid API key", () => {
      const clerkHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CLERK,
      };

      return request(app.getHttpServer()).get("/report-set-columns").set(clerkHeaders).expect(200);
    });

    it("should reject when authenticated with unauthorized role even with valid API key", () => {
      const unauthorizedHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/report-set-columns").set(unauthorizedHeaders).expect(403);
    });

    it("should reject when API key is invalid regardless of role", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/report-set-columns").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject when not authenticated at all", () => {
      return request(app.getHttpServer()).get("/report-set-columns").expect(401);
    });
  });

  describe("/report-set-columns/:id (GET)", () => {
    it("should return a single report set column when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .get("/report-set-columns/1")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.reportSetColumn.findUnique).toHaveBeenCalledWith({
            where: { id: 1, deleted_at: null },
          });
          expect(response.body).toEqual(mockReportSetColumn);
        });
    });

    it("should return 404 if report set column not found", () => {
      jest.spyOn(databaseService.reportSetColumn, "findUnique").mockResolvedValueOnce(null);

      return request(app.getHttpServer()).get("/report-set-columns/999").set(authHeaders).expect(404);
    });

    it("should reject when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/report-set-columns/1").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject when not authenticated", () => {
      return request(app.getHttpServer()).get("/report-set-columns/1").expect(401);
    });
  });

  describe("/report-set-columns (POST)", () => {
    const createReportSetColumnDto = {
      name: "New Column",
      description: "A new report set column",
      unit_type: ReportSetColumnUnitType.EACH,
      report_set_id: 1,
      parent_id: null,
    };

    it("should create a new report set column when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .post("/report-set-columns")
        .set(authHeaders)
        .send(createReportSetColumnDto)
        .expect(201)
        .then((response) => {
          expect(databaseService.reportSetColumn.create).toHaveBeenCalledWith({
            data: expect.objectContaining({
              name: createReportSetColumnDto.name,
              description: createReportSetColumnDto.description,
              unit_type: createReportSetColumnDto.unit_type,
              report_set_id: createReportSetColumnDto.report_set_id,
              parent_id: createReportSetColumnDto.parent_id,
              code: expect.any(String), // Random code is generated
            }),
          });
          expect(response.body).toEqual(mockReportSetColumn);
        });
    });

    it("should reject creation when authenticated with unauthorized role", () => {
      const unauthorizedHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .post("/report-set-columns")
        .set(unauthorizedHeaders)
        .send(createReportSetColumnDto)
        .expect(403);
    });

    it("should reject creation when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .post("/report-set-columns")
        .set(invalidApiKeyHeaders)
        .send(createReportSetColumnDto)
        .expect(401);
    });

    it("should reject creation when not authenticated", () => {
      return request(app.getHttpServer()).post("/report-set-columns").send(createReportSetColumnDto).expect(401);
    });
  });

  describe("/report-set-columns/:id (DELETE)", () => {
    it("should delete a report set column when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .delete("/report-set-columns/1")
        .set(authHeaders)
        .expect(200)
        .then(() => {
          expect(databaseService.reportSetColumn.findUnique).toHaveBeenCalledWith({
            where: { id: 1 },
          });
          expect(databaseService.$transaction).toHaveBeenCalled();
        });
    });

    it("should return 404 if report set column not found", () => {
      jest.spyOn(databaseService.reportSetColumn, "findUnique").mockResolvedValueOnce(null);

      return request(app.getHttpServer()).delete("/report-set-columns/999").set(authHeaders).expect(404);
    });

    it("should reject deletion when authenticated with unauthorized role", () => {
      const unauthorizedHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).delete("/report-set-columns/1").set(unauthorizedHeaders).expect(403);
    });

    it("should reject deletion when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).delete("/report-set-columns/1").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject deletion when not authenticated", () => {
      return request(app.getHttpServer()).delete("/report-set-columns/1").expect(401);
    });
  });

  describe("System role access", () => {
    it("should allow access with SYSTEM role and valid API key", () => {
      const systemHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/report-set-columns").set(systemHeaders).expect(200);
    });

    it("should reject access with SYSTEM role but invalid API key", () => {
      const invalidSystemHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/report-set-columns").set(invalidSystemHeaders).expect(401);
    });
  });
});
