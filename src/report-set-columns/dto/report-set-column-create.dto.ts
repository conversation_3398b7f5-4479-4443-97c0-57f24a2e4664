import { ApiProperty } from "@nestjs/swagger";
import { ReportSetColumnUnitType } from "@prisma/client";

export class CreateReportSetColumnDto {
  @ApiProperty({
    required: true,
    description: "Name of the report set fraction column",
  })
  name: string;

  @ApiProperty({
    required: true,
    description: "Description of the report set fraction column",
  })
  description: string;

  @ApiProperty({
    required: true,
    description: "Unit type of the report set fraction column",
  })
  unit_type: ReportSetColumnUnitType;

  @ApiProperty({
    required: true,
    description: "ID of the associated report set",
  })
  report_set_id: number;

  @ApiProperty({
    required: false,
    description: "ID of the parent column (if any)",
  })
  parent_id?: number;
}
