import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { DatabaseService } from "../database/database.service";
import { CreateReportSetColumnDto } from "./dto/report-set-column-create.dto";
import { randomBytes } from "node:crypto";

@Injectable()
export class ReportSetColumnsService {
  constructor(private readonly databaseService: DatabaseService) {}

  async create(data: CreateReportSetColumnDto) {
    if (!data.report_set_id || Number.isNaN(Number(data.report_set_id))) {
      throw new BadRequestException("Invalid report set ID");
    }

    const reportSet = await this.databaseService.reportSet.findUnique({
      where: { id: data.report_set_id },
    });

    if (!reportSet) {
      throw new NotFoundException("Report set not found");
    }

    if (data.parent_id && Number.isNaN(Number(data.parent_id))) {
      throw new BadRequestException("Invalid parent ID");
    }

    if (data.parent_id) {
      const parentColumn = await this.databaseService.reportSetColumn.findUnique({
        where: { id: data.parent_id },
      });

      if (!parentColumn) {
        throw new NotFoundException("Parent column not found");
      }
    }

    return this.databaseService.reportSetColumn.create({
      data: {
        code: randomBytes(16).toString("hex"),
        name: data.name,
        description: data.description,
        unit_type: data.unit_type,
        report_set_id: data.report_set_id,
        parent_id: data.parent_id,
      },
    });
  }

  async findAll() {
    return this.databaseService.reportSetColumn.findMany({
      where: { deleted_at: null },
    });
  }

  async findOne(id: number) {
    if (!id || Number.isNaN(Number(id))) throw new BadRequestException("Invalid report set column ID");

    const reportSetColumn = await this.databaseService.reportSetColumn.findUnique({
      where: { id, deleted_at: null },
    });

    if (!reportSetColumn) {
      throw new NotFoundException("Report set column not found");
    }

    return reportSetColumn;
  }

  async remove(id: number) {
    if (!id || Number.isNaN(Number(id))) throw new BadRequestException("Invalid report set column ID");

    const reportSetColumn = await this.databaseService.reportSetColumn.findUnique({
      where: { id },
    });

    if (!reportSetColumn) {
      throw new NotFoundException("Report set column not found");
    }

    await this.databaseService.$transaction(async (prisma) => {
      await prisma.reportSetColumn.update({
        where: { id },
        data: { deleted_at: new Date() },
      });

      await prisma.reportSetColumn.updateMany({
        where: { parent_id: id },
        data: { deleted_at: new Date() },
      });
    });
  }
}
