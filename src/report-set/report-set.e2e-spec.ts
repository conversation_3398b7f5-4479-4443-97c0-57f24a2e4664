import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { AppModule } from "../app.module";
import { Role } from "../shared/auth/role.enum";
import { HEADER_SYSTEM_API_KEY, HEADER_USER_ID, HEADER_USER_ROLE, HEADER_USER_EMAIL } from "../shared/auth/const";
import { DatabaseService } from "../database/database.service";
import { PriceListType, PriceListConditionType } from "@prisma/client";

jest.setTimeout(30000);

describe("PriceListController (e2e)", () => {
  let app: INestApplication;
  let databaseService: DatabaseService;

  const validApiKey = "test-api-key";

  beforeAll(() => {
    process.env.SYSTEM_API_KEY = validApiKey;
  });

  const authHeaders = {
    [HEADER_SYSTEM_API_KEY]: validApi<PERSON>ey,
    [HEADER_USER_ID]: "1",
    [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
    [HEADER_USER_EMAIL]: "<EMAIL>",
  };

  const mockPriceList = {
    id: 1,
    type: PriceListType.EU_LICENSE,
    name: "Standard EU License",
    description: "Standard price list for EU licenses",
    start_date: "2025-01-01T00:00:00.000Z",
    end_date: "2025-12-31T23:59:59.999Z",
    basic_price: 100,
    minimum_price: 50,
    registration_fee: 25,
    variable_handling_fee: 0.05,
    price: null,
    condition_type: PriceListConditionType.LICENSE_YEAR,
    condition_type_value: "2025",
    handling_fee: 10,
    thresholds: JSON.stringify({ "10000": 5, "50000": 3, "100000": 1 }),
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
    deleted_at: null,
  };

  const mockPriceLists = [
    mockPriceList,
    {
      id: 2,
      type: PriceListType.DIRECT_LICENSE,
      name: "Premium Direct License",
      description: "Premium price list for direct licenses",
      start_date: "2025-01-01T00:00:00.000Z",
      end_date: "2025-12-31T23:59:59.999Z",
      basic_price: 200,
      minimum_price: 100,
      registration_fee: 50,
      variable_handling_fee: 0.1,
      price: 500,
      condition_type: PriceListConditionType.LICENSE_YEAR,
      condition_type_value: "2025",
      handling_fee: 20,
      thresholds: null,
      created_at: "2025-04-07T22:58:17.004Z",
      updated_at: "2025-04-07T22:58:17.004Z",
      deleted_at: null,
    },
  ];

  const mockCountryPriceList = {
    id: 1,
    country_id: 1,
    price_list_id: 1,
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
    deleted_at: null,
  };

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(DatabaseService)
      .useValue({
        priceList: {
          create: jest.fn().mockResolvedValue(mockPriceList),
          findMany: jest.fn().mockResolvedValue(mockPriceLists),
          findUnique: jest.fn().mockResolvedValue(mockPriceList),
          update: jest.fn().mockImplementation((params) => {
            return Promise.resolve({
              ...mockPriceList,
              ...params.data,
            });
          }),
          delete: jest.fn().mockResolvedValue(mockPriceList),
        },
        countryPriceList: {
          create: jest.fn().mockResolvedValue(mockCountryPriceList),
          findMany: jest.fn().mockResolvedValue([mockCountryPriceList]),
          findUnique: jest.fn().mockResolvedValue(mockCountryPriceList),
          delete: jest.fn().mockResolvedValue(mockCountryPriceList),
        },
        $transaction: jest.fn().mockImplementation((callbackFn) =>
          callbackFn({
            priceList: {
              update: jest.fn().mockImplementation((params) => {
                return Promise.resolve({
                  ...mockPriceList,
                  ...params.data,
                  deleted_at: params.data.deleted_at,
                });
              }),
            },
          })
        ),
      })
      .compile();

    app = moduleFixture.createNestApplication();
    databaseService = moduleFixture.get<DatabaseService>(DatabaseService);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("/price-lists (GET)", () => {
    it("should return price lists when authenticated as SUPER_ADMIN with valid API key", () => {
      return request(app.getHttpServer())
        .get("/price-lists")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.priceList.findMany).toHaveBeenCalled();
          expect(response.body).toEqual(mockPriceLists);
        });
    });

    it("should return price lists when authenticated as ADMIN with valid API key", () => {
      const adminHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.ADMIN,
      };

      return request(app.getHttpServer()).get("/price-lists").set(adminHeaders).expect(200);
    });

    it("should return price lists when authenticated as CLERK with valid API key", () => {
      const clerkHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CLERK,
      };

      return request(app.getHttpServer()).get("/price-lists").set(clerkHeaders).expect(200);
    });

    it("should reject when authenticated with unauthorized role even with valid API key", () => {
      const unauthorizedHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/price-lists").set(unauthorizedHeaders).expect(403);
    });

    it("should reject when API key is invalid regardless of role", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/price-lists").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject when not authenticated at all", () => {
      return request(app.getHttpServer()).get("/price-lists").expect(401);
    });
  });

  describe("/price-lists/:id (GET)", () => {
    it("should reject when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/price-lists/1").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject when not authenticated", () => {
      return request(app.getHttpServer()).get("/price-lists/1").expect(401);
    });
  });

  describe("/price-lists (POST)", () => {
    const createPriceListDto = {
      type: PriceListType.EU_LICENSE,
      name: "New EU License",
      description: "New price list for EU licenses",
      start_date: "2025-01-01T00:00:00.000Z",
      end_date: "2025-12-31T23:59:59.999Z",
      basic_price: 150,
      minimum_price: 75,
      registration_fee: 35,
      variable_handling_fee: 0.07,
      handling_fee: 15,
      condition_type: PriceListConditionType.LICENSE_YEAR,
      condition_type_value: "2025",
      country_ids: [1, 2],
    };

    it("should create a new price list when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .post("/price-lists")
        .set(authHeaders)
        .send(createPriceListDto)
        .expect(201)
        .then((response) => {
          expect(databaseService.priceList.create).toHaveBeenCalledWith({
            data: expect.objectContaining({
              type: createPriceListDto.type,
              name: createPriceListDto.name,
              description: createPriceListDto.description,
            }),
          });
          expect(response.body).toEqual(mockPriceList);
        });
    });

    it("should reject creation when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .post("/price-lists")
        .set(invalidApiKeyHeaders)
        .send(createPriceListDto)
        .expect(401);
    });

    it("should reject creation when not authenticated", () => {
      return request(app.getHttpServer()).post("/price-lists").send(createPriceListDto).expect(401);
    });
  });

  describe("/price-lists/:id (PUT)", () => {
    const updatePriceListDto = {
      name: "Updated EU License",
      description: "Updated price list description",
      basic_price: 175,
      minimum_price: 85,
      country_ids: [1, 3],
    };

    it("should reject update when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .put("/price-lists/1")
        .set(invalidApiKeyHeaders)
        .send(updatePriceListDto)
        .expect(401);
    });

    it("should reject update when not authenticated", () => {
      return request(app.getHttpServer()).put("/price-lists/1").send(updatePriceListDto).expect(401);
    });
  });

  describe("/price-lists/:id (DELETE)", () => {
    it("should reject deletion when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).delete("/price-lists/1").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject deletion when not authenticated", () => {
      return request(app.getHttpServer()).delete("/price-lists/1").expect(401);
    });
  });

  describe("System role access", () => {
    it("should allow access with SYSTEM role and valid API key", () => {
      const systemHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/price-lists").set(systemHeaders).expect(200);
    });

    it("should reject access with SYSTEM role but invalid API key", () => {
      const invalidSystemHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/price-lists").set(invalidSystemHeaders).expect(401);
    });
  });
});
