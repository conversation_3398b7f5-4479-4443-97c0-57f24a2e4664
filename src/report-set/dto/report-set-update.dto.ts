import { ApiProperty } from "@nestjs/swagger";
import { ReportSetColumn, ReportSetFraction, ReportSetPriceList } from "@prisma/client";

export class UpdateReportSetDto {
  @ApiProperty({
    required: false,
    description: "Name of the report set",
  })
  name?: string;

  @ApiProperty({
    required: false,
    description: "Sheet file id of the report set",
    example: "123e4567-e89b-12d3-a456-426614174000",
  })
  sheet_file_id?: string;

  @ApiProperty({
    required: false,
    description: "Description of the sheet file",
  })
  sheet_file_description?: string;

  @ApiProperty({
    required: false,
    description: "Fractions of the report set",
  })
  fractions?: Omit<ReportSetFraction, "report_set_id" | "created_at" | "updated_at" | "deleted_at">[];

  @ApiProperty({
    required: false,
    description: "Columns of the report set",
  })
  columns?: (Omit<ReportSetColumn, "report_set_id" | "created_at" | "updated_at" | "deleted_at"> & {
    fractions?: {
      column_id: number | null;
      column_code: string;
      fraction_code: string;
    }[];
  })[];

  @ApiProperty({
    required: false,
    description: "Price lists of the report set",
  })
  price_lists?: (Omit<ReportSetPriceList, "report_set_id" | "created_at" | "updated_at" | "deleted_at"> & {
    items?: {
      fraction_code: string;
      price: number;
      id?: number | undefined;
      fraction_id?: number | undefined;
    }[];
  })[];
}
