import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { generateCode } from "@/shared/utils/generate-code";
import { DatabaseService } from "../database/database.service";
import { CreateReportSetDto } from "./dto/report-set-create.dto";
import { UpdateReportSetDto } from "./dto/report-set-update.dto";
import { ReportSetFindAllDto } from "./dto/report-set-find-all.dto";

@Injectable()
export class ReportSetService {
  constructor(private readonly databaseService: DatabaseService) {}

  async create(data: CreateReportSetDto) {
    return this.databaseService.reportSet.create({
      data: {
        packaging_service_id: data.packaging_service_id,
        name: data.name,
        mode: data.mode,
        type: data.type,
      },
    });
  }

  async findAll(query: ReportSetFindAllDto) {
    return this.databaseService.reportSet.findMany({
      where: {
        deleted_at: null,
        ...(Number.isInteger(query.packaging_service_id) && {
          packaging_service_id: Number(query.packaging_service_id),
        }),
      },
      include: {
        columns: {
          where: {
            deleted_at: null,
          },
        },
        fractions: {
          where: {
            deleted_at: null,
          },
        },
      },
    });
  }

  async findOne(id: number) {
    if (!id || Number.isNaN(Number(id))) throw new BadRequestException("Invalid report set ID");

    const reportSet = await this.databaseService.reportSet.findUnique({
      where: { id, deleted_at: null },
    });

    if (!reportSet) {
      throw new NotFoundException("Report set not found");
    }

    return reportSet;
  }

  async update(id: number, data: UpdateReportSetDto) {
    if (!id || Number.isNaN(Number(id))) throw new BadRequestException("Invalid report set ID");

    const reportSet = await this.databaseService.reportSet.findUnique({
      where: { id },
    });

    if (!reportSet) {
      throw new NotFoundException("Report set not found");
    }

    return this.databaseService.$transaction(
      async (tx) => {
        await tx.reportSet.update({
          where: { id },
          data: {
            name: data.name,
            sheet_file_id: data.sheet_file_id || null,
            sheet_file_description: data.sheet_file_description || null,
          },
        });

        data.price_lists = data.price_lists || [];

        const priceListIds = data.price_lists.filter((price_list) => price_list.id).map((price_list) => price_list.id);

        if (priceListIds.length) {
          await tx.reportSetPriceListItem.deleteMany({
            where: {
              fraction_code: {
                in: data.fractions?.map((f) => f.code),
              },
            },
          });
        }

        data.columns = data.columns || [];
        await tx.reportSetColumnFraction.deleteMany({
          where: {
            column_code: {
              in: data.columns.map((column) => column.code),
            },
          },
        });

        await tx.reportSetFraction.deleteMany({
          where: { report_set_id: id },
        });

        await tx.reportSetColumn.deleteMany({
          where: { report_set_id: id },
        });

        if (data.fractions) {
          await tx.reportSetFraction.createMany({
            data: data.fractions.map((fraction) => ({
              code: fraction.code,
              parent_code: fraction.parent_code,
              name: fraction.name,
              description: fraction.description,
              icon: fraction.icon,
              fraction_icon_id: fraction.fraction_icon_id,
              level: fraction.level,
              order: fraction.order,
              parent_id: fraction.parent_id,
              report_set_id: id,
              has_second_level: fraction.has_second_level,
              has_third_level: fraction.has_third_level,
            })),
          });
        }

        if (data.columns) {
          await tx.reportSetColumn.createMany({
            data: data.columns.map((column) => ({
              code: column.code,
              parent_code: column.parent_code,
              name: column.name,
              description: column.description,
              unit_type: column.unit_type,
              level: column.level,
              order: column.order,
              parent_id: column.parent_id,
              report_set_id: id,
            })),
          });

          const columnFractions = data.columns.reduce((acc: any, column) => {
            if (column.fractions) {
              acc.push(...column.fractions);
            }
            return acc;
          }, []);

          await tx.reportSetColumnFraction.createMany({
            data: columnFractions.map((fraction: any) => ({
              column_code: fraction.column_code,
              fraction_code: fraction.fraction_code,
            })),
          });
        }

        if (data.price_lists) {
          for (const price_list of data.price_lists) {
            const items = (() => {
              if (!price_list.items) return undefined;

              return {
                createMany: {
                  data: price_list.items.map((item) => ({
                    fraction_code: item.fraction_code,
                    price: item.price,
                  })),
                },
              };
            })();

            await tx.reportSetPriceList.upsert({
              where: { id: price_list.id || 0 },
              update: {
                title: price_list.title,
                start_date: new Date(price_list.start_date),
                end_date: new Date(price_list.end_date),
                type: price_list.type,
                fixed_price: price_list.fixed_price,
                base_price: price_list.base_price,
                minimum_fee: price_list.minimum_fee,
                items,
              },
              create: {
                title: price_list.title,
                start_date: new Date(price_list.start_date),
                end_date: new Date(price_list.end_date),
                type: price_list.type,
                fixed_price: price_list.fixed_price,
                base_price: price_list.base_price,
                minimum_fee: price_list.minimum_fee,
                report_set_id: id,
                items,
              },
            });
          }
        }

        return tx.reportSet.findUnique({
          where: { id },
          include: {
            sheet_file: true,
            fractions: {
              include: {
                fraction_icon: true,
                children: {
                  include: {
                    fraction_icon: true,
                    children: true,
                  },
                },
              },
            },
            columns: {
              include: {
                children: {
                  include: {
                    children: true,
                  },
                },
              },
            },
            price_lists: {
              include: {
                items: true,
              },
            },
          },
        });
      },
      {
        timeout: 15000,
        maxWait: 15000,
      }
    );
  }

  async duplicate(id: number) {
    if (!id || Number.isNaN(Number(id))) throw new BadRequestException("Invalid report set ID");

    return await this.databaseService.$transaction(async (tx) => {
      const reportSet = await this.databaseService.reportSet.findUnique({
        where: { id },
        include: {
          sheet_file: true,
          fractions: {
            where: {
              deleted_at: null,
            },
          },
          columns: {
            where: {
              deleted_at: null,
            },
            include: {
              fractions: {
                where: { deleted_at: null },
              },
            },
          },
          price_lists: {
            where: { deleted_at: null },
            include: {
              items: {
                where: { deleted_at: null },
              },
            },
          },
        },
      });

      if (!reportSet) throw new NotFoundException("Report set not found");

      const duplicatedReportSet = await tx.reportSet.create({
        data: {
          packaging_service_id: reportSet.packaging_service_id,
          name: `${reportSet.name} - ${generateCode()}`,
          mode: reportSet.mode,
          type: reportSet.type,
        },
      });

      const codeDict: Record<string, string> = {};

      const fractions = reportSet.fractions.map((fraction: any) => {
        if (!codeDict[fraction.code]) codeDict[fraction.code] = generateCode();
        if (!codeDict[fraction.parent_code] && fraction.level !== 1) codeDict[fraction.parent_code] = generateCode();

        return {
          report_set_id: duplicatedReportSet.id,
          code: codeDict[fraction.code],
          parent_code: codeDict[fraction.parent_code],
          name: fraction.name,
          description: fraction.description,
          is_active: fraction.is_active,
          icon: fraction.icon,
          fraction_icon_id: fraction.fraction_icon_id,
          level: fraction.level,
          order: fraction.order,
          has_second_level: fraction.has_second_level,
          has_third_level: fraction.has_third_level,
        };
      });

      await tx.reportSetFraction.createMany({
        data: fractions.sort((a, b) => a.level - b.level),
      });

      const columns = reportSet.columns.map((column: any) => {
        if (!codeDict[column.code]) codeDict[column.code] = generateCode();
        if (!codeDict[column.parent_code] && column.level !== 1) codeDict[column.parent_code] = generateCode();

        return {
          report_set_id: duplicatedReportSet.id,
          name: column.name,
          description: column.description,
          unit_type: column.unit_type,
          level: column.level,
          order: column.order,
          code: codeDict[column.code],
          parent_code: codeDict[column.parent_code],
          fractions: column.fractions.map((fraction: any) => ({
            ...fraction,
            id: undefined,
            column_code: codeDict[fraction.column_code],
            fraction_code: codeDict[fraction.fraction_code],
          })),
        };
      });

      await tx.reportSetColumn.createMany({
        data: columns
          .map((c) => {
            const f = { ...c };
            delete f.fractions;
            return f;
          })
          .sort((a, b) => a.level - b.level),
      });

      const columnFractions = columns.reduce((acc: any, column) => {
        if (column.fractions) acc.push(...column.fractions);
        return acc;
      }, []);

      await tx.reportSetColumnFraction.createMany({
        data: columnFractions,
      });

      for (const price_list of reportSet.price_lists) {
        const createdPriceList = await tx.reportSetPriceList.create({
          data: {
            title: price_list.title,
            start_date: new Date(price_list.start_date),
            end_date: new Date(price_list.end_date),
            type: price_list.type,
            fixed_price: price_list.fixed_price,
            base_price: price_list.base_price,
            minimum_fee: price_list.minimum_fee,
            report_set_id: duplicatedReportSet.id,
          },
        });

        await tx.reportSetPriceListItem.createMany({
          data: price_list.items.map((item: any) => ({
            price: item.price,
            fraction_code: codeDict[item.fraction_code],
            price_list_id: createdPriceList.id,
          })),
        });
      }

      return { ok: true };
    });
  }

  async remove(id: number) {
    if (!id || Number.isNaN(Number(id))) throw new BadRequestException("Invalid report set ID");

    const reportSet = await this.databaseService.reportSet.findUnique({
      where: { id },
    });

    if (!reportSet) {
      throw new NotFoundException("Report set not found");
    }

    await this.databaseService.reportSet.update({
      where: { id },
      data: { deleted_at: new Date() },
    });
  }
}
