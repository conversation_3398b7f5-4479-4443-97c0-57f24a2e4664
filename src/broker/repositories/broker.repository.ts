import { Injectable, Logger } from "@nestjs/common";
import { DatabaseService } from "../../database/database.service";
import { Broker, Prisma } from "@prisma/client";
import { PaginationDto } from "../../shared/dto/pagination.dto";
import { BrokerPaginationReturn } from "../types";

@Injectable()
export class BrokerRepository {
  private readonly logger = new Logger(BrokerRepository.name);

  constructor(private readonly databaseService: DatabaseService) {}

  public async findAll(option?: Prisma.BrokerWhereInput, pagination?: PaginationDto): Promise<BrokerPaginationReturn> {
    try {
      const take = pagination?.limit || 10;
      const skip = ((pagination?.page || 1) - 1) * take;
      const [data, total] = await this.databaseService.$transaction([
        this.databaseService.broker.findMany({
          where: {
            ...option,
            deleted_at: null,
          },
          take,
          skip,
          select: {
            id: true,
            name: true,
            enroled_at: true,
          },
        }),
        this.databaseService.broker.count({
          where: {
            ...option,
            deleted_at: null,
          },
        }),
      ]);
      return {
        brokers: data,
        count: total,
        current_page: pagination?.page || 1,
        pages: Math.ceil(total / (pagination?.limit || 10)),
      };
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  public async findOne(option: Prisma.BrokerWhereUniqueInput): Promise<Broker> {
    try {
      return await this.databaseService.broker.findFirstOrThrow({
        where: {
          ...option,
          deleted_at: null,
        },
      });
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  public async create(data: Prisma.BrokerCreateInput): Promise<Broker> {
    try {
      return await this.databaseService.broker.create({ data });
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  public async update(options: Prisma.BrokerWhereUniqueInput, data: Prisma.BrokerUpdateInput): Promise<Broker> {
    try {
      return await this.databaseService.broker.update({
        where: {
          ...options,
          deleted_at: null,
        },
        data,
      });
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  public async softDelete(options: Prisma.BrokerWhereUniqueInput): Promise<Broker> {
    try {
      return await this.databaseService.broker.update({
        where: {
          ...options,
          deleted_at: null,
        },
        data: {
          deleted_at: new Date(),
        },
      });
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }

  public async delete(options: Prisma.BrokerWhereUniqueInput): Promise<Broker> {
    try {
      return await this.databaseService.broker.delete({ where: options });
    } catch (error) {
      this.logger.error(error);
      throw error;
    }
  }
}
