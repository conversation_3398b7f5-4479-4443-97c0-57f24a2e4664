import { Controller, Get, Post, Body, Patch, Param, Delete, Query, HttpCode } from "@nestjs/common";

import { FindByIdDto, QueryParams, CreateBrokerDto, UpdateBrokerDto } from "./dto";
import { CreatorService, FinderService, IndexerService, RemoverService, UpdaterService } from "./services";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK, Role.BROKER_MANAGER)
@ApiTags("Broker")
@Controller("broker")
export class BrokerDataController {
  constructor(
    private readonly creatorService: CreatorService,
    private readonly indexerService: IndexerService,
    private readonly finderService: FinderService,
    private readonly updaterService: UpdaterService,
    private readonly removerService: RemoverService
  ) {}

  @Post()
  @ApiOperation({ summary: "Create a new broker" })
  @ApiResponse({
    status: 201,
    description: "The broker has been successfully created.",
    schema: {
      example: {
        id: 1,
        name: "Broker 1",
        user_id: 1,
        email: "<EMAIL>",
        phone: "+39 333 1234567",
        enroled_at: "2024-03-20T10:00:00Z",
        company_name: "Broker 1",
        vat: "EXAMPLE_VAT",
        tax: null,
        created_at: "2024-03-20T10:00:00Z",
        updated_at: "2024-03-20T10:00:00Z",
        deleted_at: null,
        is_active: true,
      },
    },
  })
  async create(@Body() createBrokerDatumDto: CreateBrokerDto) {
    return this.creatorService.create(createBrokerDatumDto);
  }

  @Get()
  @ApiOperation({ summary: "Get all brokers" })
  @ApiResponse({
    status: 200,
    description: "The brokers have been successfully retrieved.",
    schema: {
      example: [
        {
          id: 1,
          name: "Broker 1",
          user_id: 1,
          email: "<EMAIL>",
          phone: "+39 333 1234567",
          enroled_at: "2024-03-20T10:00:00Z",
          company_name: "Broker 1",
          vat: "EXAMPLE_VAT",
          tax: null,
          created_at: "2024-03-20T10:00:00Z",
          updated_at: "2024-03-20T10:00:00Z",
          deleted_at: null,
          is_active: true,
        },
        {
          id: 2,
          name: "Broker 2",
          user_id: 2,
          email: "<EMAIL>",
          phone: "+39 333 1234567",
          enroled_at: "2024-03-20T10:00:00Z",
          company_name: "Broker 2",
          vat: null,
          tax: "EXAMPLE_TAX",
          created_at: "2024-03-20T10:00:00Z",
          updated_at: "2024-03-20T10:00:00Z",
          deleted_at: null,
          is_active: true,
        },
      ],
    },
  })
  async findAll(@Query() dto?: QueryParams) {
    return this.indexerService.index(dto);
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a broker by ID" })
  @ApiResponse({
    status: 200,
    description: "The broker has been successfully retrieved.",
    schema: {
      example: {
        id: 1,
        name: "Broker 1",
        user_id: 1,
        email: "<EMAIL>",
        phone: "+39 333 1234567",
        enroled_at: "2024-03-20T10:00:00Z",
        company_name: "Broker 1",
        vat: "EXAMPLE_VAT",
        tax: null,
        created_at: "2024-03-20T10:00:00Z",
        updated_at: "2024-03-20T10:00:00Z",
        deleted_at: null,
        is_active: true,
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: "The broker was not found.",
  })
  async findOne(@Param() { id }: FindByIdDto) {
    return this.finderService.findById(id);
  }

  @Get("auth/:id")
  @ApiOperation({ summary: "Get a broker by auth ID" })
  @ApiResponse({
    status: 200,
    description: "The broker has been successfully retrieved.",
    schema: {
      example: {
        id: 1,
        name: "Broker 1",
        user_id: 1,
        email: "<EMAIL>",
        phone: "+39 333 1234567",
        enroled_at: "2024-03-20T10:00:00Z",
        company_name: "Broker 1",
        vat: "EXAMPLE_VAT",
        tax: null,
        created_at: "2024-03-20T10:00:00Z",
        updated_at: "2024-03-20T10:00:00Z",
        deleted_at: null,
        is_active: true,
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: "The broker was not found.",
  })
  async findByAuthId(@Param() { id }: FindByIdDto) {
    return this.finderService.findByAuthId(id);
  }

  @Patch(":id")
  @ApiOperation({ summary: "Update a broker by ID" })
  @ApiResponse({
    status: 200,
    description: "The broker has been successfully updated.",
    schema: {
      example: {
        id: 1,
        name: "Broker 1",
        user_id: 1,
        email: "<EMAIL>",
        phone: "+39 333 1234567",
        enroled_at: "2024-03-20T10:00:00Z",
        company_name: "Broker 1",
        vat: "EXAMPLE_VAT",
        tax: null,
        created_at: "2024-03-20T10:00:00Z",
        updated_at: "2024-03-20T10:00:00Z",
        deleted_at: null,
        is_active: true,
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: "The broker was not found.",
  })
  async update(@Param() { id }: FindByIdDto, @Body() updateBrokerDatumDto: UpdateBrokerDto) {
    return this.updaterService.updateById(id, updateBrokerDatumDto);
  }

  @Delete(":id")
  @HttpCode(204)
  @ApiOperation({ summary: "Delete a broker by ID" })
  @ApiResponse({
    status: 204,
    description: "The broker has been successfully deleted.",
  })
  @ApiResponse({
    status: 404,
    description: "The broker was not found.",
  })
  async remove(@Param() { id }: FindByIdDto) {
    return this.removerService.removeById(id);
  }
}
