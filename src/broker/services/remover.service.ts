import { Injectable, InternalServerErrorException, Logger } from "@nestjs/common";
import { BrokerRepository } from "../repositories";

@Injectable()
export class RemoverService {
  private readonly logger = new Logger("BrokerRemoverService");

  constructor(private readonly brokerRepository: BrokerRepository) {}

  public async removeById(id: number): Promise<void> {
    try {
      await this.brokerRepository.softDelete({ id });
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException(error);
    }
  }
}
