import { <PERSON><PERSON><PERSON>, <PERSON>rism<PERSON> } from "@prisma/client";
import { BrokerRepository } from "../repositories";
import { Injectable, InternalServerErrorException, Logger, NotFoundException } from "@nestjs/common";
import { PrismaErrorCodes } from "../../shared/enums/prisma-error-codes.enum";

@Injectable()
export class FinderService {
  private readonly logger = new Logger("BrokerFinderService");

  constructor(private readonly brokerRepository: BrokerRepository) {}

  public async findById(id: number): Promise<Broker> {
    try {
      return await this.brokerRepository.findOne({ id });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === PrismaErrorCodes.NOT_FOUND)
        throw new NotFoundException(`could not find broker with id: ${id}`, error.message);
      this.logger.error(error);
      throw new InternalServerErrorException(error);
    }
  }

  public async findByAuthId(id: number): Promise<Broker> {
    try {
      return await this.brokerRepository.findOne({ user_id: id });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === PrismaErrorCodes.NOT_FOUND)
        throw new NotFoundException(`could not find broker with id: ${id}`, error.message);
      this.logger.error(error);
      throw new InternalServerErrorException(error);
    }
  }

  public async findByEmail(email: string): Promise<Broker> {
    try {
      return await this.brokerRepository.findOne({ email });
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException(error);
    }
  }
}
