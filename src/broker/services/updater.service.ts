import { Injectable, InternalServerErrorException, Logger } from "@nestjs/common";
import { UpdateBrokerDto } from "../dto/update-broker.dto";
import { Broker } from "@prisma/client";
import { BrokerRepository } from "../repositories";
import { HttpModuleService } from "../../http/http.service";
import { firstValueFrom } from "rxjs";

@Injectable()
export class UpdaterService {
  private readonly logger = new Logger(UpdaterService.name);

  constructor(
    private readonly brokerRepository: BrokerRepository,
    private readonly httpModuleService: HttpModuleService
  ) {}

  private async updateAuthUser(userId: number, params: Partial<UpdateBrokerDto>) {
    if (!params.email && !params.name) {
      this.logger.warn(`Skipping auth update: No email or name provided for user ${userId}`);
      return;
    }

    try {
      const response = await firstValueFrom(
        this.httpModuleService.auth({
          url: `/user/${userId}`,
          params,
          method: "PATCH",
        })
      );

      if (!response?.data) {
        this.logger.error(`Failed to update broker in auth: No response data received for user ${userId}`);
        throw new InternalServerErrorException("Error updating broker in auth");
      }

      this.logger.log(`Successfully updated auth user ${userId}`);
      return response.data;
    } catch (error) {
      this.logger.error(`Error updating broker in auth for user ${userId}: ${error.message}`, error.stack);
      throw new InternalServerErrorException("Error updating broker in auth");
    }
  }

  public async updateById(id: number, data: UpdateBrokerDto): Promise<Broker> {
    try {
      const updatedBroker = await this.brokerRepository.update({ id }, data);
      this.logger.log(`Successfully updated broker`);

      if (data.email || data.name) {
        await this.updateAuthUser(updatedBroker.user_id, { email: data.email, name: data.name });
        this.logger.log(`Updating broker in auth database`);
      }

      return updatedBroker;
    } catch (error) {
      this.logger.error(`Error updating broker ${id}: ${error.message}`, error.stack);
      throw new InternalServerErrorException("An error occurred while updating the broker");
    }
  }
}
