import { Injectable, InternalServerErrorException, Logger } from "@nestjs/common";
import { BrokerRepository } from "../repositories";
import { Prisma } from "@prisma/client";
import { QueryParams } from "../dto";
import { PaginationDto } from "../../shared/dto/pagination.dto";
import { BrokerPaginationReturn } from "../types";

@Injectable()
export class IndexerService {
  private readonly logger = new Logger("BrokerIndexerService");

  constructor(private readonly brokerRepository: BrokerRepository) {}

  public async index(query?: QueryParams): Promise<BrokerPaginationReturn> {
    try {
      const pagination: PaginationDto = {
        limit: Number(query?.limit) || 10,
        page: Number(query?.page) || 1,
      };
      const where: Prisma.BrokerWhereInput = {};

      if (query?.search) where.name = { contains: query.search, mode: "insensitive" };

      if (query?.year) where.enroled_at = { gte: new Date(`${query.year}-01-01T00:00:00.000Z`) };

      if (query?.startDate && query?.endDate)
        where.enroled_at = { gte: new Date(query.startDate), lte: new Date(query.endDate) };
      return await this.brokerRepository.findAll(where, pagination);
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException(error);
    }
  }
}
