import { ApiProperty } from "@nestjs/swagger";
import { IsDateString, IsEmail, IsOptional, IsString } from "class-validator";

export class CreateBrokerDto {
  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsString()
  @IsEmail()
  email: string;

  @ApiProperty()
  @IsString()
  phone: string;

  @ApiProperty()
  @IsDateString({ strict: false })
  enroled_at: string;

  @ApiProperty()
  @IsString()
  company_name: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  vat?: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  tax?: string;
}
