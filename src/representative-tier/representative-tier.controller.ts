import { Body, Controller, Delete, Get, Param, Post, Put } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { RepresentativeTierService } from "./representative-tier.service";
import { CreateRepresentativeTierDto } from "./dto/representative-tier-create.dto";
import { UpdateRepresentativeTierDto } from "./dto/representative-tier-update.dto";
import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";
import { RepresentativeTier } from "@prisma/client";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK)
@ApiTags("RepresentativeTier")
@Controller("representative-tiers")
export class RepresentativeTierController {
  constructor(private readonly representativeTierService: RepresentativeTierService) {}

  @Post()
  @ApiOperation({ summary: "Create a new representative tier" })
  @ApiResponse({
    status: 200,
    description: "Representative tier created successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        name: { type: "string" },
        price: { type: "number" },
        country_id: { type: "number" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
    },
    example: {
      name: "Tier 1",
      price: 100,
      country_id: 1,
    },
  })
  @ApiResponse({ status: 400, description: "Invalid country ID" })
  @ApiResponse({ status: 404, description: "Country not found" })
  create(@Body() data: CreateRepresentativeTierDto) {
    return this.representativeTierService.create(data);
  }

  @Get()
  @ApiOperation({ summary: "Get all representative tiers" })
  @ApiResponse({
    status: 200,
    description: "Representative tiers retrieved successfully",
    schema: {
      type: "array",
      items: {
        type: "object",
        properties: {
          id: { type: "number" },
          name: { type: "string" },
          price: { type: "number" },
          country_id: { type: "number" },
          created_at: { type: "string", format: "date-time" },
          updated_at: { type: "string", format: "date-time" },
          deleted_at: { type: "string", format: "date-time", nullable: true },
        },
      },
    },
    example: [
      {
        id: 1,
        name: "Tier 1",
        price: 100,
        country_id: 1,
      },
    ],
  })
  findAll() {
    return this.representativeTierService.findAll();
  }

  @Get(":id")
  @ApiOperation({ summary: "Get representative tier by ID" })
  @ApiResponse({
    status: 200,
    description: "Representative tier retrieved successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        name: { type: "string" },
        price: { type: "number" },
        country_id: { type: "number" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
    },
    example: {
      id: 1,
      name: "Tier 1",
      price: 100,
      country_id: 1,
    },
  })
  @ApiResponse({ status: 404, description: "Representative tier not found" })
  @ApiResponse({ status: 400, description: "Invalid representative tier ID" })
  findOne(@Param("id") id: string) {
    return this.representativeTierService.findOne(+id);
  }

  @Put(":id")
  @ApiOperation({ summary: "Update representative tier by ID" })
  @ApiResponse({
    status: 200,
    description: "Representative tier updated successfully",
    schema: {
      type: "object",
      properties: {
        id: { type: "number" },
        name: { type: "string" },
        price: { type: "number" },
        country_id: { type: "number" },
        created_at: { type: "string", format: "date-time" },
        updated_at: { type: "string", format: "date-time" },
        deleted_at: { type: "string", format: "date-time", nullable: true },
      },
    },
    example: {
      id: 1,
      name: "Tier 1",
      price: 100,
      country_id: 1,
    },
  })
  @ApiResponse({ status: 404, description: "Representative tier not found" })
  @ApiResponse({ status: 400, description: "Invalid representative tier ID" })
  update(@Param("id") id: string, @Body() data: UpdateRepresentativeTierDto) {
    return this.representativeTierService.update(+id, data);
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete representative tier by ID" })
  @ApiResponse({
    status: 200,
    description: "Representative tier deleted successfully",
  })
  @ApiResponse({ status: 404, description: "Representative tier not found" })
  @ApiResponse({ status: 400, description: "Invalid representative tier ID" })
  remove(@Param("id") id: string) {
    return this.representativeTierService.remove(+id);
  }
}
