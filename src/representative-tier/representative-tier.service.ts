import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { DatabaseService } from "../database/database.service";
import { CreateRepresentativeTierDto } from "./dto/representative-tier-create.dto";
import { UpdateRepresentativeTierDto } from "./dto/representative-tier-update.dto";

@Injectable()
export class RepresentativeTierService {
  constructor(private readonly databaseService: DatabaseService) {}

  async create(data: CreateRepresentativeTierDto) {
    if (Number.isNaN(Number(data.country_id))) {
      throw new BadRequestException("Invalid country ID");
    }

    const country = await this.databaseService.country.findUnique({
      where: { id: data.country_id },
    });

    if (!country) {
      throw new NotFoundException("Country not found");
    }

    return this.databaseService.representativeTier.create({
      data: {
        name: data.name,
        price: data.price,
        country_id: data.country_id,
      },
    });
  }

  async findAll() {
    return this.databaseService.representativeTier.findMany({
      where: { deleted_at: null },
    });
  }

  async findOne(id: number) {
    if (Number.isNaN(Number(id))) {
      throw new BadRequestException("Invalid representative tier ID");
    }

    const representativeTier = await this.databaseService.representativeTier.findUnique({
      where: { id, deleted_at: null },
    });

    if (!representativeTier) {
      throw new NotFoundException("Representative tier not found");
    }

    return representativeTier;
  }

  async update(id: number, data: UpdateRepresentativeTierDto) {
    if (Number.isNaN(Number(id))) {
      throw new BadRequestException("Invalid representative tier ID");
    }

    const representativeTier = await this.databaseService.representativeTier.findUnique({
      where: { id },
    });

    if (!representativeTier) {
      throw new NotFoundException("Representative tier not found");
    }

    return this.databaseService.representativeTier.update({
      where: { id },
      data,
    });
  }

  async remove(id: number) {
    if (!id || Number.isNaN(Number(id))) throw new BadRequestException("Invalid representative tier ID");

    const representativeTier = await this.databaseService.representativeTier.findUnique({
      where: { id },
    });

    if (!representativeTier) {
      throw new NotFoundException("Representative tier not found");
    }

    await this.databaseService.representativeTier.update({
      where: { id },
      data: { deleted_at: new Date() },
    });
  }
}
