import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { AppModule } from "../app.module";
import { Role } from "../shared/auth/role.enum";
import { HEADER_SYSTEM_API_KEY, HEADER_USER_ID, HEADER_USER_ROLE, HEADER_USER_EMAIL } from "../shared/auth/const";
import { DatabaseService } from "../database/database.service";

jest.setTimeout(30000);

describe("RepresentativeTierController (e2e)", () => {
  let app: INestApplication;
  let databaseService: DatabaseService;

  const validApiKey = "test-api-key";

  beforeAll(() => {
    process.env.SYSTEM_API_KEY = validApiKey;
  });

  const authHeaders = {
    [HEADER_SYSTEM_API_KEY]: validApi<PERSON>ey,
    [HEADER_USER_ID]: "1",
    [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
    [HEADER_USER_EMAIL]: "<EMAIL>",
  };

  const mockRepresentativeTier = {
    id: 1,
    name: "Premium",
    price: 1000,
    country_id: 1,
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
    deleted_at: null,
  };

  const mockRepresentativeTiers = [
    mockRepresentativeTier,
    {
      id: 2,
      name: "Standard",
      price: 500,
      country_id: 1,
      created_at: "2025-04-07T22:58:17.004Z",
      updated_at: "2025-04-07T22:58:17.004Z",
      deleted_at: null,
    },
  ];

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(DatabaseService)
      .useValue({
        representativeTier: {
          create: jest.fn().mockResolvedValue(mockRepresentativeTier),
          findMany: jest.fn().mockResolvedValue(mockRepresentativeTiers),
          findUnique: jest.fn().mockResolvedValue(mockRepresentativeTier),
          update: jest.fn().mockImplementation((params) => {
            return Promise.resolve({
              ...mockRepresentativeTier,
              ...params.data,
            });
          }),
        },
      })
      .compile();

    app = moduleFixture.createNestApplication();
    databaseService = moduleFixture.get<DatabaseService>(DatabaseService);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("/representative-tiers (GET)", () => {
    it("should return representative tiers when authenticated as SUPER_ADMIN with valid API key", () => {
      return request(app.getHttpServer())
        .get("/representative-tiers")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.representativeTier.findMany).toHaveBeenCalled();
          expect(response.body).toEqual(mockRepresentativeTiers);
        });
    });

    it("should return representative tiers when authenticated as ADMIN with valid API key", () => {
      const adminHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.ADMIN,
      };

      return request(app.getHttpServer()).get("/representative-tiers").set(adminHeaders).expect(200);
    });

    it("should return representative tiers when authenticated as CLERK with valid API key", () => {
      const clerkHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CLERK,
      };

      return request(app.getHttpServer()).get("/representative-tiers").set(clerkHeaders).expect(200);
    });

    it("should reject when authenticated with unauthorized role even with valid API key", () => {
      const unauthorizedHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/representative-tiers").set(unauthorizedHeaders).expect(403);
    });

    it("should reject when API key is invalid regardless of role", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/representative-tiers").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject when not authenticated at all", () => {
      return request(app.getHttpServer()).get("/representative-tiers").expect(401);
    });
  });

  describe("/representative-tiers/:id (GET)", () => {
    it("should return a single representative tier when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .get("/representative-tiers/1")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.representativeTier.findUnique).toHaveBeenCalledWith({
            where: { id: 1, deleted_at: null },
          });
          expect(response.body).toEqual(mockRepresentativeTier);
        });
    });

    it("should reject when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/representative-tiers/1").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject when not authenticated", () => {
      return request(app.getHttpServer()).get("/representative-tiers/1").expect(401);
    });
  });

  describe("/representative-tiers (POST)", () => {
    const createRepresentativeTierDto = {
      name: "Enterprise",
      price: 2000,
      country_id: 1,
    };

    it("should create a new representative tier when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .post("/representative-tiers")
        .set(authHeaders)
        .send(createRepresentativeTierDto)
        .expect(201)
        .then((response) => {
          expect(databaseService.representativeTier.create).toHaveBeenCalledWith({
            data: createRepresentativeTierDto,
          });
          expect(response.body).toEqual(mockRepresentativeTier);
        });
    });

    it("should reject creation when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .post("/representative-tiers")
        .set(invalidApiKeyHeaders)
        .send(createRepresentativeTierDto)
        .expect(401);
    });

    it("should reject creation when not authenticated", () => {
      return request(app.getHttpServer()).post("/representative-tiers").send(createRepresentativeTierDto).expect(401);
    });
  });

  describe("/representative-tiers/:id (PUT)", () => {
    const updateRepresentativeTierDto = {
      name: "Premium Plus",
      price: 1500,
    };

    it("should update a representative tier when authenticated with valid API key", () => {
      const expectedResponse = {
        ...mockRepresentativeTier,
        ...updateRepresentativeTierDto,
      };

      return request(app.getHttpServer())
        .put("/representative-tiers/1")
        .set(authHeaders)
        .send(updateRepresentativeTierDto)
        .expect(200)
        .then((response) => {
          expect(databaseService.representativeTier.update).toHaveBeenCalledWith({
            where: { id: 1 },
            data: updateRepresentativeTierDto,
          });
          expect(response.body).toEqual(expectedResponse);
        });
    });

    it("should reject update when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .put("/representative-tiers/1")
        .set(invalidApiKeyHeaders)
        .send(updateRepresentativeTierDto)
        .expect(401);
    });

    it("should reject update when not authenticated", () => {
      return request(app.getHttpServer()).put("/representative-tiers/1").send(updateRepresentativeTierDto).expect(401);
    });
  });

  describe("/representative-tiers/:id (DELETE)", () => {
    it("should reject deletion when API key is invalid", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).delete("/representative-tiers/1").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject deletion when not authenticated", () => {
      return request(app.getHttpServer()).delete("/representative-tiers/1").expect(401);
    });
  });

  describe("System role access", () => {
    it("should allow access with SYSTEM role and valid API key", () => {
      const systemHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/representative-tiers").set(systemHeaders).expect(200);
    });

    it("should reject access with SYSTEM role but invalid API key", () => {
      const invalidSystemHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/representative-tiers").set(invalidSystemHeaders).expect(401);
    });
  });
});
