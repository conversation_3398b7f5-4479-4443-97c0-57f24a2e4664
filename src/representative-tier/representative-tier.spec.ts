import { Test, TestingModule } from "@nestjs/testing";
import { RepresentativeTierService } from "./representative-tier.service";
import { DatabaseService } from "../database/database.service";
import { CreateRepresentativeTierDto } from "./dto/representative-tier-create.dto";
import { UpdateRepresentativeTierDto } from "./dto/representative-tier-update.dto";
import { NotFoundException, BadRequestException } from "@nestjs/common";

describe("RepresentativeTierService", () => {
  let service: RepresentativeTierService;
  let databaseServiceMock: any;

  beforeEach(async () => {
    databaseServiceMock = {
      representativeTier: {
        create: jest.fn(),
        findMany: jest.fn(),
        findUnique: jest.fn(),
        update: jest.fn(),
      },
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [RepresentativeTierService, { provide: DatabaseService, useValue: databaseServiceMock }],
    }).compile();

    service = module.get<RepresentativeTierService>(RepresentativeTierService);
  });

  describe("create", () => {
    it("should create a representative tier successfully", async () => {
      const createDto: CreateRepresentativeTierDto = {
        name: "Tier 1",
        price: 100,
        country_id: 1,
      };

      const mockResponse = {
        id: 1,
        ...createDto,
      };

      databaseServiceMock.representativeTier.create.mockResolvedValue(mockResponse);

      const result = await service.create(createDto);

      expect(result).toEqual(mockResponse);
      expect(databaseServiceMock.representativeTier.create).toHaveBeenCalledWith({
        data: createDto,
      });
    });
  });

  describe("findAll", () => {
    it("should return all representative tiers", async () => {
      const mockResponse = [
        { id: 1, name: "Tier 1", price: 100, country_id: 1 },
        { id: 2, name: "Tier 2", price: 150, country_id: 2 },
      ];

      databaseServiceMock.representativeTier.findMany.mockResolvedValue(mockResponse);

      const result = await service.findAll();

      expect(result).toEqual(mockResponse);
      expect(databaseServiceMock.representativeTier.findMany).toHaveBeenCalledWith({
        where: { deleted_at: null },
      });
    });
  });

  describe("findOne", () => {
    it("should return a representative tier by ID", async () => {
      const mockResponse = { id: 1, name: "Tier 1", price: 100, country_id: 1 };

      databaseServiceMock.representativeTier.findUnique.mockResolvedValue(mockResponse);

      const result = await service.findOne(1);

      expect(result).toEqual(mockResponse);
      expect(databaseServiceMock.representativeTier.findUnique).toHaveBeenCalledWith({
        where: { id: 1, deleted_at: null },
      });
    });

    it("should throw NotFoundException if representative tier not found", async () => {
      databaseServiceMock.representativeTier.findUnique.mockResolvedValue(null);

      await expect(service.findOne(1)).rejects.toThrow(NotFoundException);
      await expect(service.findOne(1)).rejects.toThrow("Representative tier not found");
    });
  });

  describe("update", () => {
    it("should update a representative tier successfully", async () => {
      const updateDto: UpdateRepresentativeTierDto = { name: "Updated Tier", price: 120, country_id: 1 };
      const mockResponse = { id: 1, ...updateDto };

      databaseServiceMock.representativeTier.findUnique.mockResolvedValue(mockResponse);
      databaseServiceMock.representativeTier.update.mockResolvedValue(mockResponse);

      const result = await service.update(1, updateDto);

      expect(result).toEqual(mockResponse);
      expect(databaseServiceMock.representativeTier.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: updateDto,
      });
    });

    it("should throw NotFoundException if representative tier not found for update", async () => {
      const updateDto: UpdateRepresentativeTierDto = { name: "Updated Tier", price: 120, country_id: 1 };

      databaseServiceMock.representativeTier.findUnique.mockResolvedValue(null);

      await expect(service.update(1, updateDto)).rejects.toThrow(NotFoundException);
      await expect(service.update(1, updateDto)).rejects.toThrow("Representative tier not found");
    });
  });

  describe("remove", () => {
    it("should remove a representative tier successfully", async () => {
      const mockResponse = { id: 1, deleted_at: new Date() };

      databaseServiceMock.representativeTier.findUnique.mockResolvedValue(mockResponse);
      databaseServiceMock.representativeTier.update.mockResolvedValue(mockResponse);

      const result = await service.remove(1);

      expect(result).toEqual(mockResponse);
      expect(databaseServiceMock.representativeTier.update).toHaveBeenCalledWith({
        where: { id: 1 },
        data: { deleted_at: expect.any(Date) },
      });
    });

    it("should throw BadRequestException if ID is invalid", async () => {
      await expect(service.remove(NaN)).rejects.toThrow(BadRequestException);
      await expect(service.remove(NaN)).rejects.toThrow("Invalid representative tier ID");
    });

    it("should throw NotFoundException if representative tier not found for removal", async () => {
      databaseServiceMock.representativeTier.findUnique.mockResolvedValue(null);

      await expect(service.remove(1)).rejects.toThrow(NotFoundException);
      await expect(service.remove(1)).rejects.toThrow("Representative tier not found");
    });
  });
});
