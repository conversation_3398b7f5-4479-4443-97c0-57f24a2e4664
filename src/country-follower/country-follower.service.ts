import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { DatabaseService } from "../database/database.service";
import { CreateCountryFollowerDto } from "./dto/country-follower-create.dto";
import { RemoveCountryFollowerDto } from "./dto/country-follower-delete.dto.ts";
import { HttpModuleService } from "@/http/http.service";
import { lastValueFrom } from "rxjs";

@Injectable()
export class CountryFollowerService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly httpModuleService: HttpModuleService
  ) {}

  async create(data: CreateCountryFollowerDto) {
    if (!data.country_id || Number.isNaN(data.country_id)) {
      throw new BadRequestException("Country ID is required");
    }

    if (!data.user_ids || !data.user_ids.length) {
      throw new BadRequestException("User IDs are required");
    }

    const usersResponse = await lastValueFrom(
      this.httpModuleService.auth({
        url: `/user`,
        method: "GET",
        params: {
          ids: data.user_ids.join(","),
        },
      })
    );

    if (!usersResponse) {
      throw new NotFoundException("Users not found");
    }

    const formattedUsers = data.user_ids.map((userId: number) => {
      const user = usersResponse.data.find((user: any) => user.id === userId);

      if (!user) {
        throw new NotFoundException(`User ${userId} not found`);
      }

      return {
        id: user.id,
        first_name: user.name.split(" ")[0] || "",
        last_name: user.name.split(" ")[1] || "",
        email: user.email,
      };
    });

    if (!formattedUsers) {
      throw new NotFoundException("Users not found");
    }

    await this.databaseService.countryFollower.createMany({
      data: formattedUsers.map((user: any) => ({
        country_id: data.country_id,
        user_id: user.id,
        user_email: user.email,
        user_first_name: user.first_name,
        user_last_name: user.last_name,
      })),
    });

    return {
      message: "Country followers created successfully",
    };
  }

  async remove(data: RemoveCountryFollowerDto) {
    if (!data.country_id || Number.isNaN(Number(data.country_id))) {
      throw new BadRequestException("Country ID is required");
    }

    if (!data.user_id || Number.isNaN(Number(data.user_id))) {
      throw new BadRequestException("User ID is required");
    }

    const countryFollower = await this.databaseService.countryFollower.findFirst({
      where: {
        country_id: Number(data.country_id),
        user_id: Number(data.user_id),
      },
    });

    if (!countryFollower) {
      throw new NotFoundException("Country follower not found");
    }

    await this.databaseService.countryFollower.deleteMany({
      where: {
        country_id: Number(data.country_id),
        user_id: Number(data.user_id),
      },
    });

    return {
      message: "Country follower deleted successfully",
    };
  }
}
