import { Body, Controller, Delete, Post, Query } from "@nestjs/common";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { CountryFollowerService } from "./country-follower.service";
import { CreateCountryFollowerDto } from "./dto/country-follower-create.dto";
import { RemoveCountryFollowerDto } from "./dto/country-follower-delete.dto.ts";
import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK)
@ApiTags("Country Follower")
@Controller("country-followers")
export class CountryFollowerController {
  constructor(private readonly countryFollowerService: CountryFollowerService) {}

  @Post()
  @ApiOperation({ summary: "Create a new country follower" })
  @ApiResponse({
    status: 201,
    description: "Country follower created successfully",
    schema: {
      type: "object",
      properties: {
        message: { type: "string" },
      },
    },
  })
  @ApiResponse({ status: 400, description: "Invalid input data" })
  create(@Body() data: CreateCountryFollowerDto) {
    return this.countryFollowerService.create(data);
  }

  @Delete()
  @ApiOperation({ summary: "Delete country follower by ID" })
  @ApiResponse({ status: 200, description: "Country follower deleted successfully" })
  @ApiResponse({ status: 400, description: "Invalid input data" })
  @ApiResponse({ status: 404, description: "Country follower not found" })
  remove(@Query() query: RemoveCountryFollowerDto) {
    return this.countryFollowerService.remove(query);
  }
}
