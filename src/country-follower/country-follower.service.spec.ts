import { Test, TestingModule } from "@nestjs/testing";
import { CountryService } from "../country/country.service";
import { CountryFollowerService } from "./country-follower.service";
import { DatabaseService } from "../database/database.service";
import { HttpModuleService } from "@/http/http.service";
import { BadRequestException, NotFoundException } from "@nestjs/common";
import { of } from "rxjs";

const mockDatabaseService = {
  country: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  countryFollower: {
    createMany: jest.fn(),
    findFirst: jest.fn(),
    deleteMany: jest.fn(),
  },
};

const mockHttpModuleService = {
  customer: jest.fn().mockImplementation(() => of({ data: {} })),
  auth: jest.fn().mockImplementation(() => of({ data: [{ id: 1, name: "<PERSON>", email: "<EMAIL>" }] })),
};

describe("CountryService", () => {
  let service: CountryService;
  let databaseService: typeof mockDatabaseService;
  let httpModuleService: typeof mockHttpModuleService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CountryService,
        { provide: DatabaseService, useValue: mockDatabaseService },
        { provide: HttpModuleService, useValue: mockHttpModuleService },
      ],
    }).compile();

    service = module.get<CountryService>(CountryService);
    databaseService = module.get(DatabaseService);
    httpModuleService = module.get(HttpModuleService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });

  describe("create", () => {
    it("should create a country", async () => {
      const dto = { name: "Brazil", code: "BR", flag_url: "https://flag.url" };
      databaseService.country.create.mockResolvedValue(dto);
      const result = await service.create(dto);
      expect(result).toEqual(dto);
      expect(databaseService.country.create).toHaveBeenCalledWith({ data: dto });
    });
  });

  describe("CountryFollowerService", () => {
    let followerService: CountryFollowerService;

    beforeEach(async () => {
      const module: TestingModule = await Test.createTestingModule({
        providers: [
          CountryFollowerService,
          { provide: DatabaseService, useValue: mockDatabaseService },
          { provide: HttpModuleService, useValue: mockHttpModuleService },
        ],
      }).compile();

      followerService = module.get<CountryFollowerService>(CountryFollowerService);
    });

    describe("create", () => {
      it("should create country followers", async () => {
        const dto = { country_id: 1, user_ids: [1, 2] };
        mockHttpModuleService.auth.mockImplementation(() =>
          of({
            data: [
              { id: 1, name: "John Doe", email: "<EMAIL>" },
              { id: 2, name: "Jane Smith", email: "<EMAIL>" },
            ],
          })
        );
        databaseService.countryFollower.createMany.mockResolvedValue(undefined);

        const result = await followerService.create(dto);
        expect(result).toEqual({ message: "Country followers created successfully" });
        expect(databaseService.countryFollower.createMany).toHaveBeenCalled();
      });

      it("should throw BadRequestException if country_id is missing", async () => {
        const dto = { user_ids: [1] };
        await expect(followerService.create(dto as any)).rejects.toThrow(BadRequestException);
      });

      it("should throw BadRequestException if user_ids are missing", async () => {
        const dto = { country_id: 1, user_ids: [] };
        await expect(followerService.create(dto)).rejects.toThrow(BadRequestException);
      });

      it("should throw NotFoundException if user not found", async () => {
        mockHttpModuleService.auth.mockImplementation(() => of({ data: [] }));
        const dto = { country_id: 1, user_ids: [1] };
        await expect(followerService.create(dto)).rejects.toThrow(NotFoundException);
      });
    });

    describe("remove", () => {
      it("should remove a country follower", async () => {
        const dto = { country_id: 1, user_id: 1 };
        databaseService.countryFollower.findFirst.mockResolvedValue(dto);
        databaseService.countryFollower.deleteMany.mockResolvedValue(undefined);

        const result = await followerService.remove(dto);
        expect(result).toEqual({ message: "Country follower deleted successfully" });
      });

      it("should throw BadRequestException if country_id is missing", async () => {
        const dto = { user_id: 1 };
        await expect(followerService.remove(dto as any)).rejects.toThrow(BadRequestException);
      });

      it("should throw BadRequestException if user_id is missing", async () => {
        const dto = { country_id: 1 };
        await expect(followerService.remove(dto as any)).rejects.toThrow(BadRequestException);
      });

      it("should throw NotFoundException if country follower not found", async () => {
        databaseService.countryFollower.findFirst.mockResolvedValue(null);
        const dto = { country_id: 1, user_id: 1 };
        await expect(followerService.remove(dto)).rejects.toThrow(NotFoundException);
      });
    });
  });
});
