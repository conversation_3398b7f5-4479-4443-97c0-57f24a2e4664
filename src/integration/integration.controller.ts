import { Body, Controller, Get, Param, Post } from "@nestjs/common";
import { CustomerIoService } from "./customer-io.service";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";

import { Roles } from "@/shared/auth/role.decorator";
import { Role } from "@/shared/auth/role.enum";

import { CreateCustomerEventDto } from "./dto/create-customer-event.dto";
import { CreateCustomerEmailDto } from "./dto/create-customer-email.dto";

@Roles(Role.SUPER_ADMIN, Role.ADMIN, Role.CLERK)
@ApiTags("Integration")
@Controller("integrations")
export class IntegrationController {
  constructor(private readonly customerIoService: CustomerIoService) {}

  @Get("/customer-io/:email")
  @ApiOperation({ summary: "Get customer.io customer by email" })
  @ApiResponse({
    status: 200,
    description: "Customer.io customer retrieved successfully",
    schema: {
      type: "object",
    },
  })
  findOne(@Param("email") email: string) {
    return this.customerIoService.findOne(email);
  }

  @Post("/customer-io/:email")
  @ApiOperation({ summary: "Create a new customer.io customer" })
  @ApiResponse({
    status: 200,
    description: "Customer.io customer created successfully",
    schema: {
      type: "object",
    },
  })
  create(@Param("email") email: string, @Body() data: unknown) {
    return this.customerIoService.uploadCustomer(email, data);
  }

  @Post("/customer-io/event/:email")
  @ApiOperation({ summary: "Create a new customer.io customer" })
  @ApiResponse({
    status: 200,
    description: "Customer.io customer created successfully",
    schema: {
      type: "object",
    },
  })
  trackEvent(@Param("email") email: string, @Body() data: CreateCustomerEventDto) {
    return this.customerIoService.trackEvent(email, data.eventName, data.eventData);
  }

  @Post("/customer-io/update-email/:oldEmail")
  @ApiOperation({ summary: "Update customer.io customer email" })
  @ApiResponse({
    status: 200,
    description: "Customer.io customer email updated successfully",
    schema: {
      type: "object",
    },
  })
  updateCustomerEmail(@Param("oldEmail") oldEmail: string, @Body() data: CreateCustomerEmailDto) {
    return this.customerIoService.updateCustomerEmail(oldEmail, data.newEmail);
  }
}
