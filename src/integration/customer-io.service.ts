import { HttpException, HttpStatus, Injectable } from "@nestjs/common";
import { APIClient, IdentifierType, RegionEU, TrackClient } from "customerio-node";

@Injectable()
export class CustomerIoService {
  private api;
  private cio;

  constructor() {
    const customerIoKey = process.env.CUSTOMER_IO_KEY;
    const customerIoSiteId = process.env.CUSTOMER_IO_SITE_ID;
    const customerIoApiKey = process.env.CUSTOMER_IO_API_KEY;

    if (!customerIoKey || !customerIoSiteId || !customerIoApiKey) {
      throw new Error("Missing required environment variables for Customer.io");
    }

    this.api = new APIClient(customerIoKey, { region: RegionEU });
    this.cio = new TrackClient(customerIoSiteId, customerIoApiKey, { region: RegionEU });
  }

  async findOne(email: string) {
    try {
      const apiInformation = await this.api.getAttributes(email, IdentifierType.Email);
      return apiInformation;
    } catch (error) {
      console.error(error);
      throw new HttpException("Error when making the request", HttpStatus.BAD_REQUEST);
    }
  }

  async uploadCustomer(email: string, data: any) {
    try {
      await this.cio.identify(email, data);

      return {
        message: `User updated successfully`,
      };
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  async trackEvent(email: string, eventName: string, eventData: Record<string, unknown>) {
    try {
      await this.cio.track(email, { name: eventName, data: eventData });

      return {
        message: `Event '${eventName}' tracked successfully for user ${email}`,
      };
    } catch (error) {
      console.error(error);
      throw new HttpException(
        `Failed to track event '${eventName}' for user ${email}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async updateCustomerEmail(oldEmail: string, newEmail: string) {
    if (oldEmail === newEmail) return;

    try {
      let newCustomerData;

      try {
        newCustomerData = await this.api.getAttributes(newEmail, IdentifierType.Email);
      } catch (error) {
        console.error(error);
      }

      if (newCustomerData?.customer?.id) await this.cio.destroy(newCustomerData?.customer?.id);

      const oldCustomerData = await this.api.getAttributes(oldEmail, IdentifierType.Email);

      const attributes = oldCustomerData?.customer?.attributes;
      delete attributes.cio_id;

      await this.uploadCustomer(newEmail, { ...attributes, email: newEmail, id: oldCustomerData?.customer?.id });
      await this.uploadCustomer(newEmail, { timestamps: oldCustomerData?.customer?.timestamps });

      await this.cio.destroy(oldCustomerData?.customer?.id);

      return {
        message: `Customer email updated successfully from ${oldEmail} to ${newEmail}`,
      };
    } catch (error) {
      console.error(error);
      throw new HttpException(
        `Failed to update customer email from ${oldEmail} to ${newEmail}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
