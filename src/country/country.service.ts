import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { DatabaseService } from "../database/database.service";
import { CreateCountryDto } from "./dto/country-create.dto";
import { UpdateCountryDto } from "./dto/country-update.dto";
import { lastValueFrom } from "rxjs";
import { HttpModuleService } from "@/http/http.service";
import { Prisma } from "@prisma/client";

@Injectable()
export class CountryService {
  constructor(
    private readonly databaseService: DatabaseService,
    private readonly httpModuleService: HttpModuleService
  ) {}

  async create(data: CreateCountryDto) {
    return this.databaseService.country.create({
      data,
    });
  }

  async findAll() {
    const countries = await this.databaseService.country.findMany({
      orderBy: {
        name: "asc",
      },
    });

    return countries;
  }

  async findOne(id: number) {
    if (!id || Number.isNaN(id)) {
      throw new BadRequestException("Country ID is required");
    }

    const country = await this.databaseService.country.findUnique({
      where: { id },
    });

    if (!country) {
      throw new NotFoundException("Country not found");
    }

    return country;
  }

  async overview(query: { search?: string }) {
    const searchFilter: Prisma.CountryWhereInput = {
      OR: [
        { name: { contains: query.search, mode: "insensitive" } },
        { code: { contains: query.search, mode: "insensitive" } },
      ],
    };

    const countries = await this.databaseService.country.findMany({
      where: query.search ? searchFilter : {},
      select: {
        id: true,
        name: true,
        code: true,
        flag_url: true,
        followers: true,
      },
      orderBy: {
        name: "asc",
      },
    });

    const customersGroupedByCountryResponse = await lastValueFrom(
      this.httpModuleService.customer({
        url: "/customer/by-country",
        method: "GET",
        params: {},
      })
    );

    const customersGroupedByCountry = customersGroupedByCountryResponse.data as Record<
      string,
      {
        country_id: number;
        country_code: string;
        country_flag: string;
        licensed_customer_count: number;
        unlicensed_customer_count: number;
      }
    >;

    const countriesWithCustomers = countries.map((country) => {
      const customersInfo = customersGroupedByCountry[country.code];
      return {
        ...country,
        licensed_customer_count: customersInfo?.licensed_customer_count || 0,
        unlicensed_customer_count: customersInfo?.unlicensed_customer_count || 0,
        tasks: 0,
      };
    });

    return countriesWithCustomers;
  }

  async published() {
    return this.databaseService.country.findMany({
      where: { is_published: true },
      orderBy: {
        name: "asc",
      },
    });
  }

  async countryOverview(countryCode: string) {
    const country = await this.databaseService.country.findUnique({
      where: { code: countryCode },
      include: {
        followers: true,
      },
    });

    if (!country) {
      throw new NotFoundException("Country not found");
    }

    const customersGroupedByCountryResponse = await lastValueFrom(
      this.httpModuleService.customer({
        url: "/customer/by-country",
        method: "GET",
        params: {},
      })
    );

    const customersGroupedByCountry = customersGroupedByCountryResponse.data as Record<
      string,
      {
        country_id: number;
        country_code: string;
        country_flag: string;
        licensed_customer_count: number;
        unlicensed_customer_count: number;
      }
    >;

    const customersInfo = customersGroupedByCountry[countryCode];

    return {
      id: country.id,
      name: country.name,
      code: country.code,
      flag_url: country.flag_url,
      followers: country.followers,
      licensed_customer_count: customersInfo?.licensed_customer_count || 0,
      unlicensed_customer_count: customersInfo?.unlicensed_customer_count || 0,
      tasks: 0,
    };
  }

  async findOneByCode(code: string) {
    const country = await this.databaseService.country.findUnique({
      where: { code },
      include: {
        criterias: {
          where: {
            deleted_at: null,
            type: {
              in: ["AUTHORIZE_REPRESENTATIVE", "OTHER_COST", "REPRESENTATIVE_TIER"],
            },
          },
        },
      },
    });

    if (!country) {
      throw new NotFoundException("Country not found");
    }

    return {
      ...country,
      has_authorize_representative_criteria:
        country.criterias.filter((c) => c.type === "AUTHORIZE_REPRESENTATIVE").length > 0,
      has_other_cost_criteria: country.criterias.filter((c) => c.type === "OTHER_COST").length > 0,
      has_representative_tier_criteria: country.criterias.filter((c) => c.type === "REPRESENTATIVE_TIER").length > 0,
    };
  }

  async update(id: number, data: UpdateCountryDto) {
    const country = await this.databaseService.country.findUnique({
      where: { id },
    });

    if (!country) {
      throw new NotFoundException("Country not found");
    }

    return this.databaseService.country.update({
      where: { id },
      data: {
        authorize_representative_obligated: data.authorize_representative_obligated,
        other_costs_obligated: data.other_costs_obligated,
        is_published: data.is_published,
      },
    });
  }

  async remove(id: number) {
    const country = await this.databaseService.country.findUnique({
      where: { id },
    });

    if (!country) {
      throw new NotFoundException("Country not found");
    }

    await this.databaseService.country.delete({
      where: { id },
    });
  }
}
