import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { AppModule } from "../app.module";
import { Role } from "../shared/auth/role.enum";
import { HEADER_SYSTEM_API_KEY, HEADER_USER_ID, HEADER_USER_ROLE, HEADER_USER_EMAIL } from "../shared/auth/const";
import { HttpModuleService } from "../http/http.service";
import { DatabaseService } from "../database/database.service";
import { of } from "rxjs";

jest.setTimeout(30000);

describe("CountryController (e2e)", () => {
  let app: INestApplication;
  let httpModuleService: HttpModuleService;
  let databaseService: DatabaseService;

  const validApiKey = "test-api-key";

  beforeAll(() => {
    process.env.SYSTEM_API_KEY = validApiKey;
  });

  const authHeaders = {
    [HEADER_SYSTEM_API_KEY]: validApiKey,
    [HEADER_USER_ID]: "1",
    [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
    [HEADER_USER_EMAIL]: "<EMAIL>",
  };

  const mockCountry = {
    id: 1,
    name: "Germany",
    code: "DE",
    flag_url: "https://example.com/flags/de.png",
    authorize_representative_obligated: true,
    other_costs_obligated: false,
    is_published: true,
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
  };

  const mockCountryWithCriterias = {
    ...mockCountry,
    criterias: [
      { id: 1, type: "AUTHORIZE_REPRESENTATIVE", deleted_at: null },
      { id: 2, type: "OTHER_COST", deleted_at: null },
    ],
    has_authorize_representative_criteria: true,
    has_other_cost_criteria: true,
    has_representative_tier_criteria: false,
  };

  const mockCountries = [
    mockCountry,
    {
      id: 2,
      name: "France",
      code: "FR",
      flag_url: "https://example.com/flags/fr.png",
      authorize_representative_obligated: false,
      other_costs_obligated: true,
      is_published: true,
      created_at: "2025-04-07T22:58:17.004Z",
      updated_at: "2025-04-07T22:58:17.004Z",
    },
  ];

  const mockCustomersByCountry = {
    DE: {
      country_id: 1,
      country_code: "DE",
      country_flag: "https://example.com/flags/de.png",
      licensed_customer_count: 25,
      unlicensed_customer_count: 10,
    },
    FR: {
      country_id: 2,
      country_code: "FR",
      country_flag: "https://example.com/flags/fr.png",
      licensed_customer_count: 15,
      unlicensed_customer_count: 5,
    },
  };

  const mockCountryWithFollowers = {
    ...mockCountry,
    followers: [{ id: 1, user_id: 1, country_id: 1 }],
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(HttpModuleService)
      .useValue({
        customer: jest.fn().mockImplementation((config) => {
          if (config.url === "/customer/by-country" && config.method === "GET") {
            return of({ data: mockCustomersByCountry });
          }
          return of({ data: {} });
        }),
      })
      .overrideProvider(DatabaseService)
      .useValue({
        country: {
          create: jest.fn().mockResolvedValue(mockCountry),
          findMany: jest.fn().mockImplementation((params) => {
            if (params?.where?.is_published === true) {
              return Promise.resolve(mockCountries);
            }
            return Promise.resolve(mockCountries);
          }),
          findUnique: jest.fn().mockImplementation((params) => {
            if (params?.include?.criterias) {
              return Promise.resolve(mockCountryWithCriterias);
            }
            if (params?.include?.followers) {
              return Promise.resolve(mockCountryWithFollowers);
            }
            return Promise.resolve(mockCountry);
          }),
          update: jest.fn().mockImplementation((params) => {
            return Promise.resolve({
              ...mockCountry,
              ...params.data,
            });
          }),
          delete: jest.fn().mockResolvedValue(mockCountry),
        },
      })
      .compile();

    app = moduleFixture.createNestApplication();
    httpModuleService = moduleFixture.get<HttpModuleService>(HttpModuleService);
    databaseService = moduleFixture.get<DatabaseService>(DatabaseService);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("/countries (GET)", () => {
    it("should return countries without authentication (public route)", () => {
      return request(app.getHttpServer())
        .get("/countries")
        .expect(200)
        .then((response) => {
          expect(databaseService.country.findMany).toHaveBeenCalled();
          expect(response.body).toEqual(mockCountries);
        });
    });
  });

  describe("/countries/published (GET)", () => {
    it("should return published countries without authentication (public route)", () => {
      return request(app.getHttpServer())
        .get("/countries/published")
        .expect(200)
        .then((response) => {
          expect(databaseService.country.findMany).toHaveBeenCalledWith({
            where: { is_published: true },
            orderBy: { name: "asc" },
          });
          expect(response.body).toEqual(mockCountries);
        });
    });
  });

  describe("/countries/overview (GET)", () => {
    it("should return countries overview when authenticated as SUPER_ADMIN with valid API key", () => {
      return request(app.getHttpServer())
        .get("/countries/overview")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.country.findMany).toHaveBeenCalled();
          expect(httpModuleService.customer).toHaveBeenCalledWith({
            url: "/customer/by-country",
            method: "GET",
            params: {},
          });
          expect(response.body).toBeDefined();
          expect(response.body.length).toBe(2);
          expect(response.body[0].licensed_customer_count).toBe(25);
        });
    });

    it("should return countries overview with search parameter", () => {
      return request(app.getHttpServer())
        .get("/countries/overview?search=Germany")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.country.findMany).toHaveBeenCalledWith({
            where: {
              OR: [
                { name: { contains: "Germany", mode: "insensitive" } },
                { code: { contains: "Germany", mode: "insensitive" } },
              ],
            },
            select: {
              id: true,
              name: true,
              code: true,
              flag_url: true,
              followers: true,
            },
            orderBy: { name: "asc" },
          });
        });
    });

    it("should return countries overview when authenticated as ADMIN with valid API key", () => {
      const adminHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.ADMIN,
      };

      return request(app.getHttpServer()).get("/countries/overview").set(adminHeaders).expect(200);
    });

    it("should return countries overview when authenticated as CLERK with valid API key", () => {
      const clerkHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CLERK,
      };

      return request(app.getHttpServer()).get("/countries/overview").set(clerkHeaders).expect(200);
    });

    it("should reject when authenticated with unauthorized role even with valid API key", () => {
      const unauthorizedHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/countries/overview").set(unauthorizedHeaders).expect(403);
    });

    it("should reject when API key is invalid regardless of role", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/countries/overview").set(invalidApiKeyHeaders).expect(401);
    });
  });

  describe("/countries/:id (GET)", () => {
    it("should return a single country when authenticated as SUPER_ADMIN with valid API key", () => {
      return request(app.getHttpServer())
        .get("/countries/1")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.country.findUnique).toHaveBeenCalledWith({
            where: { id: 1 },
          });
          expect(response.body).toEqual(mockCountry);
        });
    });

    it("should reject when authenticated with unauthorized role even with valid API key", () => {
      const unauthorizedHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/countries/1").set(unauthorizedHeaders).expect(403);
    });
  });

  describe("/countries/code/:code (GET)", () => {
    it("should return a country by code when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .get("/countries/code/DE")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.country.findUnique).toHaveBeenCalledWith({
            where: { code: "DE" },
            include: {
              criterias: {
                where: {
                  deleted_at: null,
                  type: {
                    in: ["AUTHORIZE_REPRESENTATIVE", "OTHER_COST", "REPRESENTATIVE_TIER"],
                  },
                },
              },
            },
          });
          expect(response.body).toEqual(mockCountryWithCriterias);
        });
    });

    it("should reject when authenticated with unauthorized role even with valid API key", () => {
      const unauthorizedHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/countries/code/DE").set(unauthorizedHeaders).expect(403);
    });
  });

  describe("/countries/code/:code/overview (GET)", () => {
    it("should return country overview by code when authenticated with valid API key", () => {
      return request(app.getHttpServer())
        .get("/countries/code/DE/overview")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.country.findUnique).toHaveBeenCalledWith({
            where: { code: "DE" },
            include: { followers: true },
          });
          expect(httpModuleService.customer).toHaveBeenCalledWith({
            url: "/customer/by-country",
            method: "GET",
            params: {},
          });
          expect(response.body).toHaveProperty("licensed_customer_count", 25);
          expect(response.body).toHaveProperty("unlicensed_customer_count", 10);
          expect(response.body).toHaveProperty("followers");
        });
    });

    it("should reject when authenticated with unauthorized role even with valid API key", () => {
      const unauthorizedHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get("/countries/code/DE/overview").set(unauthorizedHeaders).expect(403);
    });
  });

  describe("/countries (POST)", () => {
    const createCountryDto = {
      name: `Italy-${Date.now()}`,
      code: `IT-${Date.now()}`,
      flag_url: "https://example.com/flags/it.png",
    };

    it("should create a new country when authenticated as SUPER_ADMIN with valid API key", () => {
      return request(app.getHttpServer())
        .post("/countries")
        .set(authHeaders)
        .send(createCountryDto)
        .expect(201)
        .then((response) => {
          expect(databaseService.country.create).toHaveBeenCalledWith({
            data: createCountryDto,
          });
          expect(response.body).toEqual(mockCountry);
        });
    });

    it("should reject creation when authenticated as CUSTOMER even with valid API key", () => {
      const customerHeaders = {
        ...authHeaders,
        [HEADER_USER_ROLE]: Role.CUSTOMER,
      };

      return request(app.getHttpServer()).post("/countries").set(customerHeaders).send(createCountryDto).expect(403);
    });

    it("should reject creation when API key is invalid regardless of role", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .post("/countries")
        .set(invalidApiKeyHeaders)
        .send(createCountryDto)
        .expect(401);
    });

    it("should reject creation when not authenticated", () => {
      return request(app.getHttpServer()).post("/countries").send(createCountryDto).expect(401);
    });
  });

  describe("/countries/:id (PUT)", () => {
    const updateCountryDto = {
      authorize_representative_obligated: true,
      other_costs_obligated: false,
      is_published: true,
    };

    it("should update a country when authenticated as SUPER_ADMIN with valid API key", () => {
      return request(app.getHttpServer())
        .put("/countries/1")
        .set(authHeaders)
        .send(updateCountryDto)
        .expect(200)
        .then((response) => {
          expect(databaseService.country.findUnique).toHaveBeenCalledWith({
            where: { id: 1 },
          });
          expect(databaseService.country.update).toHaveBeenCalledWith({
            where: { id: 1 },
            data: updateCountryDto,
          });
          expect(response.body).toEqual({
            ...mockCountry,
            ...updateCountryDto,
          });
        });
    });

    it("should reject update when authenticated with unauthorized role even with valid API key", () => {
      const unauthorizedHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .put("/countries/1")
        .set(unauthorizedHeaders)
        .send(updateCountryDto)
        .expect(403);
    });

    it("should reject update when API key is invalid regardless of role", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer())
        .put("/countries/1")
        .set(invalidApiKeyHeaders)
        .send(updateCountryDto)
        .expect(401);
    });

    it("should reject update when not authenticated", () => {
      return request(app.getHttpServer()).put("/countries/1").send(updateCountryDto).expect(401);
    });
  });

  describe("/countries/:id (DELETE)", () => {
    it("should delete a country when authenticated as SUPER_ADMIN with valid API key", () => {
      return request(app.getHttpServer())
        .delete("/countries/1")
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(databaseService.country.findUnique).toHaveBeenCalledWith({
            where: { id: 1 },
          });
          expect(databaseService.country.delete).toHaveBeenCalledWith({
            where: { id: 1 },
          });
          expect(response.body).toEqual(mockCountry);
        });
    });

    it("should reject deletion when authenticated with unauthorized role even with valid API key", () => {
      const unauthorizedHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).delete("/countries/1").set(unauthorizedHeaders).expect(403);
    });

    it("should reject deletion when API key is invalid regardless of role", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).delete("/countries/1").set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject deletion when not authenticated", () => {
      return request(app.getHttpServer()).delete("/countries/1").expect(401);
    });
  });

  describe("System role access", () => {
    it("should allow access with SYSTEM role and valid API key", () => {
      const systemHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/countries/overview").set(systemHeaders).expect(200);
    });

    it("should reject access with SYSTEM role but invalid API key", () => {
      const invalidSystemHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer()).get("/countries/overview").set(invalidSystemHeaders).expect(401);
    });
  });
});
