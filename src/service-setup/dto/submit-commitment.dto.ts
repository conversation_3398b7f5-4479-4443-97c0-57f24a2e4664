import { ApiProperty } from "@nestjs/swagger";

export class SubmitCommitmentDto {
  @ApiProperty({
    description: "The year",
    example: 2025,
  })
  year: number;

  @ApiProperty({
    description: "The commitment answers",
    example: [
      { id: 1, answer: "OBLIGED" },
      { id: 2, answer: "REQUEST" },
    ],
  })
  commitment: CommitmentAnswer[];
}

export interface CommitmentAnswer {
  id: number;
  answer: string;
}
