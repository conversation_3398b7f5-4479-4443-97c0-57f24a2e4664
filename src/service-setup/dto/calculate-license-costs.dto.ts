import { ApiProperty } from "@nestjs/swagger";

export class CalculateLicenseCostsDto {
  @ApiProperty({
    description: "The license year",
    example: 2025,
  })
  year: number;

  @ApiProperty({
    description: "The report set id",
    example: [
      {
        id: 1,
        fractions: [
          { code: "V6JKO4", weight: 100 },
          { code: "LM50SL", weight: 200 },
        ],
      },
    ],
  })
  report_sets: {
    id: number;
    fractions: { code: string; weight: number }[];
  }[];
}
