import {
  Country,
  CountryPriceList,
  Files,
  OtherCost,
  PackagingService,
  PriceList,
  ReportSet,
  ReportSetColumn,
  ReportSetColumnFraction,
  ReportSetFraction,
  ReportSetFrequency,
  ReportSetPriceList,
  ReportSetPriceListItem,
  RepresentativeTier,
  RequiredInformation,
} from "@prisma/client";
import { Frequency } from "@/report-set-frequency/dto/report-set-frequency-create.dto";

export type ServiceSetupDto = Country & {
  packaging_services: (PackagingService & {
    report_set_frequencies: (ReportSetFrequency & {
      frequency: Frequency;
    })[];
    report_sets: (ReportSet & {
      fractions: ReportSetFraction[];
      columns: (ReportSetColumn & {
        children: (ReportSetColumn & {
          fractions: ReportSetColumnFraction[];
        })[];
      })[];
      price_lists: (ReportSetPriceList & {
        items: ReportSetPriceListItem[];
      })[];
    })[];
  })[];
  representative_tiers: RepresentativeTier[];
  required_informations: (RequiredInformation & {
    file: Files | null;
  })[];
  other_costs: OtherCost[];
  country_price_lists: (CountryPriceList & {
    price_list: PriceList;
  })[];
};
