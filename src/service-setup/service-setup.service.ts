import { BadRequestException, Injectable, NotFoundException } from "@nestjs/common";
import { DatabaseService } from "../database/database.service";
import { ServiceSetupDto } from "./dto/service-setup.dto";
import {
  CriteriaType,
  OtherCost,
  PackagingService,
  PriceList,
  Prisma,
  ReportSet,
  ReportSetFrequency,
  RepresentativeTier,
  RequiredInformation,
} from "@prisma/client";
import { SubmitCommitmentDto } from "./dto/submit-commitment.dto";
import { CalculateLicenseCostsDto } from "./dto/calculate-license-costs.dto";

@Injectable()
export class ServiceSetupService {
  constructor(private readonly databaseService: DatabaseService) {}

  async findServiceSetup(countryCode: string) {
    const country = await this.databaseService.country.findUnique({
      where: { code: countryCode },
      include: {
        packaging_services: {
          where: { deleted_at: null },
          include: {
            report_set_frequencies: {
              where: { deleted_at: null },
            },
            report_sets: {
              where: { deleted_at: null },
              include: {
                sheet_file: true,
                fractions: {
                  where: { deleted_at: null, parent_code: null },
                  orderBy: { order: "asc" },
                  include: {
                    fraction_icon: true,
                    children: {
                      where: { deleted_at: null },
                      orderBy: { order: "asc" },
                      include: {
                        fraction_icon: true,
                        children: {
                          where: { deleted_at: null },
                          orderBy: { order: "asc" },
                        },
                      },
                    },
                  },
                },
                columns: {
                  where: { deleted_at: null, parent_code: null },
                  orderBy: { order: "asc" },
                  include: {
                    children: {
                      where: { deleted_at: null },
                      orderBy: { order: "asc" },
                      include: {
                        fractions: {
                          where: { deleted_at: null },
                        },
                      },
                    },
                  },
                },
                price_lists: {
                  where: { deleted_at: null },
                  orderBy: { created_at: "asc" },
                  include: {
                    items: true,
                  },
                },
              },
            },
          },
        },
        country_price_lists: {
          where: { deleted_at: null },
          include: {
            price_list: true,
          },
        },
        representative_tiers: {
          where: { deleted_at: null },
        },
        required_informations: {
          where: { deleted_at: null },
          include: {
            file: true,
          },
        },
        other_costs: {
          where: { deleted_at: null },
        },
      },
    });

    if (!country) throw new NotFoundException("Country not found");

    const serviceSetup: ServiceSetupDto = {
      ...country,
      packaging_services: country.packaging_services.map((packagingService) => ({
        ...packagingService,
        report_set_frequencies: packagingService.report_set_frequencies.map((reportSetFrequency) => ({
          ...reportSetFrequency,
          frequency: reportSetFrequency.frequency ? JSON.parse(reportSetFrequency.frequency as string) : null,
        })),
        report_sets: packagingService.report_sets.map((reportSet) => ({
          ...reportSet,
          fractions: reportSet.fractions,
          columns: reportSet.columns,
          price_lists: reportSet.price_lists,
        })),
      })),
      country_price_lists: country.country_price_lists,
      representative_tiers: country.representative_tiers,
      required_informations: country.required_informations.map((requiredInformation) => ({
        ...requiredInformation,
        file: requiredInformation.file,
      })),
      other_costs: country.other_costs,
    };

    return serviceSetup;
  }

  async getServiceSetupStatus(countryCode: string) {
    const currentYear = new Date().getFullYear();

    const country = await this.databaseService.country.findUnique({
      where: { code: countryCode },
      select: {
        id: true,
        code: true,
        name: true,
        authorize_representative_obligated: true,
        other_costs_obligated: true,
        criterias: {
          select: {
            id: true,
            type: true,
            mode: true,
          },
          where: {
            deleted_at: null,
            mode: "COMMITMENT",
          },
        },
        packaging_services: {
          select: {
            id: true,
            name: true,
            report_set_frequencies: {
              select: {
                id: true,
                rhythm: true,
                packaging_service_id: true,
              },
              where: {
                deleted_at: null,
              },
            },
            report_sets: {
              select: {
                id: true,
                name: true,
                packaging_service_id: true,
                sheet_file: true,
                fractions: {
                  select: {
                    _count: true,
                    has_second_level: true,
                    has_third_level: true,
                    children: {
                      select: {
                        _count: true,
                        children: {
                          select: {
                            _count: true,
                          },
                        },
                      },
                    },
                  },
                  where: { deleted_at: null },
                },
                columns: {
                  select: {
                    _count: true,
                  },
                  where: { deleted_at: null, parent_code: null },
                },
                price_lists: {
                  select: {
                    license_year: true,
                    _count: true,
                  },
                  where: { deleted_at: null },
                },
              },
              where: {
                deleted_at: null,
              },
            },
            criterias: {
              select: {
                id: true,
                type: true,
                mode: true,
              },
              where: {
                deleted_at: null,
                mode: "COMMITMENT",
              },
            },
          },
          where: { deleted_at: null },
        },
        country_price_lists: {
          select: {
            id: true,
            price_list: {
              select: {
                id: true,
                condition_type_value: true,
              },
            },
          },
          where: { deleted_at: null },
        },
        representative_tiers: {
          select: {
            id: true,
          },
          where: { deleted_at: null },
        },
        required_informations: {
          select: {
            id: true,
          },
          where: { deleted_at: null },
        },
        other_costs: {
          select: {
            id: true,
          },
          where: { deleted_at: null },
        },
      },
    });

    if (!country) throw new NotFoundException("Country not found");

    // Packaging services
    if (!country.packaging_services.length) {
      return {
        completed: false,
        message: "Packaging services are empty",
      };
    }

    // Report sets
    if (!country.packaging_services.every((ps) => ps.report_sets.length)) {
      return {
        completed: false,
        message: "There are packaging services without report sets",
      };
    }

    for (const packagingService of country.packaging_services) {
      if (!packagingService.report_sets.length) {
        return {
          completed: false,
          message: `The packaging service "${packagingService.name}" is without any report set`,
        };
      }

      for (const reportSet of packagingService.report_sets) {
        if (!reportSet.fractions.length) {
          return {
            completed: false,
            message: `The report set "${reportSet.name}" of packaging service "${packagingService.name}" is missing fractions`,
          };
        }

        if (reportSet.fractions.some((f) => f.has_second_level && !f.children.length)) {
          return {
            completed: false,
            message: `The report set "${reportSet.name}" of packaging service "${packagingService.name}" is missing second level fractions`,
          };
        }

        if (reportSet.fractions.some((c) => c.has_third_level && c.children.some((c) => !c.children.length))) {
          return {
            completed: false,
            message: `The report set "${reportSet.name}" of packaging service "${packagingService.name}" is missing third level fractions`,
          };
        }

        if (!reportSet.columns.length) {
          return {
            completed: false,
            message: `The report set "${reportSet.name}" of packaging service "${packagingService.name}" is missing columns`,
          };
        }

        if (!reportSet.price_lists.length) {
          return {
            completed: false,
            message: `The report set "${reportSet.name}" of packaging service "${packagingService.name}" is missing price lists`,
          };
        }

        const currentYearPriceList = reportSet.price_lists.find((pl) => pl.license_year === currentYear);

        if (!currentYearPriceList) {
          return {
            completed: false,
            message: `The report set "${reportSet.name}" of packaging service "${packagingService.name}" is missing price list for the current year (${currentYear})`,
          };
        }
      }
    }

    // Report frequencies
    if (!country.packaging_services.every((ps) => ps.report_set_frequencies.length)) {
      return {
        completed: false,
        message: "There are packaging services without report frequencies",
      };
    }

    for (const packagingService of country.packaging_services) {
      // Report sets
      if (
        packagingService.report_sets.length > 1 &&
        !packagingService.criterias.filter((c) => c.type === "REPORT_SET").length
      ) {
        return {
          completed: false,
          message: "There are packaging services with multiple report sets without commitment criteria",
        };
      }

      // Report frequencies
      if (
        packagingService.report_set_frequencies.length > 1 &&
        !packagingService.criterias.filter((c) => c.type === "REPORT_FREQUENCY").length
      ) {
        return {
          completed: false,
          message: "There are packaging services with multiple report frequencies without commitment criteria",
        };
      }
    }

    if (!country.country_price_lists.length) {
      return {
        completed: false,
        message: "There are no price lists selected",
      };
    }

    const currentYearPriceList = country.country_price_lists.find(
      (pl) => pl.price_list.condition_type_value === currentYear.toString()
    );
    if (!currentYearPriceList) {
      return {
        completed: false,
        message: `There is no price list for the current year (${currentYear})`,
      };
    }

    return { completed: true };
  }

  async findServiceSetupPackagingServices(countryCode: string) {
    const packagingServices = await this.databaseService.packagingService.findMany({
      where: {
        country: { code: countryCode },
        deleted_at: null,
      },
      include: {
        criterias: {
          where: {
            deleted_at: null,
            type: {
              in: ["PACKAGING_SERVICE", "REPORT_SET", "REPORT_FREQUENCY"],
            },
            mode: "COMMITMENT",
          },
        },
      },
    });

    return packagingServices.map((packagingService) => ({
      ...packagingService,
      has_criteria: packagingService.criterias.filter((c) => c.type === "PACKAGING_SERVICE").length > 0,
      has_report_set_criteria: packagingService.criterias.filter((c) => c.type === "REPORT_SET").length > 0,
      has_report_frequency_criteria: packagingService.criterias.filter((c) => c.type === "REPORT_FREQUENCY").length > 0,
    }));
  }

  async findServiceSetupReportSets(countryCode: string) {
    const reportSets = await this.databaseService.reportSet.findMany({
      where: {
        packaging_service: {
          country: { code: countryCode },
          deleted_at: null,
        },
        deleted_at: null,
      },
      include: {
        packaging_service: {
          include: {
            criterias: {
              where: {
                deleted_at: null,
                type: "REPORT_SET",
                mode: "COMMITMENT",
              },
            },
          },
        },
      },
    });

    return reportSets.map((reportSet) => ({
      ...reportSet,
      has_criteria: reportSet.packaging_service.criterias.length > 0,
    }));
  }

  async findServiceSetupReportSet(countryCode: string, reportSetId: number) {
    const reportSet = await this.databaseService.reportSet.findUnique({
      where: {
        id: reportSetId,
        packaging_service: {
          country: { code: countryCode },
          deleted_at: null,
        },
        deleted_at: null,
      },
      include: {
        packaging_service: {
          include: {
            criterias: {
              where: {
                deleted_at: null,
                type: "REPORT_SET",
                mode: "COMMITMENT",
              },
            },
          },
        },
        sheet_file: true,
        fractions: {
          where: { deleted_at: null, parent_code: null },
          include: {
            fraction_icon: true,
            children: {
              where: { deleted_at: null },
              orderBy: { order: "asc" },
              include: {
                fraction_icon: true,
                children: {
                  where: { deleted_at: null },
                  orderBy: { order: "asc" },
                  include: {
                    fraction_icon: true,
                  },
                },
              },
            },
          },
          orderBy: { order: "asc" },
        },
        columns: {
          where: { deleted_at: null, parent_code: null },
          orderBy: { order: "asc" },
          include: {
            children: {
              where: { deleted_at: null },
              orderBy: { order: "asc" },
              include: {
                fractions: {
                  where: { deleted_at: null },
                },
              },
            },
          },
        },
        price_lists: {
          where: { deleted_at: null },
          orderBy: { created_at: "asc" },
          include: {
            items: true,
          },
        },
      },
    });

    if (!reportSet) throw new NotFoundException("Report set not found");

    return {
      ...reportSet,
      has_criteria: reportSet.packaging_service.criterias.length > 0,
    };
  }

  async findServiceSetupReportFrequencies(countryCode: string) {
    const reportFrequencies = await this.databaseService.reportSetFrequency.findMany({
      where: {
        packaging_service: {
          country: { code: countryCode },
          deleted_at: null,
        },
        deleted_at: null,
      },
      include: {
        packaging_service: {
          include: {
            criterias: {
              where: {
                deleted_at: null,
                type: "REPORT_FREQUENCY",
                mode: "COMMITMENT",
              },
            },
          },
        },
      },
    });

    return reportFrequencies.map((reportFrequency) => ({
      ...reportFrequency,
      frequency: reportFrequency.frequency ? JSON.parse(reportFrequency.frequency as string) : null,
      has_criteria: reportFrequency.packaging_service.criterias.length > 0,
    }));
  }

  async findServiceSetupRepresentativeTiers(countryCode: string) {
    const representativeTiers = await this.databaseService.representativeTier.findMany({
      where: {
        country: { code: countryCode },
        deleted_at: null,
      },
      include: {
        country: {
          include: {
            criterias: {
              where: {
                deleted_at: null,
                type: "REPRESENTATIVE_TIER",
                mode: "COMMITMENT",
              },
            },
          },
        },
      },
    });

    return representativeTiers.map((representativeTier) => ({
      ...representativeTier,
      has_criteria: representativeTier.country.criterias.length > 0,
    }));
  }

  async findServiceSetupOtherCosts(countryCode: string) {
    const otherCosts = await this.databaseService.otherCost.findMany({
      where: {
        country: { code: countryCode },
        deleted_at: null,
      },
      include: {
        country: {
          include: {
            criterias: {
              where: {
                deleted_at: null,
                type: "OTHER_COST",
                mode: "COMMITMENT",
              },
            },
          },
        },
      },
    });

    return otherCosts.map((otherCost) => ({
      ...otherCost,
      has_criteria: otherCost.country.criterias.length > 0,
    }));
  }

  async findServiceSetupPriceLists(countryCode: string) {
    const priceLists = await this.databaseService.countryPriceList.findMany({
      where: {
        country: { code: countryCode },
        price_list: { deleted_at: null },
        deleted_at: null,
      },
      include: {
        price_list: true,
      },
    });

    return priceLists;
  }

  async findServiceSetupRequiredInformations(countryCode: string) {
    const requiredInformations = await this.databaseService.requiredInformation.findMany({
      where: {
        country: { code: countryCode },
        deleted_at: null,
      },
      include: {
        file: true,

        criterias: {
          where: {
            deleted_at: null,
            type: "REQUIRED_INFORMATION",
            mode: "COMMITMENT",
          },
        },
      },
    });

    return requiredInformations.map((requiredInformation) => ({
      ...requiredInformation,
      has_criteria: requiredInformation.criterias.length > 0,
    }));
  }

  async findServiceSetupCriterias({
    countryCode,
    type,
    requiredInformationId,
    packagingServiceId,
  }: {
    countryCode: string;
    type: CriteriaType;
    requiredInformationId?: number;
    packagingServiceId?: number;
  }) {
    const where: Prisma.CriteriaWhereInput = {
      type,
      deleted_at: null,
      country: { code: countryCode },
    };

    if (packagingServiceId && !Number.isNaN(Number(packagingServiceId))) {
      where.packaging_service_id = Number(packagingServiceId);
    }

    if (requiredInformationId && !Number.isNaN(Number(requiredInformationId))) {
      where.required_information_id = Number(requiredInformationId);
    }

    const criterias = await this.databaseService.criteria.findMany({
      where,
      include: { options: true },
    });

    return criterias;
  }

  async findServiceSetupCommitment(countryCode: string) {
    const commitment = await this.databaseService.criteria.findMany({
      where: {
        mode: "COMMITMENT",
        country: { code: countryCode.toUpperCase() },
        deleted_at: null,
      },
      include: {
        options: true,
      },
    });

    if (!commitment.length) throw new NotFoundException("Commitment is empty");

    return commitment;
  }

  async submitServiceSetupCommitment(countryCode: string, data: SubmitCommitmentDto) {
    if (!data.year) throw new BadRequestException("Year is required");

    if (!Array.isArray(data.commitment) || !data.commitment)
      throw new BadRequestException("Commitment answers array is required");

    if (!data.commitment.every((c) => c.id && c.answer)) {
      throw new BadRequestException("Commitment answers are invalid");
    }

    const country = await this.databaseService.country.findUnique({
      where: { code: countryCode.toUpperCase() },
      include: {
        packaging_services: {
          where: { deleted_at: null },
          include: {
            report_set_frequencies: {
              where: { deleted_at: null },
            },
            report_sets: {
              where: { deleted_at: null },
              include: {
                sheet_file: true,
                fractions: {
                  where: { deleted_at: null, parent_code: null },
                  orderBy: { order: "asc" },
                  include: {
                    fraction_icon: true,
                    children: {
                      where: { deleted_at: null },
                      orderBy: { order: "asc" },
                      include: {
                        fraction_icon: true,
                        children: {
                          where: { deleted_at: null },
                          orderBy: { order: "asc" },
                        },
                      },
                    },
                  },
                },
                columns: {
                  where: { deleted_at: null, parent_code: null },
                  orderBy: { order: "asc" },
                  include: {
                    children: {
                      where: { deleted_at: null },
                      orderBy: { order: "asc" },
                      include: {
                        fractions: {
                          where: { deleted_at: null },
                        },
                      },
                    },
                  },
                },
                price_lists: {
                  where: { deleted_at: null },
                  orderBy: { created_at: "asc" },
                  include: {
                    items: true,
                  },
                },
              },
            },
          },
        },
        country_price_lists: {
          where: { deleted_at: null },
          include: {
            price_list: true,
          },
        },
        representative_tiers: {
          where: { deleted_at: null },
        },
        required_informations: {
          where: { deleted_at: null },
        },
        other_costs: {
          where: { deleted_at: null },
        },
      },
    });

    if (!country) throw new NotFoundException("Country not found");

    const commitment = await this.databaseService.criteria.findMany({
      where: {
        mode: "COMMITMENT",
        country: { code: countryCode.toUpperCase() },
        deleted_at: null,
      },
      include: {
        country: true,
        options: true,
      },
    });

    if (!commitment.length) throw new NotFoundException("Commitment is empty");

    const LICENSE_YEAR = data.year.toString();

    const result: CommitmentSubmitResult = {
      country: {
        id: country.id,
        name: country.name,
        code: country.code,
        flag_url: country.flag_url,
        authorize_representative_obligated: country.authorize_representative_obligated,
        other_costs_obligated: country.other_costs_obligated,
      },
      year: LICENSE_YEAR,
      packaging_services: [] as (PackagingService & {
        obliged: boolean;
        report_set: ReportSet;
        report_set_frequency: ReportSetFrequency;
      })[],
      authorize_representative_obligated: country.authorize_representative_obligated,
      representative_tier: null,
      other_costs_obligated: country.other_costs_obligated,
      other_costs: [],
      required_informations: country.required_informations,
      price_list:
        country.country_price_lists.find((pl) => pl.price_list.condition_type_value === LICENSE_YEAR)!.price_list || // eslint-disable-line
        null,
    };

    for (const packagingService of country.packaging_services) {
      const reportSetCriterias = commitment.filter(
        (c) => c.type === "REPORT_SET" && c.packaging_service_id === packagingService.id
      );

      const reportSet = !reportSetCriterias.length ? packagingService.report_sets[0] : ({} as ReportSet);

      const reportSetFrequencyCriterias = commitment.filter(
        (c) => c.type === "REPORT_FREQUENCY" && c.packaging_service_id === packagingService.id
      );

      const reportSetFrequency = (() => {
        if (!packagingService.report_set_frequencies.length) {
          return {} as ReportSetFrequency;
        }

        if (packagingService.report_set_frequencies.length === 1) {
          return packagingService.report_set_frequencies[0];
        }

        if (!reportSetFrequencyCriterias.length) {
          return packagingService.report_set_frequencies[0];
        }

        return {} as ReportSetFrequency;
      })();

      result.packaging_services.push({
        id: packagingService.id,
        name: packagingService.name,
        description: packagingService.description,
        country_id: packagingService.country_id,
        created_at: packagingService.created_at,
        updated_at: packagingService.updated_at,
        deleted_at: packagingService.deleted_at,
        obliged: false,
        report_set: reportSet,
        report_set_frequency: reportSetFrequency,
      });
    }

    const otherCostCriterias = commitment.filter((c) => c.type === "OTHER_COST");
    if (!otherCostCriterias.length) result.other_costs = country.other_costs;

    const requiredInformationCriterias = commitment.filter((c) => c.type === "REQUIRED_INFORMATION");
    if (!requiredInformationCriterias.length) result.required_informations = country.required_informations;

    for (const criteria of commitment) {
      const submittedCriteria = data.commitment.find((c) => c.id === criteria.id);

      if (!submittedCriteria || !submittedCriteria.answer) {
        throw new BadRequestException(`Missing answer for criteria ${criteria.id}`);
      }

      if (!criteria.options.find((o) => o.value === submittedCriteria.answer)) {
        throw new BadRequestException(`Invalid answer for criteria ${criteria.id}`);
      }

      if (criteria.type === "PACKAGING_SERVICE") {
        const packagingService = country.packaging_services.find((p) => p.id === criteria.packaging_service_id);

        if (!packagingService) continue;

        const resultPackagingService = result.packaging_services.find((p) => p.id === criteria.packaging_service_id);

        if (!resultPackagingService) continue;

        // Packaging service is obliged if at least one criteria answer is obliged
        if (submittedCriteria.answer === "OBLIGED") resultPackagingService.obliged = true;

        continue;
      }

      if (criteria.type === "REPORT_SET") {
        const packagingService = country.packaging_services.find((p) => p.id === criteria.packaging_service_id);

        if (!packagingService) continue;

        const resultPackagingService = result.packaging_services.find((p) => p.id === criteria.packaging_service_id);

        if (!resultPackagingService) continue;

        // Select report set from packaging selected by criteria
        const selectedReportSet = packagingService.report_sets.find((r) => r.id === Number(submittedCriteria.answer));

        if (!selectedReportSet) continue;

        resultPackagingService.report_set = selectedReportSet;

        continue;
      }

      if (criteria.type === "REPORT_FREQUENCY") {
        const packagingService = country.packaging_services.find((p) => p.id === criteria.packaging_service_id);

        if (!packagingService) continue;

        const resultPackagingService = result.packaging_services.find((p) => p.id === criteria.packaging_service_id);

        if (!resultPackagingService) continue;

        // Select REPORT_FREQUENCY from by selected criteria
        const selectedReportFrequency = packagingService.report_set_frequencies.find(
          (frequency) => frequency.id === Number(submittedCriteria.answer)
        );

        if (!selectedReportFrequency) continue;

        resultPackagingService.report_set_frequency = {
          ...selectedReportFrequency,
          frequency: JSON.parse(selectedReportFrequency.frequency as string),
        };

        continue;
      }

      if (criteria.type === "AUTHORIZE_REPRESENTATIVE") {
        // At least one AUTHORIZE_REPRESENTATIVE obligation criteria equals to OBLIGED sets obligatedRepresentative
        if (submittedCriteria.answer === "OBLIGED") result.authorize_representative_obligated = true;

        continue;
      }

      if (criteria.type === "REPRESENTATIVE_TIER") {
        // Select representative tier from by selected criteria
        const selectedRepresentativeTier = country.representative_tiers.find(
          (tier) => tier.id === Number(submittedCriteria.answer)
        );

        if (!selectedRepresentativeTier) continue;

        result.representative_tier = selectedRepresentativeTier;

        continue;
      }

      result.other_costs = result.other_costs || [];
      if (criteria.type === "OTHER_COST") {
        const otherCost = country.other_costs.find((oc) => oc.id === Number(submittedCriteria.answer));

        if (!otherCost) continue;

        result.other_costs.push(otherCost);

        continue;
      }

      result.required_informations = result.required_informations || [];
      if (criteria.type === "REQUIRED_INFORMATION") {
        const requiredInformation = country.required_informations.find(
          (ri) => ri.id === Number(criteria.required_information_id)
        );

        if (!requiredInformation) continue;

        if (submittedCriteria.answer !== "REQUEST") {
          result.required_informations = result.required_informations.filter((ri) => ri.id !== requiredInformation.id);
        }

        result.required_informations.push(requiredInformation);

        continue;
      }
    }

    // TODO: check if is defaulting informations, or filtering by criterias

    const formattedCommitment = commitment.map((c) => ({
      ...c,
      answer: data.commitment.find((item) => item.id === c.id)?.answer,
    }));

    return {
      year: LICENSE_YEAR,
      setup: result,
      commitment: formattedCommitment,
    };
  }

  async calculateLicenseCosts(countryCode: string, data: CalculateLicenseCostsDto) {
    const { year, report_sets } = data;

    const foundReportSets = await this.databaseService.reportSet.findMany({
      where: {
        ...(!!report_sets.length && {
          id: { in: report_sets.map((rs) => rs.id) },
        }),
        packaging_service: {
          country: { code: countryCode.toUpperCase() },
        },
      },
      include: {
        price_lists: {
          where: {
            license_year: year,
          },
          include: {
            items: {
              include: {
                fraction: true,
              },
            },
          },
        },
        packaging_service: {
          include: {
            country: true,
          },
        },
      },
    });

    if (!foundReportSets.length || (report_sets.length && foundReportSets.length !== report_sets.length))
      throw new NotFoundException("Report sets not found");

    let totalLicenseCosts = 0;

    for (const { id: reportSetId, fractions } of report_sets) {
      const foundReportSet = foundReportSets.find((rs) => rs.id === reportSetId);

      if (!foundReportSet || !foundReportSet.price_lists.length) continue;

      const priceList = foundReportSet.price_lists[0];

      if (priceList.type === "FIXED_PRICE") {
        totalLicenseCosts += priceList.fixed_price || 0;
        continue;
      }

      const priceListItems =
        priceList.type === "PRICE_PER_CATEGORY"
          ? priceList.items.filter((item) => item.fraction.level === 1)
          : priceList.items;

      const fractionCosts = fractions.reduce((acc, fraction) => {
        const item = priceListItems.find((item) => item.fraction_code === fraction.code);

        if (!item) return acc;

        const fractionPrice = item.price / 10; // Convert to cents
        const fractionWeight = fraction.weight / 1000; // Transform weight from grams (g) to kilograms (kg)

        return acc + fractionPrice * fractionWeight;
      }, 0);

      if (priceList.type === "PRICE_PER_CATEGORY") {
        totalLicenseCosts += fractionCosts;
        continue;
      }

      if (priceList.type === "PRICE_PER_VOLUME_BASE_PRICE") {
        totalLicenseCosts += fractionCosts + (priceList.base_price || 0);
        continue;
      }

      if (priceList.type === "PRICE_PER_VOLUME_MINIMUM_FEE") {
        if (fractionCosts < (priceList.minimum_fee || 0)) {
          totalLicenseCosts += priceList.minimum_fee || 0;
        } else {
          totalLicenseCosts += fractionCosts;
        }
        continue;
      }
    }

    const totalFractionWeight = report_sets.reduce((reportSetsTotal, reportSet) => {
      return (
        reportSetsTotal +
        reportSet.fractions.reduce((reportSetTotal, fraction) => {
          return reportSetTotal + fraction.weight;
        }, 0)
      );
    }, 0);

    const calculatorCriterias = await this.databaseService.criteria.findMany({
      where: {
        mode: "CALCULATOR",
        country: { code: countryCode.toUpperCase() },
        deleted_at: null,
      },
      include: {
        options: true,
        required_information: true,
      },
    });

    const calculatorResult: {
      authorize_representative_obligated: boolean;
      representive_tier?: RepresentativeTier | null;
      other_costs: OtherCost[];
      required_informations: RequiredInformation[];
    } = {
      authorize_representative_obligated: false,
      representive_tier: null,
      other_costs: [],
      required_informations: [],
    };

    for (const calculatorCriteria of calculatorCriterias) {
      if (calculatorCriteria.type === "PACKAGING_SERVICE") return;
      if (calculatorCriteria.type === "REPORT_SET") return;
      if (calculatorCriteria.type === "REPORT_FREQUENCY") return;

      const calculatorType = calculatorCriteria.calculator_type;

      const selectedOption = (() => {
        if (calculatorType === "LICENSE_FEES") {
          return calculatorCriteria.options.find(
            (o) => Number(o.option_value) < totalLicenseCosts && Number(o.option_to_value) > totalLicenseCosts
          );
        }

        if (calculatorType === "TOTAL_IN_KG") {
          return calculatorCriteria.options.find(
            (o) => Number(o.option_value) < totalFractionWeight && Number(o.option_to_value) > totalFractionWeight
          );
        }

        if (calculatorType === "TOTAL_IN_TONS") {
          return calculatorCriteria.options.find(
            (o) =>
              Number(o.option_value) < totalFractionWeight / 1000 &&
              Number(o.option_to_value) > totalFractionWeight / 1000
          );
        }
      })();

      if (!selectedOption) continue;

      if (calculatorCriteria.type === "AUTHORIZE_REPRESENTATIVE") {
        calculatorResult.authorize_representative_obligated = selectedOption?.value === "OBLIGED";
        continue;
      }

      if (calculatorCriteria.type === "REPRESENTATIVE_TIER") {
        const representativeTier = await this.databaseService.representativeTier.findUnique({
          where: {
            id: Number(selectedOption?.value),
          },
        });

        calculatorResult.representive_tier = representativeTier;
        continue;
      }

      if (calculatorCriteria.type === "OTHER_COST") {
        const otherCost = await this.databaseService.otherCost.findUnique({
          where: {
            id: Number(selectedOption?.value),
          },
        });

        if (!otherCost) continue;
        calculatorResult.other_costs.push(otherCost);
        continue;
      }

      if (calculatorCriteria.type === "REQUIRED_INFORMATION") {
        if (!calculatorCriteria.required_information_id) continue;
        const requiredInformation = await this.databaseService.requiredInformation.findUnique({
          where: {
            id: calculatorCriteria.required_information_id,
          },
        });

        if (!requiredInformation) continue;

        if (selectedOption?.value === "REQUEST") {
          calculatorResult.required_informations.push(requiredInformation);
        }

        continue;
      }
    }

    return {
      license_costs: totalLicenseCosts,
      ...calculatorResult,
    };
  }
}

export type CommitmentSubmitResult = {
  country: {
    id: number;
    name: string;
    code: string;
    flag_url: string;
    authorize_representative_obligated: boolean;
    other_costs_obligated: boolean;
  };
  year: string;
  packaging_services: (PackagingService & {
    obliged: boolean;
    report_set: ReportSet;
    report_set_frequency: ReportSetFrequency;
  })[];
  authorize_representative_obligated: boolean;
  representative_tier?: RepresentativeTier | null;
  other_costs_obligated: boolean;
  other_costs?: OtherCost[];
  required_informations?: RequiredInformation[];
  price_list: PriceList;
};
