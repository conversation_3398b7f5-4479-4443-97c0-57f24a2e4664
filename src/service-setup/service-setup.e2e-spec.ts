import { Test, TestingModule } from "@nestjs/testing";
import { INestApplication } from "@nestjs/common";
import * as request from "supertest";
import { AppModule } from "../app.module";
import { Role } from "../shared/auth/role.enum";
import { HEADER_SYSTEM_API_KEY, HEADER_USER_ID, HEADER_USER_ROLE, HEADER_USER_EMAIL } from "../shared/auth/const";
import { DatabaseService } from "../database/database.service";
import { CriteriaType } from "@prisma/client";

jest.setTimeout(30000);

describe("ServiceSetupController (e2e)", () => {
  let app: INestApplication;
  let databaseService: DatabaseService;

  const validApiKey = "test-api-key";
  const countryCode = "DE";

  beforeAll(() => {
    process.env.SYSTEM_API_KEY = validApiKey;
  });

  const authHeaders = {
    [HEADER_SYSTEM_API_KEY]: valid<PERSON><PERSON><PERSON><PERSON>,
    [HEADER_USER_ID]: "1",
    [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
    [HEADER_USER_EMAIL]: "<EMAIL>",
  };

  const mockCountry = {
    id: 1,
    code: "DE",
    name: "Germany",
    flag_url: "https://example.com/flag/de.png",
    authorize_representative_obligated: true,
    other_costs_obligated: true,
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
    deleted_at: null,
  };

  const mockPackagingService = {
    id: 1,
    name: "Household Packaging",
    description: "Packaging for household items",
    country_id: 1,
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
    deleted_at: null,
  };

  const mockReportSet = {
    id: 1,
    name: "Standard Report",
    packaging_service_id: 1,
    sheet_file_id: 1,
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
    deleted_at: null,
    sheet_file: {
      id: 1,
      filename: "report-sheet.xlsx",
      path: "/files/report-sheet.xlsx",
      size: 1024,
      mimetype: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      created_at: "2025-04-07T22:58:17.004Z",
      updated_at: "2025-04-07T22:58:17.004Z",
    },
    fractions: [
      {
        id: 1,
        code: "V6JKO4",
        name: "Paper",
        report_set_id: 1,
        parent_code: null,
        has_second_level: false,
        has_third_level: false,
        level: 1,
        order: 1,
        created_at: "2025-04-07T22:58:17.004Z",
        updated_at: "2025-04-07T22:58:17.004Z",
        deleted_at: null,
        fraction_icon: {
          id: 1,
          name: "paper",
          url: "https://example.com/icons/paper.svg",
          created_at: "2025-04-07T22:58:17.004Z",
          updated_at: "2025-04-07T22:58:17.004Z",
        },
        _count: { id: 1 },
        children: [],
      },
    ],
    columns: [
      {
        id: 1,
        code: "COL1",
        name: "Volume (kg)",
        report_set_id: 1,
        parent_code: null,
        order: 1,
        created_at: "2025-04-07T22:58:17.004Z",
        updated_at: "2025-04-07T22:58:17.004Z",
        deleted_at: null,
        _count: { id: 1 },
        children: [],
      },
    ],
    price_lists: [
      {
        id: 1,
        report_set_id: 1,
        type: "PRICE_PER_VOLUME_BASE_PRICE",
        license_year: 2025,
        base_price: 100,
        minimum_fee: null,
        fixed_price: null,
        created_at: "2025-04-07T22:58:17.004Z",
        updated_at: "2025-04-07T22:58:17.004Z",
        deleted_at: null,
        items: [
          {
            id: 1,
            report_set_price_list_id: 1,
            fraction_code: "V6JKO4",
            price: 0.5,
            created_at: "2025-04-07T22:58:17.004Z",
            updated_at: "2025-04-07T22:58:17.004Z",
          },
        ],
      },
    ],
    packaging_service: {
      id: 1,
      name: "Household Packaging",
      country_id: 1,
      criterias: [],
    },
  };

  const mockReportFrequency = {
    id: 1,
    rhythm: "QUARTERLY",
    frequency: JSON.stringify({
      type: "QUARTERLY",
      quarters: [1, 2, 3, 4],
    }),
    packaging_service_id: 1,
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
    deleted_at: null,
  };

  const mockRepresentativeTier = {
    id: 1,
    name: "Standard Tier",
    price: 500,
    country_id: 1,
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
    deleted_at: null,
    country: {
      criterias: [],
    },
  };

  const mockOtherCost = {
    id: 1,
    name: "Registration Fee",
    price: 200,
    country_id: 1,
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
    deleted_at: null,
    country: {
      criterias: [],
    },
  };

  const mockRequiredInformation = {
    id: 1,
    name: "VAT Registration",
    country_id: 1,
    file_id: 1,
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
    deleted_at: null,
    file: {
      id: 1,
      filename: "vat-form.pdf",
      path: "/files/vat-form.pdf",
      size: 1024,
      mimetype: "application/pdf",
      created_at: "2025-04-07T22:58:17.004Z",
      updated_at: "2025-04-07T22:58:17.004Z",
    },
    criterias: [],
    has_criteria: false,
  };

  const mockPriceList = {
    id: 1,
    name: "2025 Price List",
    condition_type: "LICENSE_YEAR",
    condition_type_value: "2025",
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
    deleted_at: null,
  };

  const mockCountryPriceList = {
    id: 1,
    country_id: 1,
    price_list_id: 1,
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
    deleted_at: null,
    price_list: mockPriceList,
  };

  const mockCriteria = {
    id: 1,
    type: "PACKAGING_SERVICE" as CriteriaType,
    mode: "COMMITMENT",
    text: "Are you obligated for packaging service?",
    description: null,
    calculator_type: null,
    country_id: 1,
    packaging_service_id: 1,
    required_information_id: null,
    created_at: "2025-04-07T22:58:17.004Z",
    updated_at: "2025-04-07T22:58:17.004Z",
    deleted_at: null,
    options: [
      {
        id: 1,
        criteria_id: 1,
        text: "Yes, I am obligated",
        value: "OBLIGED",
        option_value: null,
        option_to_value: null,
        created_at: "2025-04-07T22:58:17.004Z",
        updated_at: "2025-04-07T22:58:17.004Z",
      },
      {
        id: 2,
        criteria_id: 1,
        text: "No, I am not obligated",
        value: "NOT_OBLIGED",
        option_value: null,
        option_to_value: null,
        created_at: "2025-04-07T22:58:17.004Z",
        updated_at: "2025-04-07T22:58:17.004Z",
      },
    ],
  };

  const mockSubmitCommitmentResult = {
    year: "2025",
    setup: {
      country: {
        id: 1,
        name: "Germany",
        code: "DE",
        flag_url: "https://example.com/flag/de.png",
        authorize_representative_obligated: true,
        other_costs_obligated: true,
      },
      year: "2025",
      packaging_services: [
        {
          id: 1,
          name: "Household Packaging",
          description: "Packaging for household items",
          country_id: 1,
          created_at: "2025-04-07T22:58:17.004Z",
          updated_at: "2025-04-07T22:58:17.004Z",
          deleted_at: null,
          obliged: true,
          report_set: mockReportSet,
          report_set_frequency: {
            ...mockReportFrequency,
            frequency: JSON.parse(mockReportFrequency.frequency as string),
          },
        },
      ],
      authorize_representative_obligated: true,
      representative_tier: mockRepresentativeTier,
      other_costs_obligated: true,
      other_costs: [mockOtherCost],
      required_informations: [mockRequiredInformation],
      price_list: mockPriceList,
    },
    commitment: [
      {
        ...mockCriteria,
        answer: "OBLIGED",
      },
    ],
  };

  const mockCalculateLicenseCostsResult = {
    license_costs: 150,
    authorize_representative_obligated: true,
    representive_tier: mockRepresentativeTier,
    other_costs: [mockOtherCost],
    required_informations: [mockRequiredInformation],
  };

  // Setup test module and app
  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    })
      .overrideProvider(DatabaseService)
      .useValue({
        country: {
          findUnique: jest.fn().mockImplementation(({ where }) => {
            if (where.code === countryCode) {
              return Promise.resolve({
                ...mockCountry,
                packaging_services: [mockPackagingService],
                representative_tiers: [mockRepresentativeTier],
                required_informations: [mockRequiredInformation],
                other_costs: [mockOtherCost],
                country_price_lists: [mockCountryPriceList],
              });
            }
            return Promise.resolve(null);
          }),
        },
        packagingService: {
          findMany: jest.fn().mockResolvedValue([
            {
              ...mockPackagingService,
              criterias: [mockCriteria],
            },
          ]),
        },
        reportSet: {
          findMany: jest.fn().mockResolvedValue([mockReportSet]),
          findUnique: jest.fn().mockResolvedValue(mockReportSet),
        },
        reportSetFrequency: {
          findMany: jest.fn().mockResolvedValue([mockReportFrequency]),
        },
        representativeTier: {
          findMany: jest.fn().mockResolvedValue([mockRepresentativeTier]),
          findUnique: jest.fn().mockResolvedValue(mockRepresentativeTier),
        },
        otherCost: {
          findMany: jest.fn().mockResolvedValue([mockOtherCost]),
          findUnique: jest.fn().mockResolvedValue(mockOtherCost),
        },
        requiredInformation: {
          findMany: jest.fn().mockResolvedValue([mockRequiredInformation]),
          findUnique: jest.fn().mockResolvedValue(mockRequiredInformation),
        },
        countryPriceList: {
          findMany: jest.fn().mockResolvedValue([mockCountryPriceList]),
        },
        criteria: {
          findMany: jest.fn().mockResolvedValue([mockCriteria]),
        },
      })
      .compile();

    app = moduleFixture.createNestApplication();
    databaseService = moduleFixture.get<DatabaseService>(DatabaseService);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("/service-setups/:countryCode (GET)", () => {
    it("should reject when authenticated with unauthorized role even with valid API key", () => {
      const unauthorizedHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.CUSTOMER,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get(`/service-setups/${countryCode}`).set(unauthorizedHeaders).expect(403);
    });

    it("should reject when API key is invalid regardless of role", () => {
      const invalidApiKeyHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ID]: "1",
        [HEADER_USER_ROLE]: Role.SUPER_ADMIN,
        [HEADER_USER_EMAIL]: "<EMAIL>",
      };

      return request(app.getHttpServer()).get(`/service-setups/${countryCode}`).set(invalidApiKeyHeaders).expect(401);
    });

    it("should reject when not authenticated at all", () => {
      return request(app.getHttpServer()).get(`/service-setups/${countryCode}`).expect(401);
    });
  });

  describe("/service-setups/:countryCode/packaging-services (GET)", () => {
    it("should return packaging services when authenticated properly", () => {
      return request(app.getHttpServer())
        .get(`/service-setups/${countryCode}/packaging-services`)
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(Array.isArray(response.body)).toBe(true);
          expect(databaseService.packagingService.findMany).toHaveBeenCalled();
        });
    });
  });

  describe("/service-setups/:countryCode/report-sets (GET)", () => {
    it("should return report sets when authenticated properly", () => {
      return request(app.getHttpServer())
        .get(`/service-setups/${countryCode}/report-sets`)
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(Array.isArray(response.body)).toBe(true);
          expect(databaseService.reportSet.findMany).toHaveBeenCalled();
        });
    });
  });

  describe("/service-setups/:countryCode/report-sets/:reportSetId (GET)", () => {
    it("should return specific report set when authenticated properly", () => {
      return request(app.getHttpServer())
        .get(`/service-setups/${countryCode}/report-sets/1`)
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(response.body).toHaveProperty("id", 1);
          expect(databaseService.reportSet.findUnique).toHaveBeenCalled();
        });
    });
  });

  describe("/service-setups/:countryCode/representative-tiers (GET)", () => {
    it("should return representative tiers when authenticated properly", () => {
      return request(app.getHttpServer())
        .get(`/service-setups/${countryCode}/representative-tiers`)
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(Array.isArray(response.body)).toBe(true);
          expect(databaseService.representativeTier.findMany).toHaveBeenCalled();
        });
    });
  });

  describe("/service-setups/:countryCode/other-costs (GET)", () => {
    it("should return other costs when authenticated properly", () => {
      return request(app.getHttpServer())
        .get(`/service-setups/${countryCode}/other-costs`)
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(Array.isArray(response.body)).toBe(true);
          expect(databaseService.otherCost.findMany).toHaveBeenCalled();
        });
    });
  });

  describe("/service-setups/:countryCode/price-lists (GET)", () => {
    it("should return price lists even when not authenticated (public route)", () => {
      return request(app.getHttpServer())
        .get(`/service-setups/${countryCode}/price-lists`)
        .expect(200)
        .then((response) => {
          expect(Array.isArray(response.body)).toBe(true);
          expect(databaseService.countryPriceList.findMany).toHaveBeenCalled();
        });
    });
  });

  describe("/service-setups/:countryCode/required-informations (GET)", () => {
    it("should return required informations when authenticated properly", () => {
      return request(app.getHttpServer())
        .get(`/service-setups/${countryCode}/required-informations`)
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(Array.isArray(response.body)).toBe(true);
          expect(databaseService.requiredInformation.findMany).toHaveBeenCalled();
        });
    });
  });

  describe("/service-setups/:countryCode/criterias (GET)", () => {
    it("should return criterias when authenticated properly", () => {
      const type = "PACKAGING_SERVICE";
      return request(app.getHttpServer())
        .get(`/service-setups/${countryCode}/criterias?type=${type}`)
        .set(authHeaders)
        .expect(200)
        .then((response) => {
          expect(Array.isArray(response.body)).toBe(true);
          expect(databaseService.criteria.findMany).toHaveBeenCalled();
        });
    });
  });

  describe("/service-setups/:countryCode/commitment (GET)", () => {
    it("should return commitment even when not authenticated (public route)", () => {
      return request(app.getHttpServer())
        .get(`/service-setups/${countryCode}/commitment`)
        .expect(200)
        .then((response) => {
          expect(Array.isArray(response.body)).toBe(true);
          expect(databaseService.criteria.findMany).toHaveBeenCalled();
        });
    });
  });

  describe("/service-setups/:countryCode/calculator (POST)", () => {
    const calculatorData = {
      year: 2025,
      report_sets: [
        {
          id: 1,
          fractions: [{ code: "V6JKO4", weight: 300 }],
        },
      ],
    };

    beforeEach(() => {
      // Mock the service method for this specific test
      databaseService.criteria.findMany = jest.fn().mockImplementation(({ where }) => {
        if (where.mode === "CALCULATOR") {
          return Promise.resolve([]);
        }
        return Promise.resolve([]);
      });
    });

    it("should calculate license costs even when not authenticated (public route)", () => {
      return request(app.getHttpServer())
        .post(`/service-setups/${countryCode}/calculator`)
        .send(calculatorData)
        .expect(201)
        .then((response) => {
          expect(response.body).toHaveProperty("license_costs");
        });
    });
  });

  describe("System role access", () => {
    it("should allow access with SYSTEM role and valid API key", () => {
      const systemHeaders = {
        [HEADER_SYSTEM_API_KEY]: validApiKey,
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer())
        .get(`/service-setups/${countryCode}/packaging-services`)
        .set(systemHeaders)
        .expect(200);
    });

    it("should reject access with SYSTEM role but invalid API key", () => {
      const invalidSystemHeaders = {
        [HEADER_SYSTEM_API_KEY]: "invalid-api-key",
        [HEADER_USER_ROLE]: Role.SYSTEM,
      };

      return request(app.getHttpServer())
        .get(`/service-setups/${countryCode}/packaging-services`)
        .set(invalidSystemHeaders)
        .expect(401);
    });
  });
});
