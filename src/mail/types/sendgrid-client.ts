import { Injectable } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import * as SendGrid from "@sendgrid/mail";
import { MailDataRequired } from "@sendgrid/mail";

@Injectable()
export class SendGridClient {
  constructor(private readonly configService: ConfigService) {
    // Don't forget this one.
    // The apiKey is required to authenticate our
    // request to SendGrid API.
    const sendgridApiKey = this.configService.get<string>("SENDGRID_API_KEY");

    if (!sendgridApiKey) {
      throw new Error("Missing required environment variables for SendGrid");
    }

    SendGrid.setApiKey(sendgridApiKey);
  }

  async send(mail: MailDataRequired): Promise<void> {
    try {
      await SendGrid.send(mail);
    } catch (error) {
      throw error;
    }
  }
}
