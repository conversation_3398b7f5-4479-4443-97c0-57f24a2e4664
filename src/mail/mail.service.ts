import { Injectable, InternalServerErrorException } from "@nestjs/common";
import { APIClient, RegionEU, SendEmailRequest } from "customerio-node";
import { SendEmailRequestOptions } from "customerio-node/dist/lib/api/requests";
import { Attachment } from "nodemailer/lib/mailer";
import { ForgotPasswordMailDto } from "./dto/forgot-password.dto";

@Injectable()
export class MailService {
  public layoutAttachments: Attachment[];
  public api;

  constructor() {
    const customerIoKey = process.env.CUSTOMER_IO_KEY;

    if (!customerIoKey) {
      throw new Error("Missing required environment variables for Customer.io");
    }
    this.api = new APIClient(customerIoKey, { region: RegionEU });
  }

  async sendForgotPasswordMail(dto: ForgotPasswordMailDto) {
    try {
      const request = new SendEmailRequest({
        transactional_message_id: dto.role === "clerk" ? "32" : "3",
        identifiers: {
          email: dto.to,
        },
        to: dto.to,
        from: "Lizenzero <<EMAIL>>",
        subject: "Reset password Lizenzero",
        message_data: {
          link_reset: dto.callbackUrl,
        },
      });

      return await this.api.sendEmail(request);
    } catch (error) {
      console.log("🚀 ~ MailService ~ sendForgotPasswordMail ~ error:", error);
      throw new InternalServerErrorException(error, "Erro ao enviar email");
    }
  }

  async sendEmailVerificationMail(dto: Partial<ForgotPasswordMailDto>) {
    try {
      const request = new SendEmailRequest({
        transactional_message_id: dto.callbackUrl ? "9" : "5",
        identifiers: {
          email: dto.to!,
        },
        to: dto.to!,
        from: "Lizenzero <<EMAIL>>",
        subject: "Verify email Lizenzero",
        message_data: {
          verification_code: dto.token,
          magic_link: dto.callbackUrl,
        },
      });

      return await this.api.sendEmail(request);
    } catch (error) {
      throw new InternalServerErrorException(error, "Erro ao enviar email");
    }
  }

  async sendMagicLink(dto: ForgotPasswordMailDto) {
    try {
      const request = new SendEmailRequest({
        transactional_message_id: "38",
        identifiers: {
          email: dto.to,
        },
        to: dto.to,
        from: "Lizenzero <<EMAIL>>",
        subject: "Verify email Lizenzero",
        message_data: {
          magic_link: dto.callbackUrl,
        },
      });

      return await this.api.sendEmail(request);
    } catch (error) {
      throw new InternalServerErrorException(error, "Erro ao enviar email");
    }
  }

  async sendMessage(customerIoDto: SendEmailRequestOptions) {
    const request = new SendEmailRequest(customerIoDto);

    return await this.api.sendEmail(request);
  }
}
