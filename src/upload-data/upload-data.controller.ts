import { Body, Controller, Get, Post, Query, UploadedFile, UseInterceptors } from "@nestjs/common";
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiConsumes, ApiBody } from "@nestjs/swagger";
import { UploadDataService } from "./upload-data.service";
import { FileInterceptor } from "@nestjs/platform-express";

@ApiTags("Upload Data")
@Controller("upload-data")
export class UploadDataController {
  constructor(private readonly uploadDataService: UploadDataService) {}

  @Get()
  @ApiOperation({ summary: "Retrieve all upload data by broker ID" })
  @ApiQuery({
    name: "id",
    required: true,
    description: "The ID of the broker",
    example: "1",
  })
  @ApiResponse({
    status: 200,
    description: "Successfully retrieved upload data",
    schema: {
      example: [
        {
          id: 1,
          fileName: "example.txt",
          uploadedAt: "2023-01-01T00:00:00Z",
          size: 1024,
        },
      ],
    },
  })
  @ApiResponse({
    status: 400,
    description: "Invalid ID provided",
  })
  async findAll(@Query("id") id: string) {
    return this.uploadDataService.findAll(+id);
  }

  @Post("preview")
  @UseInterceptors(FileInterceptor("file"))
  @ApiOperation({ summary: "Upload an Excel file with orders and specify company_id" })
  @ApiConsumes("multipart/form-data")
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        file: { type: "string", format: "binary" },
        type: { type: "string", description: "order data or company data" },
      },
    },
  })
  @ApiResponse({ status: 201, description: "File uploaded and processed successfully" })
  @ApiResponse({ status: 400, description: "Invalid file format or missing broker_id" })
  async preview(@UploadedFile() file: Express.Multer.File, @Body() dto: { type: string }) {
    if (!file || !file.originalname.match(/\.(xls|xlsx)$/)) {
      throw new Error("Only Excel files are allowed!");
    }
    return await this.uploadDataService.preview(file, dto.type);
  }
}
